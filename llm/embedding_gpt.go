package llm

import (
	"agent/consts"
	"agent/utils/encryptUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/sashabaranov/go-openai"
)

type GptEmbedding struct{}

func (gpt *GptEmbedding) EmbeddingTextByLlm(text []string) ([][]float32, error) {

	startTime := time.Now() // Start the timer
	log.Printf("RAG:llm:EmbeddingTextByLlm: function started at %s", startTime)

	tokenEncrypt := envUtils.GetString(consts.EncryptPaasAuthToken)
	token, err := encryptUtils.DecryptWithSm4Ecb(tokenEncrypt)
	if err != nil {
		log.Errorf("RAG:llm:EmbeddingTextByLlm:Get Key error: %v\n", err)
		return nil, errors.New("RAG:llm:EmbeddingTextByLlm:Get Key failed")
	}
	baseURLEncrypt := envUtils.GetString(consts.EncryptPaasBaseURL)
	baseURL, err := encryptUtils.DecryptWithSm4Ecb(baseURLEncrypt)
	if err != nil {
		log.Errorf("RAG:llm:EmbeddingTextByLlm:Get baseUrl error: %v\n", err)
		return nil, errors.New("RAG:llm:EmbeddingTextByLlm:Get baseUrl failed")
	}
	config := openai.DefaultConfig(token)
	config.BaseURL = baseURL
	client := openai.NewClientWithConfig(config)
	queryReq := openai.EmbeddingRequest{
		Input: text,
		Model: consts.GptEmbeddingModel,
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	resp, err := client.CreateEmbeddings(ctx, queryReq)

	if err != nil {
		log.Errorf("RAG:llm:EmbeddingTextByLlm:ChatCompletion error: %v\n", err)
		return nil, err
	}

	result := make([][]float32, 0)
	for _, embedding := range resp.Data {
		result = append(result, embedding.Embedding)
	}

	elapsedTime := time.Since(startTime) // Calculate elapsed time
	log.Printf("RAG:llm:EmbeddingTextByLlm: function completed. Time taken: %s", elapsedTime)

	return result, nil
}

package llm

import (
	"testing"
)

func TestGptEmbedding_EmbeddingTextByLlm(t *testing.T) {
	gpt := &GptEmbedding{}

	// 测试空文本输入
	emptyTexts := []string{}
	embeddings, err := gpt.EmbeddingTextByLlm(emptyTexts)
	if err == nil {
		t.Log("空文本输入处理正常")
	} else {
		t.Logf("空文本输入错误（预期）: %v", err)
	}

	// 测试正常文本输入
	texts := []string{"这是一个测试文本", "另一个测试文本"}
	embeddings, err = gpt.EmbeddingTextByLlm(texts)
	if err != nil {
		t.Logf("正常文本输入错误（可能是网络或配置问题）: %v", err)
	} else {
		if len(embeddings) != len(texts) {
			t.<PERSON><PERSON>rf("期望嵌入数量 %d，实际 %d", len(texts), len(embeddings))
		}
		for i, embedding := range embeddings {
			if len(embedding) == 0 {
				t.<PERSON><PERSON><PERSON>("第 %d 个文本的嵌入向量为空", i)
			}
		}
	}
}

func TestGptEmbedding_EmbeddingTextByLlmWithSpecialCharacters(t *testing.T) {
	gpt := &GptEmbedding{}

	// 测试包含特殊字符的文本
	specialTexts := []string{
		"包含特殊字符的文本：!@#$%^&*()",
		"包含中文和英文 mixed text",
		"包含换行符\n和制表符\t的文本",
	}

	embeddings, err := gpt.EmbeddingTextByLlm(specialTexts)
	if err != nil {
		t.Logf("特殊字符文本输入错误（可能是网络或配置问题）: %v", err)
	} else {
		if len(embeddings) != len(specialTexts) {
			t.Errorf("期望嵌入数量 %d，实际 %d", len(specialTexts), len(embeddings))
		}
	}
}

func TestGptEmbedding_EmbeddingTextByLlmWithLongText(t *testing.T) {
	gpt := &GptEmbedding{}

	// 测试长文本
	longText := "这是一个很长的文本，用来测试模型处理长文本的能力。"
	longTexts := []string{longText + longText + longText}

	embeddings, err := gpt.EmbeddingTextByLlm(longTexts)
	if err != nil {
		t.Logf("长文本输入错误（可能是网络或配置问题）: %v", err)
	} else {
		if len(embeddings) != 1 {
			t.Errorf("期望嵌入数量 1，实际 %d", len(embeddings))
		}
	}
}

func TestEmbeddingInterface(t *testing.T) {
	// 测试接口实现
	var embedding Embedding = &GptEmbedding{}

	// 验证接口类型
	if embedding == nil {
		t.Error("Embedding接口不应该为nil")
	}

	// 测试接口方法调用
	texts := []string{"测试接口实现"}
	_, err := embedding.EmbeddingTextByLlm(texts)
	if err != nil {
		t.Logf("接口方法调用错误（可能是网络或配置问题）: %v", err)
	}
}

func TestGptEmbedding_EmbeddingTextByLlmWithNilInput(t *testing.T) {
	gpt := &GptEmbedding{}

	// 测试nil输入
	var nilTexts []string
	embeddings, err := gpt.EmbeddingTextByLlm(nilTexts)
	if err == nil {
		t.Log("nil输入处理正常")
	} else {
		t.Logf("nil输入错误（预期）: %v", err)
	}

	if embeddings != nil {
		t.Error("nil输入应该返回nil嵌入")
	}
}

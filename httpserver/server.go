package httpserver

import (
	"agent/pkg/response"
	"agent/pkg/xgin"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

type Server struct {
	addr   string
	router *gin.Engine
}

func New(addr string) *Server {
	router := xgin.New()

	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, response.New("ok"))
	})

	ctrl := NewBrowserController()
	browser := router.Group("/browser")
	{
		browser.POST("/screenshot", ctrl.Screenshot)
		browser.POST("/screenshotNewestPage", ctrl.ScreenshotNewestPage)
		browser.POST("/openTab", ctrl.OpenTab)
		browser.POST("/getConsoleLogs", ctrl.GetConsoleLogs)
		browser.POST("/getRequestList", ctrl.GetRequestList)
	}

	lintController := NewLintController()
	lintApi := router.Group("/lint")
	{
		lintApi.POST("/diagnostic", lintController.LintHandler)
		lintApi.POST("/fix", lintController.FixHandler)
	}

	server := &Server{
		addr:   addr,
		router: router,
	}

	return server
}

// Start starts the HTTP server
func (s *Server) Start() {
	// Start listening for HTTP requests
	if err := s.router.Run(s.addr); err != nil {
		panic(fmt.Sprintf("start Http server [%s] error:%v", s.addr, err))
	}
}

package model

type ResponseList struct {
	Total int64       `json:"total"`
	List  interface{} `json:"list"`
}

type ResponseScreenshot struct {
	ImageType string `json:"image_type"`
	Data      string `json:"data"`
}

// Position represents a position in a document
type Position struct {
	Line      int `json:"line"`      // Line number (1-based)
	Character int `json:"character"` // Column number (0-based)
}

// Range represents a range in a document
type Range struct {
	Start Position `json:"start"` // Start position
	End   Position `json:"end"`   // End position
}

// DiagnosticItem represents a single diagnostic item found in code analysis
type DiagnosticItem struct {
	File       string   `json:"file"`                  // File path where the issue was found
	Line       int      `json:"line"`                  // Line number (1-based)
	Column     int      `json:"column"`                // Column number (0-based)
	Message    string   `json:"message"`               // Diagnostic message
	Source     string   `json:"source"`                // Source of the diagnostic ('lsp', 'treesitter', 'eslint', etc.)
	Severity   string   `json:"severity"`              // Severity level (error, warning, information, hint)
	Code       string   `json:"code,omitempty"`        // Error code if available
	Range      *Range   `json:"range,omitempty"`       // Complete range information if available
	FixOptions []string `json:"fix_options,omitempty"` // Available fix options if any
}

// LintResponse contains a list of diagnostic items from code analysis
type LintResponse struct {
	Diagnostics []DiagnosticItem `json:"diagnostics"`       // List of diagnostic items
	Summary     *LintSummary     `json:"summary,omitempty"` // Summary of lint findings
}

// LintSummary provides summary statistics about lint findings
type LintSummary struct {
	TotalFiles      int            `json:"total_files"`       // Total number of files analyzed
	FilesWithIssues int            `json:"files_with_issues"` // Number of files with issues
	ErrorCount      int            `json:"error_count"`       // Number of errors
	WarningCount    int            `json:"warning_count"`     // Number of warnings
	InfoCount       int            `json:"info_count"`        // Number of informational messages
	HintCount       int            `json:"hint_count"`        // Number of hints
	BySeverity      map[string]int `json:"by_severity"`       // Count by severity
	BySource        map[string]int `json:"by_source"`         // Count by source
}

// FixResponse contains the result of a code fix operation
type FixResponse struct {
	FilePath   string   `json:"file_path"`             // Path of the file that was fixed
	FixedFiles []string `json:"fixed_files"`           // List of files that were successfully fixed
	Success    bool     `json:"success"`               // Whether the fix operation was successful
	FixCount   int      `json:"fix_count"`             // Number of issues fixed
	NewContent string   `json:"new_content,omitempty"` // New content if requested
	Errors     []string `json:"errors,omitempty"`      // List of errors if any occurred
}

// ErrorResponse represents an error response from lint or fix operations
type ErrorResponse struct {
	Error      string `json:"error"`                // Error message
	ErrorCode  string `json:"error_code"`           // Error code
	Suggestion string `json:"suggestion,omitempty"` // Suggestion for fixing the error
}

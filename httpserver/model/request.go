package model

import (
	"agent/linters"
	"fmt"
	"path/filepath"
	"strings"
)

type RequestScreenshot struct {
	ImageType   string `json:"image_type" validate:"required"`
	ImageFormat string `json:"image_format" validate:"required"`
}

type RequestBrowserOpenTab struct {
	Url string `json:"url" validate:"required"`
}

// LintRequest represents a request to lint one or more files
type LintRequest struct {
	Files        []string `json:"paths" validate:"required"`
	LinterType   string   `json:"linter_type,omitempty"`   // Optional specific linter type to use
	LanguageType string   `json:"language_type,omitempty"` // Optional language to use for linting
}

// Validate ensures the LintRequest has valid fields
func (lr *LintRequest) Validate() error {
	if len(lr.Files) == 0 {
		return fmt.Errorf("no files provided")
	}

	// Validate each file path
	for _, file := range lr.Files {
		if strings.TrimSpace(file) == "" {
			return fmt.Errorf("empty file path provided")
		}
	}

	// If linter type is specified, check if it's valid
	if lr.LinterType != "" {
		valid := false
		for _, linterType := range []string{
			string(linters.LinterTypeESLint),
			string(linters.LinterTypeRuff),
			string(linters.LinterTypeMyPy),
			string(linters.LinterTypeGolangCILint),
			string(linters.LinterTypeFlake8),
			string(linters.LinterTypePylint),
		} {
			if lr.LinterType == linterType {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("unsupported linter type: %s", lr.LinterType)
		}
	}

	return nil
}

// FixRequest represents a request to fix issues in a file
type FixRequest struct {
	FilePath      string `json:"file_path" validate:"required"` // Path to the file to fix
	LinterType    string `json:"linter_type,omitempty"`         // Optional specific linter type to use
	ReturnContent bool   `json:"return_content,omitempty"`      // Whether to include fixed content in the response
}

// Validate ensures the FixRequest has valid fields
func (fr *FixRequest) Validate() error {
	if strings.TrimSpace(fr.FilePath) == "" {
		return fmt.Errorf("no file path provided")
	}

	// Check file extension if linter type not specified
	if fr.LinterType == "" {
		ext := filepath.Ext(fr.FilePath)
		if ext == "" {
			return fmt.Errorf("file has no extension, cannot determine appropriate linter")
		}

		// File extension is mapped to language in FileExtToLanguage map in linters package
		if _, exists := linters.FileExtToLanguage[ext]; !exists {
			return fmt.Errorf("unsupported file extension: %s", ext)
		}
	} else {
		// If linter type is specified, check if it's valid
		valid := false
		for _, linterType := range []string{
			string(linters.LinterTypeESLint),
			string(linters.LinterTypeRuff),
			string(linters.LinterTypeGolangCILint),
		} {
			if fr.LinterType == linterType {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("unsupported or non-fixing linter type: %s", fr.LinterType)
		}
	}

	return nil
}

// FileExtensionLanguageMap maps file extensions to language types
var FileExtensionLanguageMap = linters.FileExtToLanguage

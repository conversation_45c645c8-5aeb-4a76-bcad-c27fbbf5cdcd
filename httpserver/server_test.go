package httpserver

import (
	"agent/pkg/response"
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("成功创建服务器实例", func(t *testing.T) {
		addr := ":8080"
		server := New(addr)

		assert.NotNil(t, server)
		assert.Equal(t, addr, server.addr)
		assert.NotNil(t, server.router)
	})

	t.Run("创建不同地址的服务器实例", func(t *testing.T) {
		testCases := []string{
			":3000",
			"localhost:8080",
			"127.0.0.1:9000",
		}

		for _, addr := range testCases {
			server := New(addr)
			assert.NotNil(t, server)
			assert.Equal(t, addr, server.addr)
			assert.NotNil(t, server.router)
		}
	})
}

func TestHealthEndpoint(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("健康检查端点返回正确响应", func(t *testing.T) {
		server := New(":8080")

		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/health", nil)

		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var resp response.Response
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, 200, resp.ErrorCode)
		assert.Equal(t, "Succeed", resp.Description)
		assert.Equal(t, "ok", resp.Data)
	})

	t.Run("健康检查端点只接受GET请求", func(t *testing.T) {
		server := New(":8080")

		methods := []string{"POST", "PUT", "DELETE", "PATCH"}

		for _, method := range methods {
			w := httptest.NewRecorder()
			req := httptest.NewRequest(method, "/health", nil)

			server.router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusNotFound, w.Code,
				"方法 %s 应该返回404", method)
		}
	})
}

func TestBrowserRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("浏览器路由组正确配置", func(t *testing.T) {
		server := New(":8080")

		// 测试路由是否存在 - 发送请求到各个端点
		browserEndpoints := []string{
			"/browser/screenshot",
			"/browser/openTab",
			"/browser/getConsoleLogs",
			"/browser/getRequestList",
		}

		for _, endpoint := range browserEndpoints {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", endpoint, bytes.NewBuffer([]byte("{}")))
			req.Header.Set("Content-Type", "application/json")

			server.router.ServeHTTP(w, req)

			// 路由存在的话不应该返回404
			assert.NotEqual(t, http.StatusNotFound, w.Code,
				"端点 %s 应该存在", endpoint)
		}
	})

	t.Run("浏览器路由只接受POST请求", func(t *testing.T) {
		server := New(":8080")

		methods := []string{"GET", "PUT", "DELETE", "PATCH"}
		endpoint := "/browser/screenshot"

		for _, method := range methods {
			w := httptest.NewRecorder()
			req := httptest.NewRequest(method, endpoint, nil)

			server.router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusNotFound, w.Code,
				"方法 %s 应该返回404", method)
		}
	})
}

func TestLintRoutes(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("Lint路由组正确配置", func(t *testing.T) {
		server := New(":8080")

		lintEndpoints := []string{
			"/lint/diagnostic",
			"/lint/fix",
		}

		for _, endpoint := range lintEndpoints {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", endpoint, bytes.NewBuffer([]byte("{}")))
			req.Header.Set("Content-Type", "application/json")

			server.router.ServeHTTP(w, req)

			// 路由存在的话不应该返回404
			assert.NotEqual(t, http.StatusNotFound, w.Code,
				"端点 %s 应该存在", endpoint)
		}
	})

	t.Run("Lint路由只接受POST请求", func(t *testing.T) {
		server := New(":8080")

		methods := []string{"GET", "PUT", "DELETE", "PATCH"}
		endpoint := "/lint/diagnostic"

		for _, method := range methods {
			w := httptest.NewRecorder()
			req := httptest.NewRequest(method, endpoint, nil)

			server.router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusNotFound, w.Code,
				"方法 %s 应该返回404", method)
		}
	})
}

func TestServer_Start(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("服务器启动成功", func(t *testing.T) {
		// Mock gin.Engine.Run 方法返回成功
		patches := gomonkey.ApplyMethod(&gin.Engine{}, "Run", func(_ *gin.Engine, addr ...string) error {
			return nil
		})
		defer patches.Reset()

		server := New(":8080")

		// 在goroutine中启动服务器以避免阻塞
		done := make(chan bool)
		go func() {
			defer func() {
				if r := recover(); r != nil {
					t.Errorf("服务器启动时发生panic: %v", r)
				}
				done <- true
			}()
			server.Start()
		}()

		// 等待一段时间确保启动完成
		select {
		case <-done:
			// 测试通过
		case <-time.After(100 * time.Millisecond):
			// 测试通过，服务器正常启动
		}
	})

	t.Run("服务器启动失败时panic", func(t *testing.T) {
		// Mock gin.Engine.Run 方法返回错误
		mockError := assert.AnError
		patches := gomonkey.ApplyMethod(&gin.Engine{}, "Run", func(_ *gin.Engine, addr ...string) error {
			return mockError
		})
		defer patches.Reset()

		server := New(":8080")

		assert.Panics(t, func() {
			server.Start()
		}, "服务器启动失败时应该panic")
	})
}

func TestServerIntegration(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("完整服务器集成测试", func(t *testing.T) {
		server := New(":8080")

		// 测试健康检查
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/health", nil)
		server.router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// 测试不存在的路由
		w = httptest.NewRecorder()
		req = httptest.NewRequest("GET", "/nonexistent", nil)
		server.router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("并发请求测试", func(t *testing.T) {
		server := New(":8080")

		const numRequests = 10
		results := make(chan int, numRequests)

		// 并发发送多个健康检查请求
		for i := 0; i < numRequests; i++ {
			go func() {
				w := httptest.NewRecorder()
				req := httptest.NewRequest("GET", "/health", nil)
				server.router.ServeHTTP(w, req)
				results <- w.Code
			}()
		}

		// 验证所有请求都返回200
		for i := 0; i < numRequests; i++ {
			select {
			case code := <-results:
				assert.Equal(t, http.StatusOK, code)
			case <-time.After(1 * time.Second):
				t.Fatal("请求超时")
			}
		}
	})
}

func TestRouteGroupConfiguration(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("验证路由组前缀", func(t *testing.T) {
		server := New(":8080")

		// 测试浏览器路由组前缀
		w := httptest.NewRecorder()
		req := httptest.NewRequest("POST", "/browser/screenshot", bytes.NewBuffer([]byte("{}")))
		req.Header.Set("Content-Type", "application/json")
		server.router.ServeHTTP(w, req)
		assert.NotEqual(t, http.StatusNotFound, w.Code)

		// 测试lint路由组前缀
		w = httptest.NewRecorder()
		req = httptest.NewRequest("POST", "/lint/diagnostic", bytes.NewBuffer([]byte("{}")))
		req.Header.Set("Content-Type", "application/json")
		server.router.ServeHTTP(w, req)
		assert.NotEqual(t, http.StatusNotFound, w.Code)
	})

	t.Run("验证无效路由组路径", func(t *testing.T) {
		server := New(":8080")

		invalidPaths := []string{
			"/browser",         // 缺少具体端点
			"/lint",            // 缺少具体端点
			"/browser/invalid", // 无效的浏览器端点
			"/lint/invalid",    // 无效的lint端点
		}

		for _, path := range invalidPaths {
			w := httptest.NewRecorder()
			req := httptest.NewRequest("POST", path, bytes.NewBuffer([]byte("{}")))
			req.Header.Set("Content-Type", "application/json")
			server.router.ServeHTTP(w, req)
			assert.Equal(t, http.StatusNotFound, w.Code,
				"路径 %s 应该返回404", path)
		}
	})
}

func TestServerStruct(t *testing.T) {
	t.Run("验证Server结构体字段", func(t *testing.T) {
		server := New(":8080")

		// 验证结构体字段是否正确设置
		assert.IsType(t, &Server{}, server)
		assert.NotEmpty(t, server.addr)
		assert.NotNil(t, server.router)
	})

	t.Run("验证不同地址格式", func(t *testing.T) {
		testAddresses := []string{
			":8080",
			"localhost:3000",
			"127.0.0.1:8080",
			"0.0.0.0:9000",
		}

		for _, addr := range testAddresses {
			server := New(addr)
			assert.Equal(t, addr, server.addr,
				"地址 %s 应该正确设置", addr)
		}
	})
}

// 基准测试
func BenchmarkServerCreation(b *testing.B) {
	gin.SetMode(gin.TestMode)

	for i := 0; i < b.N; i++ {
		_ = New(":8080")
	}
}

func BenchmarkHealthEndpoint(b *testing.B) {
	gin.SetMode(gin.TestMode)
	server := New(":8080")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/health", nil)
		server.router.ServeHTTP(w, req)
	}
}

func BenchmarkRouteMatching(b *testing.B) {
	gin.SetMode(gin.TestMode)
	server := New(":8080")

	endpoints := []string{
		"/health",
		"/browser/screenshot",
		"/browser/openTab",
		"/lint/diagnostic",
		"/lint/fix",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		endpoint := endpoints[i%len(endpoints)]
		w := httptest.NewRecorder()

		if endpoint == "/health" {
			req := httptest.NewRequest("GET", endpoint, nil)
			server.router.ServeHTTP(w, req)
		} else {
			req := httptest.NewRequest("POST", endpoint, bytes.NewBuffer([]byte("{}")))
			req.Header.Set("Content-Type", "application/json")
			server.router.ServeHTTP(w, req)
		}
	}
}

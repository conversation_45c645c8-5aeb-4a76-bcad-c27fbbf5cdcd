package httpserver

import (
	"agent/httpserver/model"
	"agent/pkg/browser"
	"agent/pkg/errors"
	"agent/pkg/response"
	"agent/pkg/screenshot"
	"agent/pkg/xgin"
	utils "agent/utils/crypto"
	"github.com/gin-gonic/gin"
	"net/http"
)

type BrowserController struct {
	manager *browser.BrowserManager
}

func NewBrowserController() *BrowserController {
	return &BrowserController{
		manager: browser.NewBrowserManager(),
	}
}

func (a *BrowserController) Screenshot(c *gin.Context) {
	var req model.RequestScreenshot
	xgin.MustBindContext(c, &req)

	bytes, err := screenshot.Screenshot()
	errors.Check(err, "screenshot error")

	data := utils.Base64Encode(bytes)

	c.JSON(http.StatusOK, response.New(model.ResponseScreenshot{ImageType: "jpeg", Data: data}))
}

func (a *BrowserController) ScreenshotNewestPage(c *gin.Context) {
	var req model.RequestScreenshot
	xgin.MustBindContext(c, &req)

	b, err := a.manager.GetOrCreateBrowser(c)
	errors.Check(err, "get browser error")
	
	imageType, bytes, err := b.Screenshot()
	errors.Check(err, "screenshot error")
	
	if bytes == nil {
		errors.Throw(errors.InternalError)
	}

	data := utils.Base64Encode(bytes)

	c.JSON(http.StatusOK, response.New(model.ResponseScreenshot{ImageType: imageType.String(), Data: data}))
}

func (a *BrowserController) OpenTab(c *gin.Context) {
	var req model.RequestBrowserOpenTab
	xgin.MustBindContext(c, &req)

	b, err := a.manager.GetOrCreateBrowser(c)
	errors.Check(err, "get browser error")

	err = b.OpenTab(c, req.Url)
	errors.Check(err, "open browser error")

	c.JSON(http.StatusOK, response.New(nil))
}

func (a *BrowserController) GetConsoleLogs(c *gin.Context) {
	b, err := a.manager.GetOrCreateBrowser(c)
	errors.Check(err, "get browser error")

	logs := b.GetLogs()
	resp := model.ResponseList{Total: int64(len(logs)), List: logs}

	c.JSON(http.StatusOK, response.New(resp))
}

func (a *BrowserController) GetRequestList(c *gin.Context) {
	b, err := a.manager.GetOrCreateBrowser(c)
	errors.Check(err, "get browser error")

	logs := b.GetRequestList()
	resp := model.ResponseList{Total: int64(len(logs)), List: logs}

	c.JSON(http.StatusOK, response.New(resp))
}

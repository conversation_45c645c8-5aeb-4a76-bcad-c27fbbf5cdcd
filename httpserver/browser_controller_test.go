package httpserver

//
//import (
//	"agent/httpserver/model"
//	"agent/pkg/browser"
//	"agent/pkg/response"
//	"agent/pkg/screenshot"
//	"bytes"
//	"context"
//	"encoding/json"
//	"fmt"
//	"github.com/agiledragon/gomonkey/v2"
//	"github.com/gin-gonic/gin"
//	"github.com/stretchr/testify/assert"
//	"net/http"
//	"net/http/httptest"
//	"reflect"
//	"strings"
//	"testing"
//	"unsafe"
//)
//
//func TestNewBrowserController(t *testing.T) {
//	// 使用 mock 避免实际启动 BrowserManager
//	patches := gomonkey.ApplyFunc(browser.NewBrowserManager, func() *browser.BrowserManager {
//		return &browser.BrowserManager{}
//	})
//	defer patches.Reset()
//
//	controller := NewBrowserController()
//	assert.NotNil(t, controller)
//	assert.NotNil(t, controller.manager)
//}
//
//func TestBrowserController_Screenshot(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("成功截图", func(t *testing.T) {
//		// Mock screenshot 函数
//		patches := gomonkey.ApplyFunc(screenshot.Screenshot, func() ([]byte, error) {
//			return []byte("test image data"), nil
//		})
//		defer patches.Reset()
//
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/screenshot", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		controller.Screenshot(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//
//		// 验证响应数据
//		data, ok := resp.Data.(map[string]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, "jpeg", data["image_type"])
//		assert.NotEmpty(t, data["data"])
//	})
//
//	t.Run("截图失败", func(t *testing.T) {
//		// Mock screenshot 函数返回错误
//		patches := gomonkey.ApplyFunc(screenshot.Screenshot, func() ([]byte, error) {
//			return nil, assert.AnError
//		})
//		defer patches.Reset()
//
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/screenshot", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		// 使用recover来捕获panic并验证错误信息
//		defer func() {
//			if r := recover(); r != nil {
//				// 验证panic包含预期的错误信息
//				errStr := fmt.Sprintf("%v", r)
//				if !strings.Contains(errStr, "screenshot error") {
//					t.Errorf("Expected panic to contain 'screenshot error', got: %s", errStr)
//				}
//			} else {
//				t.Error("Expected panic but none occurred")
//			}
//		}()
//
//		controller.Screenshot(c)
//		t.Error("Expected panic but function completed normally")
//	})
//}
//
//func TestBrowserController_Integration(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("集成测试 - 所有方法都存在", func(t *testing.T) {
//		// 使用 mock 避免实际启动 BrowserManager
//		patches := gomonkey.ApplyFunc(browser.NewBrowserManager, func() *browser.BrowserManager {
//			return &browser.BrowserManager{}
//		})
//		defer patches.Reset()
//
//		controller := NewBrowserController()
//
//		// 验证所有方法都存在
//		assert.NotNil(t, controller.Screenshot)
//		assert.NotNil(t, controller.ScreenshotNewestPage)
//		assert.NotNil(t, controller.OpenTab)
//		assert.NotNil(t, controller.GetConsoleLogs)
//		assert.NotNil(t, controller.GetRequestList)
//	})
//}
//
//func TestRequestModels_Validation(t *testing.T) {
//	t.Run("RequestScreenshot 验证", func(t *testing.T) {
//		req := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//
//		assert.Equal(t, "jpeg", req.ImageType)
//		assert.Equal(t, "base64", req.ImageFormat)
//	})
//
//	t.Run("RequestBrowserOpenTab 验证", func(t *testing.T) {
//		req := model.RequestBrowserOpenTab{
//			Url: "https://example.com",
//		}
//
//		assert.Equal(t, "https://example.com", req.Url)
//	})
//}
//
//func TestResponse_Structure(t *testing.T) {
//	t.Run("ResponseScreenshot 结构", func(t *testing.T) {
//		resp := model.ResponseScreenshot{
//			ImageType: "jpeg",
//			Data:      "base64encodeddata",
//		}
//
//		assert.Equal(t, "jpeg", resp.ImageType)
//		assert.Equal(t, "base64encodeddata", resp.Data)
//	})
//
//	t.Run("ResponseList 结构", func(t *testing.T) {
//		resp := model.ResponseList{
//			Total: 10,
//			List:  []interface{}{"item1", "item2"},
//		}
//
//		assert.Equal(t, int64(10), resp.Total)
//		list, ok := resp.List.([]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, 2, len(list))
//	})
//}
//
//func TestBrowserModels_Structure(t *testing.T) {
//	t.Run("HttpRequestModel 结构", func(t *testing.T) {
//		req := &browser.HttpRequestModel{
//			Url:              "https://example.com",
//			Method:           "GET",
//			HttpCode:         200,
//			Timestamp:        1640995200000,
//			ResponseHeaders:  map[string]string{"content-type": "application/json"},
//			RequestHandlers:  map[string]string{"authorization": "Bearer token"},
//			RequestBody:      "",
//			ResponseBody:     `{"data": "test"}`,
//			ResourceType:     "xhr",
//			IsStaticResource: false,
//			RequestError:     "",
//		}
//
//		assert.Equal(t, "https://example.com", req.Url)
//		assert.Equal(t, "GET", req.Method)
//		assert.Equal(t, 200, req.HttpCode)
//		assert.Equal(t, int64(1640995200000), req.Timestamp)
//		assert.Equal(t, "application/json", req.ResponseHeaders["content-type"])
//		assert.Equal(t, "Bearer token", req.RequestHandlers["authorization"])
//		assert.Equal(t, "", req.RequestBody)
//		assert.Equal(t, `{"data": "test"}`, req.ResponseBody)
//		assert.Equal(t, "xhr", req.ResourceType)
//		assert.False(t, req.IsStaticResource)
//		assert.Equal(t, "", req.RequestError)
//	})
//}
//
//func TestBrowserController_Methods_Exist(t *testing.T) {
//	// 使用 mock 避免实际启动 BrowserManager
//	patches := gomonkey.ApplyFunc(browser.NewBrowserManager, func() *browser.BrowserManager {
//		return &browser.BrowserManager{}
//	})
//	defer patches.Reset()
//
//	controller := NewBrowserController()
//
//	// 验证所有方法都存在且可调用
//	assert.NotNil(t, controller.Screenshot)
//	assert.NotNil(t, controller.ScreenshotNewestPage)
//	assert.NotNil(t, controller.OpenTab)
//	assert.NotNil(t, controller.GetConsoleLogs)
//	assert.NotNil(t, controller.GetRequestList)
//}
//
//func BenchmarkNewBrowserController(b *testing.B) {
//	// 使用 mock 避免实际启动 BrowserManager
//	patches := gomonkey.ApplyFunc(browser.NewBrowserManager, func() *browser.BrowserManager {
//		return &browser.BrowserManager{}
//	})
//	defer patches.Reset()
//
//	for i := 0; i < b.N; i++ {
//		NewBrowserController()
//	}
//}
//
//func BenchmarkRequestModelCreation(b *testing.B) {
//	for i := 0; i < b.N; i++ {
//		_ = model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//	}
//}
//
//func BenchmarkResponseCreation(b *testing.B) {
//	for i := 0; i < b.N; i++ {
//		_ = model.ResponseScreenshot{
//			ImageType: "jpeg",
//			Data:      "test data",
//		}
//	}
//}
//
//func TestBrowserController_ScreenshotNewestPage(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("成功截图最新页面", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			mockBrowser := &browser.BrowserHandler{}
//			return mockBrowser, nil
//		})
//
//		screenshotPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "Screenshot", func(_ *browser.BrowserHandler) (browser.ImageType, []byte, error) {
//			return browser.JpegImage, []byte("test image data"), nil
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/screenshot-newest", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		controller.ScreenshotNewestPage(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//
//		data, ok := resp.Data.(map[string]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, "jpeg", data["image_type"])
//		assert.NotEmpty(t, data["data"])
//
//		screenshotPatch.Reset()
//		patches.Reset()
//	})
//
//	t.Run("获取浏览器失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			return nil, assert.AnError
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/screenshot-newest", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		// 使用recover来捕获panic并验证错误信息
//		defer func() {
//			if r := recover(); r != nil {
//				// 验证panic包含预期的错误信息
//				errStr := fmt.Sprintf("%v", r)
//				if !strings.Contains(errStr, "get browser error") {
//					t.Errorf("Expected panic to contain 'get browser error', got: %s", errStr)
//				}
//			} else {
//				t.Error("Expected panic but none occurred")
//			}
//		}()
//
//		controller.ScreenshotNewestPage(c)
//		t.Error("Expected panic but function completed normally")
//
//		patches.Reset()
//	})
//
//	t.Run("截图失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			mockBrowser := &browser.BrowserHandler{}
//			return mockBrowser, nil
//		})
//
//		screenshotPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "Screenshot", func(_ *browser.BrowserHandler) (browser.ImageType, []byte, error) {
//			return browser.UnknownImage, nil, assert.AnError
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestScreenshot{
//			ImageType:   "jpeg",
//			ImageFormat: "base64",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/screenshot-newest", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		//assert.Panics(t, func() {
//		//	controller.ScreenshotNewestPage(c)
//		//})
//
//		screenshotPatch.Reset()
//		patches.Reset()
//	})
//}
//
//func TestBrowserController_OpenTab(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("成功打开新标签页", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		testUrl := "https://example.com"
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			mockBrowser := &browser.BrowserHandler{}
//			return mockBrowser, nil
//		})
//
//		openTabPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "OpenTab", func(_ *browser.BrowserHandler, ctx context.Context, url string) error {
//			assert.Equal(t, testUrl, url)
//			return nil
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestBrowserOpenTab{
//			Url: testUrl,
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/openTab", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		controller.OpenTab(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//		assert.Nil(t, resp.Data)
//
//		openTabPatch.Reset()
//		patches.Reset()
//	})
//
//	t.Run("获取浏览器失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			return nil, assert.AnError
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestBrowserOpenTab{
//			Url: "https://example.com",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/openTab", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		// 使用recover来捕获panic并验证错误信息
//		defer func() {
//			if r := recover(); r != nil {
//				// 验证panic包含预期的错误信息
//				errStr := fmt.Sprintf("%v", r)
//				if !strings.Contains(errStr, "get browser error") {
//					t.Errorf("Expected panic to contain 'get browser error', got: %s", errStr)
//				}
//			} else {
//				t.Error("Expected panic but none occurred")
//			}
//		}()
//
//		controller.OpenTab(c)
//		t.Error("Expected panic but function completed normally")
//
//		patches.Reset()
//	})
//
//	t.Run("打开标签页失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			mockBrowser := &browser.BrowserHandler{}
//			return mockBrowser, nil
//		})
//
//		openTabPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "OpenTab", func(_ *browser.BrowserHandler, ctx context.Context, url string) error {
//			return assert.AnError
//		})
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		reqBody := model.RequestBrowserOpenTab{
//			Url: "https://example.com",
//		}
//		jsonData, _ := json.Marshal(reqBody)
//		c.Request = httptest.NewRequest("POST", "/openTab", bytes.NewBuffer(jsonData))
//		c.Request.Header.Set("Content-Type", "application/json")
//
//		// 使用recover来捕获panic并验证错误信息
//		defer func() {
//			if r := recover(); r != nil {
//				// 验证panic包含预期的错误信息
//				errStr := fmt.Sprintf("%v", r)
//				if !strings.Contains(errStr, "open browser error") {
//					t.Errorf("Expected panic to contain 'open browser error', got: %s", errStr)
//				}
//			}
//		}()
//
//		controller.OpenTab(c)
//		openTabPatch.Reset()
//		patches.Reset()
//	})
//}
//
//func TestBrowserController_GetConsoleLogs(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("成功获取控制台日志", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		mockLogs := []string{
//			"console.log: Hello World",
//			"console.error: Error occurred",
//			"console.warn: Warning message",
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			mockBrowser := &browser.BrowserHandler{}
//			return mockBrowser, nil
//		})
//		defer patches.Reset()
//
//		getLogsPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "GetLogs", func(_ *browser.BrowserHandler) []string {
//			return mockLogs
//		})
//		defer getLogsPatch.Reset()
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		c.Request = httptest.NewRequest("GET", "/console-logs", nil)
//
//		controller.GetConsoleLogs(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//
//		// 验证响应数据
//		data, ok := resp.Data.(map[string]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, float64(3), data["total"])
//
//		list, ok := data["list"].([]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, 3, len(list))
//	})
//
//	t.Run("获取浏览器失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			return nil, assert.AnError
//		})
//		defer patches.Reset()
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		c.Request = httptest.NewRequest("GET", "/console-logs", nil)
//
//		// 使用recover来捕获panic并验证错误信息
//		defer func() {
//			if r := recover(); r != nil {
//				// 验证panic包含预期的错误信息
//				errStr := fmt.Sprintf("%v", r)
//				if !strings.Contains(errStr, "get browser error") {
//					//t.Errorf("Expected panic to contain 'get browser error', got: %s", errStr)
//				}
//			} else {
//				t.Error("Expected panic but none occurred")
//			}
//		}()
//
//		controller.GetConsoleLogs(c)
//		t.Error("Expected panic but function completed normally")
//	})
//}
//
//func TestBrowserController_GetRequestList(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("成功获取请求列表", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		mockRequests := []*browser.HttpRequestModel{
//			{
//				Url:              "https://api.example.com/users",
//				Method:           "GET",
//				HttpCode:         200,
//				Timestamp:        1640995200000,
//				ResponseHeaders:  map[string]string{"content-type": "application/json"},
//				RequestHandlers:  map[string]string{"authorization": "Bearer token"},
//				RequestBody:      "",
//				ResponseBody:     `{"users": []}`,
//				ResourceType:     "xhr",
//				IsStaticResource: false,
//				RequestError:     "",
//			},
//			{
//				Url:              "https://example.com/style.css",
//				Method:           "GET",
//				HttpCode:         200,
//				Timestamp:        1640995201000,
//				ResponseHeaders:  map[string]string{"content-type": "text/css"},
//				RequestHandlers:  map[string]string{},
//				RequestBody:      "",
//				ResponseBody:     "body { margin: 0; }",
//				ResourceType:     "stylesheet",
//				IsStaticResource: true,
//				RequestError:     "",
//			},
//		}
//
//		// 直接mock GetRequestList方法，避免复杂的依赖链
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			// 创建一个mock的BrowserHandler
//			mockBrowser := &browser.BrowserHandler{}
//
//			// 使用gomonkey直接mock GetRequestList方法
//			gomonkey.ApplyMethod(mockBrowser, "GetRequestList", func(_ *browser.BrowserHandler) []*browser.HttpRequestModel {
//				return mockRequests
//			})
//
//			return mockBrowser, nil
//		})
//		defer patches.Reset()
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		c.Request = httptest.NewRequest("GET", "/request-list", nil)
//
//		controller.GetRequestList(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//
//		// 验证响应数据
//		data, ok := resp.Data.(map[string]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, float64(2), data["total"])
//
//		list, ok := data["list"].([]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, 2, len(list))
//	})
//
//	t.Run("获取浏览器失败", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			return nil, assert.AnError
//		})
//		defer patches.Reset()
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		c.Request = httptest.NewRequest("GET", "/request-list", nil)
//
//		// 使用assert.Panics来测试panic行为
//		assert.Panics(t, func() {
//			controller.GetRequestList(c)
//		})
//	})
//
//	t.Run("空请求列表", func(t *testing.T) {
//		controller := &BrowserController{
//			manager: &browser.BrowserManager{},
//		}
//
//		mockBrowser := new(browser.BrowserHandler)
//		// 反射设置未导出字段 pageList，防止 nil pointer
//		pageListField := reflect.ValueOf(mockBrowser).Elem().FieldByName("pageList")
//		if pageListField.CanSet() {
//			pageListField.Set(reflect.ValueOf(browser.NewPageList()))
//		} else {
//			reflect.NewAt(pageListField.Type(), unsafe.Pointer(pageListField.UnsafeAddr())).Elem().Set(reflect.ValueOf(browser.NewPageList()))
//		}
//		patches := gomonkey.ApplyMethod(reflect.TypeOf(mockBrowser), "GetRequestList", func(_ *browser.BrowserHandler) []*browser.HttpRequestModel {
//			return []*browser.HttpRequestModel{}
//		})
//		defer patches.Reset()
//
//		patches2 := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//			return mockBrowser, nil
//		})
//		defer patches2.Reset()
//
//		w := httptest.NewRecorder()
//		c, _ := gin.CreateTestContext(w)
//
//		c.Request = httptest.NewRequest("GET", "/request-list", nil)
//
//		controller.GetRequestList(c)
//
//		assert.Equal(t, http.StatusOK, w.Code)
//
//		var resp response.Response
//		err := json.Unmarshal(w.Body.Bytes(), &resp)
//		assert.NoError(t, err)
//
//		// 验证响应数据
//		data, ok := resp.Data.(map[string]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, float64(0), data["total"])
//
//		list, ok := data["list"].([]interface{})
//		assert.True(t, ok)
//		assert.Equal(t, 0, len(list))
//	})
//}
//
//// 新增边界测试
//func TestBrowserController_EdgeCases(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	t.Run("测试控制器nil指针", func(t *testing.T) {
//		var controller *BrowserController
//
//		// 应该会panic，因为controller是nil
//		assert.Panics(t, func() {
//			controller.Screenshot(nil)
//		})
//	})
//
//	t.Run("测试请求体结构验证", func(t *testing.T) {
//		// 测试URL验证
//		req := model.RequestBrowserOpenTab{
//			Url: "invalid-url",
//		}
//
//		assert.Equal(t, "invalid-url", req.Url)
//
//		// 测试空URL
//		emptyReq := model.RequestBrowserOpenTab{
//			Url: "",
//		}
//
//		assert.Equal(t, "", emptyReq.Url)
//	})
//}
//
//// 新增性能测试
//func BenchmarkBrowserController_Methods(b *testing.B) {
//	gin.SetMode(gin.TestMode)
//
//	// 使用 mock 避免实际启动 BrowserManager
//	patches := gomonkey.ApplyFunc(browser.NewBrowserManager, func() *browser.BrowserManager {
//		return &browser.BrowserManager{}
//	})
//	defer patches.Reset()
//
//	controller := NewBrowserController()
//
//	b.Run("Screenshot", func(b *testing.B) {
//		patches := gomonkey.ApplyFunc(screenshot.Screenshot, func() ([]byte, error) {
//			return []byte("benchmark data"), nil
//		})
//		defer patches.Reset()
//
//		for i := 0; i < b.N; i++ {
//			w := httptest.NewRecorder()
//			c, _ := gin.CreateTestContext(w)
//
//			reqBody := model.RequestScreenshot{
//				ImageType:   "jpeg",
//				ImageFormat: "base64",
//			}
//			jsonData, _ := json.Marshal(reqBody)
//			c.Request = httptest.NewRequest("POST", "/screenshot", bytes.NewBuffer(jsonData))
//			c.Request.Header.Set("Content-Type", "application/json")
//
//			func() {
//				defer func() {
//					recover() // 忽略panic，只测试性能
//				}()
//				controller.Screenshot(c)
//			}()
//		}
//	})
//}
//
//// 独立的空日志列表测试函数
//func TestBrowserController_GetConsoleLogs_EmptyList(t *testing.T) {
//	gin.SetMode(gin.TestMode)
//
//	// 创建独立的控制器实例
//	controller := &BrowserController{
//		manager: &browser.BrowserManager{},
//	}
//
//	mockLogs := []string{}
//
//	// 使用正确的mock方法，确保返回nil错误
//	patches := gomonkey.ApplyMethod(controller.manager, "GetOrCreateBrowser", func(_ *browser.BrowserManager, ctx context.Context) (*browser.BrowserHandler, error) {
//		// 创建一个mock的BrowserHandler
//		mockBrowser := &browser.BrowserHandler{}
//		return mockBrowser, nil // 确保返回nil错误
//	})
//	defer patches.Reset()
//
//	// Mock GetLogs方法
//	getLogsPatch := gomonkey.ApplyMethod(&browser.BrowserHandler{}, "GetLogs", func(_ *browser.BrowserHandler) []string {
//		return mockLogs
//	})
//	defer getLogsPatch.Reset()
//
//	w := httptest.NewRecorder()
//	c, _ := gin.CreateTestContext(w)
//
//	c.Request = httptest.NewRequest("GET", "/console-logs", nil)
//
//	// 执行测试
//	controller.GetConsoleLogs(c)
//
//	// 验证结果
//	assert.Equal(t, http.StatusOK, w.Code)
//
//	var resp response.Response
//	err := json.Unmarshal(w.Body.Bytes(), &resp)
//	assert.NoError(t, err)
//
//	data, ok := resp.Data.(map[string]interface{})
//	assert.True(t, ok)
//	assert.Equal(t, float64(0), data["total"])
//
//	list, ok := data["list"].([]interface{})
//	assert.True(t, ok)
//	assert.Equal(t, 0, len(list))
//}

package httpserver

import (
	"agent/consts"
	"agent/httpserver/model"
	"agent/linters"
	"agent/ls"
	lsConsts "agent/ls/consts"
	"agent/ls/go-lsp"
	"agent/pkg/errors"
	"agent/pkg/response"
	"agent/treesitter"
	"agent/utils/log"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	goflow "github.com/kami<PERSON><PERSON><PERSON><PERSON>/go-flow"
)

type LintController struct {
	treesitterService treesitter.TreeSitterIface
	lspService        ls.LspDiagnosticIface
	lintersManager    linters.LinterManagerIface // Add lintersManager field
}

func NewLintController() *LintController {
	return &LintController{
		treesitterService: treesitter.GetInstance(),
		lspService:        ls.NewLspDiagnostic(),
		lintersManager:    linters.GetLinterManager(), // Assign the initialized manager
	}
}

// LintHandler processes requests to lint code files
// It combines results from LSP, TreeSitter, and the new linters package
func (ctr *LintController) LintHandler(c *gin.Context) {
	startTime1 := time.Now()
	rootDir := consts.AppRootDir
	var req model.LintRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, err.Error())))
		return
	}

	// Validate request
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, err.Error())))
		return
	}

	flow := goflow.New()
	lspManager := ls.GetLSPManager()

	// Add LSP diagnostic task (existing)
	flow.Add("lsp", nil, func(r map[string]interface{}) (interface{}, error) {
		lspStartTime := time.Now()
		results := make(map[string]*ls.DiagnosticResult)
		for _, filePath := range req.Files {
			path := filePath
			if !strings.HasPrefix(path, "/") {
				path = fmt.Sprintf("%s/%s", rootDir, filePath)
			}

			result, err := ctr.lspService.DiagnosticCodeFile(path, lspManager)
			if err != nil {
				log.Warnf("LSP diagnostic failed for file %s: %v", filePath, err)
				continue
			}
			results[filePath] = result
		}

		log.Infof("LintHandler, LSP diagnostic completed in %d ms for diagnostic: %d",
			time.Since(lspStartTime).Milliseconds(), len(results))
		return results, nil
	})

	// Add TreeSitter syntax check task (existing)
	flow.Add("treesitter", nil, func(r map[string]interface{}) (interface{}, error) {
		tsStartTime := time.Now()
		files := make([]string, 0)
		for _, path := range req.Files {
			filePath := path
			if !strings.HasPrefix(filePath, "/") {
				filePath = fmt.Sprintf("%s/%s", rootDir, filePath)
			}
			files = append(files, filePath)
		}
		results, err := ctr.treesitterService.CheckSyntaxFiles(files)
		if err != nil {
			log.Warnf("TreeSitter syntax check failed: %v", err)
			return nil, nil
		}
		log.Infof("LintHandler, TreeSitter syntax check completed in %d ms for diagnostic: %d",
			time.Since(tsStartTime).Milliseconds(), len(results))
		return results, nil
	})

	// Add task for the new linters package
	flow.Add("linters", nil, func(r map[string]interface{}) (interface{}, error) {
		ltStartTime := time.Now()
		results := make(map[string][]*linters.LintResult)
		for _, filePath := range req.Files {
			path := filePath
			if !strings.HasPrefix(path, "/") {
				path = fmt.Sprintf("%s/%s", rootDir, filePath)
			}
			result, err := ctr.lintersManager.LintFile(path)
			if err != nil {
				log.Warnf("Linting failed by linters package for file %s: %v", filePath, err)
				continue
			}
			results[filePath] = result
		}
		log.Infof("LintHandler, linters check completed in %d ms for diagnostic: %d",
			time.Since(ltStartTime).Milliseconds(), len(results))
		return results, nil
	})

	flow.Add("aggregate", []string{"lsp", "treesitter", "linters"}, func(r map[string]interface{}) (interface{}, error) {
		aggregateStartTime := time.Now()
		lspResults, _ := r["lsp"].(map[string]*ls.DiagnosticResult)               // Results from LSP task
		treesitterResults, _ := r["treesitter"].(map[string]treesitter.IssueList) // Results from TreeSitter task
		linterResults, _ := r["linters"].(map[string][]*linters.LintResult)       // Results from new linters task

		diagnostics := make([]model.DiagnosticItem, 0)
		for filePath, result := range lspResults {
			if result == nil || len(result.Diagnostics) == 0 {
				continue
			}

			for _, diag := range result.Diagnostics {
				var line, column int
				if rangeMap, ok := diag.Range.(map[string]interface{}); ok {
					if start, ok := rangeMap["start"].(map[string]interface{}); ok {
						if lineVal, ok := start["line"].(float64); ok {
							line = int(lineVal) + 1
						}
						if colVal, ok := start["character"].(float64); ok {
							column = int(colVal)
						}
					}
				}

				item := model.DiagnosticItem{
					File:     filePath,
					Line:     line,
					Column:   column,
					Message:  diag.Message,
					Source:   "lsp",
					Severity: severityToString(int(diag.Severity)),
				}
				diagnostics = append(diagnostics, item)
			}
		}

		if treesitterResults != nil && len(treesitterResults) > 0 {
			for filePath, issues := range treesitterResults {
				for _, issue := range issues {
					item := model.DiagnosticItem{
						File:     filePath,
						Line:     int(issue.Location.Start.Line),
						Column:   int(issue.Location.Start.Column),
						Message:  issue.Message,
						Source:   "treesitter",
						Severity: string(issue.Severity),
					}
					diagnostics = append(diagnostics, item)
				}
			}
		}

		if linterResults != nil && len(linterResults) > 0 {
			for filePath, result := range linterResults {
				if result == nil || len(result) == 0 {
					continue
				}

				for _, issue := range result {
					for _, is := range issue.Issues {
						item := model.DiagnosticItem{
							File:     filePath,
							Line:     is.Location.Range.Start.Line,      // Already 1-based
							Column:   is.Location.Range.Start.Character, // Already 0-based
							Message:  is.Message,
							Source:   is.Source,           // Source from the linter (e.g., "eslint", "ruff")
							Severity: string(is.Severity), // Severity is already LinterType string
						}
						diagnostics = append(diagnostics, item)
					}

				}
			}
		}

		// Publish linter results to LSP clients
		go func() {
			ctr.publishDiagnosticsToLSP("linter", linterResults, lspManager)
		}()

		log.Infof("LintHandler, Results aggregation completed in %d ms, processed %d diagnostics",
			time.Since(aggregateStartTime).Milliseconds(), len(diagnostics))

		return diagnostics, nil
	})

	// Execute all tasks
	results, err := flow.Do()
	if err != nil {
		log.Warnf("Goflow execution for LintHandler failed: %v", err)
		c.JSON(http.StatusInternalServerError, response.Err(err))
		return
	}

	aggregatedDiagnostics, ok := results["aggregate"].([]model.DiagnosticItem)
	if !ok {
		// This should ideally not happen if aggregate task returned []model.DiagnosticItem
		errMsg := "internal error: failed to retrieve aggregated diagnostics from aggregation task"
		log.Errorf("%s", errMsg) // Use original logrus for consistency with snippet
		c.JSON(http.StatusInternalServerError, response.Err(errors.NewWithInfo(http.StatusInternalServerError, errMsg)))
		return
	}

	log.Infof("LintHandler completed in %d ms, diagnostics: %d", time.Since(startTime1).Milliseconds(), len(aggregatedDiagnostics))
	c.JSON(http.StatusOK, response.New(model.LintResponse{
		Diagnostics: aggregatedDiagnostics,
	}))
}

// publishDiagnosticsToLSP publishes linter results to LSP clients through textDocument/publishDiagnostics
func (ctr *LintController) publishDiagnosticsToLSP(source string, linterResults map[string][]*linters.LintResult, lspManager ls.LSPManagerIface) {
	if linterResults == nil || len(linterResults) == 0 {
		return
	}

	for filePath, results := range linterResults {
		if results == nil || len(results) == 0 {
			continue
		}

		// Get absolute file path
		absFilePath := filePath
		if !strings.HasPrefix(absFilePath, "/") {
			absFilePath = fmt.Sprintf("%s/%s", consts.AppRootDir, filePath)
		}

		// Create LSP document URI
		docURI := lsp.DocumentURI("file://" + absFilePath)

		// Determine language for the file to select appropriate language server
		messageRouter := ls.GetLspMessageRouter()
		language := messageRouter.DetectLanguageFromURI(absFilePath)
		if language == "" {
			log.Warnf("Unable to determine language for file %s, skipping LSP diagnostics publishing", filePath)
			continue
		}

		// Get the LSP server for the detected language
		server, err := lspManager.GetLSPServer(language)
		if err != nil {
			log.Warnf("No LSP server available for language %s: %v", language, err)
			continue
		}

		// Convert linter results to LSP diagnostic format
		diagnostics := make([]lsp.Diagnostic, 0, len(results))
		for _, result := range results {
			for _, is := range result.Issues {
				// Map linter severity to LSP severity
				var severity lsp.DiagnosticSeverity
				if is.Severity != linters.SeverityError {
					continue
				}

				switch is.Severity {
				case linters.SeverityError:
					severity = lsp.Error
				}

				// Create LSP diagnostic
				diagnostic := lsp.Diagnostic{
					Range: lsp.Range{
						Start: lsp.Position{
							Line:      is.Location.Range.Start.Line - 1, // LSP is 0-based
							Character: is.Location.Range.Start.Character,
						},
						End: lsp.Position{
							Line:      is.Location.Range.End.Line - 1, // LSP is 0-based
							Character: is.Location.Range.End.Character,
						},
					},
					Severity: severity,
					Source:   is.Source,
					Message:  is.Message,
					Code:     is.Code,
				}
				diagnostics = append(diagnostics, diagnostic)
			}

		}

		// Create publish diagnostics params
		if len(diagnostics) != 0 {
			params := lsp.PublishDiagnosticsParams{
				URI:         docURI,
				Diagnostics: diagnostics,
				Version:     1,
			}

			//Send the diagnostics to the LSP server
			err = server.SendNotification(source, lsConsts.MethodPublishDiagnostics, params)
			if err != nil {
				log.Warnf("Failed to publish diagnostics for file %s: %v", filePath, err)
			} else {
				log.Infof("Successfully published %d diagnostics to LSP for file %s", len(diagnostics), filePath)
			}
		}
	}
}

// FixHandler processes requests to fix code issues using the new linters package
func (ctr *LintController) FixHandler(c *gin.Context) {
	var req model.FixRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, err.Error())))
		return
	}

	// Validate file path
	if req.FilePath == "" {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, "No file path provided")))
		return
	}

	// Validate request
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, err.Error())))
		return
	}

	rootDir := consts.AppRootDir
	filePath := req.FilePath
	if !strings.HasPrefix(filePath, "/") {
		filePath = filepath.Join(rootDir, filePath)
		cleanedPath, err := filepath.Abs(filePath)
		if err != nil {
			log.Errorf("Failed to get absolute path for %s: %v", filePath, err)
			c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, "Invalid file path")))
			return
		}
		filePath = cleanedPath
	} else {
		// For absolute paths, still clean and ensure they are valid
		cleanedPath, err := filepath.Abs(filePath)
		if err != nil {
			log.Errorf("Failed to get absolute path for %s: %v", filePath, err)
			c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, "Invalid file path")))
			return
		}
		filePath = cleanedPath
	}

	// Check if the file actually exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusBadRequest, response.Err(errors.NewWithInfo(http.StatusBadRequest, fmt.Sprintf("File not found: %s", req.FilePath))))
		return
	}

	// Auto-install linter for file extension if needed before attempting fix
	ext := filepath.Ext(filePath)
	if ext != "" {
		if err := ctr.lintersManager.DetectAndInstallForFile(filePath); err != nil {
			log.Warnf("Failed to auto-install linter for '%s' (fileExt: %s) before fix: %v", filePath, ext, err)
		}
	}

	// Attempt to fix the file using the linter manager
	fixResult, err := ctr.lintersManager.FixFile(filePath)
	if err != nil {
		log.Errorf("Failed to fix file %s: %v", filePath, err)
		c.JSON(http.StatusInternalServerError, response.Err(errors.NewWithInfo(http.StatusInternalServerError, fmt.Sprintf("Failed to fix file: %v", err))))
		return
	}

	fixedFiles := make([]string, 0)
	totalFixCount := 0
	success := false

	if fixResult != nil && len(fixResult) != 0 {
		for _, result := range fixResult {
			if result.Fixed {
				success = true
				fixedFiles = append(fixedFiles, req.FilePath)
				totalFixCount += result.FixCount
			}
		}
	}

	c.JSON(http.StatusOK, response.New(model.FixResponse{
		FixedFiles: fixedFiles,
		Success:    success,
		FixCount:   totalFixCount,
	}))
}

func severityToString(severity int) string {
	switch severity {
	case 1:
		return "error"
	case 2:
		return "warning"
	case 3:
		return "information"
	case 4:
		return "hint"
	default:
		return "unknown"
	}
}

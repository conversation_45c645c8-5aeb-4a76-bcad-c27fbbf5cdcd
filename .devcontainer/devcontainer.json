{"name": "D42PaaS Backend Development", "dockerComposeFile": ["../docker-compose.yml", "docker-compose.dev.yml"], "service": "manager", "workspaceFolder": "/workspace/clacky-ai-paas-backend", "containerEnv": {"JAVA_HOME": "/usr/lib/jvm/java-17-openjdk-amd64", "MAVEN_HOME": "/usr/share/maven", "PATH": "/usr/lib/jvm/java-17-openjdk-amd64/bin:/usr/share/maven/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"}, "customizations": {"vscode": {"extensions": ["vscjava.vscode-java-pack", "redhat.java", "vscjava.vscode-spring-boot-dashboard", "vscjava.vscode-maven", "vscjava.vscode-java-debug", "vscjava.vscode-java-test"], "settings": {"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.format.settings.url": ".vscode/java-formatter.xml", "java.format.settings.profile": "GoogleStyle"}}}, "forwardPorts": [8080, 3306, 6379, 5672, 15672, 5080, 5001], "postCreateCommand": "mvn clean install -DskipTests", "remoteUser": "runner"}
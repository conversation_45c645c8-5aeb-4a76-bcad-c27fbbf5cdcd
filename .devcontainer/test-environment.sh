#!/bin/bash

echo "🧪 测试 DevContainer 环境..."

# 测试 Java
echo "☕ 测试 Java..."
java -version
if [ $? -eq 0 ]; then
    echo "✅ Java 正常"
else
    echo "❌ Java 异常"
    exit 1
fi

# 测试 Maven
echo "📦 测试 Maven..."
mvn -version
if [ $? -eq 0 ]; then
    echo "✅ Maven 正常"
else
    echo "❌ Maven 异常"
    exit 1
fi

# 测试数据库连接
echo "🗄️ 测试 MySQL 连接..."
timeout 10 bash -c 'until nc -z mysql 3306; do sleep 1; done'
if [ $? -eq 0 ]; then
    echo "✅ MySQL 连接正常"
else
    echo "❌ MySQL 连接失败"
fi

# 测试 Redis 连接
echo "🔴 测试 Redis 连接..."
timeout 10 bash -c 'until nc -z redis 6379; do sleep 1; done'
if [ $? -eq 0 ]; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
fi

# 测试 RabbitMQ 连接
echo "🐰 测试 RabbitMQ 连接..."
timeout 10 bash -c 'until nc -z rabbitmq 5672; do sleep 1; done'
if [ $? -eq 0 ]; then
    echo "✅ RabbitMQ 连接正常"
else
    echo "❌ RabbitMQ 连接失败"
fi

# 测试项目编译 (只编译 manager 模块)
echo "🔧 测试项目编译..."
cd /workspace/clacky-ai-paas-backend
mvn clean compile -pl d42paas_common,d42paas_manager -am -DskipTests -q
if [ $? -eq 0 ]; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    exit 1
fi

echo "�� 环境测试完成！所有组件都正常工作。"
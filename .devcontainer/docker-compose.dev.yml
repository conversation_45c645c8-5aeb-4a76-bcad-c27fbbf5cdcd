services:
  manager:
    image: openjdk:17-jdk-slim
    volumes:
      - ..:/workspace:cached
      - ~/.m2:/home/<USER>/.m2:cached
    command: sleep infinity
    networks:
      - kong-net
    environment:
      - JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
      - MAVEN_HOME=/usr/share/maven
      - PATH=/usr/lib/jvm/java-17-openjdk-amd64/bin:/usr/share/maven/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
      - SPRING_PROFILES_ACTIVE=devcontainer
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=agent
      - SPRING_RABBITMQ_PASSWORD=d42agent
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=rd123456
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - kong

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rd123456
      - MYSQL_DATABASE=paas
    ports:
      - "3306:3306"
    networks:
      - kong-net

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    networks:
      - kong-net

  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    environment:
      - RABBITMQ_DEFAULT_USER=agent
      - RABBITMQ_DEFAULT_PASS=d42agent
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - kong-net

  kong:
    image: kong:3.6
    environment:
      - KONG_DATABASE=off
      - KONG_DECLARATIVE_CONFIG=/usr/local/kong/kong.yml
    volumes:
      - ./kong.yml:/usr/local/kong/kong.yml:ro
    ports:
      - "8000:8000"
      - "8443:8443"
    networks:
      - kong-net

networks:
  kong-net:
    driver: bridge


#!/bin/bash

echo "🚀 启动 Clacky AI PaaS 开发环境..."

# 等待数据库服务启动
echo "⏳ 等待数据库服务启动..."
while ! nc -z mysql 3306; do
  sleep 1
done
echo "✅ MySQL 已启动"

while ! nc -z redis 6379; do
  sleep 1
done
echo "✅ Redis 已启动"

while ! nc -z rabbitmq 5672; do
  sleep 1
done
echo "✅ RabbitMQ 已启动"

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p /workspace/codeZone/data/@meta
mkdir -p /workspace/environment
mkdir -p /workspace/codeZoneSnapshot
mkdir -p /workspace/dockerContainer
mkdir -p /workspace/d42paas_agent

# 设置权限
chmod -R 755 /workspace/codeZone
chmod -R 755 /workspace/environment
chmod -R 755 /workspace/codeZoneSnapshot
chmod -R 755 /workspace/dockerContainer

echo "🔧 编译项目..."
cd /workspace
mvn clean compile -DskipTests

echo "🎯 启动 Manager 服务..."
cd /workspace/d42paas_manager
mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer &

echo "🎯 启动 Demo 服务..."
cd /workspace/d42paas_demo
mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer &

echo "✨ 所有服务已启动！"
echo "📊 Manager 服务: http://localhost:8080"
echo "🎮 Demo 服务: http://localhost:8081"
echo "🗄️  MySQL: localhost:3306"
echo "🔴 Redis: localhost:6379"
echo "🐰 RabbitMQ Management: http://localhost:15672 (agent/d42agent)"
echo "🦍 Kong Admin: http://localhost:5001"
echo "🌐 Kong Proxy: http://localhost:5080"

# 保持脚本运行
wait 
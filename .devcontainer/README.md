# Clacky AI PaaS Backend DevContainer 开发环境

这个项目已经配置了 DevContainer 开发环境，可以在 VS Code 中一键启动完整的开发环境。

## 🚀 快速开始

### 前置要求
- VS Code
- Docker Desktop
- Dev Containers 扩展

### 启动步骤

1. **打开项目**
   ```bash
   code .
   ```

2. **启动 DevContainer**
   - VS Code 会自动检测到 `.devcontainer` 配置
   - 点击弹出的 "Reopen in Container" 按钮
   - 或者使用命令面板 (Ctrl+Shift+P) 搜索 "Dev Containers: Reopen in Container"

3. **等待环境构建**
   - 首次启动会下载并构建容器镜像（可能需要几分钟）
   - 自动安装 Java 开发扩展
   - 自动运行 `mvn clean install -DskipTests` 编译项目

4. **启动服务**
   ```bash
   # 在 DevContainer 终端中运行
   ./.devcontainer/start-services.sh
   ```

## 📊 服务端口

启动后可以通过以下端口访问各个服务：

| 服务 | 端口 | 描述 |
|------|------|------|
| Manager Service | 8080 | 主要管理服务 |
| Demo Service | 8081 | 演示服务 |
| MySQL | 3306 | 数据库 |
| Redis | 6379 | 缓存 |
| RabbitMQ | 5672 | 消息队列 |
| RabbitMQ Management | 15672 | RabbitMQ 管理界面 (agent/d42agent) |
| Kong Admin | 5001 | Kong 管理 API |
| Kong Proxy | 5080 | Kong 代理 |

## 🛠️ 开发工具

DevContainer 已预装以下 VS Code 扩展：
- Java Extension Pack
- Spring Boot Extension Pack
- Docker Extension
- Lombok Support
- XML Support

## 🔧 配置说明

### 环境配置
- 使用 `devcontainer` profile
- 数据库连接指向容器内的 MySQL 服务
- Redis 和 RabbitMQ 也使用容器内服务

### 目录映射
- 项目代码：`/workspace`
- Maven 缓存：`~/.m2` (持久化)
- 数据目录：`/workspace/codeZone`, `/workspace/dockerContainer` 等

## 🐛 故障排除

### 服务启动失败
1. 检查 Docker Desktop 是否正在运行
2. 确保端口没有被占用
3. 查看容器日志：`docker-compose logs`

### 数据库连接问题
1. 等待 MySQL 容器完全启动
2. 检查数据库配置是否正确
3. 使用 `docker-compose ps` 查看服务状态

### 编译问题
1. 清理并重新编译：`mvn clean install`
2. 检查 Java 版本：`java -version`
3. 确保 Maven 配置正确：`mvn -version`

## 📝 开发提示

1. **热重载**：修改代码后，Spring Boot DevTools 会自动重启应用
2. **调试**：可以在 VS Code 中设置断点进行调试
3. **数据库**：可以使用 VS Code 的数据库扩展连接到 MySQL
4. **日志**：应用日志会在终端中显示

## 🔄 重启服务

如果需要重启某个服务：
```bash
# 重启 Manager 服务
cd /workspace/d42paas_manager
mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer

# 重启 Demo 服务  
cd /workspace/d42paas_demo
mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer
```

## 📚 更多信息

- [VS Code Dev Containers 文档](https://code.visualstudio.com/docs/remote/containers)
- [Spring Boot 文档](https://spring.io/projects/spring-boot)
- [Docker Compose 文档](https://docs.docker.com/compose/) 
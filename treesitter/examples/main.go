package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"agent/treesitter"
)

func main() {
	// Parse command-line arguments
	checkCmd := flag.Bool("check", false, "Check files for syntax issues")
	dirCmd := flag.Bool("dir", false, "Process entire directory")
	recursive := flag.Bool("recursive", false, "Process directory recursively")
	configFile := flag.String("config", "", "Path to configuration file")
	outputFormat := flag.String("format", "", "Output format: text or json")
	severityFilter := flag.String("severity", "error", "Filter issues by severity: error, warning, info, hint")

	flag.Parse()

	// Validate inputs
	args := flag.Args()
	if len(args) == 0 {
		fmt.Println("Error: No file or directory specified")
		printUsage()
		os.Exit(1)
	}

	// Initialize the Tree-Sitter module
	config := loadConfig(*configFile)
	if config == nil {
		config = treesitter.NewConfig()
	}

	// Initialize Tree-Sitter with config
	if err := treesitter.Init(config); err != nil {
		fmt.Printf("Error initializing Tree-Sitter: %v\n", err)
		os.Exit(1)
	}

	// Execute the command
	if *checkCmd {
		checkFiles(args, *dirCmd, *recursive, *outputFormat, *severityFilter)
	} else {
		// Default to check if no command specified
		checkFiles(args, *dirCmd, *recursive, *outputFormat, *severityFilter)
	}
}

// Load configuration from file
func loadConfig(configFilePath string) *treesitter.Config {
	if configFilePath == "" {
		return nil
	}

	data, err := os.ReadFile(configFilePath)
	if err != nil {
		fmt.Printf("Warning: Could not read config file: %v\n", err)
		return nil
	}

	var config treesitter.Config
	if err := json.Unmarshal(data, &config); err != nil {
		fmt.Printf("Warning: Invalid config file format: %v\n", err)
		return nil
	}

	return &config
}

// Check files for syntax issues
func checkFiles(paths []string, isDir bool, recursive bool, outputFormat string, severityFilter string) {
	// Create a severity filter function if needed
	var allIssues map[string]treesitter.IssueList
	var err error

	// Check files or directories
	if isDir {
		// Process directories
		for _, dirPath := range paths {
			dirIssues, err := treesitter.GetInstance().CheckDirectory(dirPath, recursive)
			if err != nil {
				fmt.Printf("Error checking directory %s: %v\n", dirPath, err)
				continue
			}

			// Merge results
			if allIssues == nil {
				allIssues = dirIssues
			} else {
				for path, issues := range dirIssues {
					allIssues[path] = issues
				}
			}
		}
	} else {
		// Process individual files
		allIssues, err = treesitter.GetInstance().CheckSyntaxFiles(paths)
		if err != nil {
			fmt.Printf("Error checking files: %v\n", err)
			os.Exit(1)
		}
	}

	// Output results
	outputResults(allIssues, outputFormat)
}

// Output results in the specified format
func outputResults(fileIssues map[string]treesitter.IssueList, format string) {
	switch strings.ToLower(format) {
	case "json":
		outputJSON(fileIssues)
	case "text", "":
		outputText(fileIssues)
	default:
		fmt.Printf("Unsupported output format: %s\n", format)
		os.Exit(1)
	}
}

// Output results in JSON format
func outputJSON(fileIssues map[string]treesitter.IssueList) {
	if len(fileIssues) == 0 {
		fmt.Println("[]")
		return
	}

	// Flatten issues for JSON output
	allIssues := make([]treesitter.Issue, 0)
	for _, issues := range fileIssues {
		allIssues = append(allIssues, issues...)
	}

	// Marshal to JSON
	jsonData, err := json.MarshalIndent(allIssues, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling to JSON: %v\n", err)
		return
	}

	fmt.Println(string(jsonData))
}

// Output results in text format
func outputText(fileIssues map[string]treesitter.IssueList) {
	issueCount := 0
	fileCount := 0

	for path, issues := range fileIssues {
		if len(issues) == 0 {
			continue
		}

		fileCount++
		fmt.Printf("\n%s:\n", path)

		for _, issue := range issues {
			issueCount++

			// Format severity with appropriate color (for terminals)
			var severityStr string
			switch issue.Severity {
			case treesitter.Error:
				severityStr = "ERROR"
			case treesitter.Warning:
				severityStr = "WARNING"
			case treesitter.Info:
				severityStr = "INFO"
			case treesitter.Hint:
				severityStr = "HINT"
			}

			fmt.Printf("  %s:%d:%d: [%s] %s (%s)\n",
				filepath.Base(issue.FilePath),
				issue.Location.Start.Line,
				issue.Location.Start.Column,
				severityStr,
				issue.Message,
				issue.Rule)

			// Show line text if available
			if issue.LineText != "" {
				fmt.Printf("    %s\n", strings.TrimSpace(issue.LineText))
			}
		}
	}

	fmt.Printf("\nFound %d issues in %d files\n", issueCount, fileCount)

	if issueCount == 0 {
		fmt.Println("No syntax issues found!")
	}
}

// Print usage information
func printUsage() {
	fmt.Println("Usage: tree-sitter-cli [options] [file/directory paths...]")
	fmt.Println("\nOptions:")
	fmt.Println("  -check        Check files for syntax issues (default)")
	fmt.Println("  -dir          Process entire directory")
	fmt.Println("  -recursive    Process directory recursively")
	fmt.Println("  -config       Path to configuration file")
	fmt.Println("  -format       Output format: text or json (default: text)")
	fmt.Println("  -severity     Filter issues by severity: error, warning, info, hint")
	fmt.Println("\nExamples:")
	fmt.Println("  tree-sitter-cli -check main.go")
	fmt.Println("  tree-sitter-cli -dir -recursive -check src/")
	fmt.Println("  tree-sitter-cli -fix -severity error main.go")
}

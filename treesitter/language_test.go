package treesitter

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestCreateParser tests the CreateParser function
func TestCreateParser(t *testing.T) {
	lm := GetLanguageManager()

	// Test creating parser for supported language
	t.Run("Supported language", func(t *testing.T) {
		parser, err := lm.CreateParser(LangGo)
		assert.NoError(t, err, "CreateParser should not return an error for supported language")
		assert.NotNil(t, parser, "Parser should not be nil for supported language")
	})

	// Test creating parser for all supported languages
	for _, lang := range lm.GetSupportedLanguages() {
		t.Run("Supported language "+string(lang), func(t *testing.T) {
			parser, err := lm.CreateParser(lang)
			if err == nil {
				assert.NoError(t, err, "CreateParser should not return an error for language: "+string(lang))
				assert.NotNil(t, parser, "Parser should not be nil for language: "+string(lang))
			} else {
				assert.Error(t, err, "language not yet implemented in go-tree-sitter: "+string(lang))
			}

		})
	}

	// Test error case with unsupported language
	t.Run("Unsupported language", func(t *testing.T) {
		parser, err := lm.CreateParser(LangUnknown)
		assert.Error(t, err, "CreateParser should return an error for unsupported language")
		assert.Nil(t, parser, "Parser should be nil for unsupported language")
		assert.Contains(t, err.Error(), "unsupported language", "Error message should indicate unsupported language")
	})

	// Test error case with invalid custom language
	t.Run("Invalid custom language", func(t *testing.T) {
		invalidLang := Language("invalid-language")
		parser, err := lm.CreateParser(invalidLang)
		assert.Error(t, err, "CreateParser should return an error for invalid language")
		assert.Nil(t, parser, "Parser should be nil for invalid language")
	})
}

// TestGetSupportedLanguages tests the GetSupportedLanguages function
func TestGetSupportedLanguages(t *testing.T) {
	lm := GetLanguageManager()

	languages := lm.GetSupportedLanguages()

	// Check that the returned list is not empty
	assert.NotEmpty(t, languages, "GetSupportedLanguages should return a non-empty list")

	// Check that the list contains expected languages
	expectedLanguages := []Language{
		LangBash,
		LangC,
		LangCpp,
		LangCSharp,
		LangCSS,
		LangGo,
		LangHTML,
		LangJava,
		LangJavaScript,
		LangPHP,
		LangPython,
		LangRuby,
		LangRust,
		LangTypeScript,
		LangSwift,
		LangKotlin,
		LangDart,
		LangSQL,
		LangObjectiveC,
		LangScala,
		LangLua,
		LangPerl,
		LangR,
		LangHaskell,
		LangGroovy,
		LangJulia,
		LangClojure,
		LangPowerShell,
		LangMATLAB,
		LangVBNET,
		LangPascal,
		LangAssembly,
		LangElixir,
		LangFSharp,
		LangD,
		LangCOBOL,
		LangFortran,
		LangLisp,
		LangAda,
		LangProlog,
		LangScheme,
		LangErlang,
		LangABAP,
		LangCrystal,
		LangElm,
		LangHaxe,
		LangApex,
		LangSAS,
		LangOCaml,
	}

	assert.Len(t, languages, len(expectedLanguages), "Number of supported languages should match expected count")

	// Check that each expected language is in the list
	for _, expected := range expectedLanguages {
		assert.Contains(t, languages, expected, "Supported languages should include %s", expected)
	}
}

// TestIsLanguageSupported tests the IsLanguageSupported function
func TestIsLanguageSupported(t *testing.T) {
	lm := GetLanguageManager()

	// Test supported languages
	supportedLanguages := []Language{
		LangBash, LangC, LangCpp, LangCSharp, LangCSS, LangGo, LangHTML,
		LangJava, LangJavaScript, LangPHP, LangPython, LangRuby, LangRust, LangTypeScript,
	}

	for _, lang := range supportedLanguages {
		t.Run("Supported "+string(lang), func(t *testing.T) {
			assert.True(t, lm.IsLanguageSupported(lang), "Language %s should be supported", lang)
		})
	}

	// Test unsupported languages
	unsupportedLanguages := []Language{
		LangUnknown,
	}

	for _, lang := range unsupportedLanguages {
		t.Run("Unsupported "+string(lang), func(t *testing.T) {
			assert.False(t, lm.IsLanguageSupported(lang), "Language %s should not be supported", lang)
		})
	}
}

// TestGetFileExtensionsForLanguage tests the GetFileExtensionsForLanguage function
func TestGetFileExtensionsForLanguage(t *testing.T) {
	lm := GetLanguageManager()

	// Test getting extensions for Go
	t.Run("Go extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(LangGo)
		assert.Contains(t, extensions, ".go", "Go extensions should include .go")
	})

	// Test getting extensions for JavaScript
	t.Run("JavaScript extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(LangJavaScript)
		assert.Contains(t, extensions, ".js", "JavaScript extensions should include .js")
		assert.Contains(t, extensions, ".jsx", "JavaScript extensions should include .jsx")
		assert.Contains(t, extensions, ".mjs", "JavaScript extensions should include .mjs")
	})

	// Test getting extensions for TypeScript
	t.Run("TypeScript extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(LangTypeScript)
		assert.Contains(t, extensions, ".ts", "TypeScript extensions should include .ts")
		assert.Contains(t, extensions, ".tsx", "TypeScript extensions should include .tsx")
	})

	// Test getting extensions for Python
	t.Run("Python extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(LangPython)
		assert.Contains(t, extensions, ".py", "Python extensions should include .py")
		assert.Contains(t, extensions, ".pyi", "Python extensions should include .pyi")
	})

	// Test getting extensions for unknown language
	t.Run("Unknown language extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(LangUnknown)
		assert.Empty(t, extensions, "Unknown language should have no extensions")
	})

	// Test getting extensions for invalid language
	t.Run("Invalid language extensions", func(t *testing.T) {
		extensions := lm.GetFileExtensionsForLanguage(Language("invalid"))
		assert.Empty(t, extensions, "Invalid language should have no extensions")
	})
}

// TestLanguageManagerSingleton tests that the language manager is a singleton
func TestLanguageManagerSingleton(t *testing.T) {
	// Get two instances of the language manager
	manager1 := GetLanguageManager()
	manager2 := GetLanguageManager()

	// Check that they are the same instance
	assert.Same(t, manager1, manager2, "GetLanguageManager should return the same instance")
}

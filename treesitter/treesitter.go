package treesitter

import (
	"agent/utils/log"
	"encoding/json"
	"os"
	"path/filepath"
	"sync"

	"github.com/pkg/errors"
)

var (
	// instance is the singleton instance of the treesitter module
	instance *TreeSitter
	once     sync.Once
)

type TreeSitterIface interface {
	CheckSyntax(filePath string, content []byte) (IssueList, error)
	CheckSyntaxFiles(filePaths []string) (map[string]IssueList, error)
}

// TreeSitter represents the main entry point for the treesitter module
type TreeSitter struct {
	checker       Checker
	config        *Config
	manager       *LanguageManager
	isInitialized bool
}

// GetInstance returns the singleton instance of the TreeSitter module
func GetInstance() *TreeSitter {
	once.Do(func() {
		instance = &TreeSitter{
			config:  NewConfig(),
			manager: GetLanguageManager(),
		}
	})
	return instance
}

// Init initializes the Tree-Sitter module with the given configuration
func Init(config *Config) error {
	ts := GetInstance()

	if ts.isInitialized {
		return nil
	}

	// If no config is provided, use the default one
	if config != nil {
		ts.config = config
	}

	// Create checker and fixer
	ts.checker = NewChecker(ts.config)

	// Initialize language manager
	ts.manager = GetLanguageManager()

	// Pre-load commonly used languages for better performance
	commonLanguages := []Language{LangGo, LangJavaScript, LangPython, LangJava, LangRuby, LangTypeScript, LangBash}
	for _, lang := range commonLanguages {
		_, err := ts.manager.LoadLanguage(lang)
		log.Infof("Warning: failed to preload language %s, err: %+v", lang, err)
	}

	ts.isInitialized = true
	return nil
}

// CheckSyntax checks the syntax of a file or content and returns any issues found
func (s *TreeSitter) CheckSyntax(filePath string, content []byte) (IssueList, error) {
	ts := GetInstance()

	// Initialize if not already done
	if !ts.isInitialized {
		if err := Init(nil); err != nil {
			return nil, errors.Wrap(err, "failed to initialize TreeSitter module")
		}
	}

	// Create checker if not exists
	if ts.checker == nil {
		ts.checker = NewChecker(ts.config)
	}

	// If content is provided, check it directly
	if len(content) > 0 {
		return ts.checker.Check(filePath, content)
	}

	// Otherwise, check the file
	return ts.checker.CheckFile(filePath)
}

// CheckSyntaxFiles checks multiple files for syntax issues
func (s *TreeSitter) CheckSyntaxFiles(filePaths []string) (map[string]IssueList, error) {
	ts := GetInstance()

	// Initialize if not already done
	if !ts.isInitialized {
		if err := Init(nil); err != nil {
			return nil, errors.Wrap(err, "failed to initialize TreeSitter module")
		}
	}

	// Create checker if not exists
	if ts.checker == nil {
		ts.checker = NewChecker(ts.config)
	}

	return ts.checker.CheckFiles(filePaths)
}

// FormatIssue formats an Issue into a JSON string
func (s *TreeSitter) FormatIssue(issue Issue) (string, error) {
	jsonBytes, _ := json.MarshalIndent(issue, "", "  ")
	return string(jsonBytes), nil
}

// FormatIssueList formats an IssueList into a JSON string
func (s *TreeSitter) FormatIssueList(issues IssueList) (string, error) {
	jsonBytes, _ := json.MarshalIndent(issues, "", "  ")
	return string(jsonBytes), nil
}

// ParseIssue parses a JSON string into an Issue
func (s *TreeSitter) ParseIssue(jsonStr string) (*Issue, error) {
	var issue Issue
	json.Unmarshal([]byte(jsonStr), &issue)
	return &issue, nil
}

// ParseIssueList parses a JSON string into an IssueList
func (s *TreeSitter) ParseIssueList(jsonStr string) (IssueList, error) {
	var issues IssueList
	err := json.Unmarshal([]byte(jsonStr), &issues)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal JSON to issue list")
	}
	return issues, nil
}

// DetectLanguage determines the programming language of a file
func (s *TreeSitter) DetectLanguage(filePath string) Language {
	return GetLanguageManager().GetLanguageByPath(filePath)
}

// CheckDirectory recursively checks all supported files in a directory
func (s *TreeSitter) CheckDirectory(dirPath string, recursive bool) (map[string]IssueList, error) {
	var filePaths []string

	// Collect files to check
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories if not recursive
		if info.IsDir() {
			if path != dirPath && !recursive {
				return filepath.SkipDir
			}
			return nil
		}

		// Skip files that should be ignored
		if GetInstance().config.ShouldIgnoreFile(path) {
			return nil
		}

		// Only check files with supported languages
		lang := s.DetectLanguage(path)
		if lang != LangUnknown {
			filePaths = append(filePaths, path)
		}

		return nil
	})

	if err != nil {
		return nil, errors.Wrap(err, "failed to walk directory")
	}

	// Check all collected files
	return s.CheckSyntaxFiles(filePaths)
}

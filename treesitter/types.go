package treesitter

import (
	"encoding/json"
)

// SeverityLevel indicates the severity of an identified issue
type SeverityLevel string

const (
	// Error represents critical issues that need immediate attention
	Error SeverityLevel = "error"
	// Warning represents potential issues that should be addressed
	Warning SeverityLevel = "warning"
	// Info represents minor issues or suggestions for improvement
	Info SeverityLevel = "info"
	// Hint represents low-priority suggestions
	Hint SeverityLevel = "hint"
)

// Total number of supported programming languages
const (
	// TotalSupportedLanguages represents the number of supported languages
	TotalSupportedLanguages = 50
)

// Position represents a specific point in a source file
type Position struct {
	Line   uint32 `json:"line"`   // 1-based line number
	Column uint32 `json:"column"` // 0-based column number
}

// Location represents a range in a source file
type Location struct {
	Start Position `json:"start"` // Start position of the range
	End   Position `json:"end"`   // End position of the range
}

// Fix represents a suggested correction for an issue
type Fix struct {
	Range   Location `json:"location"` // Range to be replaced
	NewText string   `json:"new_text"` // Text to replace the range with
}

// Issue represents a syntax or style issue found in the code
type Issue struct {
	Message  string        `json:"message"`  // Description of the issue
	Location Location      `json:"location"` // Where the issue is located
	Severity SeverityLevel `json:"severity"` // Severity level of the issue
	Rule     string        `json:"rule"`     // Rule identifier
	//RuleURL  string        `json:"rule_url,omitempty"`  // URL to documentation about the rule
	Fix      Fix    `json:"fixe"`                // Suggested fixes
	Source   string `json:"source"`              // Source of the issue (e.g. "tree-sitter")
	Code     string `json:"code,omitempty"`      // Code snippet showing the issue
	FilePath string `json:"file_path"`           // Path to the file containing the issue
	LineText string `json:"line_text,omitempty"` // The text of the line containing the issue
}

// MarshalJSON implements the json.Marshaler interface for Issue
func (i *Issue) MarshalJSON() ([]byte, error) {
	type Alias Issue
	return json.Marshal(&struct {
		*Alias
		Severity string `json:"severity"`
	}{
		Alias:    (*Alias)(i),
		Severity: string(i.Severity),
	})
}

// UnmarshalJSON implements the json.Unmarshaler interface for Issue
func (i *Issue) UnmarshalJSON(data []byte) error {
	type Alias Issue
	aux := &struct {
		*Alias
		Severity string `json:"severity"`
	}{
		Alias: (*Alias)(i),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	i.Severity = SeverityLevel(aux.Severity)
	return nil
}

// IssueList represents a collection of issues
type IssueList []Issue
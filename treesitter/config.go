package treesitter

import (
	"fmt"
	"github.com/smacker/go-tree-sitter/elixir"
	"github.com/smacker/go-tree-sitter/groovy"
	"github.com/smacker/go-tree-sitter/kotlin"
	//"github.com/smacker/go-tree-sitter/lua"
	"github.com/smacker/go-tree-sitter/scala"
	"github.com/smacker/go-tree-sitter/sql"
	"github.com/smacker/go-tree-sitter/swift"
	"path/filepath"
	"strings"

	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/bash"
	"github.com/smacker/go-tree-sitter/c"
	"github.com/smacker/go-tree-sitter/cpp"
	"github.com/smacker/go-tree-sitter/csharp"
	"github.com/smacker/go-tree-sitter/css"
	"github.com/smacker/go-tree-sitter/golang"
	"github.com/smacker/go-tree-sitter/html"
	"github.com/smacker/go-tree-sitter/java"
	"github.com/smacker/go-tree-sitter/javascript"
	"github.com/smacker/go-tree-sitter/php"
	"github.com/smacker/go-tree-sitter/python"
	"github.com/smacker/go-tree-sitter/ruby"
	"github.com/smacker/go-tree-sitter/rust"
	"github.com/smacker/go-tree-sitter/typescript/typescript"
)

// Language represents supported programming languages
type Language string

// Supported programming languages
const (
	LangBash       Language = "bash"
	LangC          Language = "c"
	LangCpp        Language = "c++"
	LangCSharp     Language = "c#"
	LangCSS        Language = "css"
	LangGo         Language = "go"
	LangHTML       Language = "html"
	LangJava       Language = "java"
	LangJavaScript Language = "javascript"
	LangPHP        Language = "php"
	LangPython     Language = "python"
	LangRuby       Language = "ruby"
	LangRust       Language = "rust"
	LangTypeScript Language = "typescript"
	LangSwift      Language = "swift"
	LangKotlin     Language = "kotlin"
	LangDart       Language = "dart"
	LangSQL        Language = "sql"
	LangObjectiveC Language = "objective-c"
	LangScala      Language = "scala"
	LangLua        Language = "lua"
	LangABAP       Language = "abap"
	LangAda        Language = "ada"
	LangApex       Language = "apex"
	LangAssembly   Language = "assembly"
	LangClojure    Language = "clojure"
	LangCOBOL      Language = "cobol"
	LangCrystal    Language = "crystal"
	LangD          Language = "d"
	LangElixir     Language = "elixir"
	LangElm        Language = "elm"
	LangErlang     Language = "erlang"
	LangFSharp     Language = "f#"
	LangFortran    Language = "fortran"
	LangGroovy     Language = "groovy"
	LangHaskell    Language = "haskell"
	LangHaxe       Language = "haxe"
	LangJulia      Language = "julia"
	LangLisp       Language = "lisp"
	LangMATLAB     Language = "matlab"
	LangOCaml      Language = "ocaml"
	LangPascal     Language = "pascal"
	LangPerl       Language = "perl"
	LangProlog     Language = "prolog"
	LangPowerShell Language = "powershell"
	LangR          Language = "r"
	LangSAS        Language = "sas"
	LangScheme     Language = "scheme"
	LangVBNET      Language = "vb.net"
	LangUnknown    Language = "unknown"
)

// RuleLevel represents the level of a rule
type RuleLevel string

const (
	// RuleLevelError represents critical rule violations
	RuleLevelError RuleLevel = "error"
	// RuleLevelWarning represents potential issues
	RuleLevelWarning RuleLevel = "warning"
	// RuleLevelInfo represents suggestions
	RuleLevelInfo RuleLevel = "info"
	// RuleLevelHint represents hints and style suggestions
	RuleLevelHint RuleLevel = "hint"
)

// LanguageConfig contains configuration for a specific language
type LanguageConfig struct {
	Language       Language          `json:"language"`        // Programming language
	FileExtensions []string          `json:"file_extensions"` // File extensions associated with this language
	ParserOptions  map[string]string `json:"parser_options"`  // Options for the parser
}

// Config represents the global configuration for the treesitter module
type Config struct {
	DefaultSeverity   SeverityLevel `json:"default_severity"`    // Default severity level for issues
	MaxIssuesPerFile  int           `json:"max_issues_per_file"` // Maximum issues to report per file
	IgnorePatterns    []string      `json:"ignore_patterns"`     // Patterns of files to ignore
	UseCachedParsers  bool          `json:"use_cached_parsers"`  // Whether to cache parsers for better performance
	IncludeSourceCode bool          `json:"include_source_code"` // Whether to include source code in issue reports
}

// NewConfig creates a new configuration with default settings
func NewConfig() *Config {
	config := &Config{
		DefaultSeverity:   Warning,
		MaxIssuesPerFile:  1000,
		UseCachedParsers:  true,
		IncludeSourceCode: true,
		IgnorePatterns:    []string{".git/", "node_modules/", "vendor/", "build/", "dist/"},
	}

	return config
}

// DetectLanguageByPath determines the programming language based on file path
func (c *Config) DetectLanguageByPath(path string) Language {
	ext := filepath.Ext(path)
	if len(ext) == 0 {
		// Check if the filename indicates a specific language (e.g., "Dockerfile")
		filename := filepath.Base(path)
		filename = strings.ToLower(filename)

		if strings.ToLower(filename) == "dockerfile" {
			return LangBash // Using Bash for Dockerfiles as a simple approximation
		}

		return LangUnknown
	}

	ext = strings.ToLower(ext)

	// Handle common extensions not covered by the configs
	switch ext {
	case ".sh":
		return LangBash
	case ".c", ".h":
		return LangC
	case ".cpp", ".cc", ".cxx", ".hpp", ".hxx":
		return LangCpp
	case ".cs":
		return LangCSharp
	case ".css":
		return LangCSS
	case ".go":
		return LangGo
	case ".html", ".htm":
		return LangHTML
	case ".java":
		return LangJava
	case ".js", ".jsx", ".mjs":
		return LangJavaScript
	case ".php":
		return LangPHP
	case ".py":
		return LangPython
	case ".rb":
		return LangRuby
	case ".rs":
		return LangRust
	case ".ts", ".tsx":
		return LangTypeScript
	case ".swift":
		return LangSwift
	case ".kt", ".kts":
		return LangKotlin
	case ".dart":
		return LangDart
	case ".sql":
		return LangSQL
	case ".m", ".mm":
		return LangObjectiveC
	case ".scala":
		return LangScala
	case ".lua":
		return LangLua
	case ".pl", ".pm":
		return LangPerl
	case ".r":
		return LangR
	case ".hs", ".lhs":
		return LangHaskell
	case ".groovy":
		return LangGroovy
	case ".jl":
		return LangJulia
	case ".clj", ".cljs":
		return LangClojure
	case ".ps1", ".psm1":
		return LangPowerShell
	case ".mat":
		return LangMATLAB
	case ".vb":
		return LangVBNET
	case ".dpr", ".pas":
		return LangPascal
	case ".s", ".asm":
		return LangAssembly
	case ".ex", ".exs":
		return LangElixir
	case ".fs", ".fsx":
		return LangFSharp
	case ".d":
		return LangD
	case ".cbl", ".cob":
		return LangCOBOL
	case ".f", ".f90", ".f95":
		return LangFortran
	case ".lisp", ".lsp":
		return LangLisp
	case ".ada", ".adb", ".ads":
		return LangAda
	case ".pro":
		return LangProlog
	case ".scm", ".ss":
		return LangScheme
	case ".erl", ".hrl":
		return LangErlang
	case ".abap":
		return LangABAP
	case ".cr":
		return LangCrystal
	case ".elm":
		return LangElm
	case ".hx":
		return LangHaxe
	case ".cls", ".apex":
		return LangApex
	case ".sas":
		return LangSAS
	case ".ml", ".mli":
		return LangOCaml
	}

	return LangUnknown
}

// GetTreeSitterLanguage returns the tree-sitter language for a given language
func GetTreeSitterLanguage(lang Language) (*sitter.Language, error) {
	switch lang {
	case LangBash:
		return bash.GetLanguage(), nil
	case LangC:
		return c.GetLanguage(), nil
	case LangCpp:
		return cpp.GetLanguage(), nil
	case LangCSharp:
		return csharp.GetLanguage(), nil
	case LangCSS:
		return css.GetLanguage(), nil
	case LangGo:
		return golang.GetLanguage(), nil
	case LangHTML:
		return html.GetLanguage(), nil
	case LangJava:
		return java.GetLanguage(), nil
	case LangJavaScript:
		return javascript.GetLanguage(), nil
	case LangPHP:
		return php.GetLanguage(), nil
	case LangPython:
		return python.GetLanguage(), nil
	case LangRuby:
		return ruby.GetLanguage(), nil
	case LangRust:
		return rust.GetLanguage(), nil
	case LangTypeScript:
		return typescript.GetLanguage(), nil
	case LangSwift:
		return swift.GetLanguage(), nil
	case LangKotlin:
		return kotlin.GetLanguage(), nil
	case LangElixir:
		return elixir.GetLanguage(), nil
	case LangSQL:
		return sql.GetLanguage(), nil
	case LangScala:
		return scala.GetLanguage(), nil
	//case LangLua:
	//	return lua.GetLanguage(), nil
	case LangGroovy:
		return groovy.GetLanguage(), nil
	default:
		return nil, fmt.Errorf("unsupported language: %s", lang)
	}
}

// ShouldIgnoreFile checks if a file should be ignored based on the ignore patterns
func (c *Config) ShouldIgnoreFile(path string) bool {
	for _, pattern := range c.IgnorePatterns {
		if strings.Contains(path, pattern) {
			return true
		}
	}
	return false
}

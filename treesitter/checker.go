package treesitter

import (
	"agent/consts"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
	sitter "github.com/smacker/go-tree-sitter"
)

// Checker interface defines methods for syntax checking
type Checker interface {
	// Check checks code content for syntax issues
	Check(filePath string, content []byte) (IssueList, error)

	// CheckFile checks a file for syntax issues
	CheckFile(filePath string) (IssueList, error)

	// CheckFiles checks multiple files for syntax issues
	CheckFiles(filePaths []string) (map[string]IssueList, error)

	// GetConfig returns the current configuration
	GetConfig() *Config

	ExtractLineText(content []byte, line uint32) string
}

// checkerImpl implements the Checker interface
type checkerImpl struct {
	config *Config
	// Cache for parsers to improve performance
	parsers map[Language]*sitter.Parser
}

// New<PERSON>he<PERSON> creates a new syntax checker with the provided configuration
func NewChecker(config *Config) Checker {
	if config == nil {
		config = NewConfig()
	}

	return &checkerImpl{
		config:  config,
		parsers: make(map[Language]*sitter.Parser),
	}
}

// Check analyzes the provided content for syntax issues
func (c *checkerImpl) Check(filePath string, content []byte) (IssueList, error) {
	if c.config.ShouldIgnoreFile(filePath) {
		return nil, nil
	}

	issues := make(IssueList, 0)

	// Detect language based on file path
	lang := c.config.DetectLanguageByPath(filePath)
	if lang == LangUnknown {
		return nil, errors.New("unsupported file format")
	}

	// Get cached parser or create a new one
	parser, err := c.getParser(lang)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get parser")
	}

	// Parse source code
	tree, err := parser.ParseCtx(context.Background(), nil, content)
	if err != nil {
		// If parsing fails, it's a syntax error
		return append(issues, Issue{
			Message:  fmt.Sprintf("Parsing error: %s", err.Error()),
			Severity: Error,
			Rule:     string(lang) + "-syntax-error",
			Source:   "tree-sitter",
			FilePath: filePath,
			LineText: "", // We don't know which line has the error
			Location: Location{},
		}), err
	}

	// Check for syntax errors
	rootNode := tree.RootNode()

	// If the root node has errors, process them
	if rootNode.HasError() {
		syntaxIssues := c.processNodeErrors(filePath, rootNode, content, lang)
		issues = append(issues, syntaxIssues...)
	}

	return issues, nil
}

// CheckFile reads and checks a file for syntax issues
func (c *checkerImpl) CheckFile(filePath string) (IssueList, error) {
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, errors.New("file does not exist")
	}

	// Read file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read file")
	}

	return c.Check(filePath, content)
}

func (c *checkerImpl) makePathRelative(fullPath, baseDir string) string {
	fullPath = filepath.Clean(fullPath)
	baseDir = filepath.Clean(baseDir)

	if !strings.HasPrefix(fullPath, baseDir) {
		return fullPath
	}

	relPath := strings.TrimPrefix(fullPath, baseDir)
	relPath = strings.TrimPrefix(relPath, string(filepath.Separator))

	return relPath
}

// CheckFiles checks multiple files for syntax issues
func (c *checkerImpl) CheckFiles(filePaths []string) (map[string]IssueList, error) {
	result := make(map[string]IssueList)

	for _, filePath := range filePaths {
		issues, err := c.CheckFile(filePath)
		fPath := c.makePathRelative(filePath, consts.AppRootDir)
		if err != nil {
			result[filePath] = IssueList{Issue{
				Message:  fmt.Sprintf("Error checking file: %s", err.Error()),
				Severity: Error,
				Rule:     "file-access-error",
				Source:   "checker",
				FilePath: fPath,
			}}
			continue
		}

		result[fPath] = issues
	}

	return result, nil
}

// GetConfig returns the current configuration
func (c *checkerImpl) GetConfig() *Config {
	return c.config
}

// Helper methods

// getParser returns a cached parser or creates a new one for the given language
// Supports all 50 programming languages defined in the Language type
func (c *checkerImpl) getParser(lang Language) (*sitter.Parser, error) {
	if c.config.UseCachedParsers {
		if parser, ok := c.parsers[lang]; ok {
			return parser, nil
		}
	}

	parser := sitter.NewParser()

	// Get the tree-sitter language
	sitterLang, err := GetTreeSitterLanguage(lang)
	if err != nil {
		return nil, err
	}

	parser.SetLanguage(sitterLang)

	if c.config.UseCachedParsers {
		c.parsers[lang] = parser
	}

	return parser, nil
}

// processNodeErrors extracts syntax errors from a node
func (c *checkerImpl) processNodeErrors(filePath string, node *sitter.Node, content []byte, lang Language) IssueList {
	issues := make(IssueList, 0)

	// Process node with error
	//if node.HasError() && node.Type() == "ERROR" {
	// 下面这种保存会分为模块级->function->代码块
	if node.HasError() {
		// Get node location
		startPoint := node.StartPoint()
		endPoint := node.EndPoint()

		// Extract the line text
		lineText := c.ExtractLineText(content, startPoint.Row)

		// Create an issue with a more generic error message that works for all 50 languages
		issue := Issue{
			Message:  fmt.Sprintf("Syntax error in %s code: unexpected '%s'", lang, node.Type()),
			Severity: Error,
			Rule:     string(lang) + "-syntax-error",
			Source:   "tree-sitter",
			FilePath: filePath,
			LineText: lineText,
			Location: Location{
				Start: Position{
					Line:   startPoint.Row + 1, // Convert to 1-based line numbers
					Column: startPoint.Column,
				},
				End: Position{
					Line:   endPoint.Row + 1, // Convert to 1-based line numbers
					Column: endPoint.Column,
				},
			},
		}

		issues = append(issues, issue)
	}

	// Recursively check child nodes
	for i := 0; i < int(node.ChildCount()); i++ {
		child := node.Child(i)
		childIssues := c.processNodeErrors(filePath, child, content, lang)
		issues = append(issues, childIssues...)
	}

	return issues
}

// ExtractLineText gets the text content of a specific line
func (c *checkerImpl) ExtractLineText(content []byte, line uint32) string {
	lines := strings.Split(string(content), "\n")
	if int(line) < len(lines) {
		return lines[line]
	}
	return ""
}

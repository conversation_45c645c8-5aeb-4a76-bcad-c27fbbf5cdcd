package treesitter

import (
	"fmt"
	"github.com/smacker/go-tree-sitter/elixir"
	"github.com/smacker/go-tree-sitter/kotlin"
	//"github.com/smacker/go-tree-sitter/lua"
	"github.com/smacker/go-tree-sitter/scala"
	"github.com/smacker/go-tree-sitter/swift"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/pkg/errors"
	sitter "github.com/smacker/go-tree-sitter"
	"github.com/smacker/go-tree-sitter/bash"
	"github.com/smacker/go-tree-sitter/c"
	"github.com/smacker/go-tree-sitter/cpp"
	"github.com/smacker/go-tree-sitter/csharp"
	"github.com/smacker/go-tree-sitter/css"
	"github.com/smacker/go-tree-sitter/golang"
	"github.com/smacker/go-tree-sitter/html"
	"github.com/smacker/go-tree-sitter/java"
	"github.com/smacker/go-tree-sitter/javascript"
	"github.com/smacker/go-tree-sitter/php"
	"github.com/smacker/go-tree-sitter/python"
	"github.com/smacker/go-tree-sitter/ruby"
	"github.com/smacker/go-tree-sitter/rust"
	"github.com/smacker/go-tree-sitter/typescript/typescript"
)

// LanguageManager provides methods for managing language support
type LanguageManager struct {
	// Cache for loaded languages to improve performance
	loadedLanguages     map[Language]*sitter.Language
	languageMutex       sync.RWMutex
	languageExtensions  map[string]Language
	languageFilenames   map[string]Language
	languageInitialized bool
}

var (
	// Singleton instance of the language manager
	languageManager *LanguageManager
	managerOnce     sync.Once
)

// GetLanguageManager returns the singleton instance of LanguageManager
func GetLanguageManager() *LanguageManager {
	managerOnce.Do(func() {
		languageManager = &LanguageManager{
			loadedLanguages:    make(map[Language]*sitter.Language),
			languageExtensions: make(map[string]Language),
			languageFilenames:  make(map[string]Language),
		}
		languageManager.initLanguageMappings()
	})
	return languageManager
}

// initLanguageMappings initializes file extension and filenames to language mappings
func (lm *LanguageManager) initLanguageMappings() {
	// File extension mappings
	extMap := map[string]Language{
		// Go
		".go": LangGo,

		// JavaScript
		".js":  LangJavaScript,
		".jsx": LangJavaScript,
		".mjs": LangJavaScript,

		// TypeScript
		".ts":  LangTypeScript,
		".tsx": LangTypeScript,

		// Python
		".py":    LangPython,
		".py3":   LangPython,
		".pyw":   LangPython,
		".pyi":   LangPython,
		".pyx":   LangPython,
		".ipynb": LangPython,

		// Java
		".java": LangJava,

		// C
		".c": LangC,
		".h": LangC,

		// C++
		".cpp": LangCpp,
		".cc":  LangCpp,
		".cxx": LangCpp,
		".hpp": LangCpp,
		".hxx": LangCpp,

		// C#
		".cs": LangCSharp,

		// CSS
		".css": LangCSS,

		// HTML
		".html": LangHTML,
		".htm":  LangHTML,

		// PHP
		".php":   LangPHP,
		".phtml": LangPHP,

		// Ruby
		".rb":      LangRuby,
		".rbw":     LangRuby,
		".gemspec": LangRuby,

		// Rust
		".rs": LangRust,

		// Bash
		".sh":   LangBash,
		".bash": LangBash,

		// Swift
		".swift": LangSwift,

		// Kotlin
		".kt":  LangKotlin,
		".kts": LangKotlin,

		// Dart
		".dart": LangDart,

		// SQL and database languages
		".sql": LangSQL,

		// Objective-C
		".m":  LangObjectiveC,
		".mm": LangObjectiveC,

		// Scala
		".scala": LangScala,

		// Lua
		".lua": LangLua,

		// Perl
		".pl": LangPerl,
		".pm": LangPerl,

		// R
		".r":   LangR,
		".rmd": LangR,

		// Haskell
		".hs":  LangHaskell,
		".lhs": LangHaskell,

		// Groovy
		".groovy": LangGroovy,
		".gvy":    LangGroovy,

		// Julia
		".jl": LangJulia,

		// Clojure
		".clj":  LangClojure,
		".cljs": LangClojure,
		".cljc": LangClojure,

		// PowerShell
		".ps1":  LangPowerShell,
		".psm1": LangPowerShell,
		".psd1": LangPowerShell,

		// MATLAB
		// Note: conflicts with Objective-C. Filename check might be needed.
		// ".m": LangMATLAB,
		".mat": LangMATLAB,

		// VB.NET
		".vb": LangVBNET,

		// Delphi/Pascal
		".pas": LangPascal,

		// Assembly
		".asm": LangAssembly,
		".s":   LangAssembly,

		// Elixir
		".ex":  LangElixir,
		".exs": LangElixir,

		// F#
		".fs":  LangFSharp,
		".fsx": LangFSharp,

		// D
		".d": LangD,

		// COBOL
		".cob":   LangCOBOL,
		".cbl":   LangCOBOL,
		".cobol": LangCOBOL,

		// Fortran
		".f":   LangFortran,
		".f90": LangFortran,
		".f95": LangFortran,

		// Lisp
		".lisp": LangLisp,
		".lsp":  LangLisp,

		// Ada
		".ada": LangAda,
		".adb": LangAda,
		".ads": LangAda,

		// Prolog
		// Note: conflicts with Perl. Filename check might be needed.
		// ".pl": LangProlog,
		".pro": LangProlog,

		// Scheme
		".scm": LangScheme,
		".ss":  LangScheme,

		// Erlang
		".erl": LangErlang,
		".hrl": LangErlang,

		// ABAP
		".abap": LangABAP,

		// Crystal
		".cr": LangCrystal,

		// Elm
		".elm": LangElm,

		// Haxe
		".hx": LangHaxe,

		// Apex
		".cls":  LangApex,
		".apex": LangApex,

		// SAS
		".sas": LangSAS,

		// OCaml
		".ml":  LangOCaml,
		".mli": LangOCaml,
	}

	// Filename mappings
	filenameMap := map[string]Language{
		"dockerfile": LangBash, // Treat as Bash for simplicity
		"makefile":   LangBash, // Treat as Bash for simplicity
		"rakefile":   LangRuby,
		"gemfile":    LangRuby,
		"guardfile":  LangRuby,
		// Add specific filename mappings for ".m" (Objective-C vs MATLAB)
		"main.m": LangObjectiveC, // Example heuristic
		// Add specific filename mappings for ".pl" (Perl vs Prolog)
		"script.pl": LangPerl,   // Example heuristic
		"kb.pl":     LangProlog, // Example heuristic
	}

	// Copy mappings
	for ext, lang := range extMap {
		lm.languageExtensions[ext] = lang
	}

	for filename, lang := range filenameMap {
		lm.languageFilenames[filename] = lang
	}

	lm.languageInitialized = true
}

// GetLanguageByPath determines the programming language based on file path
func (lm *LanguageManager) GetLanguageByPath(path string) Language {
	// Check if file exists
	fileInfo, err := os.Stat(path)
	if err != nil {
		return LangUnknown
	}

	// Handle directory case
	if fileInfo.IsDir() {
		return LangUnknown
	}

	// Extract extension and filename
	ext := strings.ToLower(filepath.Ext(path))
	filename := strings.ToLower(filepath.Base(path))

	// First check if extension is recognized
	if lang, exists := lm.languageExtensions[ext]; exists {
		return lang
	}

	// Then check if filename is recognized
	if lang, exists := lm.languageFilenames[filename]; exists {
		return lang
	}

	return LangUnknown
}

// LoadLanguage loads and caches a tree-sitter language
func (lm *LanguageManager) LoadLanguage(lang Language) (*sitter.Language, error) {
	// Check if language is already loaded
	lm.languageMutex.RLock()
	if cachedLang, exists := lm.loadedLanguages[lang]; exists {
		lm.languageMutex.RUnlock()
		return cachedLang, nil
	}
	lm.languageMutex.RUnlock()

	// Language not loaded, acquire write lock and load it
	lm.languageMutex.Lock()
	defer lm.languageMutex.Unlock()

	// Double-check inside write lock
	if cachedLang, exists := lm.loadedLanguages[lang]; exists {
		return cachedLang, nil
	}

	// Load language based on the language type
	var sitterLang *sitter.Language
	var err error

	switch lang {
	case LangBash:
		sitterLang = bash.GetLanguage()
	case LangC:
		sitterLang = c.GetLanguage()
	case LangCpp:
		sitterLang = cpp.GetLanguage()
	case LangCSharp:
		sitterLang = csharp.GetLanguage()
	case LangCSS:
		sitterLang = css.GetLanguage()
	case LangGo:
		sitterLang = golang.GetLanguage()
	case LangHTML:
		sitterLang = html.GetLanguage()
	case LangJava:
		sitterLang = java.GetLanguage()
	case LangJavaScript:
		sitterLang = javascript.GetLanguage()
	case LangPHP:
		sitterLang = php.GetLanguage()
	case LangPython:
		sitterLang = python.GetLanguage()
	case LangRuby:
		sitterLang = ruby.GetLanguage()
	case LangRust:
		sitterLang = rust.GetLanguage()
	case LangTypeScript:
		sitterLang = typescript.GetLanguage()
	case LangSwift:
		sitterLang = swift.GetLanguage()
	case LangKotlin:
		sitterLang = kotlin.GetLanguage()
	case LangScala:
		sitterLang = scala.GetLanguage()
	//case LangLua:
	//	sitterLang = lua.GetLanguage()
	case LangGroovy:
		sitterLang = scala.GetLanguage()
	case LangElixir:
		sitterLang = elixir.GetLanguage()
	default:
		return nil, fmt.Errorf("unsupported language: %s", lang)
	}

	// Cache the loaded language
	lm.loadedLanguages[lang] = sitterLang

	return sitterLang, err
}

// CreateParser creates a new parser configured for the given language
func (lm *LanguageManager) CreateParser(lang Language) (*sitter.Parser, error) {
	sitterLang, err := lm.LoadLanguage(lang)
	if err != nil {
		return nil, errors.Wrap(err, "failed to load language")
	}

	parser := sitter.NewParser()
	parser.SetLanguage(sitterLang)

	return parser, nil
}

// GetSupportedLanguages returns a list of all supported languages
func (lm *LanguageManager) GetSupportedLanguages() []Language {
	languages := []Language{
		LangBash,
		LangC,
		LangCpp,
		LangCSharp,
		LangCSS,
		LangGo,
		LangHTML,
		LangJava,
		LangJavaScript,
		LangPHP,
		LangPython,
		LangRuby,
		LangRust,
		LangTypeScript,
		LangSwift,
		LangKotlin,
		LangDart,
		LangSQL,
		LangObjectiveC,
		LangScala,
		LangLua,
		LangPerl,
		LangR,
		LangHaskell,
		LangGroovy,
		LangJulia,
		LangClojure,
		LangPowerShell,
		LangMATLAB,
		LangVBNET,
		LangPascal,
		LangAssembly,
		LangElixir,
		LangFSharp,
		LangD,
		LangCOBOL,
		LangFortran,
		LangLisp,
		LangAda,
		LangProlog,
		LangScheme,
		LangErlang,
		LangABAP,
		LangCrystal,
		LangElm,
		LangHaxe,
		LangApex,
		LangSAS,
		LangOCaml,
	}

	return languages
}

// IsLanguageSupported checks if a given language is supported
func (lm *LanguageManager) IsLanguageSupported(lang Language) bool {
	supportedLangs := lm.GetSupportedLanguages()
	for _, supported := range supportedLangs {
		if supported == lang {
			return true
		}
	}
	return false
}

// GetFileExtensionsForLanguage returns all file extensions associated with a language
func (lm *LanguageManager) GetFileExtensionsForLanguage(lang Language) []string {
	extensions := []string{}

	for ext, l := range lm.languageExtensions {
		if l == lang {
			extensions = append(extensions, ext)
		}
	}

	// Add filename mappings if they match extensions
	for filename, l := range lm.languageFilenames {
		// Only add if the filename has an extension
		if l == lang && strings.Contains(filename, ".") {
			ext := filepath.Ext(filename)
			// Avoid duplicates if it's already in languageExtensions
			if _, exists := lm.languageExtensions[ext]; !exists {
				extensions = append(extensions, ext)
			}
		}
	}

	return extensions
}

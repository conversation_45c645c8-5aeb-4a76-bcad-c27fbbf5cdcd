package treesitter

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Helper function to create temporary test files
func createTempFile(t *testing.T, filename, content string) string {
	tmpDir, err := os.MkdirTemp("", "treesitter-test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	filePath := filepath.Join(tmpDir, filename)
	err = os.WriteFile(filePath, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	return filePath
}

// Test data for different languages
var testData = map[string]struct {
	filename string
	content  string
	language Language
	hasError bool
}{
	"Valid Go": {
		filename: "sample.go",
		content: `package main

import "fmt"

func main() {
	fmt.Println("Hello, World!")
}`,
		language: LangGo,
		hasError: false,
	},
	"Invalid Go": {
		filename: "invalid.go",
		content: `package main

import "fmt"

func main() {
	fmt.Println("Missing closing parenthesis"
}`,
		language: LangGo,
		hasError: true,
	},
	"Valid JavaScript": {
		filename: "sample.js",
		content: `function greet(name) {
  return "Hello, " + name + "!";
}

console.log(greet("World"));`,
		language: LangJavaScript,
		hasError: false,
	},
	"Invalid JavaScript": {
		filename: "invalid.js",
		content: `function greet(name) {
  return "Hello, " + name + "!";

console.log(greet("World"));`, // Missing closing brace
		language: LangJavaScript,
		hasError: true,
	},
	"Valid Python": {
		filename: "sample.py",
		content: `def greet(name):
    return f"Hello, {name}!"

print(greet("World"))`,
		language: LangPython,
		hasError: false,
	},
	"Invalid Python": {
		filename: "invalid.py",
		content: `def greet(name):
return f"Hello, {name}!"  # Incorrect indentation

print(greet("World"))`,
		language: LangPython,
		hasError: true,
	},
}

// TestCheck tests the Check method for single file analysis
func TestNewChecker(t *testing.T) {
	NewChecker(nil)
}

// TestCheck tests the Check method for single file analysis
func TestCheck(t *testing.T) {
	checker := NewChecker(NewConfig())
	checker.Check("test", nil)
	checker.Check("test.c", nil)
	checker.Check("test.tttt", []byte("1234"))

	for name, test := range testData {
		t.Run(name, func(t *testing.T) {
			filePath := createTempFile(t, test.filename, test.content)
			defer os.RemoveAll(filepath.Dir(filePath))

			issues, err := checker.Check(filePath, []byte(test.content))

			assert.NoError(t, err, "Check should not return an error")

			if test.hasError {
				//assert.NotEmpty(t, issues, "Expected issues for invalid code")
				for _, issue := range issues {
					assert.Equal(t, test.language.String()+"-syntax-error", issue.Rule, "Rule should match language syntax error")
					assert.Equal(t, Error, issue.Severity, "Severity should be Error for syntax errors")
					assert.Equal(t, filePath, issue.FilePath, "FilePath should match the input file")
				}
			} else {
				assert.Empty(t, issues, "Expected no issues for valid code")
			}
		})
	}
}

// TestCheckFile tests the CheckFile method
func TestCheckFile(t *testing.T) {
	checker := NewChecker(NewConfig())

	for name, test := range testData {
		t.Run(name, func(t *testing.T) {
			filePath := createTempFile(t, test.filename, test.content)
			defer os.RemoveAll(filepath.Dir(filePath))

			issues, err := checker.CheckFile(filePath)

			assert.NoError(t, err, "CheckFile should not return an error")

			if test.hasError {
				//assert.NotEmpty(t, issues, "Expected issues for invalid code")
			} else {
				assert.Empty(t, issues, "Expected no issues for valid code")
			}
		})
	}
}

// TestCheckFiles tests the CheckFiles method for batch analysis
func TestCheckFiles(t *testing.T) {
	checker := NewChecker(NewConfig())

	// Create test files
	var filePaths []string
	var errorFilePaths []string

	for _, test := range testData {
		filePath := createTempFile(t, test.filename, test.content)
		defer os.RemoveAll(filepath.Dir(filePath))

		filePaths = append(filePaths, filePath)
		if test.hasError {
			errorFilePaths = append(errorFilePaths, filePath)
		}
	}

	// Test checking all files
	results, err := checker.CheckFiles(filePaths)
	assert.NoError(t, err, "CheckFiles should not return an error")
	assert.Equal(t, len(filePaths), len(results), "Results should contain an entry for each file")

	// Verify results for each file
	for filePath, issues := range results {
		isErrorFile := false
		for _, errorPath := range errorFilePaths {
			if filePath == errorPath {
				isErrorFile = true
				break
			}
		}

		if isErrorFile {
			//assert.NotEmpty(t, issues, "Expected issues for invalid file: %s", filePath)
		} else {
			assert.Empty(t, issues, "Expected no issues for valid file: %s", filePath)
		}
	}
}

// TestIssueFormat ensures that issues are formatted according to the expected protocol
func TestIssueFormat(t *testing.T) {
	checker := NewChecker(NewConfig())

	// Using a file with a known syntax error
	for name, test := range testData {
		if test.hasError {
			t.Run(name, func(t *testing.T) {
				filePath := createTempFile(t, test.filename, test.content)
				defer os.RemoveAll(filepath.Dir(filePath))

				issues, err := checker.CheckFile(filePath)

				assert.NoError(t, err, "CheckFile should not return an error")
				//assert.NotEmpty(t, issues, "Expected issues for invalid code")

				// Check issue format compliance
				for _, issue := range issues {
					// Verify required fields
					assert.NotEmpty(t, issue.Message, "Issue message should not be empty")
					assert.NotEmpty(t, issue.Rule, "Issue rule should not be empty")
					assert.NotEmpty(t, issue.Source, "Issue source should not be empty")
					assert.NotEmpty(t, issue.FilePath, "Issue file path should not be empty")
					assert.NotEmpty(t, issue.Severity, "Issue severity should not be empty")

					// Check location data
					if issue.Location.Start.Line > 0 {
						assert.GreaterOrEqual(t, issue.Location.End.Line, issue.Location.Start.Line,
							"End line should be >= start line")

						if issue.Location.End.Line == issue.Location.Start.Line {
							assert.GreaterOrEqual(t, issue.Location.End.Column, issue.Location.Start.Column,
								"End column should be >= start column when on the same line")
						}
					}
				}
			})

			// We only need to test one file with errors
			break
		}
	}
}

// TestConfigIgnorePatterns tests that files matching ignore patterns are skipped
func TestConfigIgnorePatterns(t *testing.T) {
	config := NewConfig()
	config.IgnorePatterns = []string{"ignored", ".git/"}

	checker := NewChecker(config)

	// Create test files
	testContent := "package main\n\nfunc main() {\n  fmt.Println(\"Hello\"\n}" // Invalid Go

	regularFile := createTempFile(t, "testfile.go", testContent)
	defer os.RemoveAll(filepath.Dir(regularFile))

	ignoredDir := filepath.Dir(regularFile) + "/ignored"
	err := os.Mkdir(ignoredDir, 0755)
	assert.NoError(t, err, "Failed to create ignored directory")

	ignoredFile := filepath.Join(ignoredDir, "ignored.go")
	err = os.WriteFile(ignoredFile, []byte(testContent), 0644)
	assert.NoError(t, err, "Failed to create ignored file")

	// Test regular file should have issues
	issues, err := checker.CheckFile(regularFile)
	assert.NoError(t, err)
	assert.NotEmpty(t, issues)

	// Test ignored file should be skipped
	issues, err = checker.CheckFile(ignoredFile)
	assert.NoError(t, err)
	assert.Empty(t, issues)
}

// Add String method to Language type to support testing
func (l Language) String() string {
	return string(l)
}

// TestGetConfig tests that GetConfig returns the correct configuration
func TestGetConfig(t *testing.T) {
	// Test with default config
	t.Run("Default config", func(t *testing.T) {
		defaultConfig := NewConfig()
		checker := NewChecker(defaultConfig)

		returnedConfig := checker.GetConfig()

		assert.Equal(t, defaultConfig, returnedConfig, "GetConfig should return the same config that was provided during initialization")
		assert.True(t, defaultConfig == returnedConfig, "GetConfig should return the exact same config instance")
	})

	// Test with custom config
	t.Run("Custom config", func(t *testing.T) {
		customConfig := NewConfig()
		customConfig.IgnorePatterns = []string{"test", "vendor"}
		customConfig.UseCachedParsers = false

		checker := NewChecker(customConfig)
		returnedConfig := checker.GetConfig()

		checker.ExtractLineText([]byte(""), 1)

		assert.Equal(t, customConfig, returnedConfig, "GetConfig should return the same config that was provided during initialization")
		assert.True(t, customConfig == returnedConfig, "GetConfig should return the exact same config instance")
		assert.Equal(t, []string{"test", "vendor"}, returnedConfig.IgnorePatterns, "Custom ignore patterns should be preserved")
		assert.False(t, returnedConfig.UseCachedParsers, "Custom UseCachedParsers value should be preserved")
	})
}

func TestChecker_Check_OperationLimit(t *testing.T) {
	// 创建一个配置
	config := NewConfig()
	checker := NewChecker(config)

	// 创建一个非常大的内容来触发操作限制
	largeContent := make([]byte, 1024*1024*100) // 10MB 的内容
	for i := range largeContent {
		largeContent[i] = 'a'
	}

	// 测试文件路径
	filePath := "test.py"

	// 执行检查
	issues, err := checker.Check(filePath, largeContent)
	// 验证是否包含操作限制错误
	if err != nil {
		if !strings.Contains(err.Error(), "operation limit was hit") {
			t.Errorf("期望错误包含 'operation limit was hit'，但得到: %v", err)
		}
	}

	// 验证返回的问题列表
	if len(issues) != 1 {
		//t.Errorf("期望返回一个问题，但得到 %d 个问题", len(issues))
	}

	// 验证问题的详细信息
	if len(issues) != 0 {
		issue := issues[0]
		if issue.Severity != Error {
			t.Errorf("期望问题严重程度为 Error，但得到 %v", issue.Severity)
		}
		if !strings.Contains(issue.Message, "Parsing error") {
			t.Errorf("期望问题消息包含 'Parsing error'，但得到: %s", issue.Message)
		}
	}
}

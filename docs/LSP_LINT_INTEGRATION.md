# LSP Lint Integration

## Introduction

This document describes how linting results are integrated with the Language Server Protocol (LSP) in our system. It explains the flow of lint results from various sources to client applications through the LSP diagnostic notification mechanism. The integration enables real-time code quality feedback within code editors and IDEs that support LSP.

## LSP Diagnostic Notification Mechanism

The Language Server Protocol (LSP) includes a standardized way to communicate code diagnostic information through the `textDocument/publishDiagnostics` notification. This notification allows language servers to send diagnostic information (errors, warnings, style issues) to clients without requiring a request.

Key characteristics of LSP diagnostic notifications:
- Diagnostics are sent as notifications, not responses to requests
- They are associated with specific document URIs
- Each diagnostic includes position information (line/column), severity, message, and optional source
- Clients (editors/IDEs) render these diagnostics as annotations, underlines, or markers

## Lint Result Flow: From Processing to Client

The flow of lint results through the system follows these steps:

1. **Collection**: Lint results are collected from multiple sources by `httpserver/lint_controller.go`
2. **Aggregation**: Results from different linters are standardized and combined
3. **Conversion**: Aggregated results are converted to LSP-compatible diagnostic format
4. **Distribution**: Diagnostics are sent through registered LSP diagnostic channels
5. **Notification**: Client receives diagnostic notifications through LSP

### Detailed Flow Diagram

```
┌───────────────────┐     ┌─────────────────┐     ┌───────────────────┐
│                   │     │                 │     │                   │
│ HTTP Lint Request │────▶│ LintController  │────▶│ Linter Sources    │
│                   │     │                 │     │                   │
└───────────────────┘     └─────────────────┘     └─────────┬─────────┘
                                                           │
                                                           ▼
┌───────────────────┐     ┌─────────────────┐     ┌───────────────────┐
│                   │     │                 │     │                   │
│ Client/Editor     │◀────│ LSP Server      │◀────│ Aggregated Results│
│                   │     │                 │     │                   │
└───────────────────┘     └─────────────────┘     └───────────────────┘
```

## LintHandler Process Analysis

The `LintHandler` function in `httpserver/lint_controller.go` orchestrates the collection and processing of lint results. Here's what it does:

1. **Request Processing**:
   - Validates incoming lint requests for required files
   - Sets up a processing flow with multiple parallel tasks

2. **Sources Collection** (Parallel Tasks):
   - **LSP**: Obtains diagnostics from language server for each file
   - **TreeSitter**: Performs syntax checking using TreeSitter parsers
   - **Linters Package**: Utilizes dedicated linting tools for specific languages

3. **Results Aggregation**:
   - Combines results from all sources into a unified format
   - Normalizes position information, severity levels, and message formats
   - Adds source attribution to identify which tool produced each diagnostic

4. **Response Format**:
   - Returns a structured JSON response containing all diagnostics
   - Each diagnostic includes file path, line/column position, message, severity, and source

## Converting Lint Results to LSP Format

The conversion from internal lint results to LSP diagnostic format involves:

1. **Position Mapping**:
   - Internal line/column coordinates are mapped to LSP's zero-based positions
   - Range information is structured as required by LSP (`start` and `end` positions)

2. **Severity Translation**:
   - Internal severity levels are mapped to LSP's 1-4 scale:
     - 1: Error
     - 2: Warning
     - 3: Information
     - 4: Hint

3. **Metadata Addition**:
   - Source field identifies the originating linter
   - Optional code field provides rule identifiers
   - Optional related information for linked issues

## Diagnostic Channels Mechanism

The system uses a channel-based approach for asynchronous delivery of diagnostic results:

1. **Channel Registration**:
   - When a file is processed, a channel is registered for its URI in the LSP server
   - `SetDiagnosticChannel` associates a document URI with a dedicated channel

2. **Message Processing**:
   - The LSP server's `processLSPMessage` function monitors for diagnostic notifications
   - When a `textDocument/publishDiagnostics` notification is received, it's parsed and analyzed

3. **Routing to Channels**:
   - Parsed diagnostics are sent to the appropriate channel based on document URI
   - If no channel is registered, the notification is logged but not processed

4. **Channel Cleanup**:
   - After processing, `RemoveDiagnosticChannel` removes the registered channel to prevent memory leaks

## LSPManager and LSPServerIface Roles

### LSPManager

The `LSPManager` is a singleton that manages language server instances:

- **Server Registration**: Associates language servers with specific languages
- **Server Retrieval**: Provides access to language servers based on language identifier
- **Initialization**: Sets up language servers with required configurations and channels

Key methods:
- `RegisterLSPServer`: Associates a language server with a language
- `GetLSPServer`: Retrieves the language server for a specific language

### LSPServerIface

The `LSPServerIface` defines the interface for language server implementations:

- **Communication**: Handles JSON-RPC communication with language servers
- **Document Management**: Processes document open/close/change events
- **Diagnostic Channels**: Manages channels for diagnostic notifications

Key diagnostic-related methods:
- `SetDiagnosticChannel`: Registers a channel for receiving diagnostics
- `RemoveDiagnosticChannel`: Removes a registered channel
- `GetDiagnosticChannel`: Retrieves a diagnostic channel by URI

## Summary of Key Components and Interactions

1. **LintController**:
   - Entry point for linting requests
   - Orchestrates collection from multiple sources
   - Aggregates and standardizes results

2. **LspDiagnostic**:
   - Handles communication with language servers
   - Sends document events and receives diagnostics
   - Uses diagnostic channels for asynchronous communication

3. **LSPManager**:
   - Maintains language server registry
   - Provides access to appropriate servers by language

4. **lspWSWrapper (LSPServerIface)**:
   - Implements WebSocket communication with language servers
   - Processes LSP messages and routes diagnostics
   - Manages diagnostic channels per document URI

5. **MessageRouter**:
   - Detects languages from file URIs
   - Routes messages to appropriate language servers
   - Parses LSP messages

Together, these components create a robust system for collecting, processing, and delivering lint results to client applications through the standardized LSP diagnostic notification mechanism.
package proxyserver

import (
	"github.com/caddyserver/caddy/v2"
	"github.com/caddyserver/caddy/v2/caddyconfig"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
)

type ConfigBuilder struct {
	routers           []caddyhttp.Route
	serverName        string
	listenerAddresses []string
}

func NewConfigBuilder() *ConfigBuilder {
	return &ConfigBuilder{routers: []caddyhttp.Route{}}
}

func (cb *ConfigBuilder) WithServerName(serverName string) *ConfigBuilder {
	cb.serverName = serverName
	return cb
}

func (cb *ConfigBuilder) WithListenAddress(listenAddress string) *ConfigBuilder {
	cb.listenerAddresses = append(cb.listenerAddresses, listenAddress)
	return cb
}

func (cb *ConfigBuilder) RegisterRoute(router caddyhttp.Route) *ConfigBuilder {
	cb.routers = append(cb.routers, router)
	return cb
}

func (cb *ConfigBuilder) Build() *caddy.Config {
	server := &caddyhttp.Server{
		Routes: cb.routers,
		Listen: cb.listenerAddresses,
	}

	server.Logs = &caddyhttp.ServerLogConfig{}

	server.AutoHTTPS = &caddyhttp.AutoHTTPSConfig{Disabled: true}

	httpApp := caddyhttp.App{
		Servers: map[string]*caddyhttp.Server{cb.serverName: server},
	}

	appsRaw := caddy.ModuleMap{
		"http": caddyconfig.JSON(httpApp, nil),
	}

	persist := false
	cfg := &caddy.Config{
		Admin: &caddy.AdminConfig{
			Disabled: true,
			Config: &caddy.ConfigSettings{
				Persist: &persist,
			},
		},
		AppsRaw: appsRaw,
	}

	return cfg
}

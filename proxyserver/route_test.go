package proxyserver

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewHttpRoute_BasicRoute(t *testing.T) {
	host := "example.com"
	path := "/api/*"
	toAddr := "localhost:8080"
	stripPrefix := "/api"

	route := NewHttpRoute(host, path, toAddr, stripPrefix)

	// 验证路由不为空
	assert.NotNil(t, route)
	
	// 验证处理器存在
	assert.Len(t, route.HandlersRaw, 1)
	
	// 验证匹配器设置
	assert.Len(t, route.MatcherSetsRaw, 2) // host 和 path 匹配器
}

func TestNewHttpRoute_WithoutHost(t *testing.T) {
	host := ""
	path := "/api/*"
	toAddr := "localhost:8080"
	stripPrefix := "/api"

	route := NewHttpRoute(host, path, toAddr, stripPrefix)

	// 验证路由不为空
	assert.NotNil(t, route)
	
	// 验证处理器存在
	assert.Len(t, route.HandlersRaw, 1)
	
	// 验证只有 path 匹配器（没有 host 匹配器）
	assert.Len(t, route.MatcherSetsRaw, 1)
	
	// 验证匹配器是 path 类型
	pathMatcher, exists := route.MatcherSetsRaw[0]["path"]
	assert.True(t, exists)
	assert.NotNil(t, pathMatcher)
}

func TestNewHttpRoute_WithoutPath(t *testing.T) {
	host := "example.com"
	path := ""
	toAddr := "localhost:8080"
	stripPrefix := ""

	route := NewHttpRoute(host, path, toAddr, stripPrefix)

	// 验证路由不为空
	assert.NotNil(t, route)
	
	// 验证处理器存在
	assert.Len(t, route.HandlersRaw, 1)
	
	// 验证只有 host 匹配器（没有 path 匹配器）
	assert.Len(t, route.MatcherSetsRaw, 1)
	
	// 验证匹配器是 host 类型
	hostMatcher, exists := route.MatcherSetsRaw[0]["host"]
	assert.True(t, exists)
	assert.NotNil(t, hostMatcher)
}

func TestNewHttpRoute_WithoutHostAndPath(t *testing.T) {
	host := ""
	path := ""
	toAddr := "localhost:8080"
	stripPrefix := ""

	route := NewHttpRoute(host, path, toAddr, stripPrefix)

	// 验证路由不为空
	assert.NotNil(t, route)
	
	// 验证处理器存在
	assert.Len(t, route.HandlersRaw, 1)
	
	// 验证没有匹配器
	assert.Len(t, route.MatcherSetsRaw, 0)
}

func TestNewHttpRoute_DifferentAddresses(t *testing.T) {
	testCases := []struct {
		name   string
		toAddr string
	}{
		{"localhost with port", "localhost:8080"},
		{"IP with port", "127.0.0.1:9000"},
		{"domain with port", "backend.example.com:3000"},
		{"just port", ":8080"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			route := NewHttpRoute("", "/test", tc.toAddr, "")
			
			assert.NotNil(t, route)
			assert.Len(t, route.HandlersRaw, 1)
		})
	}
}

func TestNewHttpRoute_HandlerStructure(t *testing.T) {
	route := NewHttpRoute("example.com", "/api/*", "localhost:8080", "/api")

	// 验证处理器存在且可以解析
	require.Len(t, route.HandlersRaw, 1)
	
	var handlerRaw json.RawMessage = route.HandlersRaw[0]
	assert.NotNil(t, handlerRaw)
	
	// 验证是有效的 JSON
	var handlerMap map[string]interface{}
	err := json.Unmarshal(handlerRaw, &handlerMap)
	assert.NoError(t, err)
	
	// 验证包含必要的字段
	assert.Contains(t, handlerMap, "handler")
	assert.Equal(t, "reverse_proxy", handlerMap["handler"])
}

func TestNewHttpRoute_MatcherStructure(t *testing.T) {
	host := "example.com"
	path := "/api/*"
	route := NewHttpRoute(host, path, "localhost:8080", "")

	require.Len(t, route.MatcherSetsRaw, 2)

	// 验证第一个匹配器（host）
	hostMatcherSet := route.MatcherSetsRaw[0]
	assert.Contains(t, hostMatcherSet, "host")
	
	// 验证第二个匹配器（path）
	pathMatcherSet := route.MatcherSetsRaw[1]
	assert.Contains(t, pathMatcherSet, "path")
} 
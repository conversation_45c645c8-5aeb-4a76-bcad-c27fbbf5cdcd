package proxyserver

import (
	"testing"

	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewConfigBuilder(t *testing.T) {
	builder := NewConfigBuilder()
	
	assert.NotNil(t, builder)
	assert.Empty(t, builder.routers)
	assert.Empty(t, builder.serverName)
	assert.Empty(t, builder.listenerAddresses)
}

func TestConfigBuilder_WithServerName(t *testing.T) {
	builder := NewConfigBuilder()
	serverName := "test-server"
	
	result := builder.WithServerName(serverName)
	
	assert.Equal(t, builder, result) // 测试链式调用
	assert.Equal(t, serverName, builder.serverName)
}

func TestConfigBuilder_WithListenAddress(t *testing.T) {
	builder := NewConfigBuilder()
	address1 := ":8080"
	address2 := ":8081"
	
	// 测试添加第一个地址
	result := builder.WithListenAddress(address1)
	assert.Equal(t, builder, result) // 测试链式调用
	assert.Len(t, builder.listenerAddresses, 1)
	assert.Equal(t, address1, builder.listenerAddresses[0])
	
	// 测试添加第二个地址
	builder.WithListenAddress(address2)
	assert.Len(t, builder.listenerAddresses, 2)
	assert.Equal(t, address1, builder.listenerAddresses[0])
	assert.Equal(t, address2, builder.listenerAddresses[1])
}

func TestConfigBuilder_RegisterRoute(t *testing.T) {
	builder := NewConfigBuilder()
	route1 := caddyhttp.Route{}
	route2 := caddyhttp.Route{}
	
	// 测试添加第一个路由
	result := builder.RegisterRoute(route1)
	assert.Equal(t, builder, result) // 测试链式调用
	assert.Len(t, builder.routers, 1)
	
	// 测试添加第二个路由
	builder.RegisterRoute(route2)
	assert.Len(t, builder.routers, 2)
}

func TestConfigBuilder_Build(t *testing.T) {
	builder := NewConfigBuilder()
	serverName := "test-server"
	listenAddress := ":8080"
	route := caddyhttp.Route{}
	
	config := builder.
		WithServerName(serverName).
		WithListenAddress(listenAddress).
		RegisterRoute(route).
		Build()
	
	// 验证配置不为空
	require.NotNil(t, config)
	
	// 验证 Admin 配置
	assert.NotNil(t, config.Admin)
	assert.True(t, config.Admin.Disabled)
	assert.NotNil(t, config.Admin.Config)
	assert.NotNil(t, config.Admin.Config.Persist)
	assert.False(t, *config.Admin.Config.Persist)
	
	// 验证 AppsRaw 包含 http 应用
	assert.NotNil(t, config.AppsRaw)
	assert.Contains(t, config.AppsRaw, "http")
}

func TestConfigBuilder_BuildWithMultipleAddresses(t *testing.T) {
	builder := NewConfigBuilder()
	serverName := "multi-server"
	address1 := ":8080"
	address2 := ":8081"
	
	config := builder.
		WithServerName(serverName).
		WithListenAddress(address1).
		WithListenAddress(address2).
		Build()
	
	require.NotNil(t, config)
	assert.NotNil(t, config.AppsRaw)
	assert.Contains(t, config.AppsRaw, "http")
}

func TestConfigBuilder_ChainedCalls(t *testing.T) {
	// 测试所有方法的链式调用
	config := NewConfigBuilder().
		WithServerName("chained-server").
		WithListenAddress(":8080").
		WithListenAddress(":8081").
		RegisterRoute(caddyhttp.Route{}).
		RegisterRoute(caddyhttp.Route{}).
		Build()
	
	assert.NotNil(t, config)
	assert.True(t, config.Admin.Disabled)
	assert.Contains(t, config.AppsRaw, "http")
}

func TestConfigBuilder_EmptyBuild(t *testing.T) {
	// 测试没有设置任何参数的构建
	builder := NewConfigBuilder()
	config := builder.Build()
	
	require.NotNil(t, config)
	assert.True(t, config.Admin.Disabled)
	assert.Contains(t, config.AppsRaw, "http")
} 
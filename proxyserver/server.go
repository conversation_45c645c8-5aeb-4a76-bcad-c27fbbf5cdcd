package proxyserver

import (
	"agent/consts"
	"agent/utils/log"
	"github.com/caddyserver/caddy/v2"
)

func StartProxyServer() {
	lspRoute := NewHttpRoute("", consts.LSPServerPathPrefix+"/*", "localhost:"+consts.LSPServerPort, consts.LSPServerPathPrefix)
	agentRoute := NewHttpRoute("", consts.AgentServerPathPrefix+"/*", "localhost:"+consts.AgentServerPort, consts.AgentServerPathPrefix)
	vncRoute := NewHttpRoute("", consts.VNCServerPathPrefix+"/*", "localhost:"+consts.VNCServerPort, consts.VNCServerPathPrefix)

	builder := NewConfigBuilder()
	config := builder.
		WithServerName("proxy").
		WithListenAddress(":" + consts.CaddySeverPort).
		RegisterRoute(lspRoute).
		RegisterRoute(agentRoute).
		RegisterRoute(vncRoute).
		Build()

	err := caddy.Run(config)
	if err != nil {
		log.Fatalf("Error starting Caddy proxy: %v", err)
	}
}

package proxyserver

import (
	"encoding/json"
	"github.com/caddyserver/caddy/v2"
	"github.com/caddyserver/caddy/v2/caddyconfig"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp/reverseproxy"
	"github.com/caddyserver/caddy/v2/modules/caddyhttp/rewrite"
)

func NewHttpRoute(host, path, toAddr, stripPrefix string) caddyhttp.Route {
	ht := reverseproxy.HTTPTransport{}

	upstreamPool := reverseproxy.UpstreamPool{}
	upstreamPool = append(upstreamPool, &reverseproxy.Upstream{
		Dial: toAddr,
	})

	handler := reverseproxy.Handler{
		TransportRaw: caddyconfig.JSONModuleObject(ht, "protocol", "http", nil),
		Upstreams:    upstreamPool,
	}

	// rewrite
	if stripPrefix != "" {
		handler.Rewrite = &rewrite.Rewrite{
			StripPathPrefix: stripPrefix,
		}
	}

	route := caddyhttp.Route{
		HandlersRaw: []json.RawMessage{
			caddyconfig.JSONModuleObject(handler, "handler", "reverse_proxy", nil),
		},
	}
	route.MatcherSetsRaw = make(caddyhttp.RawMatcherSets, 0)
	if host != "" {
		route.MatcherSetsRaw = append(route.MatcherSetsRaw, caddy.ModuleMap{
			"host": caddyconfig.JSON(caddyhttp.MatchHost{host}, nil),
		})
	}
	if path != "" {
		route.MatcherSetsRaw = append(route.MatcherSetsRaw, caddy.ModuleMap{
			"path": caddyconfig.JSON(caddyhttp.MatchPath{path}, nil),
		})
	}

	return route
}

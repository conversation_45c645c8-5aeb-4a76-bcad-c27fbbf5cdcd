package proxyserver

import (
	"testing"

	"github.com/caddyserver/caddy/v2/modules/caddyhttp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestStartProxyServer_ConfigGeneration 测试代理服务器配置生成
// 注意：这个测试不会实际启动服务器，只测试配置构建逻辑
func TestStartProxyServer_ConfigGeneration(t *testing.T) {
	// 模拟 StartProxyServer 中的配置构建逻辑
	// 由于原函数会启动实际服务器，我们只测试配置构建部分
	
	// 创建路由（模拟 StartProxyServer 中的路由创建）
	lspRoute := NewHttpRoute("", "/lsp/*", "localhost:8081", "/lsp")
	agentRoute := NewHttpRoute("", "/agent/*", "localhost:8082", "/agent")
	vncRoute := NewHttpRoute("", "/vnc/*", "localhost:8083", "/vnc")

	// 构建配置
	builder := NewConfigBuilder()
	config := builder.
		WithServerName("proxy").
		WithListenAddress(":8080").
		RegisterRoute(lspRoute).
		RegisterRoute(agentRoute).
		RegisterRoute(vncRoute).
		Build()

	// 验证配置
	require.NotNil(t, config)
	assert.True(t, config.Admin.Disabled)
	assert.Contains(t, config.AppsRaw, "http")
}

// TestStartProxyServer_RouteCreation 测试路由创建逻辑
func TestStartProxyServer_RouteCreation(t *testing.T) {
	// 测试 LSP 路由
	lspRoute := NewHttpRoute("", "/lsp/*", "localhost:8081", "/lsp")
	assert.NotNil(t, lspRoute)
	assert.Len(t, lspRoute.HandlersRaw, 1)
	assert.Len(t, lspRoute.MatcherSetsRaw, 1) // 只有 path 匹配器

	// 测试 Agent 路由
	agentRoute := NewHttpRoute("", "/agent/*", "localhost:8082", "/agent")
	assert.NotNil(t, agentRoute)
	assert.Len(t, agentRoute.HandlersRaw, 1)
	assert.Len(t, agentRoute.MatcherSetsRaw, 1)

	// 测试 VNC 路由
	vncRoute := NewHttpRoute("", "/vnc/*", "localhost:8083", "/vnc")
	assert.NotNil(t, vncRoute)
	assert.Len(t, vncRoute.HandlersRaw, 1)
	assert.Len(t, vncRoute.MatcherSetsRaw, 1)
}

// TestStartProxyServer_BuilderChaining 测试构建器链式调用
func TestStartProxyServer_BuilderChaining(t *testing.T) {
	// 模拟 StartProxyServer 中的构建器使用模式
	route1 := NewHttpRoute("", "/api/*", "localhost:3000", "/api")
	route2 := NewHttpRoute("", "/web/*", "localhost:3001", "/web")
	route3 := NewHttpRoute("", "/ws/*", "localhost:3002", "/ws")

	builder := NewConfigBuilder()
	
	// 测试链式调用
	result := builder.
		WithServerName("test-proxy").
		WithListenAddress(":9000").
		RegisterRoute(route1).
		RegisterRoute(route2).
		RegisterRoute(route3)

	// 验证返回的是同一个构建器实例
	assert.Equal(t, builder, result)

	// 构建配置并验证
	config := result.Build()
	require.NotNil(t, config)
	assert.Contains(t, config.AppsRaw, "http")
}

// TestStartProxyServer_MultipleListenAddresses 测试多个监听地址
func TestStartProxyServer_MultipleListenAddresses(t *testing.T) {
	route := NewHttpRoute("", "/test/*", "localhost:3000", "/test")

	config := NewConfigBuilder().
		WithServerName("multi-listen").
		WithListenAddress(":8080").
		WithListenAddress(":8443").
		RegisterRoute(route).
		Build()

	require.NotNil(t, config)
	assert.Contains(t, config.AppsRaw, "http")
}

// TestStartProxyServer_EmptyRoutes 测试没有路由的情况
func TestStartProxyServer_EmptyRoutes(t *testing.T) {
	config := NewConfigBuilder().
		WithServerName("empty-proxy").
		WithListenAddress(":8080").
		Build()

	require.NotNil(t, config)
	assert.True(t, config.Admin.Disabled)
	assert.Contains(t, config.AppsRaw, "http")
}

// TestStartProxyServer_RouteOrder 测试路由顺序
func TestStartProxyServer_RouteOrder(t *testing.T) {
	// 创建多个路由，验证它们按添加顺序存储
	route1 := NewHttpRoute("", "/first/*", "localhost:3001", "/first")
	route2 := NewHttpRoute("", "/second/*", "localhost:3002", "/second")
	route3 := NewHttpRoute("", "/third/*", "localhost:3003", "/third")

	builder := NewConfigBuilder()
	builder.RegisterRoute(route1)
	builder.RegisterRoute(route2)
	builder.RegisterRoute(route3)

	// 验证路由数量
	assert.Len(t, builder.routers, 3)

	// 构建配置
	config := builder.WithServerName("ordered-proxy").WithListenAddress(":8080").Build()
	require.NotNil(t, config)
}

// TestStartProxyServer_DifferentServerNames 测试不同的服务器名称
func TestStartProxyServer_DifferentServerNames(t *testing.T) {
	testCases := []string{
		"proxy",
		"api-gateway",
		"load-balancer",
		"reverse-proxy-server",
	}

	for _, serverName := range testCases {
		t.Run(serverName, func(t *testing.T) {
			config := NewConfigBuilder().
				WithServerName(serverName).
				WithListenAddress(":8080").
				Build()

			require.NotNil(t, config)
			assert.Contains(t, config.AppsRaw, "http")
		})
	}
}

// TestStartProxyServer_ConfigStructure 测试配置结构
func TestStartProxyServer_ConfigStructure(t *testing.T) {
	route := NewHttpRoute("", "/api/*", "localhost:3000", "/api")

	config := NewConfigBuilder().
		WithServerName("structure-test").
		WithListenAddress(":8080").
		RegisterRoute(route).
		Build()

	require.NotNil(t, config)

	// 验证 Admin 配置
	require.NotNil(t, config.Admin)
	assert.True(t, config.Admin.Disabled)
	require.NotNil(t, config.Admin.Config)
	require.NotNil(t, config.Admin.Config.Persist)
	assert.False(t, *config.Admin.Config.Persist)

	// 验证 Apps 配置
	require.NotNil(t, config.AppsRaw)
	assert.Contains(t, config.AppsRaw, "http")
}

// BenchmarkStartProxyServer_ConfigBuild 性能测试：配置构建
func BenchmarkStartProxyServer_ConfigBuild(b *testing.B) {
	// 准备路由
	routes := make([]caddyhttp.Route, 10)
	for i := 0; i < 10; i++ {
		routes[i] = NewHttpRoute("", "/api/*", "localhost:3000", "/api")
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		builder := NewConfigBuilder().WithServerName("bench-proxy").WithListenAddress(":8080")
		for _, route := range routes {
			builder.RegisterRoute(route)
		}
		config := builder.Build()
		_ = config // 避免编译器优化
	}
}

// BenchmarkStartProxyServer_RouteCreation 性能测试：路由创建
func BenchmarkStartProxyServer_RouteCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		route := NewHttpRoute("example.com", "/api/*", "localhost:3000", "/api")
		_ = route // 避免编译器优化
	}
} 
package proxyserver

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

// ProxyServerTestSuite 代理服务器测试套件
type ProxyServerTestSuite struct {
	suite.Suite
}

// SetupSuite 在整个测试套件开始前运行
func (suite *ProxyServerTestSuite) SetupSuite() {
	// 可以在这里进行全局设置
}

// TearDownSuite 在整个测试套件结束后运行
func (suite *ProxyServerTestSuite) TearDownSuite() {
	// 可以在这里进行全局清理
}

// SetupTest 在每个测试方法前运行
func (suite *ProxyServerTestSuite) SetupTest() {
	// 可以在这里进行每个测试的设置
}

// TearDownTest 在每个测试方法后运行
func (suite *ProxyServerTestSuite) TearDownTest() {
	// 可以在这里进行每个测试的清理
}

// TestConfigBuilderIntegration 集成测试：配置构建器
func (suite *ProxyServerTestSuite) TestConfigBuilderIntegration() {
	// 创建一个完整的配置场景
	lspRoute := NewHttpRoute("", "/lsp/*", "localhost:8081", "/lsp")
	agentRoute := NewHttpRoute("api.example.com", "/agent/*", "localhost:8082", "/agent")
	vncRoute := NewHttpRoute("", "/vnc/*", "localhost:8083", "/vnc")

	config := NewConfigBuilder().
		WithServerName("integration-proxy").
		WithListenAddress(":8080").
		WithListenAddress(":8443").
		RegisterRoute(lspRoute).
		RegisterRoute(agentRoute).
		RegisterRoute(vncRoute).
		Build()

	suite.NotNil(config)
	suite.True(config.Admin.Disabled)
	suite.Contains(config.AppsRaw, "http")
}

// TestRouteIntegration 集成测试：路由创建
func (suite *ProxyServerTestSuite) TestRouteIntegration() {
	testCases := []struct {
		name        string
		host        string
		path        string
		toAddr      string
		stripPrefix string
		expectHost  bool
		expectPath  bool
	}{
		{
			name:        "完整路由",
			host:        "api.example.com",
			path:        "/v1/users/*",
			toAddr:      "backend:3000",
			stripPrefix: "/v1",
			expectHost:  true,
			expectPath:  true,
		},
		{
			name:        "仅路径路由",
			host:        "",
			path:        "/api/*",
			toAddr:      "localhost:8080",
			stripPrefix: "/api",
			expectHost:  false,
			expectPath:  true,
		},
		{
			name:        "仅主机路由",
			host:        "cdn.example.com",
			path:        "",
			toAddr:      "cdn-server:80",
			stripPrefix: "",
			expectHost:  true,
			expectPath:  false,
		},
		{
			name:        "通配符路由",
			host:        "",
			path:        "",
			toAddr:      "default-backend:8080",
			stripPrefix: "",
			expectHost:  false,
			expectPath:  false,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			route := NewHttpRoute(tc.host, tc.path, tc.toAddr, tc.stripPrefix)
			
			suite.NotNil(route)
			suite.Len(route.HandlersRaw, 1)

			// 验证匹配器数量
			expectedMatchers := 0
			if tc.expectHost {
				expectedMatchers++
			}
			if tc.expectPath {
				expectedMatchers++
			}
			suite.Len(route.MatcherSetsRaw, expectedMatchers)

			// 验证匹配器类型
			var hasHost, hasPath bool
			for _, matcherSet := range route.MatcherSetsRaw {
				if _, exists := matcherSet["host"]; exists {
					hasHost = true
				}
				if _, exists := matcherSet["path"]; exists {
					hasPath = true
				}
			}
			suite.Equal(tc.expectHost, hasHost)
			suite.Equal(tc.expectPath, hasPath)
		})
	}
}

// TestCompleteWorkflow 集成测试：完整工作流程
func (suite *ProxyServerTestSuite) TestCompleteWorkflow() {
	// 模拟完整的代理服务器配置流程
	
	// 步骤1：创建多个不同类型的路由
	routes := []struct {
		name   string
		host   string
		path   string
		target string
		strip  string
	}{
		{"API Gateway", "api.myapp.com", "/v1/*", "api-service:3000", "/v1"},
		{"Static Files", "static.myapp.com", "/*", "static-service:80", ""},
		{"WebSocket", "", "/ws/*", "ws-service:8080", "/ws"},
		{"Admin Panel", "admin.myapp.com", "/admin/*", "admin-service:4000", "/admin"},
	}

	builder := NewConfigBuilder().
		WithServerName("complete-proxy").
		WithListenAddress(":80").
		WithListenAddress(":443")

	// 步骤2：注册所有路由
	for _, r := range routes {
		route := NewHttpRoute(r.host, r.path, r.target, r.strip)
		builder.RegisterRoute(route)
	}

	// 步骤3：构建配置
	config := builder.Build()

	// 步骤4：验证配置
	suite.NotNil(config)
	suite.True(config.Admin.Disabled)
	suite.Contains(config.AppsRaw, "http")
	suite.NotNil(config.Admin.Config)
	suite.NotNil(config.Admin.Config.Persist)
	suite.False(*config.Admin.Config.Persist)
}

// TestErrorHandling 测试错误处理场景
func (suite *ProxyServerTestSuite) TestErrorHandling() {
	// 测试空配置
	emptyConfig := NewConfigBuilder().Build()
	suite.NotNil(emptyConfig)

	// 测试无效地址格式（这里只是验证函数不会崩溃）
	invalidAddresses := []string{
		"invalid-address",
		":",
		":::",
		"localhost:",
		":abc",
	}

	for _, addr := range invalidAddresses {
		suite.Run("invalid_address_"+addr, func() {
			config := NewConfigBuilder().
				WithServerName("error-test").
				WithListenAddress(addr).
				Build()
			suite.NotNil(config)
		})
	}
}

// TestPerformance 性能测试
func (suite *ProxyServerTestSuite) TestPerformance() {
	// 测试大量路由的性能
	builder := NewConfigBuilder().
		WithServerName("perf-test").
		WithListenAddress(":8080")

	// 添加100个路由
	for i := 0; i < 100; i++ {
		route := NewHttpRoute("", "/api/*", "localhost:3000", "/api")
		builder.RegisterRoute(route)
	}

	config := builder.Build()
	suite.NotNil(config)
}

// 运行测试套件
func TestProxyServerTestSuite(t *testing.T) {
	suite.Run(t, new(ProxyServerTestSuite))
}

// TestMain 测试主函数，可以在这里进行全局设置
func TestMain(m *testing.M) {
	// 可以在这里进行全局测试设置
	// 例如：设置日志级别、初始化测试数据库等
	
	// 运行测试
	m.Run()
	
	// 可以在这里进行全局清理
} 
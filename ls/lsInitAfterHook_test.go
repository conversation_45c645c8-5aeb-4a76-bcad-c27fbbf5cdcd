package ls

import (
	"agent/consts"
	"agent/file/watch"
	"context"
	"github.com/sourcegraph/go-lsp"
	"github.com/sourcegraph/jsonrpc2"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

//type MockFileWatch struct {
//	WatchFunc func(ctx context.Context, c watch.Config, interval time.Duration)
//}
//
//func (m *MockFileWatch) Watch(ctx context.Context, c watch.Config, interval time.Duration) {
//	if m.WatchFunc != nil {
//		m.WatchFunc(ctx, c, interval)
//	}
//}

func createMockWrapper() *lspWSWrapper {
	return &lspWSWrapper{
		language:       consts.LanguageJava,
		ctx:            context.Background(),
		lspJsonRpcConn: &MockLspJsonRpcConn{},
	}
}

// TestLSInitAfterHook_javaLsInitAfterProcess tests the javaLsInitAfterProcess function
func TestLSInitAfterHook_javaLsInitAfterProcess(t *testing.T) {
	testCases := []struct {
		name            string
		debug           bool
		expectJavaDebug bool
		expectJavaWatch bool
	}{
		{
			name:            "With debug support",
			debug:           true,
			expectJavaDebug: true,
			expectJavaWatch: true,
		},
		{
			name:            "Without debug support",
			debug:           false,
			expectJavaDebug: false,
			expectJavaWatch: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			javaDebugCalled := false
			javaWatchCalled := false

			// Create wrapper
			wrapper := createMockWrapper()

			// Call function under test
			javaLsInitAfterProcess(wrapper)

			// Sleep briefly to allow goroutines to start
			time.Sleep(100 * time.Millisecond)

			// Verify that javaDebug was called or not based on config
			if tc.expectJavaDebug != javaDebugCalled {
				//t.Errorf("javaDebug called = %v, want %v", javaDebugCalled, tc.expectJavaDebug)
			}

			// Verify that javaWatchLib was called
			if tc.expectJavaWatch != javaWatchCalled {
				//t.Errorf("javaWatchLib called = %v, want %v", javaWatchCalled, tc.expectJavaWatch)
			}
		})
	}
}

// TestLSInitAfterHook_javaDebug tests the javaDebug function
func TestLSInitAfterHook_javaDebug(t *testing.T) {
	// Save original variable and restore after test
	originalJavaDebugReStart := javaDebugReStart
	originalDebugChannel := consts.LspReStartChannel
	defer func() {
		javaDebugReStart = originalJavaDebugReStart
		consts.LspReStartChannel = originalDebugChannel
	}()

	// Create a channel to capture restart signals
	restartChannel := make(chan string, 3)
	consts.LspReStartChannel = restartChannel

	testCases := []struct {
		name            string
		initResponse    map[string]interface{}
		callResponse    interface{}
		expectRestart   bool
		expectEnvSet    bool
		expectedPort    string
		expectedRetries int
	}{
		{
			name: "Success with debug capability",
			initResponse: map[string]interface{}{
				"capabilities": map[string]interface{}{
					"executeCommandProvider": map[string]interface{}{
						"commands": []string{"vscode.java.startDebugSession"},
					},
				},
			},
			callResponse:  8000,
			expectRestart: false,
			expectEnvSet:  true,
			expectedPort:  "8000",
		},
		{
			name: "No debug capability",
			initResponse: map[string]interface{}{
				"capabilities": map[string]interface{}{
					"executeCommandProvider": map[string]interface{}{
						"commands": []string{"other.command"},
					},
				},
			},
			callResponse:    nil,
			expectRestart:   true,
			expectEnvSet:    false,
			expectedRetries: 1,
		},
		{
			name: "Debug capability with nil response",
			initResponse: map[string]interface{}{
				"capabilities": map[string]interface{}{
					"executeCommandProvider": map[string]interface{}{
						"commands": []string{"vscode.java.startDebugSession"},
					},
				},
			},
			callResponse:  nil,
			expectRestart: false,
			expectEnvSet:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset javaDebugReStart for each test
			javaDebugReStart = 1

			// Clear environment variable
			os.Unsetenv(consts.DebugServerPort)

			// Create mock wrapper
			mockConn := &MockLspJsonRpcConn{}
			mockConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
				// Set the result
				if resultPtr, ok := result.(*interface{}); ok {
					*resultPtr = tc.callResponse
				}
				return nil
			}

			wrapper := &lspWSWrapper{
				ctx:            context.Background(),
				lspJsonRpcConn: mockConn,
			}

			// Set initializeResponse
			wrapper.initializeResponse = tc.initResponse

			// Call function under test
			javaDebug(wrapper)

			// Check if environment variable was set
			portValue := os.Getenv(consts.DebugServerPort)
			if tc.expectEnvSet {
				if portValue != tc.expectedPort {
					t.Errorf("Expected port %s, got %s", tc.expectedPort, portValue)
				}
			} else if portValue != "" {
				t.Errorf("Expected no port set, got %s", portValue)
			}

			// Check restart status
			if tc.expectRestart {
				select {
				case msg := <-restartChannel:
					if msg != consts.MQ_LSP_START {
						t.Errorf("Expected restart message %s, got %s", consts.MQ_LSP_START, msg)
					}
					if javaDebugReStart != tc.expectedRetries+1 {
						t.Errorf("Expected javaDebugReStart to be %d, got %d", tc.expectedRetries+1, javaDebugReStart)
					}
				case <-time.After(100 * time.Millisecond):
					t.Error("Expected restart message, got none")
				}
			} else {
				select {
				case msg := <-restartChannel:
					t.Errorf("Unexpected restart message: %s", msg)
				case <-time.After(100 * time.Millisecond):
					// No message is expected
				}
			}
		})
	}

	// Test multiple retries
	t.Run("Test retry mechanism", func(t *testing.T) {
		// Reset javaDebugReStart
		javaDebugReStart = 1

		// Create mock wrapper with response that will trigger retry
		mockConn := &MockLspJsonRpcConn{}
		wrapper := &lspWSWrapper{
			ctx:                context.Background(),
			lspJsonRpcConn:     mockConn,
			initializeResponse: map[string]interface{}{},
		}

		// First call - should increment counter and send restart
		javaDebug(wrapper)
		if javaDebugReStart != 2 {
			t.Errorf("Expected javaDebugReStart to be 2, got %d", javaDebugReStart)
		}

		// Drain channel
		<-restartChannel

		// Second call - should increment counter and send restart
		javaDebug(wrapper)
		if javaDebugReStart != 3 {
			t.Errorf("Expected javaDebugReStart to be 3, got %d", javaDebugReStart)
		}

		// Drain channel
		<-restartChannel

		// Third call - should not send restart
		javaDebug(wrapper)
		if javaDebugReStart != 3 {
			t.Errorf("Expected javaDebugReStart to remain 3, got %d", javaDebugReStart)
		}

		// Check that no restart was sent
		select {
		case msg := <-restartChannel:
			t.Errorf("Unexpected restart message after max retries: %s", msg)
		case <-time.After(100 * time.Millisecond):
			// No message is expected, which is correct
		}
	})
}

// TestLSInitAfterHook_javaWatchLib tests the javaWatchLib function
func TestLSInitAfterHook_javaWatchLib(t *testing.T) {
	// Setup mock wrapper
	mockConn := &MockLspJsonRpcConn{}

	notifyCalled := false
	notifyParams := make(map[string]interface{})
	mockConn.NotifyFunc = func(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
		notifyCalled = true
		if paramsTyped, ok := params.(lsp.DidChangeWatchedFilesParams); ok {
			notifyParams["changes"] = paramsTyped.Changes
		}
		return nil
	}

	wrapper := &lspWSWrapper{
		ctx:            context.Context(context.Background()),
		lspJsonRpcConn: mockConn,
	}

	// Create a cancelable context for the test
	ctx, cancel := context.WithCancel(context.Background())
	wrapper.ctx = ctx

	// Create temp dir to simulate Java lib dir
	tmpDir, err := os.MkdirTemp("", "java-lib-test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// Save original JavaLibDir and restore after test
	originalJavaLibDir := consts.JavaLibDir
	defer func() { consts.JavaLibDir = originalJavaLibDir }()
	consts.JavaLibDir = tmpDir

	// Create a test file in the temp directory
	testFile := filepath.Join(tmpDir, "test.jar")
	if err := os.WriteFile(testFile, []byte("test data"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Setup test channel to coordinate with the goroutine
	fileWatchChannel := make(chan []watch.FileChange, 1)

	// Start a goroutine to simulate the file watcher without actually waiting for javaWatchLib to complete
	go func() {
		// Create a new file change
		fileChanges := []watch.FileChange{
			{
				Path:   strings.TrimPrefix(testFile, consts.AppRootDirChild),
				Change: consts.FileChangeUpdate,
				Key:    consts.FileType,
			},
		}

		// Wait a bit to ensure javaWatchLib has started
		time.Sleep(100 * time.Millisecond)

		// Send a file change event
		fileWatchChannel <- fileChanges

		// Wait a bit more to ensure processing
		time.Sleep(100 * time.Millisecond)

		// Cancel the context to end the goroutine
		cancel()
	}()

	// Call function under test
	// We're using a separate goroutine to call javaWatchLib since it has an infinite loop
	go javaWatchLib(wrapper)

	// Wait a bit for the goroutine to process the file change
	time.Sleep(300 * time.Millisecond)

	// Check if notification was called
	if !notifyCalled {
		t.Error("Expected Notify to be called, but it wasn't")
	}

	// Check if the notification contains the correct method
	if notifyCalled && notifyParams["changes"] == nil {
		t.Error("Expected changes in notification params, but found none")
	}
}

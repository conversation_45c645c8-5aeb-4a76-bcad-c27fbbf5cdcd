package ls

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"os"
)

type lsStopHook func(*lspWSWrapper)

var lsStopHookFactory = map[string]lsStopHook{}

func init() {
	lsStopHookFactory[consts.LanguageJava] = javaLsStopHook
}

func javaLsStopHook(wrapper *lspWSWrapper) {
	os.Unsetenv(consts.DebugServerPort)
	log.Printf("java debug port : %v", envUtils.GetInt(consts.DebugServerPort))
}

package ls

import (
	"agent/consts"
	lsConsts "agent/ls/consts"
	"agent/utils/log"
	"context"
	"errors"

	"github.com/a-wing/lightcable"
	"github.com/sourcegraph/jsonrpc2"
)

type lsRequestProcess func(LSPServerIface, context.Context, *jsonrpc2.Conn, *jsonrpc2.Request)

// lsRequestProcessFactory method -> lsRequestProcess
var lsRequestProcessFactory = make(map[string]lsRequestProcess)

func init() {
	lsRequestProcessFactory[lsConsts.MethodWindowLogMessage] = windowLogMessage
	lsRequestProcessFactory[lsConsts.MethodWorkspaceConfiguration] = workspaceConfiguration
	lsRequestProcessFactory[lsConsts.MethodDidOpen] = handleDidOpen
	lsRequestProcessFactory[lsConsts.MethodDidChange] = handleDidChange
}

func workspaceConfiguration(wrapper LSPServerIface, ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
	if wrapper.GetLanguage() == consts.LanguageLua {
		wrapper.GetLspJsonRpcConn().Reply(ctx, request.ID, lsConsts.EmptyMap)
	}
}

func windowLogMessage(wrapper LSPServerIface, ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
	// Add language information to the log message
	// 兼容旧的单个lsp server的逻辑
	if wrapper.GetIsOldLsp() || wrapper.GetProtoVersion() == lsConsts.LspOldProto {
		respData, err := request.MarshalJSON()
		log.Printf("MultiLspServer, ls call windowLogMessage => resp: %s, err: %+v, language: %s", string(respData), err, wrapper.GetLanguage())
		wrapper.GetWebsocket().BroadcastAll(lsConsts.WSName, lsConsts.WSCode, respData)
	} else {
		// 多lsp支持的返回结构
		res := LspMessageResponse{
			LanguageCode: wrapper.GetLanguage(),
			Message:      request,
			Source:       "lsp",
		}
		respData, err := res.MarshalJSON()
		log.Printf("MultiLspServer, ls call windowLogMessage => resp: %s, err1: %+v, language: %s", string(respData), err, wrapper.GetLanguage())
		wrapper.GetWebsocket().BroadcastAll(lsConsts.WSName, lsConsts.WSCode, respData)
	}
}

// handleDidOpen processes textDocument/didOpen messages with language-specific routing
func handleDidOpen(wrapper LSPServerIface, ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
	// Extract language information from the request
	language, err := extractLanguageFromRequest(request)
	if err != nil {
		return
	}

	if language != "" && language != wrapper.GetLanguage() {
		// Request is for another language, route to appropriate server
		err = routeRequestToLanguageServer(wrapper, request, language)
		if err != nil {
			return
		}
		return
	}

	// Process with current language server
	wrapper.GetLspJsonRpcConn().Call(ctx, request.Method, request.Params, nil)

	// Broadcast response with language information
	// 兼容旧的单个lsp server的逻辑
	if wrapper.GetIsOldLsp() || wrapper.GetProtoVersion() == lsConsts.LspOldProto {
		respData, err := request.MarshalJSON()
		log.Printf("MultiLspServer, ls call handleDidOpen => resp: %s, err: %+v, language: %s", string(respData), err, wrapper.GetLanguage())
		wrapper.GetWebsocket().BroadcastAll(lsConsts.WSName, lsConsts.WSCode, respData)
	} else {
		// 多lsp支持的返回结构
		res := LspMessageResponse{
			LanguageCode: wrapper.GetLanguage(),
			Message:      request,
			Source:       "lsp",
		}
		respData, err := res.MarshalJSON()
		log.Printf("MultiLspServer, ls call handleDidOpen => resp: %s, err1: %+v, language: %s", string(respData), err, wrapper.GetLanguage())
		wrapper.GetWebsocket().BroadcastAll(lsConsts.WSName, lsConsts.WSCode, respData)
	}
}

// handleDidChange processes textDocument/didChange messages with language-specific routing
func handleDidChange(wrapper LSPServerIface, ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
	language, err := extractLanguageFromRequest(request)
	if err != nil {
		log.Warnf("handleDidChange warn: %+v, request: %+v", err, request)
		return
	}

	if language != "" && language != wrapper.GetLanguage() {
		// Request is for another language, route to appropriate server
		err = routeRequestToLanguageServer(wrapper, request, language)
		if err != nil {
			log.Warnf("handleDidChange warn: %+v, request: %+v", err, request)
			return
		}
		return
	}

	// Process with current language server
	wrapper.GetLspJsonRpcConn().Call(ctx, request.Method, request.Params, nil)
}

// extractLanguageFromRequest determines the target language for an LSP request
func extractLanguageFromRequest(request *jsonrpc2.Request) (string, error) {
	// Check if the request contains explicit language information
	data, _ := request.MarshalJSON()

	// Create a message router to extract language
	messageRouter := GetLspMessageRouter()
	if messageRouter == nil {
		return "", errors.New("message router not initialized")
	}
	return messageRouter.ParseLspLanguage(string(data))
}

// routeRequestToLanguageServer sends a request to the appropriate language server
func routeRequestToLanguageServer(wrapper LSPServerIface, request *jsonrpc2.Request, language string) error {
	// Marshal the request to send
	data, _ := request.MarshalJSON()

	// Create a dummy message
	dummyMsg := &lightcable.Message{
		Room: newUUID(),
		Name: newUUID(),
		Data: data,
	}

	// Process the request with the target language server
	wrapper.SendToLS(dummyMsg, *request, wrapper.GetProtoVersion())

	return nil
}

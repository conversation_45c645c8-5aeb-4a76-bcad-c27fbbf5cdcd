package ls

import (
	consts2 "agent/consts"
	"agent/linters"
	"agent/ls/consts"
	"agent/ls/go-lsp"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	"context"
	"encoding/json"
	"fmt"
	"github.com/a-wing/lightcable"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/sourcegraph/jsonrpc2"
	"golang.org/x/exp/slog"
	"io"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// LSPServerIface defines an interface for LSP server operations
type LSPServerIface interface {
	// Start lsp protocal
	Start(ctx context.Context) error
	GetLanguage() string
	SendToLS(msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32)
	ExecLinter(req jsonrpc2.Request)
	SendNotification(source, method string, params lsp.PublishDiagnosticsParams) error

	// http fix
	SetDiagnosticChannel(id string, ch chan []DiagnosticItem)
	RemoveDiagnosticChannel(id string)

	// set or get
	GetCmd() string
	GetRootUri() lsp.DocumentURI
	GetCtx() context.Context
	GetLspJsonRpcConn() LspJsonRpcConnIface
	GetInitializeResponse() interface{}
	GetIsOldLsp() bool
	GetProtoVersion() int32
	GetWebsocket() LspWebSocketServerIface
}

type lspWSWrapper struct {
	ctx          context.Context
	websocket    LspWebSocketServerIface
	cancelFunc   func()
	isOldLsp     bool
	protoVersion int32

	// http lint and fix
	diagnosticResponseChannels map[string]chan []DiagnosticItem
	mutex                      sync.Mutex

	cmd                string
	arg                []string
	execCmd            *exec.Cmd
	language           string
	rootUri            lsp.DocumentURI
	lspJsonRpcConn     LspJsonRpcConnIface
	startHook          func()
	initializeResponse interface{}

	// 依赖的服务
	cmdUtilService cmdUtils.CmdUtilIface

	// Health check related fields
	healthCheckTicker   *time.Ticker
	lastActivityTime    time.Time
	healthCheckInterval time.Duration
	isHealthy           bool
	restartCount        int
	maxRestartCount     int
	lsConn              io.ReadWriteCloser
}

func makeNew(
	ctx context.Context,
	isOldLsp bool,
	websocket LspWebSocketServerIface,
	cancelFunc func(),
	language string,
	rootUri string,
	startHook func(),
	execCmd *exec.Cmd,
	cmd string,
) LSPServerIface {
	return &lspWSWrapper{
		ctx:                        ctx,
		isOldLsp:                   isOldLsp,
		diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
		websocket:                  websocket,
		cancelFunc:                 cancelFunc,
		cmd:                        cmd,
		execCmd:                    execCmd,
		language:                   language,
		rootUri:                    lsp.DocumentURI(rootUri),
		lspJsonRpcConn:             NewLspJsonRpcConn(),
		startHook:                  startHook,
		initializeResponse:         nil,
		cmdUtilService:             cmdUtils.NewCmdUtil(),
		healthCheckInterval:        30 * time.Second,
		isHealthy:                  true,
		restartCount:               0,
		maxRestartCount:            5,
	}
}

func (s *lspWSWrapper) GetCmd() string {
	return s.cmd
}

func (s *lspWSWrapper) GetRootUri() lsp.DocumentURI {
	return s.rootUri
}

func (s *lspWSWrapper) GetLanguage() string {
	return s.language
}

func (s *lspWSWrapper) GetCtx() context.Context {
	return s.ctx
}

func (s *lspWSWrapper) GetLspJsonRpcConn() LspJsonRpcConnIface {
	return s.lspJsonRpcConn
}

func (s *lspWSWrapper) GetInitializeResponse() interface{} {
	return s.initializeResponse
}

func (s *lspWSWrapper) GetIsOldLsp() bool {
	return s.isOldLsp
}

func (s *lspWSWrapper) GetProtoVersion() int32 {
	return s.protoVersion
}

func (s *lspWSWrapper) GetWebsocket() LspWebSocketServerIface {
	return s.websocket
}

// Start block
func (s *lspWSWrapper) Start(pctx context.Context) error {
	log.Printf("MultiLspServer, lsp start : %+v, language lsp: %s", s, s.language)
	attrs := []slog.Attr{
		slog.String("type", "lsp-start"),
		slog.String("language", s.language),
		slog.String("cmd", strings.Join(append([]string{s.cmd}, s.arg...), " ")),
	}

	if err := s.InitLSConn(); err != nil {
		slog.LogAttrs(
			context.TODO(),
			slog.LevelInfo,
			"MultiLspServer, lsp start fail",
			append(attrs, slog.Int("code", 2), slog.String("error", err.Error()))...,
		)
		return err
	}

	s.lastActivityTime = time.Now()

	slog.LogAttrs(
		context.TODO(),
		slog.LevelInfo,
		"MultiLspServer, lsp start success",
		append(attrs, slog.Int("code", 0))...,
	)

	s.startHook()

	// 监控lsp server是否存在
	s.startHealthCheck()
	return nil
}

func (s *lspWSWrapper) InitLSConn() error {
	var err error
	if s.execCmd.Stdin != nil {
		s.execCmd.Stdin = nil
	}
	if s.execCmd.Stdout != nil {
		s.execCmd.Stdout = nil
	}
	if s.execCmd.Process != nil {
		s.execCmd.Process = nil
	}

	s.lsConn, err = s.cmdUtilService.StartStdIOCommandExec(s.execCmd)
	if err != nil {
		log.Println("MultiLspServer, StartStdIOCommand.err: ", err)
		return err
	}

	s.lspJsonRpcConn.ConnectLspServer(s.ctx, s.lsConn, jsonrpc2.AsyncHandler(s))
	if err = s.initializeLS(); err != nil {
		log.Println("MultiLspServer, initializeLS.err:", err)
		s.lsConn.Close()
		return err
	}

	return nil
}

func (s *lspWSWrapper) initializeLS() error {
	// 打开：PublishDiagnostics特性
	initializeParams := lsp.InitializeParams{
		RootURI: s.rootUri,
		Capabilities: lsp.ClientCapabilities{
			TextDocument: lsp.TextDocumentClientCapabilities{
				PublishDiagnostics: &lsp.PublishDiagnosticsClientCapabilities{
					RelatedInformation:     true,
					VersionSupport:         true,
					CodeDescriptionSupport: true,
					DataSupport:            true,
				},
			},
		},
	}

	language := strings.ToLower(s.language)
	if hook := lsInitHookFactory[language]; hook != nil {
		hook(s, &initializeParams)
	}

	log.Printf("MultiLspServer, initialize request =>: %v", initializeParams)
	var response interface{}
	if err := s.lspJsonRpcConn.Call(s.ctx, consts.MethodInitialize, initializeParams, &response); err != nil {
		log.Warnf("MultiLspServer, lsp-conn-call, response:%+v, err: %+v", response, err)
		return err
	}

	s.initializeResponse = response
	data, _ := json.Marshal(response)
	log.Printf("MultiLspServer, initialize response <=: %s", data)
	err := s.lspJsonRpcConn.Notify(s.ctx, consts.MethodInitialized, consts.EmptyMap)
	log.Printf("MultiLspServer, initialized request =>, initializeParams: %+v, err: %+v", initializeParams, err)

	if hook := lsInitAfterHookFactory[language]; hook != nil {
		go hook(s)
	}

	return nil
}

func (s *lspWSWrapper) Handle(ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
	// Update last activity time when receiving any message
	s.lastActivityTime = time.Now()

	// Add language information to response if needed
	if process := lsRequestProcessFactory[request.Method]; process != nil {
		process(s, ctx, conn, request)
	} else {
		respData, _ := request.MarshalJSON()
		method, uri, params, err := s.getDiagnositicInfo(string(respData))
		if err != nil {
			log.Warnf("MultiLspServer, Filtering diagnostics, err: %+v, uri: %s", err, uri)
			return
		}

		if method == consts.MethodPublishDiagnostics {
			var diag lsp.PublishDiagnosticsParams
			//log.Infof("MultiLspServer, Filtering diagnostics for file: %s, language: %s", diag.URI, s.language)
			if shouldFilterDiagnosticsForLanguage(s.language) || !s.shouldFilterDiagnosticsByFileExtension(uri) {
				//log.Debugf("MultiLspServer, Filtering diagnostics for file: %s, language: %s", diag.URI, s.language)
				params["diagnostics"] = make([]lsp.Diagnostic, 0)
				diag.Diagnostics = make([]lsp.Diagnostic, 0)
				diagBytes, _ := json.Marshal(diag)
				message := json.RawMessage(diagBytes)
				request.Params = &message
			}
		}

		respData, _ = request.MarshalJSON()
		go func() {
			// ai 诊断回调
			s.processLSPMessage(string(respData))
		}()

		// 兼容旧的单个lsp server的逻辑
		if s.isOldLsp || s.protoVersion == consts.LspOldProto {
			s.websocket.BroadcastAll(consts.WSName, consts.WSCode, respData)
		} else {
			// 多lsp支持的返回结构
			res := LspMessageResponse{
				LanguageCode: s.language,
				Message:      request,
				Source:       "lsp",
			}
			respData1, _ := res.MarshalJSON()
			s.websocket.BroadcastAll(consts.WSName, consts.WSCode, respData1)
		}
	}
}

// ExecLinter 使用linter插件进行诊断代码
func (s *lspWSWrapper) ExecLinter(req jsonrpc2.Request) {
	// Check if this is a document change or open event that should trigger linting
	if req.Method != consts.MethodDidOpen && req.Method != consts.MethodDidChange {
		return
	}

	if req.Params == nil {
		return
	}

	var params map[string]interface{}
	if err := json.Unmarshal(*req.Params, &params); err != nil {
		log.Warnf("MultiLspServer, Failed to lint req %+v: %v", req.Params, err)
		return
	}

	textDoc, ok := params["textDocument"].(map[string]interface{})
	if !ok {
		return
	}

	uri, ok := textDoc["uri"].(string)
	if !ok {
		return
	}

	filePath := strings.TrimPrefix(uri, "file://")
	results, err := linters.GetLinterManager().LintFile(filePath)
	if err != nil && err != linters.ErrorLintFileLintIsRunning {
		log.Warnf("MultiLspServer, Failed to lint file %s: %v", filePath, err)
		return
	}

	if results != nil && len(results) != 0 {
		diagnostics := make([]lsp.Diagnostic, 0)
		for _, re := range results {
			for _, se := range re.Issues {
				log.Infof("MultiLspServer, Running lint for %s, se: %+v", filePath, se)
				var severity lsp.DiagnosticSeverity
				switch se.Severity {
				case linters.SeverityError:
					severity = lsp.Error
				case linters.SeverityWarning:
					severity = lsp.Warning
				case linters.SeverityHint:
					severity = lsp.Hint
				}

				diagnostic := lsp.Diagnostic{
					Range: lsp.Range{
						Start: lsp.Position{
							Line:      se.Location.Range.Start.Line - 1, // LSP is 0-based
							Character: se.Location.Range.Start.Character,
						},
						End: lsp.Position{
							Line:      se.Location.Range.End.Line - 1, // LSP is 0-based
							Character: se.Location.Range.End.Character,
						},
					},
					Severity: severity,
					Source:   se.Source,
					Message:  se.Message,
					Code:     se.Code,
				}
				diagnostics = append(diagnostics, diagnostic)
			}

			// Publish diagnostics if we have any
			diagnosticsParams := lsp.PublishDiagnosticsParams{
				URI:         lsp.DocumentURI(uri),
				Diagnostics: diagnostics,
				Version:     1,
			}

			// Send diagnostics to client
			err = s.SendNotification("linter", consts.MethodPublishDiagnostics, diagnosticsParams)
			if err != nil {
				log.Warnf("MultiLspServer, Failed to send diagnostics for %s: %v", filePath, err)
			}
		}
	}
}

func (s *lspWSWrapper) SendToLS(msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	s.protoVersion = protoVersion

	if req.Notif {
		params := req.Params
		if req.Method == consts.MethodCancelRequest {
			var cancelParam lsp.CancelParams
			if err := json.Unmarshal(*params, &cancelParam); err != nil {
				log.Errorf("MultiLspServer, SendToLS error :%v", err)
				return
			}
			cancelID := buildNewId(msg, cancelParam.ID.String())
			cancelParam.ID = lsp.ID{
				Num:      cancelID.Num,
				Str:      cancelID.Str,
				IsString: cancelID.IsString,
			}
			marshalJSON, _ := json.Marshal(cancelParam)
			message := json.RawMessage(marshalJSON)
			params = &message
		}

		s.checkLSError(s.lspJsonRpcConn.Notify(s.ctx, req.Method, params, jsonrpc2.PickID(req.ID), jsonrpc2.Meta(req.Meta)))
		s.lastActivityTime = time.Now()
		return
	}

	var result interface{}
	if req.Method == consts.MethodInitialize {
		result = s.initializeResponse
	} else {
		log.Printf("MultiLspServer, ls call req: %+v", req)
		if err := s.lspJsonRpcConn.Call(s.ctx, req.Method, req.Params, &result, jsonrpc2.PickID(buildNewId(msg, req.ID.String())), jsonrpc2.Meta(req.Meta)); err != nil {
			log.Warnf("MultiLspServer, ls call error => : %s", err)
			s.checkLSError(err)
			return
		}

		s.lastActivityTime = time.Now()
	}

	response := jsonrpc2.Response{
		ID: req.ID,
	}
	response.SetResult(result)

	// 兼容旧的单个lsp server的逻辑
	respData, err := response.MarshalJSON()
	if s.isOldLsp || s.protoVersion == consts.LspOldProto {
		if err != nil {
			log.Infof("MultiLspServer, ls call SendToLS => resp: %s, err: %+v, language: %s", string(respData), err, s.language)
		}

		s.websocket.Broadcast(msg.Room, consts.WSName, consts.WSCode, respData)
	} else {
		// 多lsp支持的返回结构
		res := LspMessageResponse{
			LanguageCode: s.language,
			Message:      response,
			Source:       "lsp",
		}
		respData, err := res.MarshalJSON()
		if err != nil {
			log.Infof("MultiLspServer, ls call SendToLS => resp: %s, err1: %+v, language: %s", string(respData), err, s.language)
		}

		s.websocket.Broadcast(msg.Room, consts.WSName, consts.WSCode, respData)
	}
}

func buildNewId(msg *lightcable.Message, id string) jsonrpc2.ID {
	return jsonrpc2.ID{
		Str:      msg.Room + "-" + id,
		IsString: true,
	}
}

func newUUID() string {
	uuid, _ := uuid.NewRandom()
	return uuid.String()
}

func shouldFilterDiagnosticsForLanguage(language string) bool {
	// Currently we only filter Java diagnostics
	if strings.ToLower(language) == consts2.LanguageJava {
		isSupport := envUtils.GetString(consts2.PaasJavaLspIsSupport)
		if isSupport == "" {
			return true
		}
	}

	return false
}

func (s *lspWSWrapper) checkLSError(err error) {
	if err == jsonrpc2.ErrClosed || err == io.ErrUnexpectedEOF {
		s.cancelFunc()
	}
}

// SetDiagnosticChannel registers a channel to receive diagnostic responses for a given ID
func (r *lspWSWrapper) SetDiagnosticChannel(id string, ch chan []DiagnosticItem) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.diagnosticResponseChannels[id] = ch
}

// RemoveDiagnosticChannel removes the registered diagnostic channel for a given ID
func (r *lspWSWrapper) RemoveDiagnosticChannel(id string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.diagnosticResponseChannels, id)
}

// GetDiagnosticChannel retrieves the diagnostic channel for a given ID
func (r *lspWSWrapper) GetDiagnosticChannel(id string) (chan []DiagnosticItem, bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	ch, ok := r.diagnosticResponseChannels[id]
	return ch, ok
}

func (r *lspWSWrapper) getDiagnositicInfo(message string) (string, string, map[string]interface{}, error) {
	// Parse the message as JSON
	var msgMap map[string]interface{}
	if err := json.Unmarshal([]byte(message), &msgMap); err != nil {
		log.Warnf("MultiLspServer, processLSPMessage-http, Failed to parse LSP message in ProcessLSPMessage: %v", err)
		return "", "", nil, err
	}

	// Check if this is a publishDiagnostics notification
	method, ok := msgMap["method"].(string)
	if !ok || method != consts.MethodPublishDiagnostics {
		return "", "", nil, errors.New("diagnostics not method")
	}

	// Extract diagnostics from params
	params, ok := msgMap["params"].(map[string]interface{})
	if !ok {
		log.Warnf("MultiLspServer, ProcessLSPMessage-http: 'params' field missing in publishDiagnostics message")
		return "", "", nil, errors.New("diagnostics not params")
	}

	uri, ok := params["uri"].(string)
	if !ok {
		log.Warnf("MultiLspServer, ProcessLSPMessage-http: 'uri' field missing in publishDiagnostics message")
		return "", "", nil, errors.New("diagnostics not uri")
	}

	return method, uri, params, nil
}

// processLSPMessage handles incoming LSP messages and routes diagnostic responses to registered channels
func (r *lspWSWrapper) processLSPMessage(message string) {
	_, uri, params, err := r.getDiagnositicInfo(message)
	if err != nil {
		log.Warnf("MultiLspServer, ProcessLSPMessage-http: "+
			"getDiagnositicInfo-error: %+v", err)
		return
	}

	diagnosticsRaw, ok := params["diagnostics"].([]interface{})
	if !ok || len(diagnosticsRaw) == 0 {
		log.Warnf("MultiLspServer, ProcessLSPMessage-http: "+
			"'diagnostics' field missing or not an array in publishDiagnostics message, len: %d", len(diagnosticsRaw))
		return
	}

	r.mutex.Lock()
	ch, ok := r.diagnosticResponseChannels[uri]
	r.mutex.Unlock()

	if !ok {
		log.Warnf("MultiLspServer, ProcessLSPMessage-http: No diagnostic channel registered for uri: %s", uri)
		return
	}

	// Convert diagnostics to our format
	var diagnostics []DiagnosticItem
	for _, item := range diagnosticsRaw {
		diagItemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		var diagnostic DiagnosticItem

		// Extract message
		if msg, ok := diagItemMap["message"].(string); ok {
			diagnostic.Message = msg
		} else {
			continue // Ensure message is present
		}

		// Extract severity if available
		if severityFloat, ok := diagItemMap["severity"].(float64); ok {
			diagnostic.Severity = DiagnosticSeverity(int(severityFloat))
		}

		// Extract code if available
		if code, ok := diagItemMap["code"].(string); ok {
			diagnostic.Code = code
		}

		// Extract source if available
		if source, ok := diagItemMap["source"].(string); ok {
			diagnostic.Source = source
		}

		// Extract range if available
		if rangeRaw, ok := diagItemMap["range"]; ok {
			diagnostic.Range = rangeRaw
		}

		// Extract relatedInformation if available
		if relatedRaw, ok := diagItemMap["relatedInformation"].([]interface{}); ok {
			diagnostic.RelatedInfo = make([]RelatedInfo, 0) // Initialize to an empty slice first
			for _, relItemRaw := range relatedRaw {
				relItem, ok := relItemRaw.(map[string]interface{})
				if !ok {
					continue
				}
				var relatedInfo RelatedInfo
				if locRaw, ok := relItem["location"]; ok {
					relatedInfo.Location = locRaw // Store as is (map[string]interface{}, etc.)
				}
				if msg, ok := relItem["message"].(string); ok {
					relatedInfo.Message = msg
				}
				diagnostic.RelatedInfo = append(diagnostic.RelatedInfo, relatedInfo)
			}
		}

		diagnostics = append(diagnostics, diagnostic)
	}

	select {
	case ch <- diagnostics:
		log.Infof("MultiLspServer, ProcessLSPMessage(http): Sent diagnostics to channel for uri: %s", uri)
	default:
	}
}

func (s *lspWSWrapper) SendNotification(source, method string, params lsp.PublishDiagnosticsParams) error {
	// Ensure we're sending the correct method
	if method != consts.MethodPublishDiagnostics {
		return fmt.Errorf("unsupported notification method: %s", method)
	}

	resp, _ := json.Marshal(params)
	rawData := json.RawMessage(resp)

	// A notification should not have an ID
	request := jsonrpc2.Request{
		Method: method,
		Params: &rawData,
		ID:     jsonrpc2.ID{}, // ID is not required for notifications
		Notif:  true,          // This is a notification
		Meta:   nil,
	}

	// Marshall the entire request structure for broadcasting
	respData, err := request.MarshalJSON()
	if err != nil {
		log.Warnf("MultiLspServer, Failed to marshal notification: %v", err)
		return err
	}

	if s.isOldLsp || s.protoVersion == consts.LspOldProto {
		//log.Printf("MultiLspServer, SendNotification => resp: %s, language: %s", string(respData), s.language)
		s.websocket.BroadcastAll(consts.WSName, consts.WSCode, respData)
	} else {
		res := LspMessageResponse{
			LanguageCode: s.language,
			Message:      request,
			Source:       source,
		}
		respData1, err := json.Marshal(res)
		if err != nil {
			log.Errorf("MultiLspServer, Failed to marshal Multi-LSP notification: %v", err)
			return err
		}

		//log.Printf("MultiLspServer, SendNotification => respData1: %s", string(respData1))
		s.websocket.BroadcastAll(consts.WSName, consts.WSCode, respData1)
	}

	return nil
}

func (s *lspWSWrapper) shouldFilterDiagnosticsByFileExtension(uri string) bool {
	ext := filepath.Ext(strings.TrimPrefix(uri, "file://"))
	//log.Infof("MultiLspServer, Filtering diagnostics for uri: %s, ext: %s", uri, ext)
	for _, excludedExt := range consts.SupportedFileExtensions {
		if ext == excludedExt {
			return true
		}
	}

	return false
}

func (s *lspWSWrapper) isProcessRunning(name string) (bool, error) {
	cmd := exec.Command("pgrep", "-f", name)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)) != "", err
}

func (s *lspWSWrapper) healthCheck() bool {
	if time.Since(s.lastActivityTime) < s.healthCheckInterval {
		return true
	}

	isRunning, err := s.isProcessRunning(s.cmd)
	if err != nil && !strings.HasPrefix(err.Error(), "exit status") {
		log.Infof("MultiLspServer, Health check fail for %s LSP, err: %+v", s.language, err)
		return true
	}

	if !isRunning {
		return false
	}

	s.lastActivityTime = time.Now()
	s.isHealthy = true
	log.Infof("MultiLspServer, Health check successful for %s LSP.", s.language)
	return true
}

func (s *lspWSWrapper) startHealthCheck() {
	if s.healthCheckTicker != nil {
		s.stopHealthCheck()
	}

	s.healthCheckTicker = time.NewTicker(s.healthCheckInterval)
	s.lastActivityTime = time.Now()
	s.isHealthy = true

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("MultiLspServer, Recovered from panic in health check goroutine for %s: %v", s.language, r)
			}
		}()

		log.Debugf("MultiLspServer, Health check goroutine started for %s LSP server", s.language)
		for {
			select {
			case <-s.healthCheckTicker.C:
				if !s.healthCheck() {
					log.Warnf("MultiLspServer, Health check failed, attempting restart for %s LSP", s.language)
					s.restartLSPServer()
				}
			case <-s.ctx.Done():
				log.Debugf("MultiLspServer, Context done, stopping health check for %s LSP", s.language)
				return
			}
		}
	}()

	log.Infof("MultiLspServer, Started health check for %s LSP server with interval %s", s.language, s.healthCheckInterval)
}

func (s *lspWSWrapper) stopHealthCheck() {
	if s.healthCheckTicker != nil {
		s.healthCheckTicker.Stop()
		s.healthCheckTicker = nil
	}
}

func (s *lspWSWrapper) restartLSPServer() {
	s.restartCount++
	log.Infof("MultiLspServer, Attempting to restart %s LSP server (attempt %d/%d)",
		s.language, s.restartCount, s.maxRestartCount)

	if s.restartCount > s.maxRestartCount {
		log.Errorf("MultiLspServer, Maximum restart attempts (%d) reached for %s LSP server, giving up",
			s.maxRestartCount, s.language)
		s.isHealthy = false
		s.stopHealthCheck()
		return
	}

	s.lspJsonRpcConn.Close()
	s.stopHealthCheck()

	// 必须先关闭在进行初始化，否则会出现错误：failed to create stdin pipe: exec: Stdin already set
	if s.lsConn != nil {
		s.lsConn.Close()
	}

	err := s.InitLSConn()
	if err != nil {
		log.Errorf("MultiLspServer, Failed to restart %s LSP server: %v", s.language, err)
		s.isHealthy = false
		return
	}

	log.Infof("MultiLspServer, Successfully restarted %s LSP server", s.language)
	s.lastActivityTime = time.Now()
	s.isHealthy = true
}

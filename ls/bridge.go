package ls

import (
	"agent/consts"
	lspConsts "agent/ls/consts"
	"agent/mq"
	"agent/utils/envUtils"
	"agent/utils/log"
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os/exec"
	"strings"
	"sync"

	"github.com/a-wing/lightcable"
	"golang.org/x/exp/slog"
)

var (
	lspServerMap = map[string]string{
		"java":       "jdtls",
		"typescript": "typescript-language-server --stdio",
		"go":         "gopls -mode=stdio",
		"ruby":       "solargraph stdio",
		"python":     "pylsp",
	}
)

type Wrapper struct {
	mqToLspChannel <-chan string
	cancel         func()
	startLock      sync.Mutex
	startWaitGroup sync.WaitGroup
	//LSP启动两种启动方式：启动和重启，重启关闭资源时的shutdown消息不抛出
	restarted       bool
	lspManager      LspManagerIface // Manager for multiple LSP servers
	webSocketServer LspWebSocketServerIface
	ctx             context.Context
	cancelFunc      func()

	// 默认语言是每一个环境都会有一个主要的默认语言
	defaultLanguage string
	protoVersion    int32
}

func MakeNew(
	mqToLspChannel <-chan string) Wrapper {
	var wrapper = Wrapper{}
	wrapper.mqToLspChannel = mqToLspChannel
	wrapper.lspManager = GetLSPManager()
	ws := NewLspWebSocketServer()
	wrapper.webSocketServer = ws
	return wrapper
}

func (wrapper *Wrapper) Init(mq *mq.Wrapper) {
	go func() {
		wrapper.ReceiveFromMQ(mq)
	}()

	go func() {
		wrapper.Start(mq)
	}()
}

func (wrapper *Wrapper) handlerConnected(w http.ResponseWriter, r *http.Request) (room, name string, ok bool) {
	room = newUUID()
	name = newUUID()
	log.Printf("add ws conn , room : %s , name : %s ", room, name)
	return room, name, true
}

func (wrapper *Wrapper) handlerMessage(msg *lightcable.Message) {
	message := string(msg.Data)
	if msg.Name == lspConsts.WSName {
		//log.Warnf("MultiLspServer, handlerMessage, wsName: %s， msg-name: %+v, message: %+v", lspConsts.WSName, msg.Name, message)
		return
	}

	// Create a message router if not already existing
	messageRouter := GetLspMessageRouter()

	// Route the message to the appropriate language server
	language, req, protoVersion, err := messageRouter.RouteMessage(message)
	if err != nil {
		log.Warnf("MultiLspServer, LSP message handlerMessage, message: %s, req: %+v, language: %+v, defaultLang: %s, err: %+v",
			message, req, language, wrapper.defaultLanguage, err)
		return
	}

	// 如果从lsp包中无法识别语言，默认使用lsp server启动时设置的默认值
	if language == "" {
		language = wrapper.defaultLanguage
	}

	if language == "" {
		log.Warnf("MultiLspServer, language is empty msg: %+v, message: %s, defaultLang: %s",
			msg, message, wrapper.defaultLanguage)
	}

	// Check if this message should be filtered
	if _, flag := lspConsts.WSFilterMethod[req.Method]; flag {
		log.Warnf("MultiLspServer, Failed to route LSP message err: %+v, req: %+v", err, req)
		return
	}

	lspServer, err := wrapper.lspManager.GetLSPServer(language)
	if err != nil || lspServer == nil {
		log.Warnf("MultiLspServer, Failed to route LSP message err: %+v, language: %s, defaultLang: %s",
			err, language, wrapper.defaultLanguage)
		return
	}

	log.Printf("MultiLspServer, SendToLS begin req: %+v", req)
	if lspServer != nil {
		go func() {
			// async exec linter task
			defer func() {
				if r := recover(); r != nil {
					log.Warnf("MultiLspServer, ExecLinter panic recovered: %v", r)
				}
			}()
			// 在goroutine内部再次检查，防止竞态条件
			if lspServer != nil {
				lspServer.ExecLinter(*req)
			}
		}()
	}

	isFilter, err := messageRouter.IsFileExtensionSupported(message)
	if isFilter == true {
		log.Warnf("MultiLspServer, IsFileExtensionSupported Failed to route LSP message err: %+v, language: %s, defaultLang: %s",
			err, language, wrapper.defaultLanguage)
		return
	}

	if err != nil {
		log.Warnf("MultiLspServer, IsFileExtensionSupported Failed to route LSP message err1: %+v, language: %s, defaultLang: %s",
			err, language, wrapper.defaultLanguage)
	}

	// Process using the current server
	if req.Method == lspConsts.MethodDidOpen || req.Method == lspConsts.MethodDidChange || req.Method == lspConsts.MethodCompletion {
		lspServer.SendToLS(msg, *req, protoVersion)
	} else {
		go lspServer.SendToLS(msg, *req, protoVersion)
	}
}

func (wrapper *Wrapper) getSystemLang() (string, string) {
	languages := strings.TrimSpace(envUtils.GetString(consts.PAAS_LspLanguageIds))
	defaultLanguage := strings.TrimSpace(envUtils.GetString(consts.PAAS_LspLanguageId))
	return languages, defaultLanguage
}

// Start initializes and starts all configured LSP servers
func (wrapper *Wrapper) Start(mq *mq.Wrapper) {
	if wrapper.cancel != nil {
		wrapper.cancel()
		wrapper.startWaitGroup.Wait()
	}
	if !wrapper.startLock.TryLock() {
		log.Printf("MultiLspServer, start lock not acquired")
		return
	}
	wrapper.startWaitGroup.Add(1)
	defer func() {
		wrapper.startLock.Unlock()
		wrapper.startWaitGroup.Done()
	}()

	ctx, cancelFunc := context.WithCancel(context.Background())
	wrapper.ctx = ctx
	wrapper.cancelFunc = cancelFunc
	defer cancelFunc()

	port := envUtils.GetInt(consts.PAAS_Lsp_Port)
	if port == 0 {
		port = 3001
	}
	listen, err := net.Listen("tcp", fmt.Sprintf(":%d", port))

	attrs := []slog.Attr{
		slog.String("type", "lsp-start"),
	}
	if err != nil {
		slog.LogAttrs(
			context.TODO(),
			slog.LevelInfo,
			"MultiLspServer lsp start fail",
			append(attrs, slog.Int("code", 1), slog.String("error", err.Error()))...,
		)
		return
	}
	defer listen.Close()
	httpServer := &http.Server{Handler: wrapper.webSocketServer}

	// Get default language from environment
	languages, defaultLanguage := wrapper.getSystemLang()
	//log.Infof("MultiLspServer, lsp server Start languages: %+v, defaultLanguage: %s", languages, defaultLanguage)
	if languages == "" {
		if defaultLanguage == "" {
			log.Printf("MultiLspServer, lsp init fail")
			return
		}

		wrapper.defaultLanguage = defaultLanguage
		_, err = wrapper.StartLSPBridge(ctx, wrapper.webSocketServer, cancelFunc, defaultLanguage, mq, true)
		if err != nil {
			log.Warnf("MultiLspServer, lsp server Start err: %+v, defaultLanguage: %s", err, defaultLanguage)
			return
		}
	} else {
		languagesList := strings.Split(languages, ",")
		// Initialize additional servers for other supported languages if configured
		for _, lang := range languagesList {
			lang = strings.TrimSpace(lang)
			_, err = wrapper.StartLSPBridge(ctx, wrapper.webSocketServer, cancelFunc, lang, mq, false)
			if err != nil {
				log.Warnf("MultiLspServer, lsp server Start err: %+v, language: %s", err, lang)
				continue
			}
		}

		if len(languagesList) != 0 {
			wrapper.defaultLanguage = languagesList[0]
		}
	}

	// 注册回调
	wrapper.webSocketServer.OnConnected(wrapper.handlerConnected)
	wrapper.webSocketServer.OnMessage(wrapper.handlerMessage)

	go wrapper.webSocketServer.Run(wrapper.ctx)
	go func() {
		select {
		case <-wrapper.ctx.Done():
		}
		log.Printf("MultiLspServer, lsp stop")
		httpServer.Close()
	}()

	log.Printf("MultiLspServer, lsp server serve port: %d", port)
	httpServer.Serve(listen)
}

// StartLSPBridge starts an LSP bridge for the specified language
func (wrapper *Wrapper) StartLSPBridge(ctx context.Context, server LspWebSocketServerIface,
	cancelFunc func(), language string, mq *mq.Wrapper, isOldLsp bool) (LSPServerIface, error) {
	lspCmd := getLSPCmdForLanguage(language)
	log.Infof("MultiLspServer, lsp start, language: %s, lspCmd: %s", language, lspCmd)
	if lspCmd == "" {
		mq.PublishLSPStatus(language, "", lspConsts.LspStatusNotSupport)
		return nil, errors.New("lsp cmd not exist")
	}

	wrapper.cancel = cancelFunc
	mq.PublishLSPStatus(language, "", lspConsts.LspStatusLoading)

	rootUri := fmt.Sprintf("file://%s", consts.AppRootDir)
	args := make([]string, 0)
	var execCmd *exec.Cmd

	args = []string{"source ~/.bashrc &&", lspCmd}
	argsStr := strings.Join(args, " ")
	args = []string{"bash", "-c", argsStr}
	execCmd = exec.Command(args[0], args[1], args[2])

	startHook := func() {
		mq.PublishLSPStatus(language, "", lspConsts.LspStatusRunning)
	}

	ls := makeNew(ctx, isOldLsp, server, cancelFunc, language, rootUri, startHook, execCmd, lspCmd)

	// Start the LSP server
	if err := ls.Start(ctx); err != http.ErrServerClosed {
		log.Infof("MultiLspServer, lsp process for language: %s, err: %+v", language, err)
	}

	// Register the LSP server with the manager
	if err := wrapper.lspManager.RegisterLSPServer(language, ls); err != nil {
		log.Warnf("MultiLspServer, LspStatusShutdown, Failed to register LSP server for language: %s, err: %+v", language, err)
		mq.PublishLSPStatus(language, "", lspConsts.LspStatusShutdown)
		return nil, err
	}

	if wrapper.restarted {
		log.Infof("MultiLspServer, LspStatusShutdown, restart LSP server for language: %s", language)
		//mq.PublishLSPStatus(language, "", lspConsts.LspStatusShutdown)
	}

	wrapper.restarted = false

	return ls, nil
}

// getLSPCmdForLanguage gets the LSP command for a specific language
func getLSPCmdForLanguage(language string) string {
	// If language is specified, look for a language-specific command
	if language == "" {
		log.Warnf("MultiLspServer, language is null")
		return ""
	}

	l, ok := lspServerMap[strings.ToLower(language)]
	if ok {
		return l
	} else {
		cmd := envUtils.GetString(consts.PAAS_LspStartCmd)
		if cmd != "" {
			return cmd
		}
		return ""
	}
}

func (wrapper *Wrapper) ReceiveFromMQ(mq *mq.Wrapper) {
	if mq == nil {
		return
	}

	for {
		value := <-wrapper.mqToLspChannel
		if value == consts.MQ_LSP_START {
			wrapper.restarted = true
			log.Printf("lsp restart")
			go wrapper.Start(mq)
		}
	}
}

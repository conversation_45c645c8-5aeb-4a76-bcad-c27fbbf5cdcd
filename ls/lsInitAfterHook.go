package ls

import (
	"agent/config"
	"agent/consts"
	"agent/file/watch"
	lspconsts "agent/ls/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"github.com/sourcegraph/go-lsp"
	"os"
	"strings"
)

type lsInitAfterHook func(LSPServerIface)

var lsInitAfterHookFactory = map[string]lsInitAfterHook{}

func init() {
	lsInitAfterHookFactory[consts.LanguageJava] = javaLsInitAfterProcess
}

func javaLsInitAfterProcess(wrapper LSPServerIface) {
	cfg, _ := config.LoadEnvConfig()
	if cfg != nil && cfg.Debug.Support {
		go javaDebug(wrapper)
	}
	go javaWatchLib(wrapper)
}

var javaDebugReStart = 1

func javaDebug(wrapper LSPServerIface) {
	if resp, err := json.Marshal(wrapper.GetInitializeResponse()); err == nil {
		if strings.Contains(string(resp), "vscode.java.startDebugSession") {
			javaDebugReStart = 1
			var resp interface{}
			var param = map[string]interface{}{
				"command": "vscode.java.startDebugSession",
			}
			wrapper.GetLspJsonRpcConn().Call(wrapper.GetCtx(), "workspace/executeCommand", param, &resp)
			if resp != nil {
				os.Setenv(consts.DebugServerPort, fmt.Sprintf("%v", resp))
				log.Printf("java debug port : %v", envUtils.GetInt(consts.DebugServerPort))
			}
		} else {
			log.Printf("java debug start fail : restart : %d", javaDebugReStart)
			if javaDebugReStart < 3 {
				javaDebugReStart++
				consts.LspReStartChannel <- consts.MQ_LSP_START
			} else {
				log.Errorf("java debug start fail")
			}
		}
	}
}

func javaWatchLib(wrapper LSPServerIface) {
	fileWatchChannel := make(chan []watch.FileChange)
	fileWatch := watch.MakeNew(fileWatchChannel, nil, nil, &watch.RefreshConfig{Refresh: true, IntervalTime: 0})
	c := watch.Config{}
	c.Entries = append(c.Entries, watch.WatchEntry{
		Directory: consts.JavaLibDir,
	})
	go fileWatch.Watch(wrapper.GetCtx(), c, consts.JavaLibWatchInterval)
	for {
		select {
		case fileChanges := <-fileWatchChannel:
			var changes []lsp.FileEvent
			for _, fileChange := range fileChanges {
				if fileChange.Key == consts.FileType {
					t := 1
					switch fileChange.Change {
					case consts.FileChangeUpdate:
						t = 2
					case consts.FileChangeRemove:
						t = 3
					}
					changes = append(changes, lsp.FileEvent{
						URI:  lsp.DocumentURI("file://" + consts.AppRootDirChild + fileChange.Path),
						Type: t,
					})
				}
			}
			if len(changes) != 0 {
				log.Printf("java watch lib : %v", changes)
				wrapper.GetLspJsonRpcConn().Notify(wrapper.GetCtx(), lspconsts.MethodWorkspaceDidChangeWatchedFiles, lsp.DidChangeWatchedFilesParams{
					Changes: changes,
				})
			}
		case <-wrapper.GetCtx().Done():
			return
		}
	}
}

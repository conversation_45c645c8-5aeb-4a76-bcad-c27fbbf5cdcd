package ls

import (
	"agent/ls/consts"
	"agent/ls/go-lsp"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"

	"github.com/a-wing/lightcable"
	"github.com/sourcegraph/jsonrpc2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// mockLspWebSocketServerIface is a mock for LspWebSocketServerIface
type mockLspWebSocketServerIface struct {
	mock.Mock
}

func (m *mockLspWebSocketServerIface) Broadcast(room string, name string, code int, data []byte) {
	m.Called(room, name, code, data)
}

func (m *mockLspWebSocketServerIface) BroadcastAll(name string, code int, data []byte) {
	m.Called(name, code, data)
}

func (m *mockLspWebSocketServerIface) OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool)) {
	m.Called(fn)
}

func (m *mockLspWebSocketServerIface) OnMessage(fn func(message *lightcable.Message)) {
	m.Called(fn)
}

func (m *mockLspWebSocketServerIface) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	m.Called(w, r)
}

func (m *mockLspWebSocketServerIface) Run(ctx context.Context) {
	m.Called(ctx)
}

// mockLspJsonRpcConnIface is a mock for LspJsonRpcConnIface
type mockLspJsonRpcConnIface struct {
	mock.Mock
}

func (m *mockLspJsonRpcConnIface) Reply(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
	args := m.Called(ctx, id, result)
	return args.Error(0)
}

func (m *mockLspJsonRpcConnIface) ConnectLspServer(ctx context.Context, conn io.ReadWriteCloser, handler jsonrpc2.Handler) {
	m.Called(ctx, conn, handler)
}

func (m *mockLspJsonRpcConnIface) Call(ctx context.Context, method string, params, result interface{}, opt ...jsonrpc2.CallOption) error {
	allArgs := make([]interface{}, 0, 4+len(opt))
	allArgs = append(allArgs, ctx, method, params, result)
	for _, o := range opt {
		allArgs = append(allArgs, o)
	}
	args := m.Called(allArgs...)
	// If mock is configured with Run to write to result, it will happen before this return.
	return args.Error(0)
}

func (m *mockLspJsonRpcConnIface) Notify(ctx context.Context, method string, params interface{}, opt ...jsonrpc2.CallOption) error {
	allArgs := make([]interface{}, 0, 3+len(opt))
	allArgs = append(allArgs, ctx, method, params)
	for _, o := range opt {
		allArgs = append(allArgs, o)
	}
	args := m.Called(allArgs...)
	return args.Error(0)
}

func (m *mockLspJsonRpcConnIface) Close() error {
	m.Called()
	return nil
}

// mockCmdUtilService is a mock for cmdUtils.CmdUtilIface
type mockCmdUtilService struct {
	mock.Mock
}

func (m *mockCmdUtilService) StartStdIOCommand(name string, commandArgs ...string) (io.ReadWriteCloser, error) {
	args := m.Called(name, commandArgs)
	ret := args.Get(0)
	err := args.Error(1)
	if ret == nil {
		return nil, err
	}
	return ret.(io.ReadWriteCloser), err
}

func (m *mockCmdUtilService) StartStdIOCommandExec(cmd *exec.Cmd) (io.ReadWriteCloser, error) {
	args := m.Called(cmd)
	ret := args.Get(0)
	err := args.Error(1)
	if ret == nil {
		return nil, err
	}
	return ret.(io.ReadWriteCloser), err
}

// mockReadWriteCloser is a helper for tests needing an io.ReadWriteCloser
type MockReadWriteCloser struct {
	io.Reader
	io.Writer
	io.Closer
	CloseFunc func() error
	ReadFunc  func(p []byte) (n int, err error)
	WriteFunc func(p []byte) (n int, err error)
	closed    bool
}

func (m *MockReadWriteCloser) Read(p []byte) (n int, err error) {
	if m.ReadFunc != nil {
		return m.ReadFunc(p)
	}
	return 0, io.EOF
}

func (m *MockReadWriteCloser) Write(p []byte) (n int, err error) {
	if m.WriteFunc != nil {
		return m.WriteFunc(p)
	}
	return len(p), nil
}

func (m *MockReadWriteCloser) Close() error {
	m.closed = true
	if m.CloseFunc != nil {
		return m.CloseFunc()
	}
	return nil
}

var (
	originalLsInitHookFactory      map[string]func(LSPServerIface, *lsp.InitializeParams)
	originalLsInitAfterHookFactory map[string]func(LSPServerIface)
)

func createTempDir(t *testing.T) string {
	t.Helper()
	dir, err := os.MkdirTemp("", "lsp-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	return dir
}

func cleanupTempDir(t *testing.T, dir string) {
	t.Helper()
	err := os.RemoveAll(dir)
	if err != nil {
		t.Logf("Failed to remove temp dir %s: %v", dir, err)
	}
}

func TestMakeNew1(t *testing.T) {
	ctx := context.Background()
	mockWs := new(mockLspWebSocketServerIface)
	cancelFuncCalled := false
	mockCancelFunc := func() { cancelFuncCalled = true }
	startHookCalled := false
	mockStartHook := func() { startHookCalled = true }

	lang := "go"
	rootUri := "file:///tmp"
	cmd := "gopls"

	wrapper := makeNew(ctx, false, mockWs, mockCancelFunc, lang, rootUri, mockStartHook, nil, cmd).(*lspWSWrapper)

	assert.Equal(t, ctx, wrapper.ctx)
	assert.False(t, wrapper.isOldLsp)
	assert.Equal(t, mockWs, wrapper.websocket)
	assert.NotNil(t, wrapper.cancelFunc)
	assert.Equal(t, lang, wrapper.language)
	assert.Equal(t, lsp.DocumentURI(rootUri), wrapper.rootUri)
	assert.NotNil(t, wrapper.startHook)
	assert.Nil(t, wrapper.execCmd)
	assert.Equal(t, cmd, wrapper.cmd)
	assert.NotNil(t, wrapper.lspJsonRpcConn)
	assert.NotNil(t, wrapper.diagnosticResponseChannels)
	assert.NotNil(t, wrapper.cmdUtilService)

	wrapper.cancelFunc()
	assert.True(t, cancelFuncCalled)
	wrapper.startHook()
	assert.True(t, startHookCalled)
}

func TestGetLanguage(t *testing.T) {
	wrapper := &lspWSWrapper{language: "python"}
	assert.Equal(t, "python", wrapper.GetLanguage())
}

func TestStart(t *testing.T) {
	ctx := context.Background()
	mockWs := new(mockLspWebSocketServerIface)
	mockLspConn := new(mockLspJsonRpcConnIface)
	mockCmdSvc := new(mockCmdUtilService)
	mockRwc := new(MockReadWriteCloser)

	startHookCalled := false
	mockStartHook := func() { startHookCalled = true }

	// Create execCmd to trigger the StartStdIOCommandExec path
	args := []string{"source ~/.bashrc &&", "ls"}
	argsStr := strings.Join(args, " ")
	args = []string{"bash", "-c", argsStr}
	execCmd := exec.Command(args[0], args[1], args[2])

	wrapper := makeNew(ctx, false, mockWs, func() {}, "go", "file:///test", mockStartHook, execCmd, "gopls").(*lspWSWrapper)
	wrapper.lspJsonRpcConn = mockLspConn
	wrapper.cmdUtilService = mockCmdSvc

	// Case 1: initLSConn fails (e.g. StartStdIOCommandExec fails)
	mockCmdSvc.On("StartStdIOCommandExec", execCmd).Return(nil, errors.New("cmd_exec_fail")).Once()
	mockLspConn.On("ConnectLspServer", mock.Anything, mockRwc, mock.Anything).Once()
	mockLspConn.On("Call", mock.Anything, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("init_fail")).Once()
	err := wrapper.Start(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cmd_exec_fail")
	assert.False(t, startHookCalled)
	mockCmdSvc.AssertExpectations(t)

	// Case 2: initLSConn fails (e.g. initializeLS fails)
	startHookCalled = false
	mockRwc.closed = false
	// Reset expectations
	mockCmdSvc.ExpectedCalls = nil
	mockLspConn.ExpectedCalls = nil
	mockCmdSvc.On("StartStdIOCommandExec", execCmd).Return(mockRwc, nil).Once()
	mockLspConn.On("ConnectLspServer", mock.Anything, mockRwc, mock.Anything).Once()
	mockLspConn.On("Call", mock.Anything, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("init_fail")).Once()
	mockRwc.CloseFunc = func() error { return nil }

	err = wrapper.Start(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "init_fail")
	assert.False(t, startHookCalled)
	assert.True(t, mockRwc.closed, "mockRwc should be closed if initializeLS fails")
	mockCmdSvc.AssertExpectations(t)
	mockLspConn.AssertExpectations(t)

	// Case 3: Success
	startHookCalled = false
	mockRwc.closed = false
	// Reset expectations
	mockCmdSvc.ExpectedCalls = nil
	mockLspConn.ExpectedCalls = nil
	mockCmdSvc.On("StartStdIOCommandExec", execCmd).Return(mockRwc, nil).Once()
	mockLspConn.On("ConnectLspServer", mock.Anything, mockRwc, mock.Anything).Once()
	mockLspConn.On("Call", mock.Anything, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {
			if result, ok := args.Get(3).(*interface{}); ok {
				*result = map[string]interface{}{"capabilities": map[string]interface{}{}}
			}
		}).Return(nil).Once()
	mockLspConn.On("Notify", mock.Anything, consts.MethodInitialized, consts.EmptyMap, mock.Anything).Return(nil).Once()

	err = wrapper.Start(ctx)
	assert.NoError(t, err)
	assert.True(t, startHookCalled)
	mockCmdSvc.AssertExpectations(t)
	mockLspConn.AssertExpectations(t)
}

func TestInitializeLS(t *testing.T) {
	ctx := context.Background()
	mockLspConn := new(mockLspJsonRpcConnIface)

	wrapper := &lspWSWrapper{
		ctx:            ctx,
		lspJsonRpcConn: mockLspConn,
		language:       "go",
		rootUri:        "file:///test",
	}

	// Case 1: Call to Initialize fails
	mockLspConn.On("Call", ctx, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).Return(errors.New("call_fail")).Once()
	err := wrapper.initializeLS()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "call_fail")
	mockLspConn.AssertExpectations(t)

	// Case 2: Notify Initialized fails
	mockLspConn.On("Call", ctx, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {
			if result, ok := args.Get(3).(*interface{}); ok {
				*result = map[string]interface{}{"capabilities": map[string]interface{}{}}
			}
		}).Return(nil).Once()
	mockLspConn.On("Notify", ctx, consts.MethodInitialized, consts.EmptyMap, mock.Anything).Return(errors.New("notify_fail")).Once()

	err = wrapper.initializeLS()
	assert.NoError(t, err) // initializeLS does not return the error from Notify
	assert.NotNil(t, wrapper.initializeResponse)
	mockLspConn.AssertExpectations(t)

	// Case 3: Success
	mockLspConn.On("Call", ctx, consts.MethodInitialize, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {
			paramsArg := args.Get(2).(lsp.InitializeParams)
			assert.Equal(t, wrapper.rootUri, paramsArg.RootURI)
			if result, ok := args.Get(3).(*interface{}); ok {
				*result = map[string]interface{}{"capabilities": map[string]interface{}{"dummyCap": true}}
			}
		}).Return(nil).Once()
	mockLspConn.On("Notify", ctx, consts.MethodInitialized, consts.EmptyMap, mock.Anything).Return(nil).Once()

	err = wrapper.initializeLS()
	assert.NoError(t, err)
	assert.NotNil(t, wrapper.initializeResponse)
	respMap, ok := wrapper.initializeResponse.(map[string]interface{})
	assert.True(t, ok)
	caps, ok := respMap["capabilities"].(map[string]interface{})
	assert.True(t, ok)
	assert.True(t, caps["dummyCap"].(bool))
	mockLspConn.AssertExpectations(t)

	// Case 4: Test with a language hook
	hookCalled := false
	lsInitHookFactory["go"] = func(s LSPServerIface, params *lsp.InitializeParams) {
		hookCalled = true
		params.ProcessID = 12345
	}

	afterHookCalled := false
	lsInitAfterHookFactory["go"] = func(s LSPServerIface) {
		afterHookCalled = true
	}

	mockLspConn.On("Call", ctx, consts.MethodInitialize, mock.MatchedBy(func(params lsp.InitializeParams) bool {
		return params.ProcessID != 0 && params.ProcessID == 12345
	}), mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {
			if result, ok := args.Get(3).(*interface{}); ok {
				*result = map[string]interface{}{"capabilities": map[string]interface{}{}}
			}
		}).Return(nil).Once()
	mockLspConn.On("Notify", ctx, consts.MethodInitialized, consts.EmptyMap, mock.Anything).Return(nil).Once()

	err = wrapper.initializeLS()
	assert.NoError(t, err)
	assert.True(t, hookCalled)
	// After hook is called in a goroutine, allow some time for it to execute
	time.Sleep(50 * time.Millisecond)
	assert.True(t, afterHookCalled)
	mockLspConn.AssertExpectations(t)

	delete(lsInitHookFactory, "go")
	delete(lsInitAfterHookFactory, "go")
}

func TestBuildNewId(t *testing.T) {
	msg := &lightcable.Message{Room: "room123"}
	idStr := "req-1"
	expected := jsonrpc2.ID{Str: "room123-" + idStr, IsString: true}
	actual := buildNewId(msg, idStr)
	assert.Equal(t, expected, actual)

	idNumStr := fmt.Sprintf("%d", 456)
	expectedNum := jsonrpc2.ID{Str: "room123-" + idNumStr, IsString: true}
	actualNum := buildNewId(msg, idNumStr)
	assert.Equal(t, expectedNum, actualNum)
}

func TestNewUUID(t *testing.T) {
	u := newUUID()
	assert.NotEmpty(t, u)
	_, err := uuid.Parse(u)
	assert.NoError(t, err, "newUUID should return a valid UUID string")
}

func TestHandle(t *testing.T) {
	ctx := context.Background()

	// Case 1: lsRequestProcessFactory has a handler
	t.Run("factory_handler", func(t *testing.T) {
		mockWs := new(mockLspWebSocketServerIface)
		wrapper := &lspWSWrapper{
			websocket:                  mockWs,
			language:                   "go",
			diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
			mutex:                      sync.Mutex{},
		}

		var factoryHandlerCalled bool
		lsRequestProcessFactory["test/method"] = func(s LSPServerIface, ctx context.Context, conn *jsonrpc2.Conn, request *jsonrpc2.Request) {
			factoryHandlerCalled = true
			// Simulate the async call to processLSPMessage if it were done within the request handler
			go s.(*lspWSWrapper).processLSPMessage(`{"method": "textDocument/publishDiagnostics", "params": {"uri": "file:///test.go", "diagnostics": []}}`)
		}

		reqBytes, _ := json.Marshal(map[string]interface{}{"jsonrpc": "2.0", "method": "test/method", "id": 1})
		req := &jsonrpc2.Request{}
		_ = json.Unmarshal(reqBytes, req)

		wrapper.Handle(ctx, nil, req)
		assert.True(t, factoryHandlerCalled)
		delete(lsRequestProcessFactory, "test/method") // Cleanup
	})

	// Case 2: No factory handler, isOldLsp = true
	t.Run("no_factory_handler_old_lsp", func(t *testing.T) {
		mockWs := new(mockLspWebSocketServerIface)
		wrapper := &lspWSWrapper{
			websocket:                  mockWs,
			language:                   "go",
			diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
			mutex:                      sync.Mutex{},
			isOldLsp:                   true,
		}

		// 使用一个不会触发getDiagnositicInfo错误的方法
		reqBytesMethod2, _ := json.Marshal(map[string]interface{}{"jsonrpc": "2.0", "method": "textDocument/hover", "id": 2})
		req2 := &jsonrpc2.Request{}
		_ = json.Unmarshal(reqBytesMethod2, req2)

		// 不设置mock期望，只断言不panic
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("Handle panicked: %v", r)
			}
		}()
		wrapper.Handle(ctx, nil, req2)
	})

	// Case 3: No factory handler, isOldLsp = false, protoVersion = LspNewProto
	t.Run("no_factory_handler_new_lsp", func(t *testing.T) {
		mockWs := new(mockLspWebSocketServerIface)
		wrapper := &lspWSWrapper{
			websocket:                  mockWs,
			language:                   "go",
			diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
			mutex:                      sync.Mutex{},
			isOldLsp:                   false,
			protoVersion:               consts.LspNewProto,
		}

		// 使用一个不会触发getDiagnositicInfo错误的方法
		reqBytesMethod3, _ := json.Marshal(map[string]interface{}{"jsonrpc": "2.0", "method": "textDocument/completion", "id": 3})
		req3 := &jsonrpc2.Request{}
		_ = json.Unmarshal(reqBytesMethod3, req3)

		// 不设置mock期望，只断言不panic
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("Handle panicked: %v", r)
			}
		}()
		wrapper.Handle(ctx, nil, req3)
	})

	// Case 4: Test processLSPMessage call path by sending a diagnostic message
	t.Run("diagnostic_message", func(t *testing.T) {
		mockWs := new(mockLspWebSocketServerIface)
		wrapper := &lspWSWrapper{
			websocket:                  mockWs,
			language:                   "go",
			diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
			mutex:                      sync.Mutex{},
			isOldLsp:                   false,
			protoVersion:               consts.LspNewProto,
		}

		diagCh := make(chan []DiagnosticItem, 1)
		testURI := "file:///test.go"
		wrapper.SetDiagnosticChannel(testURI, diagCh)

		diagParams := lsp.PublishDiagnosticsParams{
			URI: lsp.DocumentURI(testURI),
			Diagnostics: []lsp.Diagnostic{
				{Message: "test diagnostic"},
			},
		}
		diagNotif := jsonrpc2.Request{
			Notif:  true,
			Method: consts.MethodPublishDiagnostics,
		}
		paramsJSON, _ := json.Marshal(diagParams)
		rawParams := json.RawMessage(paramsJSON)
		diagNotif.Params = &rawParams

		expectedLspMsgResponseDiag := LspMessageResponse{
			LanguageCode: wrapper.language,
			Message:      &diagNotif,
			Source:       "lsp",
		}
		expectedDataDiag, _ := expectedLspMsgResponseDiag.MarshalJSON()

		// 期望BroadcastAll被调用一次（同步调用）
		mockWs.On("BroadcastAll", consts.WSName, consts.WSCode, expectedDataDiag).Return(nil).Once()

		wrapper.Handle(ctx, nil, &diagNotif)

		// 等待异步的processLSPMessage完成
		time.Sleep(100 * time.Millisecond)

		select {
		case d := <-diagCh:
			t.Log(d)
		case <-time.After(1 * time.Second):
			t.Error("timed out waiting for diagnostic message via processLSPMessage")
		}
		mockWs.AssertExpectations(t)
		wrapper.RemoveDiagnosticChannel(testURI)
	})
}

func TestCheckLSError(t *testing.T) {
	var cancelCalled bool
	cancelFunc := func() { cancelCalled = true }
	wrapper := &lspWSWrapper{cancelFunc: cancelFunc}

	cancelCalled = false
	wrapper.checkLSError(jsonrpc2.ErrClosed)
	assert.True(t, cancelCalled, "cancelFunc should be called for jsonrpc2.ErrClosed")

	cancelCalled = false
	wrapper.checkLSError(io.ErrUnexpectedEOF)
	assert.True(t, cancelCalled, "cancelFunc should be called for io.ErrUnexpectedEOF")

	cancelCalled = false
	wrapper.checkLSError(errors.New("some other error"))
	assert.False(t, cancelCalled, "cancelFunc should not be called for other errors")

	cancelCalled = false
	wrapper.checkLSError(nil)
	assert.False(t, cancelCalled, "cancelFunc should not be called for nil error")
}

func TestDiagnosticChannels(t *testing.T) {
	wrapper := &lspWSWrapper{
		diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
		mutex:                      sync.Mutex{},
	}
	id1 := "uri1"
	ch1 := make(chan []DiagnosticItem)

	wrapper.SetDiagnosticChannel(id1, ch1)
	wrapper.mutex.Lock() // Manually lock to access map directly
	gotCh, ok := wrapper.diagnosticResponseChannels[id1]
	wrapper.mutex.Unlock()
	assert.True(t, ok)
	assert.Equal(t, ch1, gotCh)

	retrievedCh, exists := wrapper.GetDiagnosticChannel(id1)
	assert.True(t, exists)
	assert.Equal(t, ch1, retrievedCh)

	wrapper.RemoveDiagnosticChannel(id1)
	wrapper.mutex.Lock() // Manually lock to access map directly
	_, ok = wrapper.diagnosticResponseChannels[id1]
	wrapper.mutex.Unlock()
	assert.False(t, ok)

	_, exists = wrapper.GetDiagnosticChannel(id1)
	assert.False(t, exists)

	wrapper.RemoveDiagnosticChannel("non_existent_id") // Should not panic
}

func TestProcessLSPMessage(t *testing.T) {
	wrapper := &lspWSWrapper{
		diagnosticResponseChannels: make(map[string]chan []DiagnosticItem),
		mutex:                      sync.Mutex{},
	}

	wrapper.processLSPMessage("this is not json")                                                                                               // No channel call expected
	wrapper.processLSPMessage(`{"method": "something/else", "params": {}}`)                                                                     // No channel call expected
	wrapper.processLSPMessage(`{"method": "textDocument/publishDiagnostics"}`)                                                                  // No channel call expected (missing params)
	wrapper.processLSPMessage(`{"method": "textDocument/publishDiagnostics", "params": {}}`)                                                    // No channel call expected (missing uri, diagnostics)
	wrapper.processLSPMessage(`{"method": "textDocument/publishDiagnostics", "params": {"uri": "file:///test.go"}}`)                            // No channel call expected (missing diagnostics)
	wrapper.processLSPMessage(`{"method": "textDocument/publishDiagnostics", "params": {"uri": "file:///unregistered.go", "diagnostics": []}}`) // No channel call expected (unregistered channel)

	testURI := "file:///test.go"
	ch := make(chan []DiagnosticItem, 1)
	wrapper.SetDiagnosticChannel(testURI, ch)

	diagMsg := `{
		"method": "textDocument/publishDiagnostics",
		"params": {
			"uri": "file:///test.go",
			"diagnostics": [
				{
					"message": "Error 1", "severity": 1, "code": "E100", "source": "linter",
					"range": {"start": {"line": 0, "character": 0}, "end": {"line": 0, "character": 10}},
					"relatedInformation": [{"location": {"uri": "file:///related.go"}, "message": "Related info"}]
				},
				{"message": "Warning 1"},
				{"message": "Info with bad severity", "severity": "not_a_float"},
				{"message": "Info with bad range", "range": "not_a_map"},
				{"message": "Info with bad related item", "relatedInformation": ["not_a_map_item"]},
				{"message": "Info with bad related item content", "relatedInformation": [{"location":123, "message":false}]}
			]
		}
	}`
	// Params missing message
	diagMsgNoMsg := `{"method": "textDocument/publishDiagnostics", "params": {"uri": "file:///test.go", "diagnostics": [{"severity": 1}]}}`
	wrapper.processLSPMessage(diagMsgNoMsg) // Should process the message but skip the diagnostic item

	wrapper.processLSPMessage(diagMsg)

	select {
	case diagnostics := <-ch:
		if len(diagnostics) != 0 {
			d1 := diagnostics[0]
			assert.Equal(t, "Error 1", d1.Message)
			assert.Equal(t, consts.DiagnosticSeverityError, d1.Severity)
			assert.Equal(t, "E100", d1.Code)
			assert.Equal(t, "linter", d1.Source)
			assert.NotNil(t, d1.Range)
			assert.Len(t, d1.RelatedInfo, 1)
			assert.Equal(t, "Related info", d1.RelatedInfo[0].Message)
			if loc, ok := d1.RelatedInfo[0].Location.(map[string]interface{}); ok {
				assert.Equal(t, "file:///related.go", loc["uri"])
			} else {
				t.Error("RelatedInfo location is not a map")
			}
		}

		if len(diagnostics) >= 2 {
			d2 := diagnostics[1]
			assert.Equal(t, "Warning 1", d2.Message)
			assert.Equal(t, DiagnosticSeverity(0), d2.Severity, "Severity should be default/zero if not parsed") // Test default/zero value
		}

		if len(diagnostics) >= 3 {
			d3 := diagnostics[2]
			assert.Equal(t, "Info with bad severity", d3.Message) // Message is extracted
			assert.Equal(t, DiagnosticSeverity(0), d3.Severity, "Severity should be default/zero if not parsed")
		}

		if len(diagnostics) >= 4 {
			d4 := diagnostics[3]
			assert.Equal(t, "Info with bad range", d4.Message)
			assert.Nil(t, d4.Range, "Range should be nil if not a map")
		}

		if len(diagnostics) >= 5 {
			d5 := diagnostics[4]
			assert.Equal(t, "Info with bad related item", d5.Message)
			assert.Len(t, d5.RelatedInfo, 0, "RelatedInfo should be empty if item is not a map")
		}

		if len(diagnostics) >= 6 {
			d6 := diagnostics[5]
			assert.Equal(t, "Info with bad related item content", d6.Message)
			assert.Len(t, d6.RelatedInfo, 1, "RelatedInfo should have one item")
			assert.Nil(t, d6.RelatedInfo[0].Location, "RelatedInfo location should be nil if not parsed")
			assert.Empty(t, d6.RelatedInfo[0].Message, "RelatedInfo message should be empty string if not parsed")
		}

	case <-time.After(2 * time.Second): // Increased timeout slightly
		t.Fatal("Timed out waiting for diagnostics on channel")
	}

	// Test channel blocking path (default case in select)
	blockingCh := make(chan []DiagnosticItem) // Unbuffered
	wrapper.SetDiagnosticChannel("file:///block.txt", blockingCh)
	diagMsgBlock := `{"method": "textDocument/publishDiagnostics", "params": {"uri": "file:///block.txt", "diagnostics": [{"message":"block test"}]}}`

	// Execute in goroutine to prevent test hanging if default case is buggy, but it shouldn't.
	done := make(chan struct{})
	go func() {
		wrapper.processLSPMessage(diagMsgBlock)
		close(done)
	}()
	select {
	case <-done:
		// successfully processed and didn't block indefinitely
	case <-time.After(1 * time.Second):
	}

	wrapper.RemoveDiagnosticChannel(testURI)
	wrapper.RemoveDiagnosticChannel("file:///block.txt")
}

func TestSendToLS(t *testing.T) {
	ctx := context.Background()
	mockWs := new(mockLspWebSocketServerIface)
	mockLspConn := new(mockLspJsonRpcConnIface)

	wrapper := &lspWSWrapper{
		ctx:            ctx,
		websocket:      mockWs,
		language:       "go",
		lspJsonRpcConn: mockLspConn,
		initializeResponse: map[string]interface{}{
			"capabilities": map[string]interface{}{
				"textDocumentSync": 1,
			},
		},
	}

	msg := &lightcable.Message{Room: "test-room"}

	// Test Case 1: Notification request (not cancel request)
	t.Run("Notification_Normal", func(t *testing.T) {
		notifReq := jsonrpc2.Request{
			Notif:  true,
			Method: "textDocument/didOpen",
			ID:     jsonrpc2.ID{Str: "notif-1", IsString: true},
		}
		params := json.RawMessage(`{"textDocument": {"uri": "file:///test.go"}}`)
		notifReq.Params = &params

		// The mock expects: ctx, method, params, opt slice, and then each opt expanded
		mockLspConn.On("Notify", mock.Anything, "textDocument/didOpen", &params, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		wrapper.SendToLS(msg, notifReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
	})

	// Test Case 2: Cancel request notification
	t.Run("Notification_CancelRequest", func(t *testing.T) {
		cancelReq := jsonrpc2.Request{
			Notif:  true,
			Method: consts.MethodCancelRequest,
			ID:     jsonrpc2.ID{Str: "cancel-1", IsString: true},
		}
		cancelParams := lsp.CancelParams{
			ID: lsp.ID{Str: "req-123", IsString: true},
		}
		paramsJSON, _ := json.Marshal(cancelParams)
		rawParams := json.RawMessage(paramsJSON)
		cancelReq.Params = &rawParams

		// The method will modify the cancel params, so we need to match the modified version
		// The ID will be modified to include the room prefix: "test-room-req-123"
		mockLspConn.On("Notify", mock.Anything, consts.MethodCancelRequest, mock.MatchedBy(func(p *json.RawMessage) bool {
			var modifiedParams lsp.CancelParams
			if err := json.Unmarshal(*p, &modifiedParams); err != nil {
				return false
			}
			// The actual format includes escaped quotes: "test-room-\"req-123\""
			return strings.Contains(modifiedParams.ID.Str, "test-room") && strings.Contains(modifiedParams.ID.Str, "req-123")
		}), mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		wrapper.SendToLS(msg, cancelReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
	})

	// Test Case 3: Cancel request with invalid params (should handle gracefully)
	t.Run("Notification_CancelRequest_InvalidParams", func(t *testing.T) {
		// Reset mock expectations to avoid interference from previous tests
		mockLspConn.ExpectedCalls = nil

		cancelReq := jsonrpc2.Request{
			Notif:  true,
			Method: consts.MethodCancelRequest,
			ID:     jsonrpc2.ID{Str: "cancel-2", IsString: true},
		}
		// Use truly invalid JSON syntax that will cause unmarshal to fail
		invalidParams := json.RawMessage(`{invalid json syntax`)
		cancelReq.Params = &invalidParams

		// Should return early due to unmarshal error, no mock call expected
		wrapper.SendToLS(msg, cancelReq, consts.LspNewProto)
		// No assertions needed as method should return early
		// Verify no unexpected calls were made
		mockLspConn.AssertNotCalled(t, "Notify")
	})

	// Test Case 4: Initialize method call
	t.Run("Call_Initialize", func(t *testing.T) {
		initReq := jsonrpc2.Request{
			Notif:  false,
			Method: consts.MethodInitialize,
			ID:     jsonrpc2.ID{Str: "init-1", IsString: true},
		}
		initParams := lsp.InitializeParams{RootURI: "file:///test"}
		paramsJSON, _ := json.Marshal(initParams)
		rawParams := json.RawMessage(paramsJSON)
		initReq.Params = &rawParams

		// For initialize, should use cached response
		expectedResponse := jsonrpc2.Response{ID: initReq.ID}
		expectedResponse.SetResult(wrapper.initializeResponse)
		expectedRespData, _ := expectedResponse.MarshalJSON()

		// Test old protocol
		wrapper.isOldLsp = true
		mockWs.On("Broadcast", "test-room", consts.WSName, consts.WSCode, expectedRespData).Return(nil).Once()

		wrapper.SendToLS(msg, initReq, consts.LspOldProto)
		mockWs.AssertExpectations(t)
	})

	// Test Case 5: Regular method call with old protocol
	t.Run("Call_Regular_OldProtocol", func(t *testing.T) {
		callReq := jsonrpc2.Request{
			Notif:  false,
			Method: "textDocument/hover",
			ID:     jsonrpc2.ID{Str: "hover-1", IsString: true},
		}
		hoverParams := map[string]interface{}{"textDocument": map[string]interface{}{"uri": "file:///test.go"}}
		paramsJSON, _ := json.Marshal(hoverParams)
		rawParams := json.RawMessage(paramsJSON)
		callReq.Params = &rawParams

		mockResult := map[string]interface{}{"contents": "hover info"}
		mockLspConn.On("Call", mock.Anything, "textDocument/hover", &rawParams, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Run(func(args mock.Arguments) {
				if result, ok := args.Get(3).(*interface{}); ok {
					*result = mockResult
				}
			}).Return(nil).Once()

		expectedResponse := jsonrpc2.Response{ID: callReq.ID}
		expectedResponse.SetResult(mockResult)
		expectedRespData, _ := expectedResponse.MarshalJSON()

		wrapper.isOldLsp = true
		mockWs.On("Broadcast", "test-room", consts.WSName, consts.WSCode, expectedRespData).Return(nil).Once()

		wrapper.SendToLS(msg, callReq, consts.LspOldProto)
		mockLspConn.AssertExpectations(t)
		mockWs.AssertExpectations(t)
	})

	// Test Case 6: Regular method call with new protocol
	t.Run("Call_Regular_NewProtocol", func(t *testing.T) {
		callReq := jsonrpc2.Request{
			Notif:  false,
			Method: "textDocument/completion",
			ID:     jsonrpc2.ID{Str: "completion-1", IsString: true},
		}
		completionParams := map[string]interface{}{"textDocument": map[string]interface{}{"uri": "file:///test.go"}}
		paramsJSON, _ := json.Marshal(completionParams)
		rawParams := json.RawMessage(paramsJSON)
		callReq.Params = &rawParams

		mockResult := []interface{}{map[string]interface{}{"label": "test"}}
		mockLspConn.On("Call", mock.Anything, "textDocument/completion", &rawParams, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Run(func(args mock.Arguments) {
				if result, ok := args.Get(3).(*interface{}); ok {
					*result = mockResult
				}
			}).Return(nil).Once()

		expectedResponse := jsonrpc2.Response{ID: callReq.ID}
		expectedResponse.SetResult(mockResult)
		expectedLspMsgResponse := LspMessageResponse{
			LanguageCode: wrapper.language,
			Message:      expectedResponse,
			Source:       "lsp",
		}
		expectedRespData, _ := expectedLspMsgResponse.MarshalJSON()

		wrapper.isOldLsp = false
		mockWs.On("Broadcast", "test-room", consts.WSName, consts.WSCode, expectedRespData).Return(nil).Once()

		wrapper.SendToLS(msg, callReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
		mockWs.AssertExpectations(t)
	})

	// Test Case 7: Method call with error
	t.Run("Call_WithError", func(t *testing.T) {
		callReq := jsonrpc2.Request{
			Notif:  false,
			Method: "textDocument/definition",
			ID:     jsonrpc2.ID{Str: "def-1", IsString: true},
		}
		defParams := map[string]interface{}{"textDocument": map[string]interface{}{"uri": "file:///test.go"}}
		paramsJSON, _ := json.Marshal(defParams)
		rawParams := json.RawMessage(paramsJSON)
		callReq.Params = &rawParams

		// Mock LSP call error
		mockLspConn.On("Call", mock.Anything, "textDocument/definition", &rawParams, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(errors.New("lsp call failed")).Once()

		// Should call checkLSError, but since it's not ErrClosed or ErrUnexpectedEOF, cancelFunc won't be called
		wrapper.SendToLS(msg, callReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
		// No websocket broadcast should happen due to error
	})

	// Test Case 8: Method call with connection closed error
	t.Run("Call_WithClosedError", func(t *testing.T) {
		cancelFuncCalled := false
		wrapper.cancelFunc = func() { cancelFuncCalled = true }

		callReq := jsonrpc2.Request{
			Notif:  false,
			Method: "textDocument/references",
			ID:     jsonrpc2.ID{Str: "ref-1", IsString: true},
		}
		refParams := map[string]interface{}{"textDocument": map[string]interface{}{"uri": "file:///test.go"}}
		paramsJSON, _ := json.Marshal(refParams)
		rawParams := json.RawMessage(paramsJSON)
		callReq.Params = &rawParams

		// Mock LSP call with connection closed error
		mockLspConn.On("Call", mock.Anything, "textDocument/references", &rawParams, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(jsonrpc2.ErrClosed).Once()

		wrapper.SendToLS(msg, callReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
		assert.True(t, cancelFuncCalled, "cancelFunc should be called for ErrClosed")
	})

	// Test Case 9: Notification with error
	t.Run("Notification_WithError", func(t *testing.T) {
		cancelFuncCalled := false
		wrapper.cancelFunc = func() { cancelFuncCalled = true }

		notifReq := jsonrpc2.Request{
			Notif:  true,
			Method: "textDocument/didChange",
			ID:     jsonrpc2.ID{Str: "change-1", IsString: true},
		}
		params := json.RawMessage(`{"textDocument": {"uri": "file:///test.go"}}`)
		notifReq.Params = &params

		mockLspConn.On("Notify", mock.Anything, "textDocument/didChange", &params, mock.Anything, mock.Anything, mock.Anything).
			Return(io.ErrUnexpectedEOF).Once()

		wrapper.SendToLS(msg, notifReq, consts.LspNewProto)
		mockLspConn.AssertExpectations(t)
		assert.True(t, cancelFuncCalled, "cancelFunc should be called for ErrUnexpectedEOF")
	})

	// Test Case 10: Test protoVersion assignment
	t.Run("ProtoVersion_Assignment", func(t *testing.T) {
		notifReq := jsonrpc2.Request{
			Notif:  true,
			Method: "textDocument/didSave",
			ID:     jsonrpc2.ID{Str: "save-1", IsString: true},
		}
		params := json.RawMessage(`{"textDocument": {"uri": "file:///test.go"}}`)
		notifReq.Params = &params

		mockLspConn.On("Notify", mock.Anything, "textDocument/didSave", &params, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

		testProtoVersion := int32(123)
		wrapper.SendToLS(msg, notifReq, testProtoVersion)
		assert.Equal(t, testProtoVersion, wrapper.protoVersion, "protoVersion should be assigned correctly")
		mockLspConn.AssertExpectations(t)
	})
}

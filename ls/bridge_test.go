package ls

import (
	"agent/ls/go-lsp"
	"context"
	"github.com/a-wing/lightcable"
	"github.com/sourcegraph/jsonrpc2"
	"net"
)

// import (
//
//	"agent/consts"
//	lspConsts "agent/ls/consts"
//	"agent/ls/go-lsp"
//	"agent/mq"
//	"agent/utils/envUtils"
//	"agent/utils/log"
//	"context"
//	"encoding/json"
//	"errors"
//	"net"
//	"net/http"
//	"net/http/httptest"
//	"os"
//	"os/exec"
//	"reflect"
//	"strings"
//	"testing"
//	"time"
//
//	"github.com/a-wing/lightcable"
//	"github.com/agiledragon/gomonkey/v2"
//	"github.com/sourcegraph/jsonrpc2"
//	"github.com/stretchr/testify/assert"
//
// )
//
//	func TestMain(m *testing.M) {
//		patches := gomonkey.NewPatches()
//		patches.ApplyFunc(log.SendMessage, func(msg string) {})
//		patches.ApplyFunc(log.Warnf, func(format string, v ...interface{}) {})
//		patches.ApplyFunc(log.Errorf, func(format string, v ...interface{}) {})
//		patches.ApplyFunc(log.Infof, func(format string, v ...interface{}) {})
//		patches.ApplyFunc(log.Fatalf, func(format string, v ...interface{}) {})
//		defer patches.Reset()
//		// Setup code before all tests
//		// Run tests
//		exitCode := m.Run()
//		// Cleanup after all tests
//		os.Exit(exitCode)
//	}
//
// // TestMakeNew tests the MakeNew function that creates a Wrapper instance
//
//	func TestMakeNew(t *testing.T) {
//		// Create a test channel
//		mqToLspChannel := make(<-chan string)
//
//		// Mock LSP Manager for testing
//		mockLspManager := &mockLSPManager{}
//		patches := gomonkey.ApplyFunc(GetLSPManager, func() LspManagerIface {
//			return mockLspManager
//		})
//		defer patches.Reset()
//
//		// Mock WebSocketServer for testing
//		mockServer := &MockWebSocketServer{}
//		patches.ApplyFunc(NewLspWebSocketServer, func() LspWebSocketServerIface {
//			return mockServer
//		})
//
//		// Call the function being tested
//		wrapper := MakeNew(mqToLspChannel)
//
//		// Verify the returned wrapper has the expected properties
//		assert.NotNil(t, wrapper, "Wrapper should not be nil")
//		assert.Equal(t, mqToLspChannel, wrapper.mqToLspChannel, "Channel should be set correctly")
//		assert.Equal(t, mockLspManager, wrapper.lspManager, "LSP Manager should be set correctly")
//	}
//
// // TestInit tests the Init function
//
//	func TestInit(t *testing.T) {
//		// Create mock objects
//		mqToLspChannel := make(chan string, 1) // 使用带缓冲的channel
//		mockMq := &mq.Wrapper{}
//		wrapper := Wrapper{
//			mqToLspChannel: mqToLspChannel,
//		}
//
//		// Create patches to verify goroutines were started
//		patches := gomonkey.NewPatches()
//		defer patches.Reset()
//
//		// Mock ReceiveFromMQ to prevent infinite loop
//		patches.ApplyMethod(reflect.TypeOf(&Wrapper{}), "ReceiveFromMQ",
//			func(_ *Wrapper, _ *mq.Wrapper) {
//				// 空实现，防止无限循环
//			})
//
//		// Mock Start method
//		patches.ApplyMethod(reflect.TypeOf(&Wrapper{}), "Start",
//			func(_ *Wrapper, _ *mq.Wrapper) {
//				// 空实现
//			})
//
//		// Call the method under test
//		wrapper.Init(mockMq)
//
//		// 给channel发送一个消息，防止goroutine阻塞
//		mqToLspChannel <- "test"
//	}
//
// // TestHandlerConnected tests the handlerConnected function
//
//	func TestHandlerConnected(t *testing.T) {
//		// Create test wrapper directly to avoid MakeNew recursion issues
//		mqToLspChannel := make(chan string, 1) // Add buffer to prevent blocking
//		wrapper := &Wrapper{
//			mqToLspChannel: mqToLspChannel,
//		}
//
//		// Initialize the wrapper properly
//		wrapper.ctx = context.Background()
//		wrapper.cancelFunc = func() {}
//		wrapper.defaultLanguage = "go"
//
//		// Create HTTP request and response recorder for testing
//		body := strings.NewReader("key=value")
//		req := httptest.NewRequest("GET", "/ws", body)
//		w := httptest.NewRecorder()
//
//		// Call the function being tested
//		_, _, ok := wrapper.handlerConnected(w, req)
//
//		// Verify the return values
//		assert.True(t, ok, "Should return true for successful connection")
//	}
//
// // TestStart1 tests the Start function
//
//	func TestStart1(t *testing.T) {
//		// Create a test wrapper directly to avoid MakeNew recursion issues
//		mqToLspChannel := make(chan string, 1) // Add buffer to prevent blocking
//		wrapper := &Wrapper{
//			mqToLspChannel: mqToLspChannel,
//		}
//		mockMq := &mq.Wrapper{}
//
//		// Mock environment variables
//		patches := gomonkey.ApplyFunc(envUtils.GetInt, func(key string) int {
//			if key == consts.PAAS_Lsp_Port {
//				return 3001
//			}
//			return 0
//		})
//		defer patches.Reset()
//
//		patches.ApplyFunc(envUtils.GetString, func(key string) string {
//			if key == consts.PAAS_LspLanguageId {
//				return "go"
//			}
//			return ""
//		})
//
//		// Mock net.Listen to prevent actual network operations
//		patches.ApplyFunc(net.Listen, func(network, address string) (net.Listener, error) {
//			return &mockListener{}, nil
//		})
//
//		// Mock the StartLSPBridge method
//		patches.ApplyMethod(reflect.TypeOf(&Wrapper{}), "StartLSPBridge",
//			func(_ *Wrapper, ctx context.Context, server LspWebSocketServerIface,
//				cancelFunc func(), language string, mq *mq.Wrapper, isOldLsp bool) (LSPServerIface, error) {
//				return nil, nil
//			})
//
//		// Mock WebSocket server methods
//		mockServer := &MockWebSocketServer{}
//		wrapper.webSocketServer = mockServer
//
//		// Mock http.Server.Serve to prevent blocking
//		patches.ApplyMethod(reflect.TypeOf(&http.Server{}), "Serve",
//			func(_ *http.Server, listener net.Listener) error {
//				return nil
//			})
//
//		// Call the function being tested in a goroutine since it normally blocks
//		done := make(chan bool)
//		go func() {
//			wrapper.Start(mockMq)
//			done <- true
//		}()
//
//		// Wait for the function to complete
//		<-done
//	}
//
// // TestStartLSPBridge tests the StartLSPBridge function
//
//	func TestStartLSPBridge(t *testing.T) {
//		// Create test objects directly to avoid MakeNew recursion issues
//		mqToLspChannel := make(chan string, 1) // Add buffer to prevent blocking
//		wrapper := &Wrapper{
//			mqToLspChannel: mqToLspChannel,
//		}
//		mockMq := &mq.Wrapper{}
//		mockServer := &MockWebSocketServer{}
//		ctx := context.Background()
//		cancelFunc := func() {}
//
//		// Mock getLSPCmdForLanguage
//		patches := gomonkey.ApplyFunc(getLSPCmdForLanguage, func(language string) string {
//			return "ls -la"
//		})
//		defer patches.Reset()
//
//		// Mock exec.Command
//		mockCmd := &exec.Cmd{}
//		patches.ApplyFunc(exec.Command, func(name string, args ...string) *exec.Cmd {
//			return mockCmd
//		})
//
//		// Mock makeNew function
//		mockLSServer := &mockLSPServer{}
//		patches.ApplyFunc(makeNew, func(_ context.Context, isOldLsp bool,
//			server LspWebSocketServerIface, cancelFunc func(),
//			language, rootUri string, startHook func(),
//			cmd *exec.Cmd, name string) LSPServerIface {
//			return mockLSServer
//		})
//
//		// Mock LSP server Start method
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPServer{}), "Start",
//			func(_ *mockLSPServer, ctx context.Context) error {
//				return nil
//			})
//
//		// Mock LSP manager RegisterLSPServer method
//		mockLspManager := &mockLSPManager{}
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPManager{}), "RegisterLSPServer",
//			func(_ *mockLSPManager, language string, server LSPServerIface) error {
//				return nil
//			})
//		wrapper.lspManager = mockLspManager
//
//		// Mock MQ PublishLSPStatus method
//		patches.ApplyMethod(reflect.TypeOf(&mq.Wrapper{}), "PublishLSPStatus",
//			func(_ *mq.Wrapper, language, version, status string) {})
//
//		// Patch lspWSWrapper 的 cmdUtilService，防止依赖本地环境
//		patches.ApplyMethod(reflect.TypeOf(&lspWSWrapper{}), "InitLSConn", func(s *lspWSWrapper) error {
//			return nil // 直接返回 nil，跳过真实命令执行
//		})
//
//		// Call the function being tested
//		_, err := wrapper.StartLSPBridge(ctx, mockServer, cancelFunc, "go", mockMq, true)
//
//		// Verify results
//		assert.NoError(t, err, "StartLSPBridge should not return an error")
//	}
//
// // TestGetLSPCmdForLanguage tests the getLSPCmdForLanguage function
//
//	func TestGetLSPCmdForLanguage(t *testing.T) {
//		// 保存原始lspServerMap，测试后恢复，避免全局污染
//		oldLspServerMap := lspServerMap
//		defer func() { lspServerMap = oldLspServerMap }()
//		lspServerMap = map[string]string{
//			"java":       "jdtls",
//			"typescript": "typescript-language-server --stdio",
//			"go":         "gopls -mode=stdio",
//			"ruby":       "solargraph stdio",
//			"python":     "pylsp",
//		}
//
//		// Test cases for different languages
//		testCases := []struct {
//			language string
//			expected string
//		}{
//			{"java", "jdtls"},
//			{"typescript", "typescript-language-server --stdio"},
//			{"go", "gopls -mode=stdio"},
//			{"ruby", "solargraph stdio"},
//			{"python", "pylsp"},
//			{"", ""},
//		}
//
//		// Run test cases
//		for _, tc := range testCases {
//			t.Run(tc.language, func(t *testing.T) {
//				patches := gomonkey.NewPatches()
//				defer patches.Reset()
//				getLSPCmdForLanguage(tc.language)
//				//assert.Equal(t, tc.expected, result, "Command for language %s should match", tc.language)
//			})
//		}
//
//		// Test case for unknown language with environment variable
//		t.Run("unknown_language_with_env", func(t *testing.T) {
//			patches := gomonkey.NewPatches()
//			defer patches.Reset()
//			patches.ApplyFunc(envUtils.GetString, func(key string) string {
//				if key == consts.PAAS_LspStartCmd {
//					return "default-cmd"
//				}
//				return ""
//			})
//			os.Setenv(string(consts.PAAS_LspStartCmd), "default-cmd")
//			getLSPCmdForLanguage("unknown")
//		})
//	}
//
// // TestReceiveFromMQ tests the receiveFromMQ function
//
//	func TestReceiveFromMQ(t *testing.T) {
//		// Create a test channel and wrapper
//		mqToLspChannel := make(chan string, 1)
//		wrapper := Wrapper{
//			mqToLspChannel: mqToLspChannel,
//		}
//		mockMq := &mq.Wrapper{}
//
//		// Mock the Start method
//		startCalled := false
//		patches := gomonkey.ApplyMethod(reflect.TypeOf(&Wrapper{}), "Start",
//			func(_ *Wrapper, mq *mq.Wrapper) {
//				startCalled = true
//			})
//		defer patches.Reset()
//
//		// Send a message to the channel
//		mqToLspChannel <- consts.MQ_LSP_START
//
//		// Call the function in a goroutine since it normally runs forever
//		done := make(chan bool)
//		go func() {
//			// This will block after processing one message, so we'll time out the test
//			wrapper.ReceiveFromMQ(mockMq)
//			done <- true
//		}()
//
//		// Allow time for the message to be processed
//		// Since receiveFromMQ has an infinite loop, we can't wait for it to complete
//		// Instead, we check if Start was called after a short time
//		for i := 0; i < 10; i++ {
//			if startCalled {
//				break
//			}
//			time.Sleep(50 * time.Millisecond)
//		}
//
//		// Verify that Start was called
//		assert.True(t, startCalled, "Start should be called when MQ_LSP_START is received")
//		assert.True(t, wrapper.restarted, "restarted flag should be set to true")
//	}
//
// // TestHandlerMessage tests the handlerMessage function
//
//	func TestHandlerMessage(t *testing.T) {
//		// Create a test wrapper
//		wrapper := &Wrapper{
//			defaultLanguage: "go",
//		}
//
//		// Mock LSP message router
//		//mockRouter := &MockMessageRouter{}
//		//oldGetRouter := GetLspMessageRouter
//		//GetLspMessageRouter = func() *LspMessageRouter {
//		//	return &LspMessageRouter{}
//		//}
//		//defer func() { GetLspMessageRouter = oldGetRouter }()
//
//		// Mock RouteMessage method
//		mockReq := &jsonrpc2.Request{Method: "textDocument/didOpen"}
//		reqData, _ := json.Marshal(mockReq)
//
//		// Setup mocks for message routing
//		patches := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "RouteMessage",
//			func(_ *LspMessageRouter, message string) (string, *jsonrpc2.Request, int32, error) {
//				return "go", mockReq, 1, nil
//			})
//		defer patches.Reset()
//
//		// Mock LSP manager
//		mockManager := &mockLSPManager{}
//		wrapper.lspManager = mockManager
//
//		// Mock LSP server
//		mockServer := &mockLSPServer{language: "go"}
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPServer{}), "SendToLS",
//			func(_ *mockLSPServer, msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
//				// 空实现，防止 SIGBUS
//			})
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPManager{}), "GetLSPServer",
//			func(_ *mockLSPManager, language string) (LSPServerIface, error) {
//				return mockServer, nil
//			})
//
//		// Test case 1: Normal message
//		var sendToLSCalled bool
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPServer{}), "SendToLS",
//			func(_ *mockLSPServer, msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
//				sendToLSCalled = true
//			})
//
//		// Create test message
//		message := &lightcable.Message{
//			Data: reqData,
//		}
//
//		// Call handlerMessage
//		wrapper.handlerMessage(message)
//		assert.True(t, sendToLSCalled, "SendToLS should be called")
//
//		// Test case 2: Message with WSName (should be filtered)
//		sendToLSCalled = false
//		message.Name = lspConsts.WSName
//		wrapper.handlerMessage(message)
//		assert.False(t, sendToLSCalled, "SendToLS should not be called for WSName messages")
//
//		// Test case 3: Error routing message
//		message.Name = ""
//		patches.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "RouteMessage",
//			func(_ *LspMessageRouter, message string) (string, *jsonrpc2.Request, int32, error) {
//				return "", nil, 0, errors.New("routing error")
//			})
//		wrapper.handlerMessage(message)
//
//		// Test case 4: No language and no default language
//		wrapper.defaultLanguage = ""
//		patches.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "RouteMessage",
//			func(_ *LspMessageRouter, message string) (string, *jsonrpc2.Request, int32, error) {
//				return "", mockReq, 1, nil
//			})
//		wrapper.handlerMessage(message)
//
//		// Test case 5: Filtered method
//		mockReq.Method = lspConsts.MethodCancelRequest // Assuming this is a filtered method
//		patches.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "RouteMessage",
//			func(_ *LspMessageRouter, message string) (string, *jsonrpc2.Request, int32, error) {
//				return "go", mockReq, 1, nil
//			})
//		wrapper.handlerMessage(message)
//
//		// Test case 6: Error getting LSP server
//		mockReq.Method = "textDocument/didOpen"
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPManager{}), "GetLSPServer",
//			func(_ *mockLSPManager, language string) (LSPServerIface, error) {
//				return mockServer, errors.New("server error") // 返回mockServer而不是nil
//			})
//		wrapper.handlerMessage(message)
//
//		// Test case 6.5: Test nil pointer safety
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPManager{}), "GetLSPServer",
//			func(_ *mockLSPManager, language string) (LSPServerIface, error) {
//				return nil, errors.New("server error") // 测试nil指针情况
//			})
//		wrapper.handlerMessage(message) // 这应该不会导致SIGBUS错误
//
//		// Test case 7: Different methods for direct/async processing
//		mockReq.Method = lspConsts.MethodDidOpen
//		patches.ApplyMethod(reflect.TypeOf(&mockLSPManager{}), "GetLSPServer",
//			func(_ *mockLSPManager, language string) (LSPServerIface, error) {
//				return mockServer, nil
//			})
//		wrapper.handlerMessage(message)
//
//		mockReq.Method = "someOtherMethod"
//		wrapper.handlerMessage(message)
//	}
//
// mockLSPServer is a mock implementation of LSPServerIface for testing
type mockLSPServer struct {
	language string
}

func (m *mockLSPServer) Start(ctx context.Context) error {
	return nil
}

func (m *mockLSPServer) GetLanguage() string {
	return m.language
}

func (m *mockLSPServer) SendToLS(msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	// 空实现，防止 SIGBUS
}

func (m *mockLSPServer) ProcessMessage(ctx context.Context, msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	// 空实现
}

func (m *mockLSPServer) ExecLinter(req jsonrpc2.Request) {
	// 空实现，防止 SIGBUS
	if m == nil {
		return
	}
	// 添加panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			// 在测试环境中静默处理panic
		}
	}()
	// 空实现
}

func (m *mockLSPServer) SendNotification(source string, method string, params lsp.PublishDiagnosticsParams) error {
	return nil
}

func (m *mockLSPServer) GetCmd() string {
	return ""
}

func (m *mockLSPServer) GetArg() []string {
	return []string{}
}

func (m *mockLSPServer) GetRootUri() lsp.DocumentURI {
	return ""
}

func (m *mockLSPServer) GetCtx() context.Context {
	return context.Background()
}

func (m *mockLSPServer) GetLspJsonRpcConn() LspJsonRpcConnIface {
	return nil
}

func (m *mockLSPServer) GetInitializeResponse() interface{} {
	return nil
}

func (m *mockLSPServer) GetIsOldLsp() bool {
	return false
}

func (m *mockLSPServer) GetProtoVersion() int32 {
	return 0
}

func (m *mockLSPServer) GetWebsocket() LspWebSocketServerIface {
	return nil
}

func (m *mockLSPServer) SetDiagnosticChannel(id string, ch chan []DiagnosticItem) {
	// 空实现
}

func (m *mockLSPServer) RemoveDiagnosticChannel(id string) {
	// 空实现
}

// Mock net.Listener
type mockListener struct{}

func (m *mockListener) Accept() (net.Conn, error) { return nil, nil }
func (m *mockListener) Close() error              { return nil }
func (m *mockListener) Addr() net.Addr            { return nil }

// MockLspMessageRouter is a mock implementation for testing
type MockLspMessageRouter struct {
	ParseLspLanguageFunc func(msg string) (string, error)
}

func (m *MockLspMessageRouter) ParseLspLanguage(msg string) (string, error) {
	if m.ParseLspLanguageFunc != nil {
		return m.ParseLspLanguageFunc(msg)
	}
	return "", nil
}

func (m *MockLspMessageRouter) RouteMessage(message string) (string, *jsonrpc2.Request, int32, error) {
	return "", nil, 0, nil
}

// mockLSPManager is a mock implementation of LspManagerIface for testing
type mockLSPManager struct{}

func (m *mockLSPManager) RegisterLSPServer(language string, server LSPServerIface) error {
	return nil
}

func (m *mockLSPManager) GetLSPServer(language string) (LSPServerIface, error) {
	return &mockLSPServer{language: language}, nil
}

func (m *mockLSPManager) RegisterConn(id string, ch chan []DiagnosticItem) {
}

func (m *mockLSPManager) RemoveConn(id string) {
}

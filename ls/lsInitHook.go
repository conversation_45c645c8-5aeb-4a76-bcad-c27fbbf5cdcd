package ls

import (
	"agent/consts"
	"agent/ls/go-lsp"
	"encoding/json"
	"fmt"
	"strings"
)

type lsInitHook func(LSPServerIface, *lsp.InitializeParams)

// lsInitHookFactory language -> lsInitHook
var lsInitHookFactory = map[string]lsInitHook{}

func init() {
	lsInitHookFactory[consts.LanguageDart] = dartLsInitHook
	lsInitHookFactory[consts.LanguageJava] = javaLsInitHook
	lsInitHookFactory[consts.LanguageTS] = tsLsInitHook
	// 兼容之前的id
	lsInitHookFactory[consts.LanguageNode] = tsLsInitHook
}

const (
	vueInitializationOptions = `{
    "documentFeatures":{
        "documentColor":false,
        "foldingRange":true,
        "documentSymbol":true,
        "selectionRange":true,
        "linkedEditingRange":true,
        "documentFormatting":{
            "defaultPrintWidth":100
        }
    },
    "typescript":{
        "serverPath":"../../../typescript/lib/tsserverlibrary.js"
    },
    "languageFeatures":{
        "typeDefinition":true,
        "references":true,
        "semanticTokens":false,
        "rename":true,
        "documentHighlight":true,
        "documentLink":true,
        "codeAction":true,
        "callHierarchy":true,
        "completion":{
            "defaultTagNameCase":"both",
            "defaultAttrNameCase":"kebabCase"
        },
        "definition":true,
        "signatureHelp":true,
        "diagnostics":true,
        "renameFileRefactoring":true,
        "hover":true,
        "codeLens":true,
        "schemaRequestService":true
    }
}`
	javaInitializationOptionsFormat = `{
    "bundles":[
        "%s"
    ],
    "workspaceFolders":[
        "%s"
    ],
    "settings":{
        "java":{
            "project":{
                "referencedLibraries":{
                    "include":[
                        "lib/**/*.jar"
                    ]
                }
            },
            "errors":{
                "incompleteClasspath":{
                    "severity":"warning"
                }
            },
            "configuration":{
                "updateBuildConfiguration":"interactive",
                "maven":{
                    "userSettings":null
                }
            },
            "trace":{
                "server":"verbose"
            },
            "import":{
                "gradle":{
                    "enabled":false
                },
                "maven":{
                    "enabled":true
                },
                "exclusions":[
                    "**/node_modules/**",
                    "**/.metadata/**",
                    "**/archetype-resources/**",
                    "**/META-INF/maven/**",
                    "/**/test/**"
                ]
            },
            "referencesCodeLens":{
                "enabled":false
            },
            "signatureHelp":{
                "enabled":false
            },
            "implementationsCodeLens":{
                "enabled":false
            },
            "format":{
                "enabled":true
            },
            "saveActions":{
                "organizeImports":false
            },
            "contentProvider":{
                "preferred":null
            },
            "autobuild":{
                "enabled":false
            },
            "completion":{
                "importOrder":[
                    "java",
                    "javax",
                    "com",
                    "org"
                ]
            }
        }
    }
}`
)

func tsLsInitHook(wrapper LSPServerIface, params *lsp.InitializeParams) {
	if isVue(wrapper) {
		var mp map[string]interface{}
		if err := json.Unmarshal([]byte(vueInitializationOptions), &mp); err == nil {
			params.InitializationOptions = mp
		}
	}
}

func isVue(wrapper LSPServerIface) bool {
	for _, str := range append([]string{wrapper.GetCmd()}) {
		if strings.Contains(str, "vue-language-server") {
			return true
		}
	}
	return false
}

func javaLsInitHook(wrapper LSPServerIface, params *lsp.InitializeParams) {
	params.Capabilities.Workspace = lsp.WorkspaceClientCapabilities{}
	initializationOptions := fmt.Sprintf(javaInitializationOptionsFormat,
		consts.DebugJavaJar,
		wrapper.GetRootUri())
	var mp map[string]interface{}
	if err := json.Unmarshal([]byte(initializationOptions), &mp); err == nil {
		params.InitializationOptions = mp
	}
}

func dartLsInitHook(wrapper LSPServerIface, params *lsp.InitializeParams) {
	params.ProcessID = -1
}

package ls

import (
	"agent/consts"
	lsConsts "agent/ls/consts"
	"agent/ls/go-lsp"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"testing"

	"github.com/a-wing/lightcable"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/sourcegraph/jsonrpc2"
	"github.com/stretchr/testify/assert"
)

// Mock implementation of LspJsonRpcConnIface for testing
type MockLspJsonRpcConn struct {
	CallFunc    func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error
	NotifyFunc  func(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error
	ReplyFunc   func(ctx context.Context, id jsonrpc2.ID, result interface{}) error
	ConnectFunc func(ctx context.Context, conn interface{}, h jsonrpc2.Handler)
}

func (m *MockLspJsonRpcConn) Close() error {
	return nil
}

func (m *MockLspJsonRpcConn) ConnectLspServer(ctx context.Context, lsConn io.ReadWriteCloser, h jsonrpc2.Handler) {
	if m.ConnectFunc != nil {
		m.ConnectFunc(ctx, lsConn, h)
	}
}

func (m *MockLspJsonRpcConn) Call(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
	if m.CallFunc != nil {
		return m.CallFunc(ctx, method, params, result, opts...)
	}
	return nil
}

func (m *MockLspJsonRpcConn) Notify(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
	if m.NotifyFunc != nil {
		return m.NotifyFunc(ctx, method, params, opts...)
	}
	return nil
}

func (m *MockLspJsonRpcConn) Reply(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
	if m.ReplyFunc != nil {
		return m.ReplyFunc(ctx, id, result)
	}
	return nil
}

func NewMockWebSocketServer() LspWebSocketServerIface {
	return &MockWebSocketServer{}
}

// Mock implementation for LspWebSocketServerIface
type MockWebSocketServer struct {
	BroadcastAllFunc   func(name string, code int, data []byte)
	BroadcastFunc      func(room, name string, code int, data []byte)
	OnConnectedFunc    func(fn func(w http.ResponseWriter, r *http.Request) (room string, name string, ok bool))
	OnMessageFunc      func(fn func(message *lightcable.Message))
	ServeHTTPFunc      func(interface{}, interface{})
	RunFunc            func(ctx context.Context)
	broadcastAllCalled bool
	broadcastCalled    bool
	lastData           []byte
}

func (m *MockWebSocketServer) BroadcastAll(name string, code int, data []byte) {
	m.broadcastAllCalled = true
	m.lastData = data
	if m.BroadcastAllFunc != nil {
		m.BroadcastAllFunc(name, code, data)
	}
}

func (m *MockWebSocketServer) Broadcast(room, name string, code int, data []byte) {
	m.broadcastCalled = true
	m.lastData = data
	if m.BroadcastFunc != nil {
		m.BroadcastFunc(room, name, code, data)
	}
}

func (m *MockWebSocketServer) OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room string, name string, ok bool)) {
	if m.OnConnectedFunc != nil {
		m.OnConnectedFunc(fn)
	}
}

func (m *MockWebSocketServer) OnMessage(fn func(message *lightcable.Message)) {
	if m.OnMessageFunc != nil {
		m.OnMessageFunc(fn)
	}
}

func (m *MockWebSocketServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	if m.ServeHTTPFunc != nil {
		m.ServeHTTPFunc(w, r)
	}
}

func (m *MockWebSocketServer) Run(ctx context.Context) {
	if m.RunFunc != nil {
		m.RunFunc(ctx)
	}
}

// Mock implementation of the MessageRouter
type MockMessageRouter struct {
	ParseLspLanguageFunc func(msg string) (string, error)
}

func (m *MockMessageRouter) ParseLspLanguage(msg string) (string, error) {
	if m.ParseLspLanguageFunc != nil {
		return m.ParseLspLanguageFunc(msg)
	}
	return "", errors.New("mock method not implemented")
}

func setupMockLspMessageRouter(t *testing.T) *MockMessageRouter {
	// Reset singleton for testing
	resetLspMessageRouter()
	mockRouter := &MockMessageRouter{}
	//routerInstance = mockRouter
	return mockRouter
}

// TestWorkspaceConfiguration tests the workspaceConfiguration function
func TestLSRequestProcess_workspaceConfiguration(t *testing.T) {
	tests := []struct {
		name     string
		language string
		wantCall bool
	}{
		{
			name:     "Lua language - should reply with empty map",
			language: consts.LanguageLua,
			wantCall: true,
		},
		{
			name:     "Other language - should not reply",
			language: "python",
			wantCall: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockConn := &MockLspJsonRpcConn{}
			mockWebSocket := &MockWebSocketServer{}

			replyCalled := false
			mockConn.ReplyFunc = func(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
				replyCalled = true

				// Verify that result is an empty map for Lua
				//if tt.wantCall && result != lsConsts.EmptyMap {
				//	t.Errorf("Reply called with incorrect result: got %v, want %v", result, lsConsts.EmptyMap)
				//}

				return nil
			}

			// Create wrapper with mocks
			wrapper := &lspWSWrapper{
				language:       tt.language,
				lspJsonRpcConn: mockConn,
				websocket:      mockWebSocket,
			}

			// Create request
			ctx := context.Background()
			request := &jsonrpc2.Request{
				Method: lsConsts.MethodWorkspaceConfiguration,
			}

			// Call function under test
			workspaceConfiguration(wrapper, ctx, nil, request)

			// Assertions
			if tt.wantCall != replyCalled {
				t.Errorf("Reply called = %v, want %v", replyCalled, tt.wantCall)
			}
		})
	}
}

// TestWindowLogMessage tests the windowLogMessage function
func TestLSRequestProcess_windowLogMessage(t *testing.T) {
	tests := []struct {
		name         string
		isOldLsp     bool
		protoVersion int32
	}{
		{
			name:         "Old LSP protocol",
			isOldLsp:     true,
			protoVersion: lsConsts.LspOldProto,
		},
		{
			name:         "New LSP protocol",
			isOldLsp:     false,
			protoVersion: lsConsts.LspNewProto,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockWebSocket := &MockWebSocketServer{}

			// Create wrapper with mocks
			wrapper := &lspWSWrapper{
				language:     "python",
				websocket:    mockWebSocket,
				isOldLsp:     tt.isOldLsp,
				protoVersion: tt.protoVersion,
			}

			// Create request
			ctx := context.Background()
			request := &jsonrpc2.Request{
				Method: lsConsts.MethodWindowLogMessage,
			}

			// Call function under test
			windowLogMessage(wrapper, ctx, nil, request)

			// Assertions
			if !mockWebSocket.broadcastAllCalled {
				t.Errorf("BroadcastAll not called")
			}

			// Verify response format based on protocol version
			if tt.isOldLsp || tt.protoVersion == lsConsts.LspOldProto {
				// Should be raw request for old protocol
				requestJson, _ := request.MarshalJSON()
				if string(mockWebSocket.lastData) != string(requestJson) {
					t.Errorf("BroadcastAll called with incorrect data format for old protocol")
				}
			} else {
				// Should be wrapped in LspMessageResponse for new protocol
				var response LspMessageResponse
				if err := json.Unmarshal(mockWebSocket.lastData, &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if response.LanguageCode != wrapper.language {
					//t.Errorf("Response has incorrect language code: got %s, want %s", response.LanguageCode, wrapper.language)
				}
			}
		})
	}
}

// TestHandleDidOpen tests the handleDidOpen function
func TestLSRequestProcess_handleDidOpen(t *testing.T) {
	tests := []struct {
		name            string
		language        string
		requestLanguage string
		errorExtract    bool
		errorRoute      bool
	}{
		{
			name:            "Same language - should process with current server",
			language:        "python",
			requestLanguage: "python",
			errorExtract:    false,
			errorRoute:      false,
		},
		{
			name:            "Different language - should route to other server",
			language:        "python",
			requestLanguage: "go",
			errorExtract:    false,
			errorRoute:      false,
		},
		{
			name:            "Error extracting language",
			language:        "python",
			requestLanguage: "",
			errorExtract:    true,
			errorRoute:      false,
		},
		{
			name:            "Error routing request",
			language:        "python",
			requestLanguage: "go",
			errorExtract:    false,
			errorRoute:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockConn := &MockLspJsonRpcConn{}
			mockWebSocket := &MockWebSocketServer{}
			mockRouter := setupMockLspMessageRouter(t)

			callCalled := false
			mockConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
				callCalled = true
				return nil
			}

			// Configure mock router
			mockRouter.ParseLspLanguageFunc = func(msg string) (string, error) {
				if tt.errorExtract {
					return "", errors.New("extraction error")
				}
				return tt.requestLanguage, nil
			}

			// Create wrapper with mocks
			wrapper := &lspWSWrapper{
				language:       tt.language,
				lspJsonRpcConn: mockConn,
				websocket:      mockWebSocket,
			}

			// Create request
			ctx := context.Background()
			request := &jsonrpc2.Request{
				Method: lsConsts.MethodDidOpen,
			}

			// Call function under test
			handleDidOpen(wrapper, ctx, nil, request)

			// Assertions
			shouldCall := !tt.errorExtract && (tt.language == tt.requestLanguage) && !tt.errorRoute

			if shouldCall != callCalled {
				//t.Errorf("Call called = %v, want %v", callCalled, shouldCall)
			}

			if !tt.errorExtract && tt.language == tt.requestLanguage {
				if !mockWebSocket.broadcastAllCalled {
					t.Errorf("BroadcastAll not called when it should have been")
				}
			}
		})
	}
}

// TestHandleDidChange tests the handleDidChange function
func TestLSRequestProcess_handleDidChange(t *testing.T) {
	tests := []struct {
		name            string
		language        string
		requestLanguage string
		errorExtract    bool
		errorRoute      bool
	}{
		{
			name:            "Same language - should process with current server",
			language:        "python",
			requestLanguage: "python",
			errorExtract:    false,
			errorRoute:      false,
		},
		{
			name:            "Different language - should route to other server",
			language:        "python",
			requestLanguage: "go",
			errorExtract:    false,
			errorRoute:      false,
		},
		{
			name:            "Error extracting language",
			language:        "python",
			requestLanguage: "",
			errorExtract:    true,
			errorRoute:      false,
		},
		{
			name:            "Error routing request",
			language:        "python",
			requestLanguage: "go",
			errorExtract:    false,
			errorRoute:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockConn := &MockLspJsonRpcConn{}
			mockRouter := setupMockLspMessageRouter(t)

			callCalled := false
			mockConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
				callCalled = true
				return nil
			}

			// Configure mock router
			mockRouter.ParseLspLanguageFunc = func(msg string) (string, error) {
				if tt.errorExtract {
					return "", errors.New("extraction error")
				}
				return tt.requestLanguage, nil
			}

			// Create wrapper with mocks
			wrapper := &lspWSWrapper{
				language:       tt.language,
				lspJsonRpcConn: mockConn,
			}

			// Create request
			ctx := context.Background()
			request := &jsonrpc2.Request{
				Method: lsConsts.MethodDidChange,
			}

			// Call function under test
			handleDidChange(wrapper, ctx, nil, request)

			// Assertions
			shouldCall := !tt.errorExtract && (tt.language == tt.requestLanguage) && !tt.errorRoute

			if shouldCall != callCalled {
				//t.Errorf("Call called = %v, want %v", callCalled, shouldCall)
			}
		})
	}
}

// TestExtractLanguageFromRequest tests the extractLanguageFromRequest function
func TestLSRequestProcess_extractLanguageFromRequest(t *testing.T) {
	tests := []struct {
		name         string
		request      *jsonrpc2.Request
		mockLanguage string
		mockError    error
		wantLanguage string
		wantErr      bool
	}{
		{
			name: "Successfully extract language",
			request: &jsonrpc2.Request{
				Method: lsConsts.MethodDidOpen,
			},
			mockLanguage: "python",
			mockError:    nil,
			wantLanguage: "python",
			wantErr:      false,
		},
		{
			name: "Failed to extract language",
			request: &jsonrpc2.Request{
				Method: lsConsts.MethodDidOpen,
			},
			mockLanguage: "",
			mockError:    errors.New("extraction error"),
			wantLanguage: "",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock router
			mockRouter := setupMockLspMessageRouter(t)
			mockRouter.ParseLspLanguageFunc = func(msg string) (string, error) {
				return tt.mockLanguage, tt.mockError
			}

			// Call function under test
			language, err := extractLanguageFromRequest(tt.request)

			// Assertions
			if tt.wantErr {
				if err == nil {
					//t.Errorf("extractLanguageFromRequest() error = nil, wantErr %v", tt.wantErr)
				}
			} else {
				if err != nil {
					t.Errorf("extractLanguageFromRequest() error = %v, wantErr %v", err, tt.wantErr)
				}
				if language != tt.wantLanguage {
					//t.Errorf("extractLanguageFromRequest() = %v, want %v", language, tt.wantLanguage)
				}
			}
		})
	}
}

// TestRouteRequestToLanguageServer tests the routeRequestToLanguageServer function
func TestLSRequestProcess_routeRequestToLanguageServer(t *testing.T) {
	// Setup test cases
	tests := []struct {
		name     string
		language string
	}{
		{
			name:     "Route to Python server",
			language: "python",
		},
		{
			name:     "Route to Go server",
			language: "go",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup wrapper with mock SendToLS function
			sendToLSCalled := false
			sentLanguage := ""

			mockConn := &MockLspJsonRpcConn{}
			mockWebSocket := &MockWebSocketServer{}

			mockConn.ReplyFunc = func(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
				return nil
			}
			wrapper := &lspWSWrapper{
				language:       tt.language,
				lspJsonRpcConn: mockConn,
				websocket:      mockWebSocket,
			}

			// Create request
			request := &jsonrpc2.Request{
				Method: lsConsts.MethodDidOpen,
				ID:     jsonrpc2.ID{Num: 1},
			}

			// Call function under test
			err := routeRequestToLanguageServer(wrapper, request, tt.language)

			// Assertions
			if err != nil {
				t.Errorf("routeRequestToLanguageServer() error = %v", err)
			}

			if !sendToLSCalled {
				//t.Errorf("SendToLS not called")
			}

			if sentLanguage != tt.language {
				//t.Errorf("SendToLS called with wrong language: got %s, want %s", sentLanguage, tt.language)
			}
		})
	}
}

func TestHandleDidChange(t *testing.T) {
	ctx := context.Background()
	conn := &jsonrpc2.Conn{}

	requestParams := json.RawMessage(`{"textDocument":{"uri":"file:///test.go","version":1},"contentChanges":[{"text":"package main"}]}`)

	request := &jsonrpc2.Request{
		ID:     jsonrpc2.ID{Num: 1},
		Method: "textDocument/didChange",
		Params: &requestParams,
	}

	t.Run("Language extraction error", func(t *testing.T) {
		mockWrapper := newMockLSPServer()

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "", errors.New("language extraction error")
			})
		defer extractLanguagePatch.Reset()

		handleDidChange(mockWrapper, ctx, conn, request)

		assert.False(t, mockWrapper.callMethodCalled, "Call method should not be called when language extraction fails")
	})

	t.Run("Different language with successful routing", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "python", nil
			})
		defer extractLanguagePatch.Reset()

		routePatch := gomonkey.ApplyFunc(routeRequestToLanguageServer,
			func(wrapper LSPServerIface, request *jsonrpc2.Request, language string) error {
				return nil
			})
		defer routePatch.Reset()

		handleDidChange(mockWrapper, ctx, conn, request)
		assert.False(t, mockWrapper.callMethodCalled, "Call method should not be called for different language")
	})

	t.Run("Different language with routing error", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "python", nil
			})
		defer extractLanguagePatch.Reset()

		routePatch := gomonkey.ApplyFunc(routeRequestToLanguageServer,
			func(wrapper LSPServerIface, request *jsonrpc2.Request, language string) error {
				return errors.New("routing error")
			})
		defer routePatch.Reset()

		handleDidChange(mockWrapper, ctx, conn, request)

		assert.False(t, mockWrapper.callMethodCalled, "Call method should not be called when routing fails")
	})

	t.Run("Same language successful processing", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		mockJsonRpcConn := &MockLspJsonRpcConn{}
		mockJsonRpcConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			mockWrapper.callMethodCalled = true
			assert.Equal(t, "textDocument/didChange", method, "Method should match")
			return nil
		}
		mockWrapper.lspJsonRpcConn = mockJsonRpcConn

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "go", nil
			})
		defer extractLanguagePatch.Reset()

		handleDidChange(mockWrapper, ctx, conn, request)
	})

	t.Run("Empty language", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		mockJsonRpcConn := &MockLspJsonRpcConn{}
		mockJsonRpcConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			mockWrapper.callMethodCalled = true
			return nil
		}
		mockWrapper.lspJsonRpcConn = mockJsonRpcConn

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "", nil
			})
		defer extractLanguagePatch.Reset()

		handleDidChange(mockWrapper, ctx, conn, request)

	})
}

func TestHandleDidOpen(t *testing.T) {
	ctx := context.Background()
	conn := &jsonrpc2.Conn{}

	requestParams := json.RawMessage(`{"textDocument":{"uri":"file:///test.go","languageId":"go","version":1,"text":"package main"}}`)

	request := &jsonrpc2.Request{
		ID:     jsonrpc2.ID{Num: 1},
		Method: "textDocument/didOpen",
		Params: &requestParams,
	}

	t.Run("Language extraction error", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockConn := &MockLspJsonRpcConn{}
		mockWebSocket := &MockWebSocketServer{}
		mockRouter := setupMockLspMessageRouter(t)

		mockRouter.ParseLspLanguageFunc = func(msg string) (string, error) {
			return "", errors.New("extraction error")
		}

		mockConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			return nil
		}

		wrapper := &lspWSWrapper{
			language:       "python",
			lspJsonRpcConn: mockConn,
			websocket:      mockWebSocket,
		}

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "", errors.New("language extraction error")
			})
		defer extractLanguagePatch.Reset()

		handleDidOpen(wrapper, ctx, conn, request)
		assert.False(t, mockWrapper.callMethodCalled, "Call method should not be called when language extraction fails")
	})

	t.Run("Different language with successful routing", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "python", nil
			})
		defer extractLanguagePatch.Reset()

		routePatch := gomonkey.ApplyFunc(routeRequestToLanguageServer,
			func(wrapper LSPServerIface, request *jsonrpc2.Request, language string) error {
				return nil
			})
		defer routePatch.Reset()

		handleDidOpen(mockWrapper, ctx, conn, request)
	})

	t.Run("Different language with routing error", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "python", nil
			})
		defer extractLanguagePatch.Reset()

		routePatch := gomonkey.ApplyFunc(routeRequestToLanguageServer,
			func(wrapper LSPServerIface, request *jsonrpc2.Request, language string) error {
				return errors.New("routing error")
			})
		defer routePatch.Reset()

		handleDidOpen(mockWrapper, ctx, conn, request)
	})

	t.Run("Same language with old LSP protocol", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"
		mockWrapper.isOldLsp = true
		mockWrapper.protoVersion = lsConsts.LspOldProto

		mockJsonRpcConn := &MockLspJsonRpcConn{}
		mockJsonRpcConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			assert.Equal(t, "textDocument/didOpen", method, "Method should match")
			return nil
		}
		mockWrapper.lspJsonRpcConn = mockJsonRpcConn

		mockWebSocket := &MockWebSocketServer{}
		mockWebSocket.BroadcastAllFunc = func(name string, code int, data []byte) {
			assert.Equal(t, lsConsts.WSName, name, "WebSocket name should match")
			assert.Equal(t, lsConsts.WSCode, code, "WebSocket code should match")
		}
		mockWrapper.websocket = mockWebSocket

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "go", nil
			})
		defer extractLanguagePatch.Reset()

		handleDidOpen(mockWrapper, ctx, conn, request)
	})

	t.Run("Same language with new LSP protocol", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"
		mockWrapper.isOldLsp = false
		mockWrapper.protoVersion = lsConsts.LspNewProto

		mockJsonRpcConn := &MockLspJsonRpcConn{}
		var callMethodCalled bool
		mockJsonRpcConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			callMethodCalled = true
			assert.Equal(t, "textDocument/didOpen", method, "Method should match")
			return nil
		}
		mockWrapper.lspJsonRpcConn = mockJsonRpcConn

		mockWebSocket := &MockWebSocketServer{}
		var broadcastAllCalled bool
		mockWebSocket.BroadcastAllFunc = func(name string, code int, data []byte) {
			broadcastAllCalled = true
			assert.Equal(t, lsConsts.WSName, name, "WebSocket name should match")
			assert.Equal(t, lsConsts.WSCode, code, "WebSocket code should match")

			var msgResp map[string]interface{}
			err := json.Unmarshal(data, &msgResp)
			assert.NoError(t, err, "Should unmarshal response correctly")
		}
		mockWrapper.websocket = mockWebSocket

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "go", nil
			})
		defer extractLanguagePatch.Reset()

		handleDidOpen(mockWrapper, ctx, conn, request)

		if broadcastAllCalled && callMethodCalled {
		}
	})

	t.Run("Empty language", func(t *testing.T) {
		mockWrapper := newMockLSPServer()
		mockWrapper.language = "go"
		mockWrapper.isOldLsp = false

		mockJsonRpcConn := &MockLspJsonRpcConn{}
		mockJsonRpcConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			mockWrapper.callMethodCalled = true
			return nil
		}
		mockWrapper.lspJsonRpcConn = mockJsonRpcConn

		mockWebSocket := &MockWebSocketServer{}
		mockWrapper.websocket = mockWebSocket

		extractLanguagePatch := gomonkey.ApplyFunc(extractLanguageFromRequest,
			func(request *jsonrpc2.Request) (string, error) {
				return "", nil
			})
		defer extractLanguagePatch.Reset()

		handleDidOpen(mockWrapper, ctx, conn, request)
	})

	t.Run("JSON marshal error in old LSP protocol", func(t *testing.T) {
		mockConn := &MockLspJsonRpcConn{}
		mockWebSocket := &MockWebSocketServer{}
		mockRouter := setupMockLspMessageRouter(t)

		mockRouter.ParseLspLanguageFunc = func(msg string) (string, error) {
			return "go", nil
		}

		// 使用 gomonkey patch GetLspMessageRouter 函数
		patches := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{}
		})
		defer patches.Reset()

		// 确保 Call 方法有有效实现
		mockConn.CallFunc = func(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
			return nil
		}

		data := json.RawMessage{}
		brokenRequest := &jsonrpc2.Request{
			Method: lsConsts.MethodDidOpen,
			Params: &data,
		}

		wrapper := &lspWSWrapper{
			language:       "go",
			lspJsonRpcConn: mockConn,
			websocket:      mockWebSocket,
			isOldLsp:       true,
		}

		ctx := context.Background()

		handleDidOpen(wrapper, ctx, conn, brokenRequest)
	})
}

// 辅助函数 - 创建模拟LSP服务器
func newMockLSPServer() *mockLSPServerForRequestProcessing {
	wrapper := &mockLSPServerForRequestProcessing{
		language:  "go",
		websocket: &mockWebsocket{},
	}
	wrapper.lspJsonRpcConn = &mockLspJsonRpcConn{wrapper: wrapper}
	return wrapper
}

// 模拟的LspJsonRpcConn实现
type mockLspJsonRpcConn struct {
	wrapper *mockLSPServerForRequestProcessing
}

func (m *mockLspJsonRpcConn) Close() error {
	return nil
}

func (m *mockLspJsonRpcConn) ConnectLspServer(ctx context.Context, lsConn io.ReadWriteCloser, h jsonrpc2.Handler) {
}

func (m *mockLspJsonRpcConn) Call(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
	if m.wrapper != nil {
		m.wrapper.callMethodCalled = true
	}
	return nil
}

func (m *mockLspJsonRpcConn) Notify(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
	return nil
}

func (m *mockLspJsonRpcConn) Reply(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
	return nil
}

// 模拟的WebSocket实现
type mockWebsocket struct{}

func (m *mockWebsocket) BroadcastAll(name string, code int, data []byte) {
}

func (m *mockWebsocket) Broadcast(room, name string, code int, data []byte) {
}

func (m *mockWebsocket) OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool)) {
}

func (m *mockWebsocket) OnMessage(fn func(message *lightcable.Message)) {
}

func (m *mockWebsocket) ServeHTTP(w http.ResponseWriter, r *http.Request) {
}

func (m *mockWebsocket) Run(ctx context.Context) {
}

// 辅助类型 - 模拟LSP服务器
type mockLSPServerForRequestProcessing struct {
	language         string
	isOldLsp         bool
	protoVersion     int32
	lspJsonRpcConn   LspJsonRpcConnIface
	websocket        LspWebSocketServerIface
	callMethodCalled bool
}

func (m *mockLSPServerForRequestProcessing) ExecLinter(req jsonrpc2.Request) {
	return
}

func (m *mockLSPServerForRequestProcessing) SendNotification(source, method string, params lsp.PublishDiagnosticsParams) error {
	return nil
}

func (m *mockLSPServerForRequestProcessing) Start(ctx context.Context) error {
	return nil
}

func (m *mockLSPServerForRequestProcessing) SetDiagnosticChannel(id string, ch chan []DiagnosticItem) {
}

func (m *mockLSPServerForRequestProcessing) RemoveDiagnosticChannel(id string) {
}

func (m *mockLSPServerForRequestProcessing) GetCmd() string {
	return ""
}

func (m *mockLSPServerForRequestProcessing) GetArg() []string {
	return []string{}
}

func (m *mockLSPServerForRequestProcessing) GetRootUri() lsp.DocumentURI {
	return ""
}

func (m *mockLSPServerForRequestProcessing) GetCtx() context.Context {
	return context.Background()
}

func (m *mockLSPServerForRequestProcessing) GetInitializeResponse() interface{} {
	return ""
}

func (m *mockLSPServerForRequestProcessing) GetLanguage() string {
	return m.language
}

func (m *mockLSPServerForRequestProcessing) GetIsOldLsp() bool {
	return m.isOldLsp
}

func (m *mockLSPServerForRequestProcessing) GetProtoVersion() int32 {
	return m.protoVersion
}

func (m *mockLSPServerForRequestProcessing) GetLspJsonRpcConn() LspJsonRpcConnIface {
	return m.lspJsonRpcConn
}

func (m *mockLSPServerForRequestProcessing) GetWebsocket() LspWebSocketServerIface {
	return m.websocket
}

func (m *mockLSPServerForRequestProcessing) SendToLS(msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	// 实现为空或记录调用
}

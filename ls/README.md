### LSP
### impl https://microsoft.github.io/language-server-protocol/implementors/servers/

有些lsp nix没有,需要使用到私库

| 语言 | LSP | Github  | nix  | cmd | 备注
| :-----| :---- | :---- |  :----  |  :---- |  :----
| C | ccls | https://github.com/MaskRay/ccls | ccls |ccls
| C++ | ccls | https://github.com/MaskRay/ccls | ccls  |ccls
| Objective-C | ccls | https://github.com/MaskRay/ccls | ccls  |ccls
| Java | Eclipse JDT LS | https://github.com/eclipse/eclipse.jdt.ls | | |支持maven和gradle
| Go     | gopls  | https://github.com/golang/tools/tree/master/gopls | gopls | gopls |
| Ruby     | solargraph   |  https://github.com/castwide/solargraph | rubyPackages_3_0.solargraph 和 rubyPackages.solargraph | solargraph stdio
| PHP     | php-language-server   |  https://github.com/felixfbecker/php-language-server |  |  |  依赖需要改 ： "jetbrains/phpstorm-stubs": "^2018.1.2"
| Python     | python-lsp-server   |  https://github.com/python-lsp/python-lsp-server | python39Packages.python-lsp-server | pylsp |  python2需要重新build, pip install "python-lsp-server[yapf]"
| Rust     | rust-analyzer   |  https://github.com/rust-lang/rust-analyzer |  rust-analyzer | rust-analyzer |
| C#     | omnisharp-roslyn   |  https://github.com/OmniSharp/omnisharp-roslyn |  omnisharp-roslyn | omnisharp -lsp |
| Dart     | dart sdk   |  https://github.com/dart-lang/sdk/blob/master/pkg/analysis_server/tool/lsp_spec/README.md |  dart | dart language-server |
| Kotlin     | kotlin-language-server   |  https://github.com/fwcd/kotlin-language-server |   |  |
| Erlang     | erlang_ls   |  https://github.com/erlang-ls/erlang_ls | erlang-ls | erlang_ls |
| Swift     |    SourceKit-LSP   |  https://github.com/apple/sourcekit-lsp | swift | sourcekit-lsp |
| Lua     |    lua-language-server   |  https://github.com/sumneko/lua-language-server | sumneko-lua-language-server | lua-language-server |
| TypeScript | typescript-language-server | https://github.com/typescript-language-server/typescript-language-server | typescript-language-server | typescript-language-server --stdio |

### Examples

Examples of LSP client implementations can be found in the `examples` directory:

1. **TypeScript Example**: Located at `ls/examples/typescript` - Demonstrates how to connect to a TypeScript language server, send document notifications, and handle diagnostics responses. This example shows the basic workflow of LSP initialization, document synchronization, and diagnostic processing.

For more details about specific examples, refer to their individual README files.
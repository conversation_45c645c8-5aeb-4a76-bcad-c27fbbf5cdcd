package ls

import (
	"sync"
	"testing"
)

// Helper function to reset the singleton instance for testing
func resetLSPManager() {
	instance = nil
	once = sync.Once{}
}

func TestGetLSPManager(t *testing.T) {
	// Reset the singleton before test
	resetLSPManager()

	// First call should create a new instance
	manager1 := GetLSPManager()
	if manager1 == nil {
		t.<PERSON>rror("GetLSPManager() returned nil")
	}

	// Second call should return the same instance
	manager2 := GetLSPManager()
	if manager1 != manager2 {
		t.<PERSON><PERSON>("GetLSPManager() returned different instances")
	}
}

func TestRegisterLSPServer(t *testing.T) {
	// Reset the singleton before test
	resetLSPManager()
	manager := GetLSPManager()

	// Test case 1: Register a new LSP server
	server1 := &mockLSPServer{language: "go"}
	err := manager.RegisterLSPServer("go", server1)
	if err != nil {
		t.<PERSON><PERSON>("RegisterLSPServer() failed: %v", err)
	}

	// Test case 2: Register another server for the same language (override)
	server2 := &mockLSPServer{language: "go"}
	err = manager.RegisterLSPServer("go", server2)
	if err != nil {
		t.<PERSON><PERSON>("RegisterLSPServer() failed on override: %v", err)
	}

	// Verify the server was overridden
	registeredServer, _ := manager.GetLSPServer("go")
	if registeredServer != server2 {
		t.Error("RegisterLSPServer() did not override existing server")
	}

	// Test case 3: Register with different case
	server3 := &mockLSPServer{language: "Python"}
	err = manager.RegisterLSPServer("Python", server3)
	if err != nil {
		t.Errorf("RegisterLSPServer() failed with capitalized language: %v", err)
	}

	// Verify case insensitivity
	registeredServer, _ = manager.GetLSPServer("python")
	if registeredServer != server3 {
		t.Error("RegisterLSPServer() did not handle case insensitive language ID")
	}

	// Test case 4: Try to register with empty language ID
	invalidServer := &mockLSPServer{language: ""}
	err = manager.RegisterLSPServer("", invalidServer)
	if err == nil {
		t.Error("RegisterLSPServer() should fail with empty language ID")
	}
}

func TestGetLSPServer(t *testing.T) {
	// Reset the singleton before test
	resetLSPManager()
	manager := GetLSPManager()

	// Setup test servers
	goServer := &mockLSPServer{language: "go"}
	pythonServer := &mockLSPServer{language: "Python"}

	// Register the servers
	_ = manager.RegisterLSPServer("go", goServer)
	_ = manager.RegisterLSPServer("Python", pythonServer)

	// Test case 1: Get existing server
	server, err := manager.GetLSPServer("go")
	if err != nil {
		t.Errorf("GetLSPServer() failed for existing server: %v", err)
	}
	if server != goServer {
		t.Error("GetLSPServer() returned wrong server")
	}

	// Test case 2: Get non-existent server
	server, err = manager.GetLSPServer("nonexistent")
	if err == nil {
		t.Error("GetLSPServer() should return error for non-existent language")
	}
	if server != nil {
		t.Error("GetLSPServer() should return nil for non-existent language")
	}

	// Test case 3: Case insensitivity
	server, err = manager.GetLSPServer("PYTHON")
	if err != nil {
		t.Errorf("GetLSPServer() failed for case-insensitive lookup: %v", err)
	}
	if server != pythonServer {
		t.Error("GetLSPServer() did not handle case insensitive language ID")
	}

	// Test case 4: Empty language ID
	server, err = manager.GetLSPServer("")
	if err == nil {
		t.Error("GetLSPServer() should return error for empty language ID")
	}
	if server != nil {
		t.Error("GetLSPServer() should return nil for empty language ID")
	}
}

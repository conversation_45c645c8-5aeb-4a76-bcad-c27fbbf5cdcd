package ls

import (
	"agent/consts"
	"context"
	"os"
	"testing"
)

// TestJavaLsStopHook tests the javaLsStopHook function
func TestJavaLsStopHook(t *testing.T) {
	testCases := []struct {
		name            string
		initialPort     string
		expectPortUnset bool
	}{
		{
			name:            "Debug port set",
			initialPort:     "8765",
			expectPortUnset: true,
		},
		{
			name:            "Debug port not set",
			initialPort:     "",
			expectPortUnset: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Save original environment and restore after test
			origPort := os.Getenv(consts.DebugServerPort)
			defer os.Setenv(consts.DebugServerPort, origPort)

			// Set test environment
			if tc.initialPort != "" {
				os.Setenv(consts.DebugServerPort, tc.initialPort)
			} else {
				os.Unsetenv(consts.DebugServerPort)
			}

			// Create test wrapper
			wrapper := &lspWSWrapper{
				language: consts.LanguageJava,
				ctx:      context.Background(),
			}

			// Call the function to test
			javaLsStopHook(wrapper)

			// Verify the debug port was unset
			if tc.expectPortUnset {
				port := os.Getenv(consts.DebugServerPort)
				if port != "" {
					t.Errorf("Expected debug port to be unset, but got %s", port)
				}
			}
		})
	}
}

// TestLsStopHookFactory tests the lsStopHookFactory initialization
func TestLsStopHookFactory(t *testing.T) {
	// Check if Java language has a stop hook registered
	if hook, exists := lsStopHookFactory[consts.LanguageJava]; !exists {
		t.Error("Expected Java language to have a stop hook registered")
	} else if hook == nil {
		t.Error("Java stop hook exists but is nil")
	}

	// Check that other languages don't have stop hooks unless they should
	knownLanguages := []string{
		consts.LanguageGo,
		consts.LanguagePython,
		consts.LanguageRuby,
	}

	for _, lang := range knownLanguages {
		if lang == consts.LanguageJava {
			continue // Already tested Java
		}

		if hook, exists := lsStopHookFactory[lang]; exists && hook != nil {
			// If we're not expecting a hook for this language, this would fail
			// Only fail if we know this language shouldn't have a hook
			t.Logf("Language %s has a stop hook registered (this is informational)", lang)
		}
	}
}

// TestStopHookIntegration tests the stop hook integration with the LSPServer interface
func TestStopHookIntegration(t *testing.T) {
	// Save original environment and restore after test
	origPort := os.Getenv(consts.DebugServerPort)
	defer os.Setenv(consts.DebugServerPort, origPort)

	// Set test environment
	testPort := "9999"
	os.Setenv(consts.DebugServerPort, testPort)

	// Create test wrapper
	wrapper := &lspWSWrapper{
		language: consts.LanguageJava,
		ctx:      context.Background(),
	}

	// Register the LSP server
	manager := GetLSPManager()
	// Reset the singleton before test
	resetLSPManager()
	manager = GetLSPManager()

	err := manager.RegisterLSPServer(consts.LanguageJava, wrapper)
	if err != nil {
		t.Fatalf("Failed to register LSP server: %v", err)
	}

	// Get the registered server
	server, err := manager.GetLSPServer(consts.LanguageJava)
	if err != nil {
		t.Fatalf("Failed to get LSP server: %v", err)
	}

	// Check if it's the same instance
	if server != wrapper {
		t.Error("Registered server is not the same as original wrapper")
	}

	// Call the stop hook
	if hook, exists := lsStopHookFactory[consts.LanguageJava]; exists {
		hook(wrapper)
	}

	// Verify the debug port was unset
	port := os.Getenv(consts.DebugServerPort)
	if port != "" {
		t.Errorf("Expected debug port to be unset, but got %s", port)
	}
}

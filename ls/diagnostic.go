package ls

import (
	lsConsts "agent/ls/consts"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"time"

	"github.com/a-wing/lightcable"
	"github.com/google/uuid"
	"github.com/sourcegraph/go-lsp"
	"github.com/sourcegraph/jsonrpc2"
)

type LspDiagnosticIface interface {
	DiagnosticCodeFile(filePath string, lspManager LSPManagerIface) (*DiagnosticResult, error)
	DocumentDidOpen(server LSPServerIface, docURI lsp.DocumentURI, language, content, filePath string) error
	DocumentDidClose(server LSPServerIface, docURI lsp.DocumentURI) error
}

type LspDiagnostic struct {
}

func NewLspDiagnostic() LspDiagnosticIface {
	return &LspDiagnostic{}
}

// DiagnosticSeverity represents the severity level of a diagnostic
type DiagnosticSeverity int

// DiagnosticItem represents a single diagnostic issue found in the code
type DiagnosticItem struct {
	//Range       lsp.Range          `json:"range"`
	Range       interface{}        `json:"range"`
	Severity    DiagnosticSeverity `json:"severity"`
	Code        string             `json:"code,omitempty"`
	Source      string             `json:"source,omitempty"`
	Message     string             `json:"message"`
	RelatedInfo []RelatedInfo      `json:"relatedInformation,omitempty"`
}

// RelatedInfo represents additional information related to a diagnostic
type RelatedInfo struct {
	//Location lsp.Location `json:"location"`
	Location interface{} `json:"location"`
	Message  string      `json:"message"`
}

// DiagnosticResult contains all diagnostic information for a file
type DiagnosticResult struct {
	FilePath    string           `json:"filePath"`
	Language    string           `json:"language"`
	Diagnostics []DiagnosticItem `json:"diagnostics"`
	Error       string           `json:"error,omitempty"`
}

func isNil(i interface{}) bool {
	if i == nil {
		return true
	}
	v := reflect.ValueOf(i)
	return v.Kind() == reflect.Ptr && v.IsNil()
}

// DiagnosticCodeFile sends a given file to the appropriate language server for diagnostic analysis
// and returns the diagnostic results
func (s *LspDiagnostic) DiagnosticCodeFile(filePath string, lspManager LSPManagerIface) (*DiagnosticResult, error) {
	// Verify the file exists and is readable
	if _, err := os.Stat(filePath); err != nil {
		return &DiagnosticResult{
			FilePath: filePath,
			Language: "",
			Error:    fmt.Sprintf("File not found or not accessible: %v", err),
		}, err
	}

	// Read file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return &DiagnosticResult{
			FilePath: filePath,
			Language: "",
			Error:    fmt.Sprintf("Failed to read file: %v", err),
		}, err
	}

	// Determine language from file extension
	ext := filepath.Ext(filePath)
	messageRouter := GetLspMessageRouter()
	language := messageRouter.DetectLanguageFromURI(filePath)

	if language == "" {
		return &DiagnosticResult{
			FilePath: filePath,
			Language: "",
			Error:    "Could not determine language for file",
		}, fmt.Errorf("unsupported file type: %s", ext)
	}

	// Get the LSP server for the detected language
	if isNil(lspManager) {
		return nil, fmt.Errorf("unsupported file type: %s", ext)
	}

	server, err := lspManager.GetLSPServer(language)
	if err != nil {
		return &DiagnosticResult{
			FilePath: filePath,
			Language: language,
			Error:    fmt.Sprintf("No LSP server available for language %s: %v", language, err),
		}, err
	}

	if isNil(server) {
		return &DiagnosticResult{
			FilePath: filePath,
			Language: language,
			Error:    "LSP server instance is nil",
		}, fmt.Errorf("LSP server instance is nil for language: %s", language)
	}

	// Setup a response channel for diagnostics
	diagChan := make(chan []DiagnosticItem, 1)

	// Setup diagnostic response handler in message router
	docURI := lsp.DocumentURI("file://" + filePath)
	server.SetDiagnosticChannel(string(docURI), diagChan)
	defer server.RemoveDiagnosticChannel(string(docURI))

	// 先关闭
	tsStartTime := time.Now()
	err = s.DocumentDidClose(server, docURI)
	log.Infof("MultiLspServer, DiagnosticFile, documentDidClose for file: %s, err: %+v, time: %d ms", filePath, err, time.Since(tsStartTime).Milliseconds())

	// 打开文档
	tsStartTime = time.Now()
	err = s.DocumentDidOpen(server, docURI, language, string(content), filePath)
	log.Infof("MultiLspServer, DiagnosticFile, DocumentDidOpen for file: %s, err: %+v, time: %d ms", filePath, err, time.Since(tsStartTime).Milliseconds())
	if err != nil {
		log.Printf("MultiLspServer, Received diagnostics documentDidOpen for file: %s, err: %+v", filePath, err)
		return &DiagnosticResult{
			FilePath: filePath,
			Language: language,
			Error:    fmt.Sprintf("Failed to marshal didOpen params: %v", err),
		}, nil
	}

	// Wait for diagnostic response with timeout
	var diagnostics []DiagnosticItem
	tsStartTime = time.Now()
	select {
	case diagnostics = <-diagChan:
		log.Printf("MultiLspServer, DiagnosticFile Received diagnostics for file: %s, count: %d", filePath, len(diagnostics))
	case <-time.After(3 * time.Second):
		return &DiagnosticResult{
			FilePath:    filePath,
			Language:    language,
			Error:       "Timeout waiting for diagnostic results",
			Diagnostics: []DiagnosticItem{},
		}, fmt.Errorf("Timeout waiting for diagnostic results")
	}

	log.Infof("MultiLspServer, DiagnosticFile, diagChan for file: %s, err: %+v, time: %d ms, diagnostic: %d",
		filePath, err, time.Since(tsStartTime).Milliseconds(), len(diagnostics))

	//err = s.DocumentDidClose(server, docURI)
	//if err != nil {
	//	log.Infof("documentDidClose for file: %s, err: %+v", filePath, err)
	//}

	return &DiagnosticResult{
		FilePath:    filePath,
		Language:    language,
		Diagnostics: diagnostics,
	}, nil
}

func (s *LspDiagnostic) DocumentDidClose(server LSPServerIface, docURI lsp.DocumentURI) error {
	// Send didClose notification to clean up
	didCloseParams := lsp.DidCloseTextDocumentParams{
		TextDocument: lsp.TextDocumentIdentifier{
			URI: docURI,
		},
	}
	closeJSON, _ := json.Marshal(didCloseParams)
	closeParams := (*json.RawMessage)(&closeJSON)
	closeReq := &jsonrpc2.Request{
		Method: lsConsts.MethodDidClose,
		Params: closeParams,
		Notif:  true,
	}
	closeData, _ := closeReq.MarshalJSON()
	closeMsg := &lightcable.Message{
		Room: uuid.New().String(),
		Name: uuid.New().String(),
		Data: closeData,
	}
	server.SendToLS(closeMsg, *closeReq, lsConsts.LspNewProto)
	return nil
}

func (s *LspDiagnostic) DocumentDidOpen(server LSPServerIface, docURI lsp.DocumentURI, language, content, filePath string) error {
	// Create didOpen notification to trigger diagnostics
	didOpenParams := lsp.DidOpenTextDocumentParams{
		TextDocument: lsp.TextDocumentItem{
			URI:        docURI,
			LanguageID: language,
			Version:    30,
			Text:       content,
		},
	}

	// Convert params to JSON
	paramsJSON, err := json.Marshal(didOpenParams)
	if err != nil {
		return err
	}
	params := (*json.RawMessage)(&paramsJSON)

	// Create the jsonrpc2 request
	diagID := uuid.New().String()
	req := &jsonrpc2.Request{
		ID:     jsonrpc2.ID{Str: diagID, IsString: true},
		Method: lsConsts.MethodDidOpen,
		Params: params,
		Notif:  true,
	}

	// Marshal the request
	reqData, _ := req.MarshalJSON()

	// Create a dummy message to send to LSP server
	dummyMsg := &lightcable.Message{
		Room: diagID,
		Name: lsConsts.WSName,
		Code: lsConsts.WSCode,
		Data: reqData,
	}

	// Process the request with the target language server
	server.SendToLS(dummyMsg, *req, lsConsts.LspNewProto)

	return nil
}

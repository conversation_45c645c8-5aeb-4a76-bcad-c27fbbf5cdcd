package ls

import (
	lsConsts "agent/ls/consts"
	"agent/pkg/errors"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"sync"

	"github.com/sourcegraph/jsonrpc2"
)

var (
	UnsupportedLanguageError = errors.New("unsupported parse lsp language")
)

var (
	routerInstance *LspMessageRouter
	routerOnce     sync.Once
)

type LspMessageRouterIface interface {
	ParseLspLanguage(message string) (string, error)
	RouteMessage(message string) (string, *jsonrpc2.Request, int32, error)
	DetectLanguageFromURI(uri string) string
	IsFileExtensionSupported(message string) (bool, error)
}

// LspMessageRouter handles routing of LSP messages to appropriate language servers
type LspMessageRouter struct{}

// GetLspMessageRouter returns the singleton instance of LspMessageRouter
func GetLspMessageRouter() LspMessageRouterIface {
	routerOnce.Do(func() {
		routerInstance = &LspMessageRouter{}
	})
	// Double check in case of race condition
	if routerInstance == nil {
		routerInstance = &LspMessageRouter{}
	}
	return routerInstance
}

// IsFileExtensionSupported checks if the file extension from the given URI is in the supported list
func (r *LspMessageRouter) IsFileExtensionSupported(message string) (bool, error) {
	// Try to parse the message as JSON
	var msgMap map[string]interface{}
	if err := json.Unmarshal([]byte(message), &msgMap); err != nil {
		log.Warnf("MultiLspServer, Failed to parse LSP message as JSON: %v, message: %s", err, message)
		return false, err
	}

	if method, ok := msgMap[lsConsts.LSPMethodField].(string); ok {
		if method == lsConsts.MethodDidOpen || method == lsConsts.MethodDidChange {
			uri := r.extractURIFromTextDocument(msgMap)

			// Extract file extension from URI
			ext := strings.ToLower(filepath.Ext(uri))
			if ext == "" {
				return false, nil
			}

			// Check if the extension is in the supported list
			for _, supportedExt := range lsConsts.SupportedFileExtensions {
				if ext == supportedExt {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// ParseLspLanguage extracts language information from an LSP message
func (r *LspMessageRouter) ParseLspLanguage(message string) (string, error) {
	// Try to parse the message as JSON
	var msgMap map[string]interface{}
	if err := json.Unmarshal([]byte(message), &msgMap); err != nil {
		log.Warnf("MultiLspServer, Failed to parse LSP message as JSON: %v, message: %s", err, message)
		return "", err
	}

	// Extract language from the message if available
	if langVal, ok := msgMap[lsConsts.LSPLanguageField]; ok {
		if language, ok := langVal.(string); ok && language != "" {
			return strings.ToLower(language), nil
		}
	}

	// Handle textDocument/didOpen and textDocument/didChange messages specially
	// by extracting language information from the document URI or content
	if method, ok := msgMap[lsConsts.LSPMethodField].(string); ok {
		if method == lsConsts.MethodDidOpen || method == lsConsts.MethodDidChange {
			return r.extractLanguageFromTextDocument(msgMap), nil
		}
	}

	return "", UnsupportedLanguageError
}

// extractURIFromTextDocument extracts the URI from textDocument messages
func (r *LspMessageRouter) extractURIFromTextDocument(msgMap map[string]interface{}) string {
	// Try to get params object which contains the textDocument
	paramsRaw, ok := msgMap["params"]
	if !ok {
		return ""
	}

	params, ok := paramsRaw.(map[string]interface{})
	if !ok {
		return ""
	}

	// Get textDocument object
	textDocRaw, ok := params["textDocument"]
	if !ok {
		return ""
	}

	textDoc, ok := textDocRaw.(map[string]interface{})
	if !ok {
		return ""
	}

	// Get URI
	if uriRaw, ok := textDoc["uri"].(string); ok {
		return uriRaw
	}

	return ""
}

// extractLanguageFromTextDocument attempts to determine language from textDocument messages
func (r *LspMessageRouter) extractLanguageFromTextDocument(msgMap map[string]interface{}) string {
	// Try to get params object which contains the textDocument
	paramsRaw, ok := msgMap["params"]
	if !ok {
		return ""
	}

	params, ok := paramsRaw.(map[string]interface{})
	if !ok {
		return ""
	}

	// Get textDocument object
	textDocRaw, ok := params["textDocument"]
	if !ok {
		return ""
	}

	textDoc, ok := textDocRaw.(map[string]interface{})
	if !ok {
		return ""
	}

	// Try to determine language from URI
	if uriRaw, ok := textDoc["uri"].(string); ok {
		language := r.DetectLanguageFromURI(uriRaw)
		if language != "" {
			return language
		}
	}

	// For didOpen, we can also check the languageId field
	if langID, ok := textDoc["languageId"].(string); ok {
		return r.normalizeLanguage(langID)
	}

	return ""
}

// DetectLanguageFromURI tries to determine the language based on file extension
func (r *LspMessageRouter) DetectLanguageFromURI(uri string) string {
	// Check file extension from URI
	uri = strings.ToLower(uri)

	// Map file extensions to languages
	if strings.HasSuffix(uri, ".py") {
		return lsConsts.LangPython
	} else if strings.HasSuffix(uri, ".js") {
		return lsConsts.LangTypeScript
	} else if strings.HasSuffix(uri, ".ts") || strings.HasSuffix(uri, ".tsx") {
		return lsConsts.LangTypeScript
	} else if strings.HasSuffix(uri, ".java") {
		return lsConsts.LangJava
	} else if strings.HasSuffix(uri, ".go") {
		return lsConsts.LangGo
	} else if strings.HasSuffix(uri, ".rs") {
		return lsConsts.LangRust
	} else if strings.HasSuffix(uri, ".cpp") || strings.HasSuffix(uri, ".cc") ||
		strings.HasSuffix(uri, ".h") || strings.HasSuffix(uri, ".hpp") {
		return lsConsts.LangCPP
	} else if strings.HasSuffix(uri, ".cs") {
		return lsConsts.LangCSharp
	} else if strings.HasSuffix(uri, ".php") {
		return lsConsts.LangPHP
	} else if strings.HasSuffix(uri, ".rb") {
		return lsConsts.LangRuby
	} else if strings.HasSuffix(uri, ".swift") {
		return lsConsts.LangSwift
	} else if strings.HasSuffix(uri, ".dart") {
		return lsConsts.LangDart
	} else if strings.HasSuffix(uri, ".kt") {
		return lsConsts.LangKotlin
	}

	return ""
}

// normalizeLanguage normalizes language IDs to our internal representation
func (r *LspMessageRouter) normalizeLanguage(langID string) string {
	langID = strings.ToLower(langID)

	// Map language IDs to our constants
	switch langID {
	case "python", "py":
		return lsConsts.LangPython
	case "javascript", "js":
		return lsConsts.LangTypeScript
	case "typescript", "ts":
		return lsConsts.LangTypeScript
	case "java":
		return lsConsts.LangJava
	case "go", "golang":
		return lsConsts.LangGo
	case "rust", "rs":
		return lsConsts.LangRust
	case "cpp", "c++":
		return lsConsts.LangCPP
	case "csharp", "c#":
		return lsConsts.LangCSharp
	case "php":
		return lsConsts.LangPHP
	case "ruby", "rb":
		return lsConsts.LangRuby
	case "swift":
		return lsConsts.LangSwift
	case "dart":
		return lsConsts.LangDart
	case "kotlin", "kt":
		return lsConsts.LangKotlin
	default:
		return ""
	}
}

// RouteMessage routes an LSP message to the appropriate language server
func (r *LspMessageRouter) RouteMessage(message string) (string, *jsonrpc2.Request, int32, error) {
	// Parse the LSP message to extract the language
	language, err := r.ParseLspLanguage(message)
	if err != nil && err != UnsupportedLanguageError {
		return "", nil, lsConsts.LspOldProtoDefault, err
	}

	var msgMap map[string]interface{}
	if err1 := json.Unmarshal([]byte(message), &msgMap); err1 != nil {
		return "", nil, lsConsts.LspOldProtoDefault, err1
	}

	languageCode, ok := msgMap[lsConsts.LSPLanguageField]
	if ok && languageCode != "" {
		var lspMessage LspMessageRequest
		if err1 := json.Unmarshal([]byte(message), &lspMessage); err1 != nil {
			return "", nil, lsConsts.LspOldProtoDefault, fmt.Errorf("failed to parse message as JSON-RPC request: %v", err1)
		}

		return lspMessage.LanguageCode, &lspMessage.Message, lsConsts.LspNewProto, nil
	} else {
		var req jsonrpc2.Request
		if err1 := json.Unmarshal([]byte(message), &req); err1 != nil {
			return "", nil, lsConsts.LspOldProtoDefault, fmt.Errorf("failed to parse message as JSON-RPC request: %+v", err)
		}

		return language, &req, lsConsts.LspOldProto, nil
	}
}

type LspMessageRequest struct {
	LanguageCode string           `json:"language_code"`
	Message      jsonrpc2.Request `json:"message"`
}

type LspMessageResponse struct {
	LanguageCode string      `json:"language_code"`
	Message      interface{} `json:"message"`
	Source       string      `json:"source"`
}

// MarshalJSON 自定义 LspMessageResponse 的 JSON 序列化
func (r LspMessageResponse) MarshalJSON() ([]byte, error) {
	// 定义中间结构体以避免递归并确保包含 jsonrpc 字段
	type tmpType struct {
		Language string                 `json:"language"`
		Message  map[string]interface{} `json:"message"`
		Source   string                 `json:"source"`
	}

	// 将内部 Message 序列化为通用 map 以便操作字段
	messageBytes, _ := json.Marshal(r.Message)
	var messageMap map[string]interface{}
	json.Unmarshal(messageBytes, &messageMap)
	if messageMap == nil {
		return nil, errors.New("message is null")
	}

	// 确保 message 中包含 "jsonrpc": "2.0"
	messageMap["jsonrpc"] = "2.0"

	// 创建临时结构体进行序列化
	tmp := tmpType{
		Language: r.LanguageCode,
		Message:  messageMap,
		Source:   r.Source,
	}

	return json.Marshal(tmp)
}

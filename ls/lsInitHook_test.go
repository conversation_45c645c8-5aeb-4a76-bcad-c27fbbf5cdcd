package ls

import (
	"agent/consts"
	"agent/ls/go-lsp"
	"testing"
)

// TestTsLsInitHook tests the tsLsInitHook function
func TestTsLsInitHook(t *testing.T) {
	testCases := []struct {
		name             string
		cmdArgs          []string
		expectVueOptions bool
	}{
		{
			name:             "TypeScript server without Vue",
			cmdArgs:          []string{"/usr/bin/typescript-language-server", "--stdio"},
			expectVueOptions: false,
		},
		{
			name:             "Vue language server",
			cmdArgs:          []string{"/usr/bin/vscode-vue-language-server", "--stdio"},
			expectVueOptions: true,
		},
		{
			name:             "Vue language server in args",
			cmdArgs:          []string{"node", "/path/to/vue-language-server/bin/vue-language-server.js", "--stdio"},
			expectVueOptions: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test wrapper with command args
			wrapper := &lspWSWrapper{
				cmd: tc.cmdArgs[0],
				arg: tc.cmdArgs[1:],
			}

			// Create initialize params
			params := &lsp.InitializeParams{
				RootURI:      "file:///test/project",
				Capabilities: *new(lsp.ClientCapabilities),
			}

			// Call the function to test
			tsLsInitHook(wrapper, params)

			// Check if vue options were set
			if tc.expectVueOptions {
				// Verify some expected Vue options are present
				options, ok := params.InitializationOptions.(map[string]interface{})
				if !ok {
					return
				}

				// Check for typical Vue options structure
				if _, hasDocFeatures := options["documentFeatures"]; !hasDocFeatures {
					t.Error("Vue initialization options missing documentFeatures")
				}
				if _, hasLangFeatures := options["languageFeatures"]; !hasLangFeatures {
					t.Error("Vue initialization options missing languageFeatures")
				}
			} else {
				if params.InitializationOptions != nil {
					t.Error("Expected no initialization options, but some were set")
				}
			}
		})
	}
}

// TestIsVue tests the isVue function
func TestIsVue(t *testing.T) {
	testCases := []struct {
		name     string
		cmd      string
		args     []string
		expected bool
	}{
		{
			name:     "Vue command",
			cmd:      "vue-language-server",
			args:     []string{"--stdio"},
			expected: true,
		},
		{
			name:     "Vue in args",
			cmd:      "node",
			args:     []string{"/path/to/vue-language-server", "--stdio"},
			expected: false,
		},
		{
			name:     "Not Vue",
			cmd:      "typescript-language-server",
			args:     []string{"--stdio"},
			expected: false,
		},
		{
			name:     "Empty command",
			cmd:      "",
			args:     []string{},
			expected: false,
		},
		{
			name:     "Empty args with Vue cmd",
			cmd:      "vue-language-server",
			args:     []string{},
			expected: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create wrapper with test commands
			wrapper := &lspWSWrapper{
				cmd: tc.cmd,
				arg: tc.args,
			}

			// Call the function and check the result
			result := isVue(wrapper)
			if result != tc.expected {
				t.Errorf("isVue() = %v, want %v, name: %v", result, tc.expected, tc.name)
			}
		})
	}
}

// TestJavaLsInitHook tests the javaLsInitHook function
func TestJavaLsInitHook(t *testing.T) {
	originalDebugJavaJar := consts.DebugJavaJar
	defer func() {
		consts.DebugJavaJar = originalDebugJavaJar
	}()
	consts.DebugJavaJar = "/test/path/debug.jar"

	testCases := []struct {
		name    string
		rootUri string
	}{
		{
			name:    "Standard Java project",
			rootUri: "file:///test/java/project",
		},
		{
			name:    "Empty root URI",
			rootUri: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create wrapper with test URI
			wrapper := &lspWSWrapper{
				rootUri: lsp.DocumentURI(tc.rootUri),
			}

			// Create initialize params
			params := &lsp.InitializeParams{
				RootURI: lsp.DocumentURI(tc.rootUri),
				Capabilities: lsp.ClientCapabilities{
					Workspace: lsp.WorkspaceClientCapabilities{},
				},
			}

			// Call the function to test
			javaLsInitHook(wrapper, params)

			// Check that workspace capabilities were reset
			if params.Capabilities.Workspace != (lsp.WorkspaceClientCapabilities{}) {
				t.Error("Workspace capabilities were not reset")
			}

			// Check initialization options
			options, ok := params.InitializationOptions.(map[string]interface{})
			if !ok {
				t.Error("InitializationOptions is not of expected type")
				return
			}

			// Check bundles
			bundles, ok := options["bundles"].([]interface{})
			if !ok || len(bundles) == 0 {
				t.Error("Bundles not set correctly in initialization options")
				return
			}

			if bundlePath, ok := bundles[0].(string); !ok || bundlePath != consts.DebugJavaJar {
				t.Errorf("Expected bundle path %s, got %v", consts.DebugJavaJar, bundlePath)
			}

			// Check workspaceFolders
			workspaceFolders, ok := options["workspaceFolders"].([]interface{})
			if !ok || len(workspaceFolders) == 0 {
				t.Error("WorkspaceFolders not set correctly in initialization options")
				return
			}

			if folderPath, ok := workspaceFolders[0].(string); !ok || folderPath != tc.rootUri {
				t.Errorf("Expected workspace folder path %s, got %v", tc.rootUri, folderPath)
			}

			// Check Java settings
			if settings, ok := options["settings"].(map[string]interface{}); ok {
				if java, ok := settings["java"].(map[string]interface{}); ok {
					// Verify at least one setting
					if project, ok := java["project"].(map[string]interface{}); !ok {
						t.Error("Java project settings not found")
					} else {
						if _, hasRefLibs := project["referencedLibraries"]; !hasRefLibs {
							t.Error("Java referencedLibraries setting not found")
						}
					}
				} else {
					t.Error("Java settings not found")
				}
			} else {
				t.Error("Settings not found in initialization options")
			}
		})
	}
}

// TestDartLsInitHook tests the dartLsInitHook function
func TestDartLsInitHook(t *testing.T) {
	testCases := []struct {
		name       string
		initialPID int
	}{
		{
			name:       "Standard Dart project",
			initialPID: 1234,
		},
		{
			name:       "Another PID",
			initialPID: 5678,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create wrapper
			wrapper := &lspWSWrapper{}

			// Create initialize params with test PID
			params := &lsp.InitializeParams{
				ProcessID: tc.initialPID,
			}

			// Call the function to test
			dartLsInitHook(wrapper, params)

			// Check that ProcessID was set to -1
			if params.ProcessID != -1 {
				t.Errorf("Expected ProcessID to be -1, got %d", params.ProcessID)
			}
		})
	}
}

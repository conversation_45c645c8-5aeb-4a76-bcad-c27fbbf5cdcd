package ls

import (
	"agent/utils/log"
	"fmt"
	"strings"
	"sync"
)

// LSPManagerIface
type LSPManagerIface interface {
	RegisterLSPServer(language string, server LSPServerIface) error
	GetLSPServer(language string) (LSPServerIface, error)
}

var (
	instance *LSPManager
	once     sync.Once
)

type LspManagerIface interface {
	RegisterLSPServer(language string, server LSPServerIface) error
	GetLSPServer(language string) (LSPServerIface, error)
}

// GetLSPManager returns the singleton instance of LSPManager
func GetLSPManager() LSPManagerIface {
	once.Do(func() {
		instance = &LSPManager{
			lspServers: make(map[string]LSPServerIface),
		}
	})
	return instance
}

// LSPManager manages multiple LSP server instances for different languages
type LSPManager struct {
	lock       sync.RWMutex
	lspServers map[string]LSPServerIface // map[language]LSPServer
}

// RegisterLSPServer registers an LSP server for a specific language
func (m *LSPManager) RegisterLSPServer(language string, server LSPServerIface) error {
	lang := strings.ToLower(language)
	if lang == "" {
		return fmt.Errorf("language cannot be empty")
	}

	m.lock.Lock()
	defer m.lock.Unlock()

	//if _, exists := m.lspServers[language]; exists {
	//	return fmt.Errorf("lsp server for language %s already registered", language)
	//}

	m.lspServers[lang] = server
	log.Printf("MultiLspServer, LSP server registered for language: %s, lang: %s", language, lang)
	return nil
}

// GetLSPServer returns the LSP server for the specified language
func (m *LSPManager) GetLSPServer(language string) (LSPServerIface, error) {
	language = strings.ToLower(language)
	m.lock.RLock()
	defer m.lock.RUnlock()

	server, exists := m.lspServers[language]
	if !exists {
		return nil, fmt.Errorf("no lsp server found for language: %s", language)
	}

	return server, nil
}

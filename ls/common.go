package ls

import (
	"context"
	"io"
	"net/http"

	"github.com/a-wing/lightcable"
	"github.com/sourcegraph/jsonrpc2"
)

// 多抽象了一次是为了上层可以进行mock进行单元测试
type LspWebSocketServerIface interface {
	BroadcastAll(name string, code int, data []byte)
	Broadcast(room, name string, code int, data []byte)
	OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool))
	OnMessage(fn func(message *lightcable.Message))
	ServeHTTP(w http.ResponseWriter, r *http.Request)
	Run(ctx context.Context)
}

type LspWebSocketServer struct {
	websocket *lightcable.Server
}

func NewLspWebSocketServer() LspWebSocketServerIface {
	return &LspWebSocketServer{
		websocket: lightcable.New(lightcable.DefaultConfig),
	}
}

func NewLspWebSocketServerWith(ws *lightcable.Server) LspWebSocketServerIface {
	return &LspWebSocketServer{
		websocket: ws,
	}
}

func (l *LspWebSocketServer) BroadcastAll(name string, code int, data []byte) {
	l.websocket.BroadcastAll(name, code, data)
}
func (l *LspWebSocketServer) Broadcast(room, name string, code int, data []byte) {
	l.websocket.Broadcast(room, name, code, data)
}
func (l *LspWebSocketServer) OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool)) {
	l.websocket.OnConnected(fn)
}
func (l *LspWebSocketServer) OnMessage(fn func(message *lightcable.Message)) {
	l.websocket.OnMessage(fn)
}
func (l *LspWebSocketServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	l.websocket.ServeHTTP(w, r)
}
func (l *LspWebSocketServer) Run(ctx context.Context) {
	l.websocket.Run(ctx)
}

type LspJsonRpcConnIface interface {
	ConnectLspServer(ctx context.Context, lsConn io.ReadWriteCloser, h jsonrpc2.Handler)
	Call(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error
	Notify(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error
	Reply(ctx context.Context, id jsonrpc2.ID, result interface{}) error
	Close() error
}

type LspJsonRpcConn struct {
	conn *jsonrpc2.Conn
}

func NewLspJsonRpcConn() LspJsonRpcConnIface {
	return &LspJsonRpcConn{
		conn: &jsonrpc2.Conn{},
	}
}

func (c *LspJsonRpcConn) ConnectLspServer(ctx context.Context, lsConn io.ReadWriteCloser, h jsonrpc2.Handler) {
	c.conn = jsonrpc2.NewConn(ctx, jsonrpc2.NewBufferedStream(lsConn, jsonrpc2.VSCodeObjectCodec{}), h)
}

func (c *LspJsonRpcConn) Call(ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
	return c.conn.Call(ctx, method, params, result, opts...)
}
func (c *LspJsonRpcConn) Notify(ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
	return c.conn.Notify(ctx, method, params, opts...)
}
func (c *LspJsonRpcConn) Reply(ctx context.Context, id jsonrpc2.ID, result interface{}) error {
	return c.conn.Reply(ctx, id, result)
}

func (c *LspJsonRpcConn) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
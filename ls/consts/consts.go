package consts

const (
	MethodDidOpen                        = "textDocument/didOpen"
	MethodDidChange                      = "textDocument/didChange"
	MethodCompletion                     = "textDocument/completion"
	MethodCancelRequest                  = "$/cancelRequest"
	MethodInitialize                     = "initialize"
	MethodInitialized                    = "initialized"
	MethodDidClose                       = "textDocument/didClose"
	MethodWindowLogMessage               = "window/logMessage"
	MethodWorkspaceConfiguration         = "workspace/configuration"
	MethodWorkspaceDidChangeWatchedFiles = "workspace/didChangeWatchedFiles"
	MethodPublishDiagnostics             = "textDocument/publishDiagnostics" // LSP method for publishing diagnostics
)

var WSFilterMethod = map[string]int{
	MethodInitialized: 0,
	//MethodDidClose:    0,
}

var EmptyMap = map[string]interface{}{}

const (
	WSName = "WS_NAME_FILTER"
	WSCode = 1
)

const (
	LspStatusNotSupport = "notSupport"
	LspStatusLoading    = "loading"
	LspStatusRunning    = "running"
	LspStatusShutdown   = "shutdown"
)

// Language types constants for LSP
const (
	LangPython     = "python"
	LangNode       = "node"
	LangJava       = "java"
	LangGo         = "go"
	LangRust       = "rust"
	LangCPP        = "cpp"
	LangCSharp     = "csharp"
	LangPHP        = "php"
	LangRuby       = "ruby"
	LangTypeScript = "typescript"
	LangSwift      = "swift"
	LangDart       = "dart"
	LangKotlin     = "kotlin"
)

// LSP message field constants
const (
	LSPLanguageField    = "language_code" // Field name to extract language from LSP messages
	LSPContentTypeField = "content_type"  // Content type field in LSP messages
	LSPMethodField      = "method"        // Method field in LSP messages
)

// DefaultSupportedLanguages contains the list of languages supported by default
var DefaultSupportedLanguages = []string{
	LangPython,
	LangNode,
	LangJava,
	LangGo,
	LangRust,
	LangTypeScript,
	LangDart,
}

// SupportedFileExtensions contains the list of file extensions supported for LSP processing
var SupportedFileExtensions = []string{
	".py",
	".js",
	".ts",
	//".tsx",
	//".jsx",
	".java",
	".go",
	".rs",
	".cpp",
	".cc",
	".h",
	".hpp",
	".cs",
	".php",
	".rb",
	".swift",
	".dart",
	".kt",
}

const (
	LspOldProtoDefault = 0
	LspOldProto        = 1
	LspNewProto        = 2
)

// DiagnosticSeverity levels as per LSP specification
const (
	DiagnosticSeverityError       = 1 // Error severity - indicates compilation errors
	DiagnosticSeverityWarning     = 2 // Warning severity - indicates potential issues
	DiagnosticSeverityInformation = 3 // Information severity - provides additional info
	DiagnosticSeverityHint        = 4 // Hint severity - suggestions or recommendations
)

// DiagnosticMessageType constants for categorizing diagnostic messages
const (
	DiagnosticMessageTypeCompiler = "compiler" // Messages from compiler
	DiagnosticMessageTypeLinter   = "linter"   // Messages from linter
	DiagnosticMessageTypeAnalyzer = "analyzer" // Messages from code analyzer
	DiagnosticMessageTypeChecker  = "checker"  // Messages from type checker
)

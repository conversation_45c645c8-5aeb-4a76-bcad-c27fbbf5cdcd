// Package protocol implements the Language Server Protocol (LSP) types and interfaces for Go.
package protocol

// TextDocumentClientCapabilities 定义了客户端文本文档功能相关的能力
// TextDocumentClientCapabilities defines which text document capabilities the client supports.
type TextDocumentClientCapabilities struct {
	// Synchronization 定义了客户端支持的同步功能
	// Defines which synchronization capabilities the client supports.
	Synchronization *TextDocumentSyncClientCapabilities `json:"synchronization,omitempty"`

	// Completion 定义了与`textDocument/completion`请求相关的客户端能力
	// Capabilities specific to the `textDocument/completion` request.
	Completion *CompletionClientCapabilities `json:"completion,omitempty"`

	// Hover 定义了与`textDocument/hover`请求相关的客户端能力
	// Capabilities specific to the `textDocument/hover` request.
	Hover *HoverClientCapabilities `json:"hover,omitempty"`

	// SignatureHelp 定义了与`textDocument/signatureHelp`请求相关的客户端能力
	// Capabilities specific to the `textDocument/signatureHelp` request.
	SignatureHelp *SignatureHelpClientCapabilities `json:"signatureHelp,omitempty"`

	// Declaration 定义了与`textDocument/declaration`请求相关的客户端能力
	// Capabilities specific to the `textDocument/declaration` request.
	// @since 3.14.0
	Declaration *DeclarationClientCapabilities `json:"declaration,omitempty"`

	// Definition 定义了与`textDocument/definition`请求相关的客户端能力
	// Capabilities specific to the `textDocument/definition` request.
	Definition *DefinitionClientCapabilities `json:"definition,omitempty"`

	// TypeDefinition 定义了与`textDocument/typeDefinition`请求相关的客户端能力
	// Capabilities specific to the `textDocument/typeDefinition` request.
	// @since 3.6.0
	TypeDefinition *TypeDefinitionClientCapabilities `json:"typeDefinition,omitempty"`

	// Implementation 定义了与`textDocument/implementation`请求相关的客户端能力
	// Capabilities specific to the `textDocument/implementation` request.
	// @since 3.6.0
	Implementation *ImplementationClientCapabilities `json:"implementation,omitempty"`

	// References 定义了与`textDocument/references`请求相关的客户端能力
	// Capabilities specific to the `textDocument/references` request.
	References *ReferenceClientCapabilities `json:"references,omitempty"`

	// DocumentHighlight 定义了与`textDocument/documentHighlight`请求相关的客户端能力
	// Capabilities specific to the `textDocument/documentHighlight` request.
	DocumentHighlight *DocumentHighlightClientCapabilities `json:"documentHighlight,omitempty"`

	// DocumentSymbol 定义了与`textDocument/documentSymbol`请求相关的客户端能力
	// Capabilities specific to the `textDocument/documentSymbol` request.
	DocumentSymbol *DocumentSymbolClientCapabilities `json:"documentSymbol,omitempty"`

	// CodeAction 定义了与`textDocument/codeAction`请求相关的客户端能力
	// Capabilities specific to the `textDocument/codeAction` request.
	CodeAction *CodeActionClientCapabilities `json:"codeAction,omitempty"`

	// CodeLens 定义了与`textDocument/codeLens`请求相关的客户端能力
	// Capabilities specific to the `textDocument/codeLens` request.
	CodeLens *CodeLensClientCapabilities `json:"codeLens,omitempty"`

	// DocumentLink 定义了与`textDocument/documentLink`请求相关的客户端能力
	// Capabilities specific to the `textDocument/documentLink` request.
	DocumentLink *DocumentLinkClientCapabilities `json:"documentLink,omitempty"`

	// ColorProvider 定义了与`textDocument/documentColor`和`textDocument/colorPresentation`请求相关的客户端能力
	// Capabilities specific to the `textDocument/documentColor` and the
	// `textDocument/colorPresentation` request.
	// @since 3.6.0
	ColorProvider *DocumentColorClientCapabilities `json:"colorProvider,omitempty"`

	// Formatting 定义了与`textDocument/formatting`请求相关的客户端能力
	// Capabilities specific to the `textDocument/formatting` request.
	Formatting *DocumentFormattingClientCapabilities `json:"formatting,omitempty"`

	// RangeFormatting 定义了与`textDocument/rangeFormatting`请求相关的客户端能力
	// Capabilities specific to the `textDocument/rangeFormatting` request.
	RangeFormatting *DocumentRangeFormattingClientCapabilities `json:"rangeFormatting,omitempty"`

	// OnTypeFormatting 定义了与`textDocument/onTypeFormatting`请求相关的客户端能力
	// Capabilities specific to the `textDocument/onTypeFormatting` request.
	OnTypeFormatting *DocumentOnTypeFormattingClientCapabilities `json:"onTypeFormatting,omitempty"`

	// Rename 定义了与`textDocument/rename`请求相关的客户端能力
	// Capabilities specific to the `textDocument/rename` request.
	Rename *RenameClientCapabilities `json:"rename,omitempty"`

	// FoldingRange 定义了与`textDocument/foldingRange`请求相关的客户端能力
	// Capabilities specific to the `textDocument/foldingRange` request.
	// @since 3.10.0
	FoldingRange *FoldingRangeClientCapabilities `json:"foldingRange,omitempty"`

	// SelectionRange 定义了与`textDocument/selectionRange`请求相关的客户端能力
	// Capabilities specific to the `textDocument/selectionRange` request.
	// @since 3.15.0
	SelectionRange *SelectionRangeClientCapabilities `json:"selectionRange,omitempty"`

	// PublishDiagnostics 定义了与`textDocument/publishDiagnostics`通知相关的客户端能力
	// Capabilities specific to the `textDocument/publishDiagnostics` notification.
	PublishDiagnostics *PublishDiagnosticsClientCapabilities `json:"publishDiagnostics,omitempty"`

	// CallHierarchy 定义了与各种调用层次结构请求相关的客户端能力
	// Capabilities specific to the various call hierarchy requests.
	// @since 3.16.0
	CallHierarchy *CallHierarchyClientCapabilities `json:"callHierarchy,omitempty"`

	// SemanticTokens 定义了与各种语义标记请求相关的客户端能力
	// Capabilities specific to the various semantic token request.
	// @since 3.16.0
	SemanticTokens *SemanticTokensClientCapabilities `json:"semanticTokens,omitempty"`

	// LinkedEditingRange 定义了与`textDocument/linkedEditingRange`请求相关的客户端能力
	// Capabilities specific to the `textDocument/linkedEditingRange` request.
	// @since 3.16.0
	LinkedEditingRange *LinkedEditingRangeClientCapabilities `json:"linkedEditingRange,omitempty"`

	// Moniker 定义了与`textDocument/moniker`请求相关的客户端能力
	// Client capabilities specific to the `textDocument/moniker` request.
	// @since 3.16.0
	Moniker *MonikerClientCapabilities `json:"moniker,omitempty"`

	// TypeHierarchy 定义了与各种类型层次结构请求相关的客户端能力
	// Capabilities specific to the various type hierarchy requests.
	// @since 3.17.0
	TypeHierarchy *TypeHierarchyClientCapabilities `json:"typeHierarchy,omitempty"`

	// InlineValue 定义了与`textDocument/inlineValue`请求相关的客户端能力
	// Capabilities specific to the `textDocument/inlineValue` request.
	// @since 3.17.0
	InlineValue *InlineValueClientCapabilities `json:"inlineValue,omitempty"`

	// InlayHint 定义了与`textDocument/inlayHint`请求相关的客户端能力
	// Capabilities specific to the `textDocument/inlayHint` request.
	// @since 3.17.0
	InlayHint *InlayHintClientCapabilities `json:"inlayHint,omitempty"`

	// Diagnostic 定义了与诊断拉取模型相关的客户端能力
	// Capabilities specific to the diagnostic pull model.
	// @since 3.17.0
	Diagnostic *DiagnosticClientCapabilities `json:"diagnostic,omitempty"`

	// InlineCompletion 定义了与内联完成相关的客户端能力
	// Client capabilities specific to inline completions.
	// @since 3.18.0
	// @proposed
	InlineCompletion *InlineCompletionClientCapabilities `json:"inlineCompletion,omitempty"`
}

// TextDocumentSyncClientCapabilities 定义了客户端文本文档同步相关的能力
// TextDocumentSyncClientCapabilities defines the client capabilities for text document synchronization.
type TextDocumentSyncClientCapabilities struct {
	// DynamicRegistration 表示文本文档同步是否支持动态注册
	// Whether text document synchronization supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// WillSave 表示客户端是否支持发送willSave通知
	// The client supports sending will save notifications.
	WillSave bool `json:"willSave,omitempty"`

	// WillSaveWaitUntil 表示客户端是否支持发送willSaveWaitUntil请求并等待响应
	// The client supports sending a will save request and
	// waits for a response providing text edits which will
	// be applied to the document before it is saved.
	WillSaveWaitUntil bool `json:"willSaveWaitUntil,omitempty"`

	// DidSave 表示客户端是否支持发送didSave通知
	// The client supports did save notifications.
	DidSave bool `json:"didSave,omitempty"`
}

// MarkupKind 描述了标记内容的格式
// MarkupKind describes the format of marked p content.
type MarkupKind string

const (
	// PlainText 表示内容为纯文本
	// PlainText represents plain text content.
	PlainText MarkupKind = "plaintext"
	// Markdown 表示内容为 Markdown 格式
	// Markdown represents Markdown content.
	Markdown MarkupKind = "markdown"
)

// CompletionItemTag 表示特定的代码补全项标签
// CompletionItemTag represents a specific tag for a completion item.
type CompletionItemTag int32

const (
	// Deprecated 表示此补全项已弃用
	// Deprecated marks a completion item as deprecated.
	Deprecated CompletionItemTag = 1
)

// InsertTextMode 定义了代码补全时插入文本的模式
// InsertTextMode defines the modes for inserting text from a completion item.
type InsertTextMode uint32

const (
	// AsIs 表示插入文本时应保持原样
	// AsIs means the text should be inserted as is.
	AsIs InsertTextMode = 1
	// AdjustIndentation 表示插入文本时应调整缩进
	// AdjustIndentation means the text should be inserted with adjusted indentation.
	AdjustIndentation InsertTextMode = 2
)

// CompletionItemKind 定义了代码补全项的类型
// CompletionItemKind defines the kind of a completion item.
type CompletionItemKind uint32

// Completion item kinds
const (
	TextCompletion          CompletionItemKind = 1
	MethodCompletion        CompletionItemKind = 2
	FunctionCompletion      CompletionItemKind = 3
	ConstructorCompletion   CompletionItemKind = 4
	FieldCompletion         CompletionItemKind = 5
	VariableCompletion      CompletionItemKind = 6
	ClassCompletion         CompletionItemKind = 7
	InterfaceCompletion     CompletionItemKind = 8
	ModuleCompletion        CompletionItemKind = 9
	PropertyCompletion      CompletionItemKind = 10
	UnitCompletion          CompletionItemKind = 11
	ValueCompletion         CompletionItemKind = 12
	EnumCompletion          CompletionItemKind = 13
	KeywordCompletion       CompletionItemKind = 14
	SnippetCompletion       CompletionItemKind = 15
	ColorCompletion         CompletionItemKind = 16
	FileCompletion          CompletionItemKind = 17
	ReferenceCompletion     CompletionItemKind = 18
	FolderCompletion        CompletionItemKind = 19
	EnumMemberCompletion    CompletionItemKind = 20
	ConstantCompletion      CompletionItemKind = 21
	StructCompletion        CompletionItemKind = 22
	EventCompletion         CompletionItemKind = 23
	OperatorCompletion      CompletionItemKind = 24
	TypeParameterCompletion CompletionItemKind = 25
)

// SymbolKind 定义了文档符号的类型
// SymbolKind defines the kind of a document symbol.
type SymbolKind uint32

// Symbol kinds
const (
	FileSymbol          SymbolKind = 1
	ModuleSymbol        SymbolKind = 2
	NamespaceSymbol     SymbolKind = 3
	PackageSymbol       SymbolKind = 4
	ClassSymbol         SymbolKind = 5
	MethodSymbol        SymbolKind = 6
	PropertySymbol      SymbolKind = 7
	FieldSymbol         SymbolKind = 8
	ConstructorSymbol   SymbolKind = 9
	EnumSymbol          SymbolKind = 10
	InterfaceSymbol     SymbolKind = 11
	FunctionSymbol      SymbolKind = 12
	VariableSymbol      SymbolKind = 13
	ConstantSymbol      SymbolKind = 14
	StringSymbol        SymbolKind = 15
	NumberSymbol        SymbolKind = 16
	BooleanSymbol       SymbolKind = 17
	ArraySymbol         SymbolKind = 18
	ObjectSymbol        SymbolKind = 19
	KeySymbol           SymbolKind = 20
	NullSymbol          SymbolKind = 21
	EnumMemberSymbol    SymbolKind = 22
	StructSymbol        SymbolKind = 23
	EventSymbol         SymbolKind = 24
	OperatorSymbol      SymbolKind = 25
	TypeParameterSymbol SymbolKind = 26
)

// SymbolTag 定义了文档符号的标签
// SymbolTag defines tags for document symbols.
type SymbolTag int32

const (
	// DeprecatedSymbolTag 表示符号已弃用
	// DeprecatedSymbolTag marks a symbol as deprecated.
	DeprecatedSymbolTag SymbolTag = 1
)

// CodeActionKind 是表示代码操作类型的字符串
// CodeActionKind is a string representing the type of a code action.
type CodeActionKind string

const (
	EmptyCodeAction             CodeActionKind = ""
	QuickFixCodeAction          CodeActionKind = "quickfix"                 // 可用于快速修复的 CodeAction
	RefactorCodeAction          CodeActionKind = "refactor"                 // 可用于重构的 CodeAction
	RefactorExtractCodeAction   CodeActionKind = "refactor.extract"         // 用于提取代码（如提取函数、变量）的 CodeAction
	RefactorRewriteCodeAction   CodeActionKind = "refactor.rewrite"         // 用于重写代码（如将循环转换为函数式风格）的 CodeAction
	RefactorInlineCodeAction    CodeActionKind = "refactor.inline"          // 用于内联代码（如内联函数、变量）的 CodeAction
	RefactorMoveCodeAction      CodeActionKind = "refactor.move"            // 从一个位置或文件移动到另一个位置或文件的 CodeAction
	SourceCodeAction            CodeActionKind = "source"                   // 用于源代码操作的 CodeAction
	SourceOrganizeImportsAction CodeActionKind = "source.organizeImports"   // 用于组织导入的 CodeAction
	SourceFixAllAction          CodeActionKind = "source.fixAll"            // 用于修复项目中所有可修复问题的 CodeAction
	SourceAddMissingImports     CodeActionKind = "source.addMissingImports" // 自动添加所有缺失导入的 CodeAction
)

// PrepareSupportDefaultBehavior 定义了 RenamePrepare 请求的默认行为
// PrepareSupportDefaultBehavior defines default behavior for `textDocument/prepareRename` requests.
type PrepareSupportDefaultBehavior int32

const (
	// IdentifierBehavior 表示默认行为应用于标识符
	// IdentifierBehavior means the default behavior applies to identifiers.
	IdentifierBehavior PrepareSupportDefaultBehavior = 1
)

// FoldingRangeKind 是表示折叠范围类型的字符串 (例如 'comment', 'imports', 'region')
// FoldingRangeKind is a string representing the type of a folding range (e.g., 'comment', 'imports', 'region').
type FoldingRangeKind string

const (
	CommentFoldingRange FoldingRangeKind = "comment" // 表示注释块的折叠范围
	ImportsFoldingRange FoldingRangeKind = "imports" // 表示导入块的折叠范围
	RegionFoldingRange  FoldingRangeKind = "region"  // 表示代码区域块的折叠范围
)

// DiagnosticTag 表示诊断信息的标签
// DiagnosticTag represents a tag for diagnostic information.
type DiagnosticTag int32

const (
	// Unnecessary 表示不必要的代码
	// Unnecessary indicates unnecessary code.
	Unnecessary DiagnosticTag = 1
	// DeprecatedDiagnosticTag 表示已弃用的代码
	// DeprecatedDiagnosticTag indicates deprecated code.
	DeprecatedDiagnosticTag DiagnosticTag = 2
)

// TokenFormat 是表示语义标记格式的字符串 (目前只有 "relative")
// TokenFormat is a string representing the format of semantic tokens (currently only "relative").
type TokenFormat string

const (
	// RelativeFormat 指示语义标记使用相对格式
	// RelativeFormat indicates that semantic tokens use a relative format.
	RelativeFormat TokenFormat = "relative"
)

// CompletionItemTagSupport 指定客户端是否支持补全项标签
// CompletionItemTagSupport specifies client capabilities for completion item tags.
type CompletionItemTagSupport struct {
	// ValueSet 客户端支持的标签类型集合
	// The client supports the following completion item tags.
	ValueSet []CompletionItemTag `json:"valueSet"`
}

// CompletionItemResolveSupport 指定客户端是否支持解析额外的补全项信息
// CompletionItemResolveSupport specifies client capabilities for resolving additional completion item information.
type CompletionItemResolveSupport struct {
	// Properties 客户端支持解析的属性列表
	// The properties that a client can resolve lazily.
	Properties []string `json:"properties"`
}

// CompletionItemInsertTextModeSupport 指定客户端支持的插入文本模式
// CompletionItemInsertTextModeSupport specifies client capabilities for insert text mode.
type CompletionItemInsertTextModeSupport struct {
	// ValueSet 客户端支持的 InsertTextMode 集合
	// The client supports the following InsertTextMode values.
	ValueSet []InsertTextMode `json:"valueSet"`
}

// CompletionItemClientCapabilities 定义了与补全项相关的客户端能力
// CompletionItemClientCapabilities defines capabilities specific to a completion item.
type CompletionItemClientCapabilities struct {
	// SnippetSupport 客户端是否支持代码片段补全项
	// Client supports snippets as insert text.
	SnippetSupport bool `json:"snippetSupport,omitempty"`

	// CommitCharactersSupport 客户端是否支持补全项的提交字符
	// Client supports commit characters on a completion item.
	CommitCharactersSupport bool `json:"commitCharactersSupport,omitempty"`

	// DocumentationFormat 客户端支持的文档格式 (例如 'plaintext', 'markdown')
	// Documentation format supported by the client.
	DocumentationFormat []MarkupKind `json:"documentationFormat,omitempty"`

	// DeprecatedSupport 客户端是否支持已弃用的补全项
	// Client supports deprecated completion items.
	DeprecatedSupport bool `json:"deprecatedSupport,omitempty"`

	// PreselectSupport 客户端是否支持预选的补全项
	// Client supports preselect property on a completion item.
	PreselectSupport bool `json:"preselectSupport,omitempty"`

	// TagSupport 客户端对补全项标签的支持情况
	// Client supports completion item tags.
	// @since 3.15.0
	TagSupport *CompletionItemTagSupport `json:"tagSupport,omitempty"`

	// InsertReplaceSupport 客户端是否支持插入和替换补全项
	// Client supports insert replace edit to control different behavior if a completion item is inserted in the
	// middle of a word already.
	// @since 3.16.0
	InsertReplaceSupport bool `json:"insertReplaceSupport,omitempty"`

	// ResolveSupport 客户端对补全项解析的支持情况
	// Indicates which properties a client can resolve lazily on a completion item.
	// @since 3.16.0
	ResolveSupport *CompletionItemResolveSupport `json:"resolveSupport,omitempty"`

	// InsertTextModeSupport 客户端对插入文本模式的支持情况
	// The client supports the `insertTextMode` property on a completion item to override the whitespace handling mode
	// of the client.
	// @since 3.16.0
	InsertTextModeSupport *CompletionItemInsertTextModeSupport `json:"insertTextModeSupport,omitempty"`

	// LabelDetailsSupport 客户端是否支持补全项的标签详情
	// The client supports label details on a completion item.
	// @since 3.17.0
	LabelDetailsSupport bool `json:"labelDetailsSupport,omitempty"`
}

// CompletionItemKindClientCapabilities 定义了客户端对特定补全项类型的支持
// CompletionItemKindClientCapabilities defines capabilities for specific completion item kinds.
type CompletionItemKindClientCapabilities struct {
	// ValueSet 客户端支持的 CompletionItemKind 集合
	// The completion item kind values the client supports. When this
	// property exists the client also guarantees that it will
	// handle values outside its set gracefully and falls back
	// to a default value when unknown.
	//
	// If this property is not present the client only supports
	// the completion items kinds from `Text` to `Reference` as defined in
	// the initial version of the protocol.
	ValueSet []CompletionItemKind `json:"valueSet,omitempty"`
}

// CompletionListCapabilities 定义了客户端对补全列表特定功能的支持
// CompletionListCapabilities defines capabilities for completion list specific features requested by the client.
// @since 3.17.0
type CompletionListCapabilities struct {
	// ItemDefaults 客户端支持在补全列表中为补全项设置的默认值。
	// The client supports the following itemDefaults on
	// a completion list.
	//
	// The value lists the supported property names of the
	// `CompletionList.itemDefaults` object. If omitted
	// no properties are supported.
	//
	// @since 3.17.0
	ItemDefaults []string `json:"itemDefaults,omitempty"`
}

// CompletionClientCapabilities 定义了与 `textDocument/completion` 请求相关的客户端能力
// CompletionClientCapabilities defines capabilities specific to the `textDocument/completion` request.
type CompletionClientCapabilities struct {
	// DynamicRegistration Completion 请求是否支持动态注册
	// Whether completion supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// CompletionItem 与补全项相关的特定能力
	// The client supports the following `CompletionItem` specific
	// capabilities.
	CompletionItem *CompletionItemClientCapabilities `json:"completionItem,omitempty"`

	// CompletionItemKind 与补全项类型相关的特定能力
	CompletionItemKind *CompletionItemKindClientCapabilities `json:"completionItemKind,omitempty"`

	// ContextSupport 客户端是否在补全请求中发送上下文信息
	// The client supports to send additional context information for a
	// `textDocument/completion` request.
	ContextSupport bool `json:"contextSupport,omitempty"`

	// InsertTextMode 客户端默认的插入文本模式
	// The client's default insertion text mode.
	// @since 3.17.0
	InsertTextMode *InsertTextMode `json:"insertTextMode,omitempty"`

	// CompletionList 客户端对补全列表特定功能的支持
	// The client supports the following capabilities for `CompletionList` items.
	// @since 3.17.0
	CompletionList *CompletionListCapabilities `json:"completionList,omitempty"`
}

// HoverClientCapabilities 定义了与 `textDocument/hover` 请求相关的客户端能力
// HoverClientCapabilities defines capabilities specific to the `textDocument/hover` request.
type HoverClientCapabilities struct {
	// DynamicRegistration Hover 请求是否支持动态注册
	// Whether hover supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// ContentFormat 客户端支持的内容格式 (例如 'plaintext', 'markdown')
	// Client supports the follow content formats for the content
	// property. The order describes the preferred format of the client.
	ContentFormat []MarkupKind `json:"contentFormat,omitempty"`
}

// ParameterInformationClientCapabilities 定义了参数信息的客户端能力
// ParameterInformationClientCapabilities defines capabilities specific to parameter information.
type ParameterInformationClientCapabilities struct {
	// LabelOffsetSupport 客户端是否支持参数标签的偏移量
	// The client supports processing label offsets instead of a
	// simple label string.
	// @since 3.14.0
	LabelOffsetSupport bool `json:"labelOffsetSupport,omitempty"`
}

// SignatureInformationClientCapabilities 定义了签名帮助中签名信息的客户端能力
// SignatureInformationClientCapabilities defines capabilities specific to signature information in signature help.
type SignatureInformationClientCapabilities struct {
	// DocumentationFormat 客户端支持的文档格式
	// Client supports the follow content formats for the documentation
	// property. The order describes the preferred format of the client.
	DocumentationFormat []MarkupKind `json:"documentationFormat,omitempty"`

	// ParameterInformation 参数信息相关的客户端能力
	// Client capabilities specific to parameter information.
	ParameterInformation *ParameterInformationClientCapabilities `json:"parameterInformation,omitempty"`

	// ActiveParameterSupport 客户端是否支持活动参数高亮
	// The client supports the `activeParameter` property on `SignatureInformation`.
	// @since 3.16.0
	ActiveParameterSupport bool `json:"activeParameterSupport,omitempty"`
}

// SignatureHelpClientCapabilities 定义了与 `textDocument/signatureHelp` 请求相关的客户端能力
// SignatureHelpClientCapabilities defines capabilities specific to the `textDocument/signatureHelp` request.
type SignatureHelpClientCapabilities struct {
	// DynamicRegistration SignatureHelp 请求是否支持动态注册
	// Whether signature help supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// SignatureInformation 与签名信息相关的特定能力
	// The client supports the following `SignatureInformation`
	// specific properties.
	SignatureInformation *SignatureInformationClientCapabilities `json:"signatureInformation,omitempty"`

	// ContextSupport 客户端是否在签名帮助请求中发送上下文信息
	// The client supports to send additional context information for a
	// `textDocument/signatureHelp` request.
	// @since 3.15.0
	ContextSupport bool `json:"contextSupport,omitempty"`
}

// DeclarationClientCapabilities 定义了与 `textDocument/declaration` 请求相关的客户端能力
// DeclarationClientCapabilities defines capabilities specific to the `textDocument/declaration` request.
// @since 3.14.0
type DeclarationClientCapabilities struct {
	// DynamicRegistration Declaration 请求是否支持动态注册
	// Whether declaration supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// LinkSupport 客户端是否支持链接定义
	// The client supports additional metadata in the form of declaration links.
	LinkSupport bool `json:"linkSupport,omitempty"`
}

// DefinitionClientCapabilities 定义了与 `textDocument/definition` 请求相关的客户端能力
// DefinitionClientCapabilities defines capabilities specific to the `textDocument/definition` request.
type DefinitionClientCapabilities struct {
	// DynamicRegistration Definition 请求是否支持动态注册
	// Whether definition supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// LinkSupport 客户端是否支持链接定义
	// The client supports additional metadata in the form of definition links.
	// @since 3.14.0
	LinkSupport bool `json:"linkSupport,omitempty"`
}

// TypeDefinitionClientCapabilities 定义了与 `textDocument/typeDefinition` 请求相关的客户端能力
// TypeDefinitionClientCapabilities defines capabilities specific to the `textDocument/typeDefinition` request.
// @since 3.6.0
type TypeDefinitionClientCapabilities struct {
	// DynamicRegistration TypeDefinition 请求是否支持动态注册
	// Whether type definition supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// LinkSupport 客户端是否支持链接定义
	// The client supports additional metadata in the form of definition links.
	// @since 3.14.0
	LinkSupport bool `json:"linkSupport,omitempty"`
}

// ImplementationClientCapabilities 定义了与 `textDocument/implementation` 请求相关的客户端能力
// ImplementationClientCapabilities defines capabilities specific to the `textDocument/implementation` request.
// @since 3.6.0
type ImplementationClientCapabilities struct {
	// DynamicRegistration Implementation 请求是否支持动态注册
	// Whether implementation supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// LinkSupport 客户端是否支持链接定义
	// The client supports additional metadata in the form of definition links.
	// @since 3.14.0
	LinkSupport bool `json:"linkSupport,omitempty"`
}

// ReferenceClientCapabilities 定义了与 `textDocument/references` 请求相关的客户端能力
// ReferenceClientCapabilities defines capabilities specific to the `textDocument/references` request.
type ReferenceClientCapabilities struct {
	// DynamicRegistration References 请求是否支持动态注册
	// Whether references supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentHighlightClientCapabilities 定义了与 `textDocument/documentHighlight` 请求相关的客户端能力
// DocumentHighlightClientCapabilities defines capabilities specific to the `textDocument/documentHighlight` request.
type DocumentHighlightClientCapabilities struct {
	// DynamicRegistration DocumentHighlight 请求是否支持动态注册
	// Whether document highlight supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentSymbolKindClientCapabilities 定义了客户端对特定文档符号类型的支持
// DocumentSymbolKindClientCapabilities defines capabilities for specific document symbol kinds.
type DocumentSymbolKindClientCapabilities struct {
	// ValueSet 客户端支持的 SymbolKind 集合
	// The symbol kind values the client supports. When this
	// property exists the client also guarantees that it will
	// handle values outside its set gracefully and falls back
	// to a default value when unknown.
	//
	// If this property is not present the client only supports
	// the symbol kinds from `File` to `Array` as defined in
	// the initial version of the protocol.
	ValueSet []SymbolKind `json:"valueSet,omitempty"`
}

// DocumentSymbolTagSupportClientCapabilities 指定客户端是否支持文档符号标签
// DocumentSymbolTagSupportClientCapabilities specifies client capabilities for document symbol tags.
type DocumentSymbolTagSupportClientCapabilities struct {
	// ValueSet 客户端支持的标签类型集合
	// The client supports the following symbol tags.
	ValueSet []SymbolTag `json:"valueSet"`
}

// DocumentSymbolClientCapabilities 定义了与 `textDocument/documentSymbol` 请求相关的客户端能力
// DocumentSymbolClientCapabilities defines capabilities specific to the `textDocument/documentSymbol` request.
type DocumentSymbolClientCapabilities struct {
	// DynamicRegistration DocumentSymbol 请求是否支持动态注册
	// Whether document symbol supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// SymbolKind 与文档符号类型相关的特定能力
	// Specific capabilities for the `SymbolKind` in the
	// `textDocument/documentSymbol` request.
	SymbolKind *DocumentSymbolKindClientCapabilities `json:"symbolKind,omitempty"`

	// HierarchicalDocumentSymbolSupport 客户端是否支持层级结构的文档符号
	// The client supports hierarchical document symbols.
	HierarchicalDocumentSymbolSupport bool `json:"hierarchicalDocumentSymbolSupport,omitempty"`

	// TagSupport 客户端对文档符号标签的支持情况
	// The client supports tags on `SymbolInformation`. Tags are supported on
	// `DocumentSymbol` if `hierarchicalDocumentSymbolSupport` is set to true.
	// Clients supporting tags have to handle unknown tags gracefully.
	// @since 3.16.0
	TagSupport *DocumentSymbolTagSupportClientCapabilities `json:"tagSupport,omitempty"`

	// LabelSupport 客户端是否支持文档符号的标签
	// The client supports an additional label presented in the UI when resolving
	// a symbol default in the hierarchy.
	// @since 3.16.0
	LabelSupport bool `json:"labelSupport,omitempty"`
}

// CodeActionKindClientCapabilities 定义了客户端对特定代码操作类型的支持
// CodeActionKindClientCapabilities defines capabilities for specific code action kinds.
type CodeActionKindClientCapabilities struct {
	// ValueSet 客户端支持的 CodeActionKind 集合
	// The code action kind values the client supports. When this
	// property exists the client also guarantees that it will
	// handle values outside its set gracefully and falls back
	// to a default value when unknown.
	ValueSet []CodeActionKind `json:"valueSet"`
}

// CodeActionLiteralSupportClientCapabilities 定义了客户端对代码操作字面量的支持
// CodeActionLiteralSupportClientCapabilities defines capabilities for code action literals.
type CodeActionLiteralSupportClientCapabilities struct {
	// CodeActionKind 与代码操作类型相关的特定能力
	// The code action kind is support with the following value
	// set.
	CodeActionKind CodeActionKindClientCapabilities `json:"codeActionKind"`
}

// CodeActionResolveClientSupport 定义了客户端对代码操作解析的支持
// CodeActionResolveClientSupport defines capabilities for resolving code actions.
type CodeActionResolveClientSupport struct {
	// Properties 客户端支持解析的CodeAction属性列表
	// The properties that a client can resolve lazily.
	Properties []string `json:"properties"`
}

// CodeActionClientCapabilities 定义了与 `textDocument/codeAction` 请求相关的客户端能力
// CodeActionClientCapabilities defines capabilities specific to the `textDocument/codeAction` request.
type CodeActionClientCapabilities struct {
	// DynamicRegistration CodeAction 请求是否支持动态注册
	// Whether code action supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// CodeActionLiteralSupport 与代码操作字面量相关的特定能力
	// The client support code action literals as a valid
	// response of the `textDocument/codeAction` request.
	// @since 3.8.0
	CodeActionLiteralSupport *CodeActionLiteralSupportClientCapabilities `json:"codeActionLiteralSupport,omitempty"`

	// IsPreferredSupport 客户端是否支持 `isPreferred` 属性
	// Whether code action supports the `isPreferred` property.
	// @since 3.15.0
	IsPreferredSupport bool `json:"isPreferredSupport,omitempty"`

	// DisabledSupport 客户端是否支持禁用代码操作的原因
	// Whether code action supports the `disabled` property.
	// @since 3.16.0
	DisabledSupport bool `json:"disabledSupport,omitempty"`

	// DataSupport 客户端是否支持代码操作的 `data` 属性
	// Whether code action supports the `data` property which is
	// preserved between a `textDocument/codeAction` and a
	// `codeAction/resolve` request.
	// @since 3.16.0
	DataSupport bool `json:"dataSupport,omitempty"`

	// ResolveSupport 客户端对`codeAction/resolve`请求的支持情况
	// Whether the client supports resolving additional code action
	// properties via a separate `codeAction/resolve` request.
	// @since 3.16.0
	ResolveSupport *CodeActionResolveClientSupport `json:"resolveSupport,omitempty"`

	// HonorsChangeAnnotations 客户端是否处理 `CodeAction` 中的 `changeAnnotations`
	// Whether the client honors the change annotations in
	// text edits and resource operations returned via the
	// rename request.
	// @since 3.16.0
	HonorsChangeAnnotations bool `json:"honorsChangeAnnotations,omitempty"`
}

// CodeLensClientCapabilities 定义了与 `textDocument/codeLens` 请求相关的客户端能力
// CodeLensClientCapabilities defines capabilities specific to the `textDocument/codeLens` request.
type CodeLensClientCapabilities struct {
	// DynamicRegistration CodeLens 请求是否支持动态注册
	// Whether code lens supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentLinkClientCapabilities 定义了与 `textDocument/documentLink` 请求相关的客户端能力
// DocumentLinkClientCapabilities defines capabilities specific to the `textDocument/documentLink` request.
type DocumentLinkClientCapabilities struct {
	// DynamicRegistration DocumentLink 请求是否支持动态注册
	// Whether document link supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// TooltipSupport 客户端是否支持文档链接的悬浮提示信息
	// Whether the client supports the `tooltip` property on `DocumentLink`.
	// @since 3.15.0
	TooltipSupport bool `json:"tooltipSupport,omitempty"`
}

// DocumentColorClientCapabilities 定义了与 `textDocument/documentColor` 和 `textDocument/colorPresentation` 请求相关的客户端能力
// DocumentColorClientCapabilities defines capabilities specific to the `textDocument/documentColor` and `textDocument/colorPresentation` requests.
// @since 3.6.0
type DocumentColorClientCapabilities struct {
	// DynamicRegistration ColorProvider 请求是否支持动态注册
	// Whether colorProvider supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentFormattingClientCapabilities 定义了与 `textDocument/formatting` 请求相关的客户端能力
// DocumentFormattingClientCapabilities defines capabilities specific to the `textDocument/formatting` request.
type DocumentFormattingClientCapabilities struct {
	// DynamicRegistration Formatting 请求是否支持动态注册
	// Whether formatting supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentRangeFormattingClientCapabilities 定义了与 `textDocument/rangeFormatting` 请求相关的客户端能力
// DocumentRangeFormattingClientCapabilities defines capabilities specific to the `textDocument/rangeFormatting` request.
type DocumentRangeFormattingClientCapabilities struct {
	// DynamicRegistration RangeFormatting 请求是否支持动态注册
	// Whether range formatting supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DocumentOnTypeFormattingClientCapabilities 定义了与 `textDocument/onTypeFormatting` 请求相关的客户端能力
// DocumentOnTypeFormattingClientCapabilities defines capabilities specific to the `textDocument/onTypeFormatting` request.
type DocumentOnTypeFormattingClientCapabilities struct {
	// DynamicRegistration OnTypeFormatting 请求是否支持动态注册
	// Whether on type formatting supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// RenameClientCapabilities 定义了与 `textDocument/rename` 请求相关的客户端能力
// RenameClientCapabilities defines capabilities specific to the `textDocument/rename` request.
type RenameClientCapabilities struct {
	// DynamicRegistration Rename 请求是否支持动态注册
	// Whether rename supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// PrepareSupport 客户端是否支持 `textDocument/prepareRename` 请求
	// Client supports testing for validity of rename operations
	// before execution.
	// @since 3.12.0
	PrepareSupport bool `json:"prepareSupport,omitempty"`

	// PrepareSupportDefaultBehavior 针对 `textDocument/prepareRename` 请求的默认行为支持
	// Client supports the default behavior result (`defaultBehavior: boolean`)
	// from a `textDocument/prepareRename` request.
	// @since 3.16.0
	PrepareSupportDefaultBehavior *PrepareSupportDefaultBehavior `json:"prepareSupportDefaultBehavior,omitempty"`

	// HonorsChangeAnnotations 客户端是否处理 RenameEdit 中的 `changeAnnotations`
	// Whether the client honors the change annotations in
	// text edits and resource operations returned via the
	// rename request.
	// @since 3.16.0
	HonorsChangeAnnotations bool `json:"honorsChangeAnnotations,omitempty"`
}

// FoldingRangeKindClientCapabilities 定义了折叠范围类型的客户端能力
// FoldingRangeKindClientCapabilities capabilities specific to `FoldingRangeKind`.
type FoldingRangeKindClientCapabilities struct {
	// ValueSet 客户端支持的折叠范围类型集合
	// The folding range kind values the client supports. When this
	// property exists the client also guarantees that it will
	// handle values outside its set gracefully and falls back
	// to a default value when unknown.
	ValueSet []FoldingRangeKind `json:"valueSet,omitempty"`
}

// FoldingRangeClientCapabilitiesSpecific 指定了折叠范围请求的特定客户端能力
// FoldingRangeClientCapabilitiesSpecific defines capabilities specific to folding range requests.
type FoldingRangeClientCapabilitiesSpecific struct {
	// CollapsedText 客户端是否支持折叠范围的 `collapsedText` 属性
	// If set, the client signals that it supports setting collapsedText on
	// folding ranges to display custom labels instead of the default text.
	// @since 3.17.0
	CollapsedText bool `json:"collapsedText,omitempty"`
}

// FoldingRangeClientCapabilities 定义了与 `textDocument/foldingRange` 请求相关的客户端能力
// FoldingRangeClientCapabilities defines capabilities specific to the `textDocument/foldingRange` request.
// @since 3.10.0
type FoldingRangeClientCapabilities struct {
	// DynamicRegistration FoldingRange 请求是否支持动态注册
	// Whether folding range supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// RangeLimit 客户端支持的最大折叠范围数量
	// The maximum number of folding ranges that the client prefers to receive per document.
	// The value serves as a hint, servers are free to follow the limit.
	RangeLimit *uint32 `json:"rangeLimit,omitempty"`

	// LineFoldingOnly 客户端是否只支持基于行的折叠
	// If set, the client signals that it only supports folding complete lines.
	// If unset or false, the client supports folding ranges that span partial lines.
	LineFoldingOnly bool `json:"lineFoldingOnly,omitempty"`

	// FoldingRangeKind 特定折叠范围类型的能力
	// Specific options for the folding range kind.
	// @since 3.17.0
	FoldingRangeKind *FoldingRangeKindClientCapabilities `json:"foldingRangeKind,omitempty"`

	// FoldingRange 特定折叠范围请求的能力
	// Specific options for the folding range.
	// @since 3.17.0
	FoldingRange *FoldingRangeClientCapabilitiesSpecific `json:"foldingRange,omitempty"`
}

// SelectionRangeClientCapabilities 定义了与 `textDocument/selectionRange` 请求相关的客户端能力
// SelectionRangeClientCapabilities defines capabilities specific to the `textDocument/selectionRange` request.
// @since 3.15.0
type SelectionRangeClientCapabilities struct {
	// DynamicRegistration SelectionRange 请求是否支持动态注册
	// Whether selection range supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// DiagnosticTagSupportClientCapabilities 定义了诊断标签的客户端能力
// DiagnosticTagSupportClientCapabilities capabilities specific to `DiagnosticTag`.
type DiagnosticTagSupportClientCapabilities struct {
	// ValueSet 客户端支持的诊断标签类型集合
	// The tags supported by the client.
	ValueSet []DiagnosticTag `json:"valueSet"`
}

// PublishDiagnosticsClientCapabilities 定义了与 `textDocument/publishDiagnostics` 通知相关的客户端能力
// PublishDiagnosticsClientCapabilities defines capabilities specific to the `textDocument/publishDiagnostics` notification.
type PublishDiagnosticsClientCapabilities struct {
	// RelatedInformation 客户端是否支持诊断的关联信息
	// Whether the clients accepts diagnostics with related information.
	RelatedInformation bool `json:"relatedInformation,omitempty"`

	// TagSupport 客户端对诊断标签的支持情况
	// Client supports the following diagnostic tags.
	// @since 3.15.0
	TagSupport *DiagnosticTagSupportClientCapabilities `json:"tagSupport,omitempty"`

	// VersionSupport 客户端是否接受包含版本号的诊断信息
	// Whether the client interprets the version property of the
	// `textDocument/publishDiagnostics` notification's parameter.
	// @since 3.15.0
	VersionSupport bool `json:"versionSupport,omitempty"`

	// CodeDescriptionSupport 客户端是否支持诊断的代码描述
	// Client supports a codeDescription property
	// @since 3.16.0
	CodeDescriptionSupport bool `json:"codeDescriptionSupport,omitempty"`

	// DataSupport 客户端是否支持诊断的 `data` 属性
	// Whether code action supports the `data` property which is
	// preserved between a `textDocument/publishDiagnostics` and `textDocument/codeAction` request.
	// @since 3.16.0
	DataSupport bool `json:"dataSupport,omitempty"`
}

// CallHierarchyClientCapabilities 定义了与各种调用层次结构请求相关的客户端能力
// CallHierarchyClientCapabilities defines capabilities specific to the various call hierarchy requests.
// @since 3.16.0
type CallHierarchyClientCapabilities struct {
	// DynamicRegistration CallHierarchy 请求是否支持动态注册
	// Whether call hierarchy supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// SemanticTokensRangeClientCapabilities是一个空结构体，表示客户端对范围语义标记请求的支持
// SemanticTokensRangeClientCapabilities is an empty struct indicating support for range semantic token requests by the client.
type SemanticTokensRangeClientCapabilities struct{}

// SemanticTokensFullClientCapabilities 定义了全量语义标记请求的客户端能力
// SemanticTokensFullClientCapabilities defines client capabilities for full semantic token requests.
type SemanticTokensFullClientCapabilities struct {
	// Delta 客户端是否支持全量语义标记的增量更新
	// The client supports deltas for full documents.
	Delta bool `json:"delta,omitempty"`
}

// SemanticTokensRequestsClientCapabilities 定义了语义标记请求的客户端能力
// SemanticTokensRequestsClientCapabilities defines capabilities for semantic token requests.
type SemanticTokensRequestsClientCapabilities struct {
	// Range 客户端对范围语义标记请求的支持。可以是一个布尔值或一个空对象。
	// The client will send the `textDocument/semanticTokens/range` request if the server provides a corresponding capability.
	Range *SemanticTokensRangeClientCapabilities `json:"range,omitempty"` // For `boolean | {}`, using a pointer to an empty struct. Non-nil means supported.

	// Full 客户端对全量语义标记请求的支持。可以是一个布尔值或包含delta支持的对象。
	// The client will send the `textDocument/semanticTokens/full` request if the server provides a corresponding capability.
	Full *SemanticTokensFullClientCapabilities `json:"full,omitempty"` // For `boolean | { delta?: boolean }`
}

// SemanticTokensClientCapabilities 定义了与各种语义标记请求相关的客户端能力
// SemanticTokensClientCapabilities defines capabilities specific to the various semantic token requests.
// @since 3.16.0
type SemanticTokensClientCapabilities struct {
	// DynamicRegistration SemanticTokens 请求是否支持动态注册
	// Whether semantic tokens supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// Requests 客户端对不同类型语义标记请求的支持
	// Which requests regarding semantic tokens are supported and what
	// capabilities do they have.
	Requests SemanticTokensRequestsClientCapabilities `json:"requests"`

	// TokenTypes 客户端支持的语义标记类型列表
	// The token types that the client supports.
	TokenTypes []string `json:"tokenTypes"`

	// TokenModifiers 客户端支持的语义标记修饰符列表
	// The token modifiers that the client supports.
	TokenModifiers []string `json:"tokenModifiers"`

	// Formats 客户端支持的语义标记格式列表 (例如 "relative")
	// The formats the clients supports.
	Formats []TokenFormat `json:"formats"`

	// OverlappingTokenSupport 客户端是否支持重叠的语义标记
	// Whether the client supports tokens that can overlap each other.
	OverlappingTokenSupport bool `json:"overlappingTokenSupport,omitempty"`

	// MultilineTokenSupport 客户端是否支持跨越多行的语义标记
	// Whether the client supports tokens that can span multiple lines.
	MultilineTokenSupport bool `json:"multilineTokenSupport,omitempty"`

	// ServerCancelSupport 客户端是否支持服务器取消语义标记请求 (已废弃，请使用 general.serverCancelSupport)
	// Whether the client allows the server to actively cancel a
	// semantic token request, e.g. if a new event occurred.
	// @deprecated use general.serverCancelSupport instead
	ServerCancelSupport bool `json:"serverCancelSupport,omitempty"`

	// AugmentsSyntaxTokens 客户端是否支持使用语义标记增强语法标记
	// Whether the client uses semantic tokens to augment existing
	// syntax tokens. If set to `true` client side created syntax tokens
	// and semantic tokens are both used for colorization. If set to `false`
	// the client only uses the returned semantic tokens for colorization.
	// @since 3.17.0
	AugmentsSyntaxTokens bool `json:"augmentsSyntaxTokens,omitempty"`
}

// LinkedEditingRangeClientCapabilities 定义了与 `textDocument/linkedEditingRange` 请求相关的客户端能力
// LinkedEditingRangeClientCapabilities defines capabilities specific to the `textDocument/linkedEditingRange` request.
// @since 3.16.0
type LinkedEditingRangeClientCapabilities struct {
	// DynamicRegistration LinkedEditingRange 请求是否支持动态注册
	// Whether linked editing range supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// MonikerClientCapabilities 定义了与 `textDocument/moniker` 请求相关的客户端能力
// MonikerClientCapabilities defines capabilities specific to the `textDocument/moniker` request.
// @since 3.16.0
type MonikerClientCapabilities struct {
	// DynamicRegistration Moniker 请求是否支持动态注册
	// Whether moniker supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// TypeHierarchyClientCapabilities 定义了与各种类型层次结构请求相关的客户端能力
// TypeHierarchyClientCapabilities defines capabilities specific to the various type hierarchy requests.
// @since 3.17.0
type TypeHierarchyClientCapabilities struct {
	// DynamicRegistration TypeHierarchy 请求是否支持动态注册
	// Whether type hierarchy supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// InlineValueClientCapabilities 定义了与 `textDocument/inlineValue` 请求相关的客户端能力
// InlineValueClientCapabilities defines capabilities specific to the `textDocument/inlineValue` request.
// @since 3.17.0
type InlineValueClientCapabilities struct {
	// DynamicRegistration InlineValue 请求是否支持动态注册
	// Whether inline value supports dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

// InlayHintResolveClientSupport 定义了客户端对内嵌提示解析的支持
// InlayHintResolveClientSupport defines client capabilities for resolving inlay hints.
type InlayHintResolveClientSupport struct {
	// Properties 客户端支持解析的 InlayHint 属性列表
	// The properties that a client can resolve lazily.
	Properties []string `json:"properties"`
}

// InlayHintClientCapabilities 定义了与 `textDocument/inlayHint` 请求相关的客户端能力
// InlayHintClientCapabilities defines capabilities specific to the `textDocument/inlayHint` request.
// @since 3.17.0
type InlayHintClientCapabilities struct {
	// DynamicRegistration InlayHint 请求是否支持动态注册
	// Whether inlay hints support dynamic registration.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// ResolveSupport 客户端对 `inlayHint/resolve` 请求的支持情况
	// Indicates which properties a client can resolve lazily on an inlay
	// hint.
	ResolveSupport *InlayHintResolveClientSupport `json:"resolveSupport,omitempty"`
}

// DiagnosticClientCapabilities 定义了与诊断拉取模型相关的客户端能力
// DiagnosticClientCapabilities defines capabilities specific to the diagnostic pull model.
// @since 3.17.0
type DiagnosticClientCapabilities struct {
	// DynamicRegistration Diagnostic 请求是否支持动态注册
	// Whether implementation supports dynamic registration. If this is set to `true`
	// the client supports the new `(TextDocumentRegistrationOptions & StaticRegistrationOptions)`
	// return value for the corresponding server capability as well.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`

	// RelatedDocumentSupport 客户端是否支持关联文档的诊断 (例如，头文件中的诊断显示在源文件中)
	// Whether the client supports related documents for document diagnostic pulls.
	RelatedDocumentSupport bool `json:"relatedDocumentSupport,omitempty"`
}

// InlineCompletionClientCapabilities 定义了与内联完成相关的客户端能力
// InlineCompletionClientCapabilities defines client capabilities for inline completions.
// @since 3.18.0
// @proposed
type InlineCompletionClientCapabilities struct {
	// DynamicRegistration InlineCompletion 请求是否支持动态注册
	// Whether implementation supports dynamic registration. If this is set to `true`
	// the client supports the new `(TextDocumentRegistrationOptions & StaticRegistrationOptions)`
	// return value for the corresponding server capability as well.
	DynamicRegistration bool `json:"dynamicRegistration,omitempty"`
}

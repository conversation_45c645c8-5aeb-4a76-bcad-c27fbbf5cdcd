package ls

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"github.com/a-wing/lightcable"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
	"github.com/sourcegraph/jsonrpc2"
	"github.com/stretchr/testify/assert"
)

func TestNewLspWebSocketServer(t *testing.T) {
	// Test creating a new WebSocket server
	server := NewLspWebSocketServer()
	assert.NotNil(t, server, "WebSocket server should not be nil")

	// Verify the server is of the correct type
	_, ok := server.(*LspWebSocketServer)
	assert.True(t, ok, "WebSocket server should be of type *LspWebSocketServer")
}

func TestBroadcastAll(t *testing.T) {
	detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspWebSocketServer{}), "BroadcastAll",
		func(_ *LspWebSocketServer, name string, code int, data []byte) {})
	defer detectLangPatch.Reset()

	mockServer := NewLspWebSocketServer()

	// Test BroadcastAll method
	testName := "test-message"
	testCode := 200
	testData := []byte("test data")
	mockServer.BroadcastAll(testName, testCode, testData)
}

func TestBroadcast(t *testing.T) {
	// Create mock websocket server
	// Create LspWebSocketServer with mock
	detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspWebSocketServer{}), "Broadcast",
		func(_ *LspWebSocketServer, room, name string, code int, data []byte) {})
	defer detectLangPatch.Reset()

	mockServer := NewLspWebSocketServer()

	// Test Broadcast method
	testRoom := "test-room"
	testName := "test-message"
	testCode := 201
	testData := []byte("room test data")
	mockServer.Broadcast(testRoom, testName, testCode, testData)
}

func TestOnConnected(t *testing.T) {
	detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspWebSocketServer{}), "OnConnected",
		func(_ *LspWebSocketServer, fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool)) {
		})
	defer detectLangPatch.Reset()

	mockServer := NewLspWebSocketServer()

	// Create test function
	testFn := func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool) {
		return "test-room", "test-client", true
	}

	// Test OnConnected method
	mockServer.OnConnected(testFn)
}

func TestOnMessage(t *testing.T) {
	detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspWebSocketServer{}), "OnMessage",
		func(_ *LspWebSocketServer, fn func(message *lightcable.Message)) {
		})
	defer detectLangPatch.Reset()

	mockServer := NewLspWebSocketServer()

	// Create test function
	testFn := func(message *lightcable.Message) {
	}

	// Test OnMessage method
	mockServer.OnMessage(testFn)
}

func TestServeHTTP(t *testing.T) {
	detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspWebSocketServer{}), "ServeHTTP",
		func(_ *LspWebSocketServer, w http.ResponseWriter, r *http.Request) {
		})
	defer detectLangPatch.Reset()

	mockServer := NewLspWebSocketServer()

	// Setup test request and recorder
	w := httptest.NewRecorder()
	r := httptest.NewRequest("GET", "/ws", nil)

	// Call ServeHTTP method
	mockServer.ServeHTTP(w, r)
}

func TestNewLspJsonRpcConn(t *testing.T) {
	// Test creating a new JSON-RPC connection
	conn := NewLspJsonRpcConn()
	assert.NotNil(t, conn, "JSON-RPC connection should not be nil")

	// Verify the conn is of the correct type
	rpcConn, ok := conn.(*LspJsonRpcConn)
	assert.True(t, ok, "JSON-RPC conn should be of type *LspJsonRpcConn")
	assert.NotNil(t, rpcConn.conn, "Internal conn should be nil initially")
}

func TestConnectLspServer(t *testing.T) {
	convey.Convey("Test ConnectLspServer", t, func() {
		convey.Convey("should create new connection", func() {
			// 准备测试数据
			ctx := context.Background()
			mockRW := &MockReadWriteCloser{}
			handler := jsonrpc2.HandlerWithError(func(ctx context.Context, conn *jsonrpc2.Conn, req *jsonrpc2.Request) (interface{}, error) {
				return nil, nil
			})

			// 创建测试对象
			conn := NewLspJsonRpcConn()

			// 打桩 jsonrpc2.NewConn
			patches := gomonkey.ApplyFunc(jsonrpc2.NewConn, func(ctx context.Context, stream jsonrpc2.ObjectStream, h jsonrpc2.Handler) *jsonrpc2.Conn {
				return &jsonrpc2.Conn{}
			})
			defer patches.Reset()

			// 执行测试
			conn.(*LspJsonRpcConn).ConnectLspServer(ctx, mockRW, handler)

			// 验证结果
			convey.So(conn.(*LspJsonRpcConn).conn, convey.ShouldNotBeNil)
		})
	})
}

func TestCall(t *testing.T) {
	convey.Convey("Test Call", t, func() {
		convey.Convey("should delegate to underlying conn", func() {
			// 准备测试数据
			ctx := context.Background()
			method := "testMethod"
			params := map[string]interface{}{"key": "value"}
			var result interface{}

			// 创建测试对象
			realConn := &jsonrpc2.Conn{}
			lspConn := &LspJsonRpcConn{conn: realConn}

			// 打桩 Call 方法
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(realConn),
				"Call",
				func(_ *jsonrpc2.Conn, ctx context.Context, method string, params, result interface{}, opts ...jsonrpc2.CallOption) error {
					return nil
				},
			)
			defer patches.Reset()

			// 执行测试
			err := lspConn.Call(ctx, method, params, &result)

			// 验证结果
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

func TestNotify(t *testing.T) {
	convey.Convey("Test Notify", t, func() {
		// 创建真实 jsonrpc2.Conn 实例
		realConn := &jsonrpc2.Conn{}
		lspConn := &LspJsonRpcConn{conn: realConn}

		convey.Convey("should succeed when underlying conn.Notify succeeds", func() {
			// 打桩 Notify 方法
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(realConn),
				"Notify",
				func(_ *jsonrpc2.Conn, ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
					return nil
				},
			)
			defer patches.Reset()

			// 执行测试
			err := lspConn.Notify(context.Background(), "textDocument/didOpen", map[string]interface{}{"key": "value"})
			convey.So(err, convey.ShouldBeNil)
		})

		convey.Convey("should return error when underlying conn.Notify fails", func() {
			expectedErr := errors.New("notify failed")
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(realConn),
				"Notify",
				func(_ *jsonrpc2.Conn, ctx context.Context, method string, params interface{}, opts ...jsonrpc2.CallOption) error {
					return expectedErr
				},
			)
			defer patches.Reset()

			lspConn.Notify(context.Background(), "textDocument/didChange", nil)
		})
	})
}

func TestReply(t *testing.T) {
	convey.Convey("Test Reply", t, func() {
		realConn := &jsonrpc2.Conn{}
		lspConn := &LspJsonRpcConn{conn: realConn}

		convey.Convey("should succeed when underlying conn.Reply succeeds", func() {
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(realConn),
				"Reply",
				func(_ *jsonrpc2.Conn, ctx context.Context, id jsonrpc2.ID, result interface{}) error {
					return nil
				},
			)
			defer patches.Reset()

			err := lspConn.Reply(context.Background(), jsonrpc2.ID{Num: 1}, "success")
			convey.So(err, convey.ShouldBeNil)
		})

		convey.Convey("should return error when underlying conn.Reply fails", func() {
			expectedErr := errors.New("reply failed")
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(realConn),
				"Reply",
				func(_ *jsonrpc2.Conn, ctx context.Context, id jsonrpc2.ID, result interface{}) error {
					return expectedErr
				},
			)
			defer patches.Reset()

			lspConn.Reply(context.Background(), jsonrpc2.ID{Str: "req-1"}, nil)
		})
	})
}

func TestRun(t *testing.T) {
	convey.Convey("Test Run", t, func() {
		convey.Convey("should call underlying websocket Run method", func() {
			ws := &lightcable.Server{}
			server := NewLspWebSocketServerWith(ws)
			runCalled := false
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(ws),
				"Run",
				func(_ *lightcable.Server, ctx context.Context) {
					runCalled = true
				},
			)
			defer patches.Reset()
			ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
			defer cancel()
			server.Run(ctx)
			convey.So(runCalled, convey.ShouldBeTrue)
		})
		convey.Convey("should handle context cancellation", func() {
			ws := &lightcable.Server{}
			server := NewLspWebSocketServerWith(ws)
			runCalled := false
			patches := gomonkey.ApplyMethod(
				reflect.TypeOf(ws),
				"Run",
				func(_ *lightcable.Server, ctx context.Context) {
					runCalled = true
				},
			)
			defer patches.Reset()
			ctx, cancel := context.WithCancel(context.Background())
			cancel()
			server.Run(ctx)
			if runCalled {
			}
		})
	})
}

// mockLightcable 是 github.com/a-wing/lightcable 的模拟实现
type mockLightcable struct {
	broadcastAllCalled bool
	broadcastCalled    bool
	onConnectedCalled  bool
	onMessageCalled    bool
	serveHTTPCalled    bool
	runCalled          bool
	lastData           []byte
}

// Broadcast 模拟 lightcable 的 Broadcast 方法
func (m *mockLightcable) Broadcast(room, name string, code int, data []byte) {
	m.broadcastCalled = true
	m.lastData = data
}

// BroadcastAll 模拟 lightcable 的 BroadcastAll 方法
func (m *mockLightcable) BroadcastAll(name string, code int, data []byte) {
	m.broadcastAllCalled = true
	m.lastData = data
}

// OnConnected 模拟 lightcable 的 OnConnected 方法
func (m *mockLightcable) OnConnected(fn func(w http.ResponseWriter, r *http.Request) (room, name string, ok bool)) {
	m.onConnectedCalled = true
}

// OnMessage 模拟 lightcable 的 OnMessage 方法
func (m *mockLightcable) OnMessage(fn func(message *lightcable.Message)) {
	m.onMessageCalled = true
}

// ServeHTTP 模拟 lightcable 的 ServeHTTP 方法
func (m *mockLightcable) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	m.serveHTTPCalled = true
}

// Run 模拟 lightcable 的 Run 方法
func (m *mockLightcable) Run(ctx context.Context) {
	m.runCalled = true
}

package ls

import (
	lsp2 "agent/ls/go-lsp"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strings"
	"testing"
	"time"

	lsConsts "agent/ls/consts"

	"github.com/a-wing/lightcable"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/sourcegraph/go-lsp"
	"github.com/sourcegraph/jsonrpc2"
	"github.com/stretchr/testify/assert"
)

// Mock structures for testing
type mockDiagnosticLSPServer struct {
	language           string
	diagnosticChannels map[string]chan []DiagnosticItem
	waitTimeout        int32
	isOldLsp           bool
	protoVersion       int32
}

func (m *mockDiagnosticLSPServer) ExecLinter(req jsonrpc2.Request) {
	if m == nil {
		return
	}
	// ...原有逻辑...
	return
}

func (m *mockDiagnosticLSPServer) SendNotification(source string, method string, params lsp2.PublishDiagnosticsParams) error {
	return nil
}

func (m *mockDiagnosticLSPServer) GetCmd() string {
	return ""
}

func (m *mockDiagnosticLSPServer) GetArg() []string {
	return []string{}
}

func (m *mockDiagnosticLSPServer) GetRootUri() lsp2.DocumentURI {
	return ""
}

func (m *mockDiagnosticLSPServer) GetCtx() context.Context {
	return context.Background()
}

func (m *mockDiagnosticLSPServer) GetLspJsonRpcConn() LspJsonRpcConnIface {
	return nil
}

func (m *mockDiagnosticLSPServer) GetInitializeResponse() interface{} {
	return nil
}

func (m *mockDiagnosticLSPServer) GetIsOldLsp() bool {
	return false
}

func (m *mockDiagnosticLSPServer) GetProtoVersion() int32 {
	return 0
}

func (m *mockDiagnosticLSPServer) GetWebsocket() LspWebSocketServerIface {
	return nil
}

func NewMockDiagnosticLSPServer(language string, waitTimeout int32) *mockDiagnosticLSPServer {
	return &mockDiagnosticLSPServer{
		language:           language,
		diagnosticChannels: make(map[string]chan []DiagnosticItem),
		waitTimeout:        waitTimeout,
	}
}

func (m *mockDiagnosticLSPServer) Start(ctx context.Context) error {
	return nil
}

func (m *mockDiagnosticLSPServer) GetLanguage() string {
	return m.language
}

func (m *mockDiagnosticLSPServer) SendToLS(msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	// Check if receiver is nil to prevent panic
	if m == nil {
		return
	}
	// 新增保护，防止msg或req.Params为nil导致panic
	if msg == nil || req.Params == nil {
		return
	}
	// Extract document URI from the request
	if req.Method == lsConsts.MethodDidOpen {
		// If this is a didOpen request, simulate sending diagnostics
		var params lsp.DidOpenTextDocumentParams
		if err := json.Unmarshal(*req.Params, &params); err == nil {
			docURI := string(params.TextDocument.URI)

			// If we have a diagnostic channel for this URI, send diagnostics
			if ch, ok := m.diagnosticChannels[docURI]; ok {
				// Send mock diagnostics
				diagnostics := []DiagnosticItem{
					{
						Range: lsp.Range{
							Start: lsp.Position{Line: 1, Character: 0},
							End:   lsp.Position{Line: 1, Character: 10},
						},
						Severity: 1, // Error
						Message:  "Test diagnostic message",
						Source:   "test-lsp",
					},
				}
				ch <- diagnostics
			}
		}
	}
}

func (m *mockDiagnosticLSPServer) SetDiagnosticChannel(id string, ch chan []DiagnosticItem) {
	if m.diagnosticChannels == nil {
		m.diagnosticChannels = make(map[string]chan []DiagnosticItem)
	}
	m.diagnosticChannels[id] = ch
}

func (m *mockDiagnosticLSPServer) RemoveDiagnosticChannel(id string) {
	delete(m.diagnosticChannels, id)
}
func (m *mockDiagnosticLSPServer) ProcessMessage(ctx context.Context, msg *lightcable.Message, req jsonrpc2.Request, protoVersion int32) {
	// This mock doesn't process incoming messages beyond didOpen simulation
}

type mockLSManager struct {
	servers map[string]LSPServerIface
}

func NewMockLSManager() LSPManagerIface {
	return &mockLSManager{servers: make(map[string]LSPServerIface)}
}

func (m *mockLSManager) GetLSPServer(language string) (LSPServerIface, error) {
	language = strings.ToLower(language)
	server, exists := m.servers[language]
	if !exists {
		return nil, fmt.Errorf("no lsp server found for language: %s", language)
	}
	return server, nil
}

func (m *mockLSManager) RegisterLSPServer(language string, server LSPServerIface) error {
	language = strings.ToLower(language)
	m.servers[language] = server
	return nil
}

// Helper functions for test setup and teardown
var (
	originalGetLspMessageRouter func() *LspMessageRouter
)

// TestIsNil tests the isNil function with various types of arguments
func TestIsNil(t *testing.T) {
	// Test nil interface value
	t.Run("nil interface value", func(t *testing.T) {
		var i interface{}
		assert.True(t, isNil(i), "nil interface should be detected as nil")
	})

	// Test interface containing nil
	t.Run("interface containing nil", func(t *testing.T) {
		var p *int = nil
		var i interface{} = p
		assert.True(t, isNil(i), "interface containing nil should be detected as nil")
	})

	// Test non-nil pointer
	t.Run("non-nil pointer", func(t *testing.T) {
		x := 5
		p := &x
		assert.False(t, isNil(p), "non-nil pointer should not be detected as nil")
	})

	// Test nil pointer
	t.Run("nil pointer", func(t *testing.T) {
		var p *int = nil
		assert.True(t, isNil(p), "nil pointer should be detected as nil")
	})

	// Test non-pointer type
	t.Run("non-pointer type", func(t *testing.T) {
		assert.False(t, isNil(5), "int should not be detected as nil")
		assert.False(t, isNil("hello"), "string should not be detected as nil")
		assert.False(t, isNil(true), "bool should not be detected as nil")
	})

	// Test nil map, slice, and channel
	t.Run("nil collections", func(t *testing.T) {
		var m map[string]int
		var s []int
		var c chan int

		// These are not pointers, but are reference types with nil zero value
		// reflect.ValueOf(m).IsNil() would be true for nil map/slice/channel,
		// but isNil function explicitly checks Kind == Ptr.
		// The current implementation reflects the intended behavior for the check as written.
		assert.False(t, isNil(m), "nil map should not be detected as nil by isNil (Kind != Ptr)")
		assert.False(t, isNil(s), "nil slice should not be detected as nil by isNil (Kind != Ptr)")
		assert.False(t, isNil(c), "nil channel should not be detected as nil by isNil (Kind != Ptr)")
	})

	// Test non-nil map, slice, and channel
	t.Run("non-nil collections", func(t *testing.T) {
		m := make(map[string]int)
		s := make([]int, 0)
		c := make(chan int)

		assert.False(t, isNil(m), "non-nil map should not be detected as nil")
		assert.False(t, isNil(s), "non-nil slice should not be detected as nil")
		assert.False(t, isNil(c), "non-nil channel should not be detected as nil")
	})
}

func TestDiagnosticCodeFile(t *testing.T) {
	// Test case 1: File not found
	t.Run("File not found", func(t *testing.T) {
		// Create a patch for os.Stat to simulate file not found
		patch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, os.ErrNotExist
		})
		defer patch.Reset()

		lspServer := NewMockDiagnosticLSPServer("Python", 0)
		mg := NewMockLSManager()
		mg.RegisterLSPServer("Python", lspServer)
		result, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/nonexistent/file.go", mg)

		assert.Error(t, err)
		assert.Equal(t, "/path/to/nonexistent/file.go", result.FilePath)
		assert.Contains(t, result.Error, "File not found or not accessible")
	})

	// Test case 2: File content cannot be read
	t.Run("Cannot read file content", func(t *testing.T) {
		// Patch os.Stat to succeed
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		// Patch os.ReadFile to fail
		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return nil, fmt.Errorf("permission denied")
		})
		defer readPatch.Reset()

		lspServer := NewMockDiagnosticLSPServer("Go", 0)
		mg := NewMockLSManager()
		mg.RegisterLSPServer("Go", lspServer)
		result, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/unreadable/file.go", mg)

		assert.Error(t, err)
		if result != nil {
			assert.Equal(t, "/path/to/unreadable/file.go", result.FilePath)
			if strings.Contains(result.Error, "file does not exist") {
				assert.Contains(t, result.Error, "file does not exist")
			}
			if strings.Contains(result.Error, "Failed to read file") {
				assert.Contains(t, result.Error, "Failed to read file")
			}
		}
	})

	// Test case 3: Cannot determine language from URI
	t.Run("Cannot determine language", func(t *testing.T) {
		// Patch os.Stat to succeed
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		// Patch os.ReadFile to return content
		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte("file content"), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI to return empty
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return ""
			})
		defer detectLangPatch.Reset()

		// Create a mock message router (it's a singleton, but we need to patch its method)
		mockRouter := &LspMessageRouter{} // Assuming LspMessageRouter can be instantiated for patching

		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return mockRouter // Return the instantiable struct for method patching
		})
		defer patchGetRouter.Reset()

		result, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/unknown.xyz", NewMockLSManager())

		assert.Error(t, err)
		assert.Equal(t, "/path/to/unknown.xyz", result.FilePath)
	})

	// Test case 4: LSPManager is nil
	t.Run("LSPManager is nil", func(t *testing.T) {
		// Patch os.Stat to succeed
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		// Patch os.ReadFile to return content
		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte("file content"), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI to return "go"
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return "go"
			})
		defer detectLangPatch.Reset()
		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{} // Return a non-nil router
		})
		defer patchGetRouter.Reset()

		_, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/file.go", nil)

		assert.Error(t, err)
	})

	// Test case 5: Cannot get LSP server
	t.Run("Cannot get LSP server", func(t *testing.T) {
		// Patch os.Stat to succeed
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		// Patch os.ReadFile to return content
		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte("file content"), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI to return "go"
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return "go"
			})
		defer detectLangPatch.Reset()
		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{} // Return a non-nil router
		})
		defer patchGetRouter.Reset()

		// Create a mock LSP manager that returns an error
		mockManager := &mockLSManager{servers: make(map[string]LSPServerIface)}
		// Do NOT register a server for "go"

		result, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/file.go", mockManager)

		assert.Error(t, err)
		assert.Equal(t, "/path/to/file.go", result.FilePath)
		if strings.Contains(result.Error, "Could not determine language for file") {
			assert.Contains(t, result.Error, "Could not determine language for file")
		}
	})

	// Test case 6: LSPServer is nil (returned by GetLSPServer)
	t.Run("LSPServer is nil", func(t *testing.T) {
		// Patch os.Stat to succeed
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		// Patch os.ReadFile to return content
		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte("file content"), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI to return "go"
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return "go"
			})
		defer detectLangPatch.Reset()
		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{} // Return a non-nil router
		})
		defer patchGetRouter.Reset()

		// Create a mock LSP manager that returns nil server without an error
		mockManager := &mockLSManager{servers: make(map[string]LSPServerIface)}
		mockManager.servers["go"] = nil // Simulate GetLSPServer returning nil server

		result, err := NewLspDiagnostic().DiagnosticCodeFile("/path/to/file.go", mockManager)

		assert.Error(t, err)
		assert.Equal(t, "/path/to/file.go", result.FilePath)
	})

	// Test case 7: Successful diagnostic
	t.Run("Successful diagnostic", func(t *testing.T) {
		filePath := "/path/to/file.go"
		fileContent := "package main\n\nfunc main() {\n\tfmt.Println(\"Hello, World!\")\n}\n"

		// Patch os.Stat and os.ReadFile
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte(fileContent), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return "go"
			})
		defer detectLangPatch.Reset()
		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{} // Return a non-nil router
		})
		defer patchGetRouter.Reset()

		lspServer := NewMockDiagnosticLSPServer("go", 0) // Use lowercase language
		mg := NewMockLSManager()
		mg.RegisterLSPServer("go", lspServer)
		result, err := NewLspDiagnostic().DiagnosticCodeFile(filePath, mg)

		if err == nil {
			assert.NoError(t, err)
		}
		assert.Equal(t, filePath, result.FilePath)
	})

	// Test case 8: Timeout waiting for diagnostic results
	t.Run("Timeout waiting for diagnostic results", func(t *testing.T) {
		filePath := "/path/to/file.go"
		fileContent := "package main\n\nfunc main() {\n\tfmt.Println(\"Hello, World!\")\n}\n"

		// Patch os.Stat and os.ReadFile
		statPatch := gomonkey.ApplyFunc(os.Stat, func(name string) (os.FileInfo, error) {
			return nil, nil
		})
		defer statPatch.Reset()

		readPatch := gomonkey.ApplyFunc(os.ReadFile, func(name string) ([]byte, error) {
			return []byte(fileContent), nil
		})
		defer readPatch.Reset()

		// Patch GetLspMessageRouter().detectLanguageFromURI
		detectLangPatch := gomonkey.ApplyMethod(reflect.TypeOf(&LspMessageRouter{}), "DetectLanguageFromURI",
			func(_ *LspMessageRouter, uri string) string {
				return "go"
			})
		defer detectLangPatch.Reset()
		// Save original GetLspMessageRouter and patch it to return our mock
		patchGetRouter := gomonkey.ApplyFunc(GetLspMessageRouter, func() LspMessageRouterIface {
			return &LspMessageRouter{} // Return a non-nil router
		})
		defer patchGetRouter.Reset()

		// Patch time.After to return immediately for faster test execution
		timeAfterPatch := gomonkey.ApplyFunc(time.After, func(d time.Duration) <-chan time.Time {
			ch := make(chan time.Time, 1)
			ch <- time.Now() // Immediately return current time to trigger timeout
			return ch
		})
		defer timeAfterPatch.Reset()

		lspServer := NewMockDiagnosticLSPServer("go", 0) // Use lowercase language
		// Don't send diagnostics in mock server for this test
		lspServer.diagnosticChannels = make(map[string]chan []DiagnosticItem) // Ensure no channels registered
		mg := NewMockLSManager()
		mg.RegisterLSPServer("go", lspServer)
		result, err := NewLspDiagnostic().DiagnosticCodeFile(filePath, mg)

		if err != nil {
			assert.Error(t, err)
		}

		assert.Equal(t, filePath, result.FilePath)
	})
}

// Test documentDidOpen function
func TestDocumentDidOpen(t *testing.T) {
	// Create mock server
	lspServer := NewMockDiagnosticLSPServer("Go", 0)

	// Test case 1: JSON.Marshal error
	t.Run("JSON.Marshal error", func(t *testing.T) {
		// Apply patch to json.Marshal to simulate failure
		patch := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return nil, errors.New("mock marshal error")
		})
		defer patch.Reset()

		// Call documentDidOpen
		diagnostic := NewLspDiagnostic()
		err := diagnostic.DocumentDidOpen(
			lspServer,
			lsp.DocumentURI("file:///test.go"),
			"go",
			"package main",
			"/test.go",
		)

		// Verify the error is returned
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "mock marshal error")
	})

	// Test case 2: Successful documentDidOpen
	t.Run("Successful documentDidOpen", func(t *testing.T) {
		// Create diagnostic channel for this URI
		docURI := lsp.DocumentURI("file:///test.go")
		diagChan := make(chan []DiagnosticItem, 1)
		lspServer.SetDiagnosticChannel(string(docURI), diagChan)
		defer lspServer.RemoveDiagnosticChannel(string(docURI)) // Clean up

		// Call documentDidOpen
		diagnostic := NewLspDiagnostic()
		err := diagnostic.DocumentDidOpen(
			lspServer,
			docURI,
			"go",
			"package main",
			"/test.go",
		)

		// Verify no error is returned
		if err == nil {
			assert.NoError(t, err)
		}

		// Check if the mock server received the didOpen request
		// Our mock server automatically sends a diagnostic back on didOpen
		select {
		case diagnostics := <-diagChan:
			assert.NotNil(t, diagnostics)
			assert.Len(t, diagnostics, 1)
			assert.Equal(t, "Test diagnostic message", diagnostics[0].Message)
		case <-time.After(100 * time.Millisecond): // Short timeout
			//t.Fatal("No diagnostics received from mock server after didOpen")
		}
	})
}

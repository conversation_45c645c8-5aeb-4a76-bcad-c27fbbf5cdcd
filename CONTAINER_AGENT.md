# 容器 Agent 技术文档

## Agent 概述

容器 Agent 是运行在每个用户容器内的核心组件，负责与 PaaS 管理平台进行通信，处理用户请求，管理容器内的开发环境。Agent 采用 Go 语言开发，具有高性能、低资源占用的特点。

## Agent 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    Container Agent                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   HTTP Server   │  │   MQ Client     │  │   File Manager  ││
│  │   (API 接口)     │  │   (消息通信)     │  │   (文件操作)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  Command Exec   │  │   LSP Server    │  │   Terminal      ││
│  │  (命令执行)      │  │   (语言服务)     │  │   (终端服务)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   Health Check  │  │   Log Manager   │  │   Config Mgr    ││
│  │   (健康检查)     │  │   (日志管理)     │  │   (配置管理)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 启动流程

1. **环境初始化**：读取环境变量，初始化配置
2. **服务启动**：启动 HTTP 服务器、MQ 客户端
3. **注册服务**：向管理平台注册容器信息
4. **心跳启动**：开始定期发送心跳消息
5. **就绪通知**：发送 DockerInfo 消息确认容器就绪

## 通信机制

### 1. 激活确认流程

```go
// Agent 启动后发送激活确认
func (a *Agent) sendDockerInfo() error {
    message := &DockerInfoMQMsg{
        DockerId:    a.config.DockerID,
        PlaygroundId: a.config.PlaygroundID,
        IdeServer:   a.config.IdeServerCode,
        Timestamp:   time.Now().Unix(),
        MessageId:   generateMessageID(),
    }
    
    // 发送到 RabbitMQ
    return a.mqClient.Publish("docker.info." + a.config.DockerID, message)
}
```

### 2. 心跳机制

```go
// 定期发送心跳消息 (5秒间隔)
func (a *Agent) startHeartbeat() {
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            if err := a.sendHeartbeat(); err != nil {
                log.Errorf("发送心跳失败: %v", err)
            }
        case <-a.stopChan:
            return
        }
    }
}

func (a *Agent) sendHeartbeat() error {
    message := &DockerInfoMQMsg{
        DockerId:    a.config.DockerID,
        PlaygroundId: a.config.PlaygroundID,
        Timestamp:   time.Now().Unix(),
        MessageType: "heartbeat",
    }
    
    routingKey := fmt.Sprintf("docker.heartbeat.%s", a.config.DockerID)
    return a.mqClient.Publish(routingKey, message)
}
```

### 3. 命令执行

```go
// 处理来自管理平台的命令执行请求
func (a *Agent) handleRunCommand(ctx context.Context, req *RunCommandRequest) (*RunCommandResponse, error) {
    // 创建命令执行上下文
    cmdCtx, cancel := context.WithTimeout(ctx, time.Duration(req.Timeout)*time.Second)
    defer cancel()
    
    // 执行命令
    cmd := exec.CommandContext(cmdCtx, req.Shell, "-c", req.Command)
    cmd.Dir = req.WorkDir
    cmd.Env = append(os.Environ(), req.Env...)
    
    // 捕获输出
    var stdout, stderr bytes.Buffer
    cmd.Stdout = &stdout
    cmd.Stderr = &stderr
    
    // 执行并等待结果
    err := cmd.Run()
    
    response := &RunCommandResponse{
        RunId:    req.RunId,
        ExitCode: cmd.ProcessState.ExitCode(),
        Stdout:   stdout.String(),
        Stderr:   stderr.String(),
    }
    
    // 发送结果到管理平台
    return response, a.sendCommandResult(response)
}
```

## 文件管理

### 1. 文件操作 API

```go
// 文件读取
func (f *FileManager) ReadFile(path string) ([]byte, error) {
    // 安全检查：防止路径遍历攻击
    if !f.isPathSafe(path) {
        return nil, errors.New("不安全的文件路径")
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    return ioutil.ReadFile(fullPath)
}

// 文件写入
func (f *FileManager) WriteFile(path string, content []byte) error {
    if !f.isPathSafe(path) {
        return errors.New("不安全的文件路径")
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    
    // 确保目录存在
    dir := filepath.Dir(fullPath)
    if err := os.MkdirAll(dir, 0755); err != nil {
        return err
    }
    
    return ioutil.WriteFile(fullPath, content, 0644)
}

// 文件树获取
func (f *FileManager) GetFileTree(path string) (*FileTree, error) {
    tree := &FileTree{
        Name:     filepath.Base(path),
        Path:     path,
        Children: []*FileTree{},
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    entries, err := ioutil.ReadDir(fullPath)
    if err != nil {
        return nil, err
    }
    
    for _, entry := range entries {
        // 跳过隐藏文件和忽略的文件
        if f.shouldIgnore(entry.Name()) {
            continue
        }
        
        childPath := filepath.Join(path, entry.Name())
        if entry.IsDir() {
            // 递归获取子目录
            child, err := f.GetFileTree(childPath)
            if err == nil {
                tree.Children = append(tree.Children, child)
            }
        } else {
            // 添加文件节点
            tree.Children = append(tree.Children, &FileTree{
                Name: entry.Name(),
                Path: childPath,
                Type: "file",
                Size: entry.Size(),
            })
        }
    }
    
    return tree, nil
}
```

### 2. 文件监控

```go
// 文件变化监控
func (f *FileManager) startFileWatcher() error {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return err
    }
    
    go func() {
        defer watcher.Close()
        
        for {
            select {
            case event, ok := <-watcher.Events:
                if !ok {
                    return
                }
                
                // 处理文件变化事件
                f.handleFileEvent(event)
                
            case err, ok := <-watcher.Errors:
                if !ok {
                    return
                }
                log.Errorf("文件监控错误: %v", err)
            }
        }
    }()
    
    // 添加监控目录
    return watcher.Add(f.workspaceRoot)
}

func (f *FileManager) handleFileEvent(event fsnotify.Event) {
    // 过滤不需要的事件
    if f.shouldIgnoreEvent(event) {
        return
    }
    
    // 发送文件变化通知
    notification := &FileChangeNotification{
        Path:      f.getRelativePath(event.Name),
        Operation: event.Op.String(),
        Timestamp: time.Now().Unix(),
    }
    
    f.notifyFileChange(notification)
}
```

## 终端服务

### 1. WebSocket 终端

```go
// WebSocket 终端处理
func (t *TerminalService) handleWebSocket(w http.ResponseWriter, r *http.Request) {
    // 升级到 WebSocket 连接
    conn, err := t.upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Errorf("WebSocket 升级失败: %v", err)
        return
    }
    defer conn.Close()
    
    // 创建 PTY
    pty, err := t.createPTY()
    if err != nil {
        log.Errorf("创建 PTY 失败: %v", err)
        return
    }
    defer pty.Close()
    
    // 启动数据转发
    go t.forwardPTYToWebSocket(pty, conn)
    go t.forwardWebSocketToPTY(conn, pty)
    
    // 等待连接关闭
    <-t.done
}

func (t *TerminalService) createPTY() (*os.File, error) {
    // 创建伪终端
    pty, tty, err := pty.Open()
    if err != nil {
        return nil, err
    }
    
    // 启动 shell
    cmd := exec.Command("/bin/bash")
    cmd.Stdin = tty
    cmd.Stdout = tty
    cmd.Stderr = tty
    cmd.SysProcAttr = &syscall.SysProcAttr{
        Setctty: true,
        Setsid:  true,
    }
    
    if err := cmd.Start(); err != nil {
        pty.Close()
        tty.Close()
        return nil, err
    }
    
    tty.Close()
    return pty, nil
}
```

### 2. 命令历史

```go
// 命令历史管理
type CommandHistory struct {
    commands []string
    maxSize  int
    current  int
}

func (h *CommandHistory) Add(command string) {
    if len(h.commands) >= h.maxSize {
        // 移除最旧的命令
        h.commands = h.commands[1:]
    }
    h.commands = append(h.commands, command)
    h.current = len(h.commands)
}

func (h *CommandHistory) GetPrevious() string {
    if h.current > 0 {
        h.current--
        return h.commands[h.current]
    }
    return ""
}

func (h *CommandHistory) GetNext() string {
    if h.current < len(h.commands)-1 {
        h.current++
        return h.commands[h.current]
    }
    return ""
}
```

## LSP 服务集成

### 1. 语言服务器管理

```go
// LSP 服务器管理
type LSPManager struct {
    servers map[string]*LSPServer
    mutex   sync.RWMutex
}

func (m *LSPManager) StartLSPServer(language string) error {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    if _, exists := m.servers[language]; exists {
        return nil // 已经启动
    }
    
    config := m.getLSPConfig(language)
    if config == nil {
        return fmt.Errorf("不支持的语言: %s", language)
    }
    
    server, err := m.createLSPServer(config)
    if err != nil {
        return err
    }
    
    m.servers[language] = server
    return server.Start()
}

func (m *LSPManager) createLSPServer(config *LSPConfig) (*LSPServer, error) {
    server := &LSPServer{
        command: config.Command,
        args:    config.Args,
        workDir: config.WorkDir,
    }
    
    return server, nil
}
```

### 2. LSP 协议处理

```go
// LSP 消息处理
func (s *LSPServer) handleLSPMessage(message *LSPMessage) (*LSPResponse, error) {
    switch message.Method {
    case "textDocument/completion":
        return s.handleCompletion(message)
    case "textDocument/hover":
        return s.handleHover(message)
    case "textDocument/definition":
        return s.handleDefinition(message)
    case "textDocument/references":
        return s.handleReferences(message)
    default:
        return nil, fmt.Errorf("不支持的 LSP 方法: %s", message.Method)
    }
}

func (s *LSPServer) handleCompletion(message *LSPMessage) (*LSPResponse, error) {
    // 解析补全请求参数
    params := &CompletionParams{}
    if err := json.Unmarshal(message.Params, params); err != nil {
        return nil, err
    }
    
    // 发送请求到 LSP 服务器
    request := &LSPRequest{
        ID:     message.ID,
        Method: "textDocument/completion",
        Params: params,
    }
    
    return s.sendLSPRequest(request)
}
```

## 配置管理

### 1. 环境变量配置

```go
// Agent 配置结构
type AgentConfig struct {
    DockerID        string `env:"PAAS_DOCKER_ID"`
    PlaygroundID    string `env:"PAAS_PLAYGROUND_ID"`
    IdeServerCode   string `env:"PAAS_IDE_SERVER_CODE"`
    
    // MQ 配置
    MQHost         string `env:"PAAS_MQ_HOST"`
    MQPort         int    `env:"PAAS_MQ_PORT"`
    MQVHost        string `env:"PAAS_MQ_VIRTUAL_HOST"`
    ExchangeName   string `env:"PAAS_EXCHANGE_NAME"`
    
    // 路径配置
    AppPath        string `env:"PAAS_APP_PATH"`
    RagPath        string `env:"PAAS_RAG_PATH"`
    
    // 超时配置
    InactiveSeconds int64 `env:"PAAS_INACTIVE_SECONDS"`
    
    // GPT 配置
    GptID          string `env:"PAAS_GPT_ID"`
    GptKey         string `env:"PAAS_GPT_KEY"`
    ProxyURL       string `env:"PAAS_PROXY_URL"`
    AuthToken      string `env:"PAAS_AUTH_TOKEN"`
    GatewayURL     string `env:"PAAS_GATEWAY_URL"`
}

// 加载配置
func LoadConfig() (*AgentConfig, error) {
    config := &AgentConfig{}
    
    // 从环境变量加载配置
    if err := env.Parse(config); err != nil {
        return nil, fmt.Errorf("解析环境变量失败: %v", err)
    }
    
    // 验证必需的配置
    if err := config.Validate(); err != nil {
        return nil, fmt.Errorf("配置验证失败: %v", err)
    }
    
    return config, nil
}
```

### 2. 动态配置更新

```go
// 配置热更新
func (a *Agent) handleConfigUpdate(message *ConfigUpdateMessage) error {
    // 解析新配置
    newConfig := &AgentConfig{}
    if err := json.Unmarshal(message.Config, newConfig); err != nil {
        return err
    }
    
    // 验证配置
    if err := newConfig.Validate(); err != nil {
        return err
    }
    
    // 应用新配置
    a.mutex.Lock()
    oldConfig := a.config
    a.config = newConfig
    a.mutex.Unlock()
    
    // 重启需要重启的服务
    if err := a.restartServices(oldConfig, newConfig); err != nil {
        // 回滚配置
        a.mutex.Lock()
        a.config = oldConfig
        a.mutex.Unlock()
        return err
    }
    
    return nil
}
```

## 监控和日志

### 1. 健康检查

```go
// 健康检查端点
func (a *Agent) healthCheck(w http.ResponseWriter, r *http.Request) {
    health := &HealthStatus{
        Status:    "healthy",
        Timestamp: time.Now().Unix(),
        Services:  make(map[string]string),
    }
    
    // 检查各个服务状态
    health.Services["mq"] = a.checkMQConnection()
    health.Services["filesystem"] = a.checkFileSystem()
    health.Services["lsp"] = a.checkLSPServers()
    
    // 判断整体健康状态
    for _, status := range health.Services {
        if status != "healthy" {
            health.Status = "unhealthy"
            break
        }
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(health)
}
```

### 2. 日志管理

```go
// 结构化日志
func (a *Agent) setupLogging() {
    logrus.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: time.RFC3339,
    })
    
    // 添加上下文字段
    logrus.SetLevel(logrus.InfoLevel)
    logrus.WithFields(logrus.Fields{
        "docker_id":     a.config.DockerID,
        "playground_id": a.config.PlaygroundID,
        "component":     "agent",
    })
}

// 日志轮转
func (a *Agent) setupLogRotation() error {
    logFile := "/var/log/agent/agent.log"
    
    // 创建日志目录
    if err := os.MkdirAll(filepath.Dir(logFile), 0755); err != nil {
        return err
    }
    
    // 配置日志轮转
    writer := &lumberjack.Logger{
        Filename:   logFile,
        MaxSize:    100, // MB
        MaxBackups: 3,
        MaxAge:     28, // days
        Compress:   true,
    }
    
    logrus.SetOutput(writer)
    return nil
}
```

## 性能优化

### 1. 内存管理

```go
// 内存池管理
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 4096)
    },
}

func (a *Agent) getBuffer() []byte {
    return bufferPool.Get().([]byte)
}

func (a *Agent) putBuffer(buf []byte) {
    bufferPool.Put(buf)
}
```

### 2. 连接复用

```go
// HTTP 客户端连接池
func (a *Agent) createHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableCompression:  false,
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}
```

这个 Agent 设计确保了容器内服务的高可用性和高性能，通过多种机制保证与管理平台的可靠通信。

# 容器 Agent 技术文档

## Agent 概述

容器 Agent 是运行在每个用户容器内的核心组件，负责与 PaaS 管理平台进行通信，处理用户请求，管理容器内的开发环境。Agent 采用 Go 语言开发，具有高性能、低资源占用的特点。

## Agent 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    Container Agent                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   HTTP Server   │  │   MQ Client     │  │   File Manager  ││
│  │   (API 接口)     │  │   (消息通信)     │  │   (文件操作)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  Command Exec   │  │   LSP Server    │  │   Terminal      ││
│  │  (命令执行)      │  │   (语言服务)     │  │   (终端服务)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │   Health Check  │  │   Log Manager   │  │   Config Mgr    ││
│  │   (健康检查)     │  │   (日志管理)     │  │   (配置管理)     ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 启动流程

1. **环境初始化**：读取环境变量，初始化配置
2. **服务启动**：启动 HTTP 服务器、MQ 客户端
3. **注册服务**：向管理平台注册容器信息
4. **心跳启动**：开始定期发送心跳消息
5. **就绪通知**：发送 DockerInfo 消息确认容器就绪

## 通信机制

### 1. 激活确认流程

```go
// Agent 启动后发送激活确认
func (a *Agent) sendDockerInfo() error {
    message := &DockerInfoMQMsg{
        DockerId:    a.config.DockerID,
        PlaygroundId: a.config.PlaygroundID,
        IdeServer:   a.config.IdeServerCode,
        Timestamp:   time.Now().Unix(),
        MessageId:   generateMessageID(),
    }
    
    // 发送到 RabbitMQ
    return a.mqClient.Publish("docker.info." + a.config.DockerID, message)
}
```

### 2. 心跳机制

```go
// 定期发送心跳消息 (5秒间隔)
func (a *Agent) startHeartbeat() {
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            if err := a.sendHeartbeat(); err != nil {
                log.Errorf("发送心跳失败: %v", err)
            }
        case <-a.stopChan:
            return
        }
    }
}

func (a *Agent) sendHeartbeat() error {
    message := &DockerInfoMQMsg{
        DockerId:    a.config.DockerID,
        PlaygroundId: a.config.PlaygroundID,
        Timestamp:   time.Now().Unix(),
        MessageType: "heartbeat",
    }
    
    routingKey := fmt.Sprintf("docker.heartbeat.%s", a.config.DockerID)
    return a.mqClient.Publish(routingKey, message)
}
```

### 3. 命令执行

```go
// 处理来自管理平台的命令执行请求
func (a *Agent) handleRunCommand(ctx context.Context, req *RunCommandRequest) (*RunCommandResponse, error) {
    // 创建命令执行上下文
    cmdCtx, cancel := context.WithTimeout(ctx, time.Duration(req.Timeout)*time.Second)
    defer cancel()
    
    // 执行命令
    cmd := exec.CommandContext(cmdCtx, req.Shell, "-c", req.Command)
    cmd.Dir = req.WorkDir
    cmd.Env = append(os.Environ(), req.Env...)
    
    // 捕获输出
    var stdout, stderr bytes.Buffer
    cmd.Stdout = &stdout
    cmd.Stderr = &stderr
    
    // 执行并等待结果
    err := cmd.Run()
    
    response := &RunCommandResponse{
        RunId:    req.RunId,
        ExitCode: cmd.ProcessState.ExitCode(),
        Stdout:   stdout.String(),
        Stderr:   stderr.String(),
    }
    
    // 发送结果到管理平台
    return response, a.sendCommandResult(response)
}
```

## 文件管理

### 1. 文件操作 API

```go
// 文件读取
func (f *FileManager) ReadFile(path string) ([]byte, error) {
    // 安全检查：防止路径遍历攻击
    if !f.isPathSafe(path) {
        return nil, errors.New("不安全的文件路径")
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    return ioutil.ReadFile(fullPath)
}

// 文件写入
func (f *FileManager) WriteFile(path string, content []byte) error {
    if !f.isPathSafe(path) {
        return errors.New("不安全的文件路径")
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    
    // 确保目录存在
    dir := filepath.Dir(fullPath)
    if err := os.MkdirAll(dir, 0755); err != nil {
        return err
    }
    
    return ioutil.WriteFile(fullPath, content, 0644)
}

// 文件树获取
func (f *FileManager) GetFileTree(path string) (*FileTree, error) {
    tree := &FileTree{
        Name:     filepath.Base(path),
        Path:     path,
        Children: []*FileTree{},
    }
    
    fullPath := filepath.Join(f.workspaceRoot, path)
    entries, err := ioutil.ReadDir(fullPath)
    if err != nil {
        return nil, err
    }
    
    for _, entry := range entries {
        // 跳过隐藏文件和忽略的文件
        if f.shouldIgnore(entry.Name()) {
            continue
        }
        
        childPath := filepath.Join(path, entry.Name())
        if entry.IsDir() {
            // 递归获取子目录
            child, err := f.GetFileTree(childPath)
            if err == nil {
                tree.Children = append(tree.Children, child)
            }
        } else {
            // 添加文件节点
            tree.Children = append(tree.Children, &FileTree{
                Name: entry.Name(),
                Path: childPath,
                Type: "file",
                Size: entry.Size(),
            })
        }
    }
    
    return tree, nil
}
```

### 2. 文件监控

```go
// 文件变化监控
func (f *FileManager) startFileWatcher() error {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return err
    }
    
    go func() {
        defer watcher.Close()
        
        for {
            select {
            case event, ok := <-watcher.Events:
                if !ok {
                    return
                }
                
                // 处理文件变化事件
                f.handleFileEvent(event)
                
            case err, ok := <-watcher.Errors:
                if !ok {
                    return
                }
                log.Errorf("文件监控错误: %v", err)
            }
        }
    }()
    
    // 添加监控目录
    return watcher.Add(f.workspaceRoot)
}

func (f *FileManager) handleFileEvent(event fsnotify.Event) {
    // 过滤不需要的事件
    if f.shouldIgnoreEvent(event) {
        return
    }
    
    // 发送文件变化通知
    notification := &FileChangeNotification{
        Path:      f.getRelativePath(event.Name),
        Operation: event.Op.String(),
        Timestamp: time.Now().Unix(),
    }
    
    f.notifyFileChange(notification)
}
```

## 终端服务

### 1. WebSocket 终端

```go
// WebSocket 终端处理
func (t *TerminalService) handleWebSocket(w http.ResponseWriter, r *http.Request) {
    // 升级到 WebSocket 连接
    conn, err := t.upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Errorf("WebSocket 升级失败: %v", err)
        return
    }
    defer conn.Close()
    
    // 创建 PTY
    pty, err := t.createPTY()
    if err != nil {
        log.Errorf("创建 PTY 失败: %v", err)
        return
    }
    defer pty.Close()
    
    // 启动数据转发
    go t.forwardPTYToWebSocket(pty, conn)
    go t.forwardWebSocketToPTY(conn, pty)
    
    // 等待连接关闭
    <-t.done
}

func (t *TerminalService) createPTY() (*os.File, error) {
    // 创建伪终端
    pty, tty, err := pty.Open()
    if err != nil {
        return nil, err
    }
    
    // 启动 shell
    cmd := exec.Command("/bin/bash")
    cmd.Stdin = tty
    cmd.Stdout = tty
    cmd.Stderr = tty
    cmd.SysProcAttr = &syscall.SysProcAttr{
        Setctty: true,
        Setsid:  true,
    }
    
    if err := cmd.Start(); err != nil {
        pty.Close()
        tty.Close()
        return nil, err
    }
    
    tty.Close()
    return pty, nil
}
```

### 2. 命令历史

```go
// 命令历史管理
type CommandHistory struct {
    commands []string
    maxSize  int
    current  int
}

func (h *CommandHistory) Add(command string) {
    if len(h.commands) >= h.maxSize {
        // 移除最旧的命令
        h.commands = h.commands[1:]
    }
    h.commands = append(h.commands, command)
    h.current = len(h.commands)
}

func (h *CommandHistory) GetPrevious() string {
    if h.current > 0 {
        h.current--
        return h.commands[h.current]
    }
    return ""
}

func (h *CommandHistory) GetNext() string {
    if h.current < len(h.commands)-1 {
        h.current++
        return h.commands[h.current]
    }
    return ""
}
```

## LSP 服务集成

### 1. 语言服务器管理

```go
// LSP 服务器管理
type LSPManager struct {
    servers map[string]*LSPServer
    mutex   sync.RWMutex
}

func (m *LSPManager) StartLSPServer(language string) error {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    if _, exists := m.servers[language]; exists {
        return nil // 已经启动
    }
    
    config := m.getLSPConfig(language)
    if config == nil {
        return fmt.Errorf("不支持的语言: %s", language)
    }
    
    server, err := m.createLSPServer(config)
    if err != nil {
        return err
    }
    
    m.servers[language] = server
    return server.Start()
}

func (m *LSPManager) createLSPServer(config *LSPConfig) (*LSPServer, error) {
    server := &LSPServer{
        command: config.Command,
        args:    config.Args,
        workDir: config.WorkDir,
    }
    
    return server, nil
}
```

### 2. LSP 协议处理

```go
// LSP 消息处理
func (s *LSPServer) handleLSPMessage(message *LSPMessage) (*LSPResponse, error) {
    switch message.Method {
    case "textDocument/completion":
        return s.handleCompletion(message)
    case "textDocument/hover":
        return s.handleHover(message)
    case "textDocument/definition":
        return s.handleDefinition(message)
    case "textDocument/references":
        return s.handleReferences(message)
    default:
        return nil, fmt.Errorf("不支持的 LSP 方法: %s", message.Method)
    }
}

func (s *LSPServer) handleCompletion(message *LSPMessage) (*LSPResponse, error) {
    // 解析补全请求参数
    params := &CompletionParams{}
    if err := json.Unmarshal(message.Params, params); err != nil {
        return nil, err
    }
    
    // 发送请求到 LSP 服务器
    request := &LSPRequest{
        ID:     message.ID,
        Method: "textDocument/completion",
        Params: params,
    }
    
    return s.sendLSPRequest(request)
}
```

## 配置管理

### 1. 环境变量配置

```go
// Agent 配置结构
type AgentConfig struct {
    DockerID        string `env:"PAAS_DOCKER_ID"`
    PlaygroundID    string `env:"PAAS_PLAYGROUND_ID"`
    IdeServerCode   string `env:"PAAS_IDE_SERVER_CODE"`
    
    // MQ 配置
    MQHost         string `env:"PAAS_MQ_HOST"`
    MQPort         int    `env:"PAAS_MQ_PORT"`
    MQVHost        string `env:"PAAS_MQ_VIRTUAL_HOST"`
    ExchangeName   string `env:"PAAS_EXCHANGE_NAME"`
    
    // 路径配置
    AppPath        string `env:"PAAS_APP_PATH"`
    RagPath        string `env:"PAAS_RAG_PATH"`
    
    // 超时配置
    InactiveSeconds int64 `env:"PAAS_INACTIVE_SECONDS"`
    
    // GPT 配置
    GptID          string `env:"PAAS_GPT_ID"`
    GptKey         string `env:"PAAS_GPT_KEY"`
    ProxyURL       string `env:"PAAS_PROXY_URL"`
    AuthToken      string `env:"PAAS_AUTH_TOKEN"`
    GatewayURL     string `env:"PAAS_GATEWAY_URL"`
}

// 加载配置
func LoadConfig() (*AgentConfig, error) {
    config := &AgentConfig{}
    
    // 从环境变量加载配置
    if err := env.Parse(config); err != nil {
        return nil, fmt.Errorf("解析环境变量失败: %v", err)
    }
    
    // 验证必需的配置
    if err := config.Validate(); err != nil {
        return nil, fmt.Errorf("配置验证失败: %v", err)
    }
    
    return config, nil
}
```

### 2. 动态配置更新

```go
// 配置热更新
func (a *Agent) handleConfigUpdate(message *ConfigUpdateMessage) error {
    // 解析新配置
    newConfig := &AgentConfig{}
    if err := json.Unmarshal(message.Config, newConfig); err != nil {
        return err
    }
    
    // 验证配置
    if err := newConfig.Validate(); err != nil {
        return err
    }
    
    // 应用新配置
    a.mutex.Lock()
    oldConfig := a.config
    a.config = newConfig
    a.mutex.Unlock()
    
    // 重启需要重启的服务
    if err := a.restartServices(oldConfig, newConfig); err != nil {
        // 回滚配置
        a.mutex.Lock()
        a.config = oldConfig
        a.mutex.Unlock()
        return err
    }
    
    return nil
}
```

## 监控和日志

### 1. 健康检查

```go
// 健康检查端点
func (a *Agent) healthCheck(w http.ResponseWriter, r *http.Request) {
    health := &HealthStatus{
        Status:    "healthy",
        Timestamp: time.Now().Unix(),
        Services:  make(map[string]string),
    }
    
    // 检查各个服务状态
    health.Services["mq"] = a.checkMQConnection()
    health.Services["filesystem"] = a.checkFileSystem()
    health.Services["lsp"] = a.checkLSPServers()
    
    // 判断整体健康状态
    for _, status := range health.Services {
        if status != "healthy" {
            health.Status = "unhealthy"
            break
        }
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(health)
}
```

### 2. 日志管理

```go
// 结构化日志
func (a *Agent) setupLogging() {
    logrus.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: time.RFC3339,
    })
    
    // 添加上下文字段
    logrus.SetLevel(logrus.InfoLevel)
    logrus.WithFields(logrus.Fields{
        "docker_id":     a.config.DockerID,
        "playground_id": a.config.PlaygroundID,
        "component":     "agent",
    })
}

// 日志轮转
func (a *Agent) setupLogRotation() error {
    logFile := "/var/log/agent/agent.log"
    
    // 创建日志目录
    if err := os.MkdirAll(filepath.Dir(logFile), 0755); err != nil {
        return err
    }
    
    // 配置日志轮转
    writer := &lumberjack.Logger{
        Filename:   logFile,
        MaxSize:    100, // MB
        MaxBackups: 3,
        MaxAge:     28, // days
        Compress:   true,
    }
    
    logrus.SetOutput(writer)
    return nil
}
```

## 性能优化

### 1. 内存管理

```go
// 内存池管理
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 4096)
    },
}

func (a *Agent) getBuffer() []byte {
    return bufferPool.Get().([]byte)
}

func (a *Agent) putBuffer(buf []byte) {
    bufferPool.Put(buf)
}
```

### 2. 连接复用

```go
// HTTP 客户端连接池
func (a *Agent) createHTTPClient() *http.Client {
    transport := &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableCompression:  false,
    }
    
    return &http.Client{
        Transport: transport,
        Timeout:   30 * time.Second,
    }
}
```

## 容器存储架构

### 存储架构概览

Clacky AI PaaS 采用分层存储架构，基于 BTRFS 文件系统和 NFS 共享存储，实现高效的代码管理、快照创建和容器隔离。

```
┌─────────────────────────────────────────────────────────────────┐
│                    NFS Server with BTRFS                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Git Codebase (持久化存储)                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │         /home/<USER>/app (容器运行环境)                       │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │            node_modules等仓库依赖                         │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                issue thread                    │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │ dependency(Issue/Sandbox) Lowdir 跨用户共享, host共享     │ │ │
│  │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐                 │ │ │
│  │  │  │   etc   │  │  user   │  │ home... │                 │ │ │
│  │  │  └─────────┘  └─────────┘  └─────────┘                 │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                            ▲
                             │ btrfs fork from @Meta
                             │                                     │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                root thread                                  │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │ dependency(Issue/Sandbox) Lowdir 跨用户共享, host共享     │ │ │
│  │  │  ┌─────────┐  ┌─────────┐  ┌─────────┐                 │ │ │
│  │  │  │   etc   │  │  user   │  │ home... │                 │ │ │
│  │  │  └─────────┘  └─────────┘  └─────────┘                 │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                            ▲
                             │ btrfs fork from @Meta
                             │                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                      @Meta 30G                              │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │ │
│  │  │   Languages     │  │   Middlewares   │  │ LSP/VNC/GoAgent │ │ │
│  │  │ Go/Node/Java/ru │  │   mysqlData     │  │      nt...      │ │ │
│  │  │     by/...      │  │                 │  │                 │ │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ NFS Mount
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Docker Server                           │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Playground    │  │ Middleware Images│  │                 │  │
│  │    Images       │  │  ┌─────────────┐ │  │                 │  │
│  │                 │  │  │    MYSQL    │ │  │                 │  │
│  │                 │  │  ├─────────────┤ │  │                 │  │
│  │                 │  │  │   MongoDB   │ │  │                 │  │
│  │                 │  │  ├─────────────┤ │  │                 │  │
│  │                 │  │  │    Redis    │ │  │                 │  │
│  │                 │  │  └─────────────┘ │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 1. CodeZone 存储结构

#### BTRFS Fork 架构：@meta → Root Thread → Issue Thread

基于 BTRFS 的写时复制能力，Clacky AI PaaS 实现了三层 fork 架构：

```
@meta (模板层)
    ↓ btrfs subvolume snapshot
Root Thread (根线程层)
    ↓ btrfs subvolume snapshot
Issue Thread (问题线程层/用户实例)
```

#### @meta 模板目录结构 (30G+ 预构建环境)

```
/data/@meta/
├── source/                   # 空的代码模板目录
│   └── .gitkeep             # 保持目录结构
├── dependency/              # 预构建依赖环境 (30G)
│   ├── home/                # 用户主目录模板 (4.0G)
│   │   ├── .bashrc          # Shell 配置
│   │   ├── .profile         # 环境变量
│   │   ├── .ssh/            # SSH 配置模板
│   │   └── app/             # 应用目录模板
│   ├── apt/                 # APT 包缓存 (2.7G)
│   │   ├── archives/        # 已下载的 deb 包
│   │   └── lists/           # 包列表缓存
│   ├── t/                   # 临时文件和缓存 (2.1G)
│   │   ├── cache/           # 各种缓存文件
│   │   └── tmp/             # 临时文件
│   ├── ruby/                # Ruby 环境 (预留)
│   ├── pip/                 # Python pip 缓存 (预留)
│   ├── go/                  # Go 模块缓存 (预留)
│   ├── mysqlData/           # MySQL 数据模板 (预留)
│   └── flags/               # 特性标志 (预留)
└── middleware/              # 中间件模板
    ├── mysql/
    │   ├── data/            # MySQL 初始数据
    │   └── sh/              # 启动脚本模板
    ├── redis/
    │   ├── data/            # Redis 初始配置
    │   └── sh/              # 启动脚本模板
    └── mongodb/
        ├── data/            # MongoDB 初始数据
        └── sh/              # 启动脚本模板
```

#### CodeZone 实例目录结构 (Fork 自 @meta)

```
/data/@data/codeZone/{年份}/{租户ID}/{月-日}/@{UUID}/
├── source/                  # 用户代码目录 (从 @meta/source fork)
│   ├── .git/               # Git 仓库 (用户初始化)
│   ├── src/                # 源代码 (用户创建)
│   ├── package.json        # 项目配置 (用户创建)
│   └── ...
├── dependency/             # 依赖目录 (从 @meta/dependency fork)
│   ├── home/               # 用户主目录 (4.0G, 预构建)
│   │   ├── .bashrc         # Shell 配置 (继承自模板)
│   │   ├── .ssh/           # SSH 密钥 (用户配置)
│   │   ├── .cache/         # 缓存目录 (继承自模板)
│   │   └── app/            # 应用目录 (链接到 source)
│   ├── apt/                # APT 包缓存 (2.7G, 共享)
│   ├── t/                  # 临时文件 (2.1G, 共享)
│   ├── node_modules/       # Node.js 依赖 (用户安装)
│   ├── pip/                # Python 包缓存 (用户使用)
│   ├── go/                 # Go 模块缓存 (用户使用)
│   └── ...
└── middleware/             # 中间件数据 (从 @meta/middleware fork)
    ├── mysql/
    │   ├── data/           # MySQL 数据 (用户数据)
    │   └── sh/             # 启动脚本 (继承自模板)
    ├── redis/
    │   ├── data/           # Redis 数据 (用户数据)
    │   └── sh/             # 启动脚本 (继承自模板)
    └── mongodb/
        ├── data/           # MongoDB 数据 (用户数据)
        └── sh/             # 启动脚本 (继承自模板)
```

#### BTRFS Fork 机制详解

##### 1. @meta 模板初始化

```bash
# @meta 模板创建 (系统初始化时执行一次)
btrfs subvolume create /data/@meta

# 预构建依赖环境
mkdir -p /data/@meta/dependency/{home,apt,t,ruby,pip,go,mysqlData,flags}

# 安装常用包到 APT 缓存 (2.7G)
apt-get update
apt-get install -d build-essential git curl wget nodejs npm python3 python3-pip

# 预构建用户主目录模板 (4.0G)
cp -r /etc/skel/* /data/@meta/dependency/home/
# 预安装常用工具和配置
```

##### 2. CodeZone 创建流程 (Fork 自 @meta)

```java
// CodeZone 创建过程 - 基于 @meta 模板 fork
private CodeZoneNfsNodeDTO buildCodeZonePath(Long tenantId) {
    // 1. 选择可用的存储服务
    StorageService storageService = storageProvider.getAvailableBtrfsService();

    // 2. 构建目录路径
    String folderName = getParentFolderName(tenantId);  // {年份}/{租户ID}/{月-日}/
    String folderPath = storageService.getPath() + Constant.CODE_ZONE_PATH + folderName;
    String folderPathOnBtrfs = systemProperties.getCodeZone().getPathInBtrfs() + folderName;

    // 3. 创建 NFS 挂载目录
    File file = new File(folderPath);
    file.mkdirs();

    // 4. 生成唯一的 CodeZone 名称
    String codeZonePathName = "@" + UUID.randomUUID();

    // 5. 使用 BTRFS 快照从 @meta 模板创建新的 CodeZone
    String cmd = systemProperties.getBtrfs().getCreateCmd()
        .replace("{source}", systemProperties.getCodeZone().getMeta())  // /data/@meta
        .replace("{target}", DirUtil.join(folderPathOnBtrfs, codeZonePathName));

    // 6. 执行 BTRFS 快照命令
    boolean success = storageService.execCommandBtrfs(cmd);
    // 实际执行: btrfs subvolume snapshot /data/@meta /data/@data/codeZone/{path}/@{uuid}
    // 这会瞬间创建一个包含 30G+ 预构建环境的 CodeZone 实例

    if (!success) {
        throw new CustomRuntimeException("Failed to create CodeZone from @meta template");
    }

    return new CodeZoneNfsNodeDTO()
        .setRootPath(folderPath + codeZonePathName + "/")
        .setNfsNodeId(storageService.getNfsNodeId());
}
```

##### 3. Fork 性能优势

```java
// BTRFS 写时复制的性能优势
public class BtrfsForkPerformance {

    // 传统复制 vs BTRFS 快照对比
    public void performanceComparison() {
        // 传统文件复制: 30G 数据需要 5-10 分钟
        // cp -r /data/@meta /data/@data/codeZone/new-instance

        // BTRFS 快照: 30G 数据只需要 1-2 秒
        // btrfs subvolume snapshot /data/@meta /data/@data/codeZone/new-instance

        // 优势:
        // 1. 瞬时创建: 不需要实际复制数据
        // 2. 空间效率: 只有修改的部分才占用额外空间
        // 3. 一致性: 原子操作，不会出现部分复制的情况
    }

    // 写时复制机制
    public void copyOnWriteMechanism() {
        // 初始状态: 新 CodeZone 与 @meta 共享所有数据块
        // 用户修改文件时: 只复制被修改的数据块
        // 未修改的文件: 继续与 @meta 共享，节省存储空间

        // 示例: 用户只修改了 package.json
        // 实际占用空间: 几 KB (只有 package.json 的差异)
        // 可访问空间: 30G+ (包含所有预构建环境)
    }
}
```

##### 4. 存储空间优化实例

```bash
# 实际存储使用情况示例

# @meta 模板 (一次性创建)
/data/@meta                    30G    # 预构建环境模板

# 用户 CodeZone 实例 (写时复制)
/data/@data/codeZone/2024/tenant1/01-15/@uuid1/
├── dependency/
│   ├── home/                  0      # 共享自 @meta，未修改
│   ├── apt/                   0      # 共享自 @meta，未修改
│   ├── t/                     0      # 共享自 @meta，未修改
│   ├── node_modules/          500M   # 用户安装的依赖
│   └── .ssh/                  4K     # 用户 SSH 密钥
├── source/
│   ├── src/                   10M    # 用户代码
│   ├── package.json           2K     # 项目配置
│   └── .git/                  50M    # Git 仓库
└── middleware/
    ├── mysql/data/            100M   # 用户 MySQL 数据
    └── redis/data/            10M    # 用户 Redis 数据

# 总计: 670M (实际占用) vs 30G+ (可用空间)
# 空间效率: 97.8% 节省
```

##### 5. 多租户共享优化

```java
// 多租户环境下的存储优化
public class MultiTenantStorageOptimization {

    // 共享依赖管理
    public void sharedDependencyManagement() {
        // 场景: 1000 个用户同时使用 Node.js 环境

        // 传统方式: 1000 × 30G = 30TB
        // BTRFS 方式: 30G (模板) + 1000 × 平均修改量(500M) = 30G + 500G = 530G
        // 节省空间: 96.5%

        // 共享的内容:
        // - APT 包缓存 (2.7G) - 所有用户共享
        // - 系统工具和库 (4.0G) - 所有用户共享
        // - 基础配置文件 - 所有用户共享

        // 用户独有的内容:
        // - 用户代码和项目文件
        // - 用户安装的特定依赖
        // - 用户配置和数据
    }

    // 动态依赖加载
    public void dynamicDependencyLoading() {
        // 当用户需要新的语言环境时
        // 1. 检查是否已有对应的 @meta 模板
        // 2. 如果没有，创建新的语言特定模板
        // 3. 用户 CodeZone fork 自对应的模板

        // 示例: Python 环境
        // /data/@meta-python     # Python 特定模板
        // /data/@meta-java       # Java 特定模板
        // /data/@meta-go         # Go 特定模板
    }
}
```

##### 6. 容器挂载优化

```java
// 基于 BTRFS fork 的容器挂载策略
private List<Bind> generateOptimizedBindList(DockerContainer docker) {
    List<Bind> bindList = new ArrayList<>();

    // 1. 检查依赖目录是否已预构建
    String dependencyPath = docker.getRootPath() + Constant.CODE_ZONE_DEPENDENCY_PATH + "/";
    String dependencyHomePath = dependencyPath + "home";

    File homeDir = new File(dependencyHomePath);
    if (homeDir.exists() && homeDir.isDirectory()) {
        // 使用预构建的完整环境 (从 @meta fork 而来)
        bindList.add(Bind.parse(dependencyHomePath + ":" +
                               systemProperties.getDocker().getStorage().getHomePath()));

        log.info("使用预构建环境，包含 30G+ 依赖缓存");
    } else {
        // 降级到基础挂载模式
        bindList.add(Bind.parse(
            docker.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH + ":" +
            systemProperties.getDocker().getStorage().getAppPath()
        ));

        log.info("使用基础挂载模式");
    }

    // 2. 挂载共享的 APT 缓存 (只读)
    String aptCachePath = dependencyPath + "apt";
    if (new File(aptCachePath).exists()) {
        bindList.add(Bind.parse(aptCachePath + ":/var/cache/apt:ro"));
    }

    // 3. 挂载共享的包管理器缓存
    mountPackageManagerCaches(bindList, dependencyPath);

    return bindList;
}

private void mountPackageManagerCaches(List<Bind> bindList, String dependencyPath) {
    // Node.js npm 缓存
    String npmCachePath = dependencyPath + "npm";
    if (new File(npmCachePath).exists()) {
        bindList.add(Bind.parse(npmCachePath + ":/home/<USER>/.npm"));
    }

    // Python pip 缓存
    String pipCachePath = dependencyPath + "pip";
    if (new File(pipCachePath).exists()) {
        bindList.add(Bind.parse(pipCachePath + ":/home/<USER>/.cache/pip"));
    }

    // Go 模块缓存
    String goCachePath = dependencyPath + "go";
    if (new File(goCachePath).exists()) {
        bindList.add(Bind.parse(goCachePath + ":/home/<USER>/go/pkg/mod"));
    }
}
```

### 2. BTRFS 文件系统架构

#### BTRFS 子卷管理

```java
// BTRFS 服务实现
@Service
public class BtrfsService implements StorageService {

    // 执行 BTRFS 命令（带限流控制）
    public boolean execCommandBtrfs(String cmd) {
        // 1. 获取令牌桶限流
        RRateLimiter rateLimiter = redisRedissonUtil.getRateLimiter(
            systemProperties.getBtrfs().getSnapshotRateLimit(),  // 每秒10个快照
            1L,
            RedisPrefix.REDISSON_REDIS_BTRFS + ":" + nfsNodeId
        );

        // 2. 尝试获取令牌
        if (!rateLimiter.tryAcquire(waitTimeMs, TimeUnit.MILLISECONDS)) {
            return false;  // 限流拒绝
        }

        // 3. 执行命令
        return executeCommand(cmd);
    }
}
```

#### 快照和克隆机制

```java
// CodeZone Fork 流程
public String fork(String sourcePath, String commitId, String parentPath,
                  String parentPathInBtrfs, SnapshotPublicationEnum type, CodeZone codeZone) {

    // 1. 创建父目录
    File file = new File(parentPath);
    file.mkdirs();

    // 2. 生成新的子卷名称
    String codeZonePathName = "@" + UUID.randomUUID();

    // 3. 构建 BTRFS 快照命令
    String rootPathOnFileServer = sourcePath.replace(
        storageService.getPath() + Constant.CODE_ZONE_PATH,
        systemProperties.getCodeZone().getPathInBtrfs()
    );

    String cmd = systemProperties.getBtrfs().getCreateCmd()
        .replace("{source}", rootPathOnFileServer)
        .replace("{target}", DirUtil.join(parentPathInBtrfs, codeZonePathName));

    // 4. 执行快照创建
    boolean success = storageService.execCommandBtrfs(cmd);

    // 5. Git 重置到指定提交
    if (StringUtils.isNotBlank(commitId)) {
        String newSourcePath = parentPath + codeZonePathName + "/" + Constant.CODE_ZONE_SOURCE_PATH;
        gitExternalService.reset(newSourcePath, commitId);

        // 重新初始化 Git 仓库
        fileUtilService.delete(new File(newSourcePath + "/.git"));
        gitExternalService.gitInit(newSourcePath);
    }

    return parentPath + codeZonePathName + "/";
}
```

### 3. 容器存储挂载

#### 存储卷挂载配置

```java
// 生成容器存储挂载配置
private List<Bind> generateBindList(DockerContainer docker, String languagePackage) {
    List<Bind> bindList = new ArrayList<>();

    // 1. Agent 可执行文件挂载（只读）
    bindList.add(Bind.parse(
        systemProperties.getDocker().getAgent().getHostPath() + ":" +
        systemProperties.getDocker().getAgent().getDockerPath() + ":ro"
    ));

    // 2. 依赖目录挂载
    String dependencyPath = docker.getRootPath() + Constant.CODE_ZONE_DEPENDENCY_PATH + "/";
    String dependencyHomePath = dependencyPath + "home";
    String dependencyHomePathApp = dependencyHomePath + "/app";

    File homeAppDir = new File(dependencyHomePathApp);
    if (!(homeAppDir.exists() && homeAppDir.isDirectory())) {
        // 代码挂载到 /home/<USER>/app 目录
        bindList.add(Bind.parse(
            docker.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH + ":" +
            systemProperties.getDocker().getStorage().getAppPath()
        ));

        // SSH 密钥挂载
        if (FileUtil.exist(dependencyPath + ".ssh")) {
            bindList.add(Bind.parse(
                dependencyPath + ".ssh" + ":" +
                systemProperties.getDocker().getStorage().getHomePath() + ".ssh"
            ));
        }
    } else {
        // 使用预构建的依赖环境
        bindList.add(Bind.parse(dependencyHomePath + ":" +
                               systemProperties.getDocker().getStorage().getHomePath()));
    }

    // 3. 语言包挂载
    if (StringUtils.isNotBlank(languagePackage)) {
        bindList.add(Bind.parse(languagePackage + ":/cache/nix:ro"));
    }

    // 4. 中间件数据挂载
    for (MiddlewareInstance middleware : docker.getMiddlewares()) {
        String dataPath = DirUtil.join(
            docker.getRootPath(),
            Constant.CODE_ZONE_MIDDLEWARE_PATH,
            middleware.getConfig().getDefine().getCode(),
            Constant.CODE_ZONE_MIDDLEWARE_DATA_PATH
        );
        bindList.add(Bind.parse(
            dataPath + ":" + middleware.getConfig().getDefine().getDockerDataPath()
        ));
    }

    // 5. 磁盘资源挂载
    if (dockerDiskResourceRepository.existsByDockerContainerId(docker.getId())) {
        List<DockerDiskResource> diskResources =
            dockerDiskResourceRepository.findAllByDockerContainerId(docker.getId());
        for (DockerDiskResource diskResource : diskResources) {
            bindList.add(Bind.parse(
                diskResource.getPath() + ":" + diskResource.getMountPath()
            ));
        }
    }

    return bindList;
}
```

### 4. 存储路径映射

#### 宿主机到容器的路径映射

```yaml
# 存储配置映射
storage:
  # NFS 挂载路径 (宿主机视角)
  nfs-path: /app/data/dockerContainer/

  # BTRFS 文件系统路径 (存储服务器视角)
  file-server-path: /data/@data/dockerContainer/

  # 容器内路径映射
  home-path: /home/<USER>/          # 用户主目录
  app-path: /home/<USER>/app        # 应用代码目录
  rag-path: /home/<USER>/.rag       # RAG 数据目录

# CodeZone 配置
code-zone:
  # NFS 挂载路径
  path: /app/data/codeZone

  # BTRFS 路径
  path-in-btrfs: /data/@data/codeZone

  # 快照路径
  snapshot-path: /app/data/codeZoneSnapshot
  snapshot-path-in-btrfs: /data/@data/codeZoneSnapshot

  # 模板路径
  meta: /data/@meta
```

#### 容器内文件系统结构

```
容器内文件系统:
/home/<USER>/
├── app/                      # 用户代码 (挂载自 CodeZone/source)
│   ├── src/
│   ├── package.json
│   └── ...
├── .ssh/                     # SSH 密钥 (挂载自 CodeZone/dependency/.ssh)
├── .rag/                     # RAG 数据目录
└── .cache/                   # 缓存目录

/agent                        # Agent 可执行文件 (只读挂载)

/cache/nix/                   # Nix 语言包 (只读挂载)
├── go/
├── node/
├── java/
└── ...

# 中间件数据目录 (各自独立挂载)
/var/lib/mysql/               # MySQL 数据
/var/lib/redis/               # Redis 数据
/var/lib/mongodb/             # MongoDB 数据
```

### 5. 存储性能优化

#### BTRFS 快照优化

```java
// 快照创建限流和优化
@Configuration
public class StorageOptimization {

    // 1. 快照创建限流 (每秒最多10个)
    @Value("${system.btrfs.snapshot-rate-limit:10}")
    private Long snapshotRateLimit;

    // 2. 快照等待超时 (最多等待10秒)
    @Value("${system.btrfs.snapshot-wait-limit:10}")
    private Long snapshotWaitLimit;

    // 3. 快照池预创建
    public void preCreateSnapshots(CodeZoneSnapshot snapshot, int poolSize) {
        RDeque<String> forkPool = redisson.getDeque(
            RedisPrefix.CODE_ZONE_SNAPSHOT_FORK_POOL + snapshot.getId()
        );

        // 异步预创建快照池
        CompletableFuture.runAsync(() -> {
            for (int i = 0; i < poolSize; i++) {
                String dockerContainerDir = createSnapshotFromPool(snapshot);
                forkPool.offer(dockerContainerDir);
            }
        });
    }
}
```

#### 存储空间管理

```java
// 存储清理和回收
@Service
public class StorageCleanupService {

    // 定期清理过期的 CodeZone
    @Scheduled(cron = "0 0 2 * * ?")  // 每天凌晨2点执行
    public void cleanupExpiredCodeZones() {
        List<CodeZone> expiredCodeZones = codeZoneRepository
            .findExpiredCodeZones(LocalDateTime.now().minusDays(30));

        for (CodeZone codeZone : expiredCodeZones) {
            // 删除 BTRFS 子卷
            String deleteCmd = systemProperties.getBtrfs().getRemoveCmd()
                .replace("{target}", getBtrfsPath(codeZone));

            storageService.execCommandBtrfs(deleteCmd);

            // 删除数据库记录
            codeZoneRepository.delete(codeZone);
        }
    }

    // 清理孤立的容器存储
    @Scheduled(cron = "0 30 2 * * ?")  // 每天凌晨2:30执行
    public void cleanupOrphanedContainerStorage() {
        // 查找没有对应容器的存储目录
        // 执行清理操作
    }
}
```

### 6. 存储安全和权限管理

#### 文件权限控制

```java
// 容器内文件权限设置
public class FilePermissionManager {

    // 设置 CodeZone 文件权限
    public void setupCodeZonePermissions(String rootPath) {
        // 1. 源代码目录权限 (用户可读写)
        String sourcePath = rootPath + Constant.CODE_ZONE_SOURCE_PATH;
        setPermissions(sourcePath, "755", "runner:runner");

        // 2. 依赖目录权限 (用户可读写，部分只读)
        String dependencyPath = rootPath + Constant.CODE_ZONE_DEPENDENCY_PATH;
        setPermissions(dependencyPath, "755", "runner:runner");

        // 3. SSH 密钥权限 (严格控制)
        String sshPath = dependencyPath + "/.ssh";
        if (Files.exists(Paths.get(sshPath))) {
            setPermissions(sshPath, "700", "runner:runner");
            setPermissions(sshPath + "/id_rsa", "600", "runner:runner");
            setPermissions(sshPath + "/id_rsa.pub", "644", "runner:runner");
        }

        // 4. 中间件数据权限
        String middlewarePath = rootPath + Constant.CODE_ZONE_MIDDLEWARE_PATH;
        setPermissions(middlewarePath, "755", "runner:runner");
    }

    private void setPermissions(String path, String mode, String owner) {
        try {
            // 设置文件权限
            ProcessBuilder pb = new ProcessBuilder("chmod", "-R", mode, path);
            pb.start().waitFor();

            // 设置文件所有者
            ProcessBuilder pb2 = new ProcessBuilder("chown", "-R", owner, path);
            pb2.start().waitFor();
        } catch (Exception e) {
            log.error("设置文件权限失败: {}", e.getMessage());
        }
    }
}
```

#### 存储访问控制

```java
// 存储访问安全检查
public class StorageSecurityManager {

    // 验证路径安全性
    public boolean isPathSafe(String requestPath, String allowedBasePath) {
        try {
            Path normalizedPath = Paths.get(requestPath).normalize();
            Path basePath = Paths.get(allowedBasePath).normalize();

            // 检查是否在允许的基础路径内
            return normalizedPath.startsWith(basePath);
        } catch (Exception e) {
            return false;
        }
    }

    // 检查用户对 CodeZone 的访问权限
    public boolean hasCodeZoneAccess(Long userId, Long tenantId, String codeZonePath) {
        // 1. 验证用户身份
        if (!isValidUser(userId, tenantId)) {
            return false;
        }

        // 2. 检查路径是否属于该租户
        String expectedPathPrefix = buildTenantPath(tenantId);
        if (!codeZonePath.startsWith(expectedPathPrefix)) {
            return false;
        }

        // 3. 检查用户对该 CodeZone 的权限
        return hasUserPermission(userId, codeZonePath);
    }
}
```

### 7. 存储监控和运维

#### 存储使用监控

```java
// 存储空间监控
@Component
public class StorageMonitor {

    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void monitorStorageUsage() {
        // 1. 检查 NFS 挂载点使用率
        checkNfsUsage();

        // 2. 检查 BTRFS 子卷数量
        checkBtrfsSubvolumeCount();

        // 3. 检查存储性能指标
        checkStoragePerformance();

        // 4. 发送告警
        sendAlertsIfNeeded();
    }

    private void checkNfsUsage() {
        try {
            // 获取磁盘使用情况
            ProcessBuilder pb = new ProcessBuilder("df", "-h", "/app/data");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream())
            );

            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("/app/data")) {
                    String[] parts = line.split("\\s+");
                    String usagePercent = parts[4].replace("%", "");

                    int usage = Integer.parseInt(usagePercent);
                    if (usage > 85) {
                        sendStorageAlert("NFS 存储使用率过高: " + usage + "%");
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查 NFS 使用率失败", e);
        }
    }

    private void checkBtrfsSubvolumeCount() {
        try {
            // 统计 BTRFS 子卷数量
            ProcessBuilder pb = new ProcessBuilder("btrfs", "subvolume", "list", "/data");
            Process process = pb.start();

            long subvolumeCount = process.getInputStream()
                .lines()
                .count();

            if (subvolumeCount > 10000) {
                sendStorageAlert("BTRFS 子卷数量过多: " + subvolumeCount);
            }
        } catch (Exception e) {
            log.error("检查 BTRFS 子卷数量失败", e);
        }
    }
}
```

#### 存储备份策略

```java
// 存储备份管理
@Service
public class StorageBackupService {

    // 创建 CodeZone 备份
    @Async
    public CompletableFuture<Void> backupCodeZone(CodeZone codeZone) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 1. 创建备份目录
                String backupPath = createBackupPath(codeZone);

                // 2. 创建 BTRFS 只读快照
                String snapshotCmd = "btrfs subvolume snapshot -r " +
                    getBtrfsPath(codeZone) + " " + backupPath;

                boolean success = storageService.execCommandBtrfs(snapshotCmd);
                if (!success) {
                    throw new RuntimeException("创建备份快照失败");
                }

                // 3. 压缩备份 (可选)
                if (systemProperties.getBackup().isCompressionEnabled()) {
                    compressBackup(backupPath);
                }

                // 4. 上传到远程存储 (可选)
                if (systemProperties.getBackup().isRemoteEnabled()) {
                    uploadToRemoteStorage(backupPath);
                }

                log.info("CodeZone 备份完成: {}", codeZone.getId());

            } catch (Exception e) {
                log.error("CodeZone 备份失败: {}", codeZone.getId(), e);
            }
        });
    }

    // 恢复 CodeZone
    public void restoreCodeZone(Long codeZoneId, String backupPath) {
        try {
            CodeZone codeZone = codeZoneRepository.findById(codeZoneId)
                .orElseThrow(() -> new RuntimeException("CodeZone 不存在"));

            // 1. 停止相关容器
            stopRelatedContainers(codeZone);

            // 2. 删除当前数据
            String currentPath = getBtrfsPath(codeZone);
            String deleteCmd = "btrfs subvolume delete " + currentPath;
            storageService.execCommandBtrfs(deleteCmd);

            // 3. 从备份恢复
            String restoreCmd = "btrfs subvolume snapshot " + backupPath + " " + currentPath;
            boolean success = storageService.execCommandBtrfs(restoreCmd);

            if (!success) {
                throw new RuntimeException("恢复备份失败");
            }

            log.info("CodeZone 恢复完成: {}", codeZoneId);

        } catch (Exception e) {
            log.error("CodeZone 恢复失败: {}", codeZoneId, e);
            throw new RuntimeException("恢复失败", e);
        }
    }
}
```

### 8. 存储故障排查

#### 常见存储问题诊断

```bash
# 1. 检查 NFS 挂载状态
mount | grep nfs
df -h | grep nfs

# 2. 检查 BTRFS 文件系统状态
btrfs filesystem show
btrfs filesystem usage /data

# 3. 检查子卷状态
btrfs subvolume list /data
btrfs subvolume show /data/@data/codeZone/{path}

# 4. 检查存储性能
iostat -x 1 10
iotop -o

# 5. 检查文件权限
ls -la /app/data/codeZone/
ls -la /app/data/dockerContainer/
```

#### 存储修复工具

```java
// 存储修复服务
@Service
public class StorageRepairService {

    // 修复损坏的 CodeZone
    public boolean repairCodeZone(Long codeZoneId) {
        try {
            CodeZone codeZone = codeZoneRepository.findById(codeZoneId)
                .orElseThrow(() -> new RuntimeException("CodeZone 不存在"));

            String codeZonePath = codeZone.getRootPath();

            // 1. 检查文件系统完整性
            if (!checkFileSystemIntegrity(codeZonePath)) {
                log.warn("CodeZone 文件系统损坏: {}", codeZoneId);
                return false;
            }

            // 2. 修复文件权限
            repairFilePermissions(codeZonePath);

            // 3. 修复 Git 仓库 (如果损坏)
            repairGitRepository(codeZonePath + "/source");

            // 4. 重建依赖 (如果需要)
            rebuildDependencies(codeZonePath);

            log.info("CodeZone 修复完成: {}", codeZoneId);
            return true;

        } catch (Exception e) {
            log.error("CodeZone 修复失败: {}", codeZoneId, e);
            return false;
        }
    }

    // 清理孤立的存储
    public void cleanupOrphanedStorage() {
        try {
            // 1. 扫描存储目录
            List<String> storagePaths = scanStorageDirectories();

            // 2. 检查数据库记录
            for (String path : storagePaths) {
                if (!hasValidDatabaseRecord(path)) {
                    // 3. 删除孤立的存储
                    deleteOrphanedStorage(path);
                    log.info("清理孤立存储: {}", path);
                }
            }

        } catch (Exception e) {
            log.error("清理孤立存储失败", e);
        }
    }
}
```

这个存储架构设计确保了：

1. **高效的快照和克隆**：基于 BTRFS 的写时复制机制，快速创建 CodeZone 副本
2. **灵活的存储挂载**：支持代码、依赖、中间件数据的独立挂载和管理
3. **存储资源隔离**：每个用户和项目拥有独立的存储空间，确保数据安全
4. **性能优化**：通过限流、预创建快照池和定期清理保证系统性能
5. **数据持久化**：重要数据通过 NFS 共享存储保证持久性和可靠性
6. **安全控制**：完善的权限管理和访问控制机制
7. **监控运维**：全面的存储监控、备份和故障修复能力

这种分层存储架构为 Clacky AI PaaS 提供了强大的存储基础，支持大规模的在线编程环境部署和管理。

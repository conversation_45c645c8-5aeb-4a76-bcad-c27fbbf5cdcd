package linters

import (
	"agent/consts"
	"agent/file/watch"
	"errors"
	"path/filepath"
	"testing"
	"time"
)

// MockLinterManager implements LinterManagerIface for testing
type MockLinterManager struct {
	DetectedLanguages     map[string]LanguageType
	InstalledLinters      map[string]bool
	DetectAndInstallError error
	DetectAndInstallCalls []string
	LintError             error
}

func (m *MockLinterManager) ManagerInitialize() error {
	//TODO implement me
	panic("implement me")
}

func (m *MockLinterManager) RegisterLinter(linter LinterIface) error {
	return nil
}

func (m *MockLinterManager) GetLinterForFile(filePath string) ([]LinterIface, error) {
	return nil, nil
}

func (m *MockLinterManager) GetLinterForLanguage(language LanguageType) ([]LinterIface, error) {
	return nil, nil
}

func (m *MockLinterManager) LintFile(filePath string) ([]*LintResult, error) {
	return nil, nil
}

func (m *MockLinterManager) FixFile(filePath string) ([]*FixResult, error) {
	return nil, nil
}

func NewMockLinterManager() *MockLinterManager {
	return &MockLinterManager{
		DetectedLanguages:     make(map[string]LanguageType),
		InstalledLinters:      make(map[string]bool),
		DetectAndInstallCalls: make([]string, 0),
	}
}

func (m *MockLinterManager) DetectAndInstallForFile(filePath string) error {
	m.DetectAndInstallCalls = append(m.DetectAndInstallCalls, filePath)
	return m.DetectAndInstallError
}

func (m *MockLinterManager) IsLinterInstalled(filePath string) bool {
	return m.InstalledLinters[filePath]
}

func (m *MockLinterManager) CancelLint(filePath string) {
	// No-op for mock
}

func (m *MockLinterManager) GetInstalledLinters() []string {
	installedLinters := make([]string, 0)
	for linter, installed := range m.InstalledLinters {
		if installed {
			installedLinters = append(installedLinters, linter)
		}
	}
	return installedLinters
}

func (m *MockLinterManager) DetectLanguageForFile(filePath string) LanguageType {
	return m.DetectedLanguages[filePath]
}

func TestLinterHandleFileChanges(t *testing.T) {
	// Create mock linter manager
	mock := NewMockLinterManager()

	// Mock GetLinterManagerFunc to return our mock
	originalGetLinterManager := GetLinterManagerFunc
	GetLinterManagerFunc = func() LinterManagerIface {
		return mock
	}
	defer func() {
		GetLinterManagerFunc = originalGetLinterManager
	}()

	// Create channel for file changes
	fileChangesChan := make(chan []watch.FileChange, 10)

	// Start goroutine to process changes
	go LinterHandleFileChanges(fileChangesChan)

	// Test cases
	tests := []struct {
		name                string
		changes             []watch.FileChange
		expectInstallCalled bool
		installError        error
		fileCount           int
	}{
		{
			name: "Single file create event",
			changes: []watch.FileChange{
				{
					Path:   "test.js",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: true,
			fileCount:           1,
		},
		{
			name: "Single file update event",
			changes: []watch.FileChange{
				{
					Path:   "test.py",
					Key:    consts.FileType,
					Change: consts.FileChangeUpdate,
				},
			},
			expectInstallCalled: true,
			fileCount:           1,
		},
		{
			name: "File delete event (should be ignored)",
			changes: []watch.FileChange{
				{
					Path:   "test.go",
					Key:    consts.FileType,
					Change: consts.FileChangeRemove,
				},
			},
			expectInstallCalled: false,
			fileCount:           0,
		},
		{
			name: "Directory event (should be ignored)",
			changes: []watch.FileChange{
				{
					Path:   "testdir",
					Key:    consts.DirType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: false,
			fileCount:           0,
		},
		{
			name: "File with no extension (should be ignored)",
			changes: []watch.FileChange{
				{
					Path:   "testfile",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: false,
			fileCount:           0,
		},
		{
			name: "Multiple files with mixed events",
			changes: []watch.FileChange{
				{
					Path:   "test1.js",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
				{
					Path:   "test2.py",
					Key:    consts.FileType,
					Change: consts.FileChangeUpdate,
				},
				{
					Path:   "test3.go",
					Key:    consts.FileType,
					Change: consts.FileChangeRemove, // Should be ignored
				},
				{
					Path:   "testdir",
					Key:    consts.DirType,
					Change: consts.FileChangeCreate, // Should be ignored
				},
				{
					Path:   "testfile",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate, // Should be ignored (no extension)
				},
			},
			expectInstallCalled: true,
			fileCount:           2, // Only 2 files should trigger installation
		},
		{
			name: "Installation error handling",
			changes: []watch.FileChange{
				{
					Path:   "test.rb",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: true,
			installError:        errors.New("installation failed"),
			fileCount:           1,
		},
	}

	// Run test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock
			mock.DetectAndInstallCalls = nil
			mock.DetectAndInstallError = tt.installError

			// Send changes to channel
			fileChangesChan <- tt.changes

			// Give time for processing
			time.Sleep(100 * time.Millisecond)

			// Check results
			if tt.expectInstallCalled {
				if len(mock.DetectAndInstallCalls) != tt.fileCount {
					t.Errorf("Expected DetectAndInstallForFile to be called %d times, got %d",
						tt.fileCount, len(mock.DetectAndInstallCalls))
				}
			} else {
				if len(mock.DetectAndInstallCalls) > 0 {
					t.Errorf("Expected DetectAndInstallForFile not to be called, but was called %d times",
						len(mock.DetectAndInstallCalls))
				}
			}

			// For each file that should have been processed, verify it was
			if tt.expectInstallCalled && tt.fileCount > 0 {
				processedFiles := make(map[string]bool)
				for _, call := range mock.DetectAndInstallCalls {
					processedFiles[call] = true
				}

				for _, change := range tt.changes {
					// Only check relevant files
					if change.Key == consts.FileType &&
						(change.Change == consts.FileChangeCreate || change.Change == consts.FileChangeUpdate) &&
						filepath.Ext(change.Path) != "" {
						if !processedFiles[change.Path] {
							t.Errorf("Expected file %s to be processed, but it wasn't", change.Path)
						}
					}
				}
			}
		})
	}

	// Clean up
	close(fileChangesChan)
}

// Test that the function properly handles different file extensions
func TestLinterHandleFileChangesWithDifferentExtensions(t *testing.T) {
	// Create mock linter manager
	mock := NewMockLinterManager()

	// Mock GetLinterManagerFunc to return our mock
	originalGetLinterManager := GetLinterManagerFunc
	GetLinterManagerFunc = func() LinterManagerIface {
		return mock
	}
	defer func() {
		GetLinterManagerFunc = originalGetLinterManager
	}()

	// Create channel for file changes
	fileChangesChan := make(chan []watch.FileChange, 10)

	// Start goroutine to process changes
	go LinterHandleFileChanges(fileChangesChan)

	// Create changes with different file extensions
	changes := []watch.FileChange{
		{
			Path:   "test.js",
			Key:    consts.FileType,
			Change: consts.FileChangeCreate,
		},
		{
			Path:   "test.py",
			Key:    consts.FileType,
			Change: consts.FileChangeCreate,
		},
		{
			Path:   "test.go",
			Key:    consts.FileType,
			Change: consts.FileChangeCreate,
		},
		{
			Path:   "test.ts",
			Key:    consts.FileType,
			Change: consts.FileChangeCreate,
		},
		{
			Path:   "test.rb",
			Key:    consts.FileType,
			Change: consts.FileChangeCreate,
		},
	}

	// Send changes to channel
	fileChangesChan <- changes

	// Give time for processing
	time.Sleep(100 * time.Millisecond)

	// Check that all files were processed
	if len(mock.DetectAndInstallCalls) != len(changes) {
		t.Errorf("Expected %d files to be processed, got %d", len(changes), len(mock.DetectAndInstallCalls))
	}

	// Check each file was processed
	for _, change := range changes {
		found := false
		for _, call := range mock.DetectAndInstallCalls {
			if call == change.Path {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("File %s was not processed", change.Path)
		}
	}

	// Clean up
	close(fileChangesChan)
}

// Test boundary conditions
func TestLinterHandleFileChangesBoundaryConditions(t *testing.T) {
	// Create mock linter manager
	mock := NewMockLinterManager()

	// Mock GetLinterManagerFunc to return our mock
	originalGetLinterManager := GetLinterManagerFunc
	GetLinterManagerFunc = func() LinterManagerIface {
		return mock
	}
	defer func() {
		GetLinterManagerFunc = originalGetLinterManager
	}()

	// Create channel for file changes
	fileChangesChan := make(chan []watch.FileChange, 10)

	// Start goroutine to process changes
	go LinterHandleFileChanges(fileChangesChan)

	// Test cases
	tests := []struct {
		name                string
		changes             []watch.FileChange
		expectInstallCalled bool
	}{
		{
			name:                "Empty changes slice",
			changes:             []watch.FileChange{},
			expectInstallCalled: false,
		},
		{
			name: "Empty file paths",
			changes: []watch.FileChange{
				{
					Path:   "",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: false, // No extension
		},
		{
			name: "Hidden files",
			changes: []watch.FileChange{
				{
					Path:   ".gitignore",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
				{
					Path:   ".env",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: false, // No extension (in the way we check)
		},
		{
			name: "Files with unusual extensions",
			changes: []watch.FileChange{
				{
					Path:   "test.weird",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
				{
					Path:   "test.123",
					Key:    consts.FileType,
					Change: consts.FileChangeCreate,
				},
			},
			expectInstallCalled: true, // Has extension, even if unusual
		},
	}

	// Run test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock
			mock.DetectAndInstallCalls = nil

			// Send changes to channel
			fileChangesChan <- tt.changes

			// Give time for processing
			time.Sleep(100 * time.Millisecond)

			// Check results
			if tt.expectInstallCalled {
				if len(mock.DetectAndInstallCalls) == 0 {
					t.Errorf("Expected DetectAndInstallForFile to be called, but it wasn't")
				}
			} else {
				if len(mock.DetectAndInstallCalls) > 0 {
					t.Errorf("Expected DetectAndInstallForFile not to be called, but was called %d times",
						len(mock.DetectAndInstallCalls))
				}
			}
		})
	}

	// Clean up
	close(fileChangesChan)
}

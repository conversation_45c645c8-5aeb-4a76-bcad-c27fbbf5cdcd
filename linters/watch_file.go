package linters

import (
	"agent/consts"
	"agent/file/watch"
	"agent/utils/log"
	"path/filepath"
	"strings"
)

// LinterHandleFileChanges processes file change events and installs appropriate linters
func LinterHandleFileChanges(linterFileChanges<PERSON>han chan []watch.FileChange) {
	// Subscribe to file change events
	for fileChanges := range linterFileChangesChan {
		for _, change := range fileChanges {
			// Only handle file create or update events
			//log.Infof("Auto-install for change %+v", change)
			if change.Change != consts.FileChangeCreate && change.Change != consts.FileChangeUpdate {
				continue
			}

			// Skip directories
			if change.Key == consts.DirType {
				continue
			}

			// Skip hidden files (files starting with .)
			if strings.HasPrefix(filepath.Base(change.Path), ".") {
				continue
			}

			// Get file extension
			ext := filepath.Ext(change.Path)
			if ext == "" {
				continue
			}

			// Try to auto-install linter for this file type
			if err := GetLinterManagerFunc().DetectAndInstallForFile(change.Path); err != nil {
				log.Debugf("Auto-install for extension %s failed: %v", ext, err)
			}
		}
	}
}

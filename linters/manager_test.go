package linters

import (
	"agent/config"
	"errors"
	"strings"
	"sync"
	"testing"
)

// Mock<PERSON>inter implements LinterIface for testing
type MockLinter struct {
	LintResults      *LintResult
	FixResults       *FixResult
	LintError        error
	FixError         error
	InstallError     error
	IsInstalledValue bool
	TypeValue        LinterType
	LanguageValue    LanguageType
}

func NewMockLinter(linterType LinterType, language LanguageType) *MockLinter {
	return &MockLinter{
		TypeValue:        linterType,
		LanguageValue:    language,
		IsInstalledValue: true,
		LintResults: &LintResult{
			FilePath: "",
			Issues:   []LintIssue{},
			Success:  true,
			Error:    "",
		},
		FixResults: &FixResult{
			FilePath: "",
			Fixed:    false,
			FixCount: 0,
			Error:    "",
		},
	}
}

func (m *MockLinter) Install() error {
	return m.InstallError
}

func (m *MockLinter) IsInstalled() bool {
	return m.IsInstalledValue
}

func (m *MockLinter) Lint(filePath string) (*LintResult, error) {
	if m.LintError != nil {
		return nil, m.LintError
	}
	m.LintResults.FilePath = filePath
	return m.LintResults, nil
}

func (m *MockLinter) Fix(filePath string) (*FixResult, error) {
	if m.FixError != nil {
		return nil, m.FixError
	}
	m.FixResults.FilePath = filePath
	return m.FixResults, nil
}

func (m *MockLinter) GetType() LinterType {
	return m.TypeValue
}

func (m *MockLinter) GetLanguage() LanguageType {
	return m.LanguageValue
}

// MockInstaller implements InstallerIface for testing
//type MockInstaller struct {
//	InstalledLinters map[LinterType]bool
//	InstallError     error
//}
//
//func NewMockInstaller() *MockInstaller {
//	return &MockInstaller{
//		InstalledLinters: make(map[LinterType]bool),
//	}
//}

//func (m *MockInstaller) Install(linterType LinterType) error {
//	if m.InstallError != nil {
//		return m.InstallError
//	}
//	m.InstalledLinters[linterType] = true
//	return nil
//}

func TestNewLinterManager(t *testing.T) {
	projectDir := "/test/project"
	manager := NewLinterManager(projectDir)

	if manager == nil {
		t.Fatal("Expected non-nil LinterManager")
	}

	// Type assertion to access unexported fields
	lm, ok := manager.(*LinterManager)
	if !ok {
		t.Fatalf("Expected *LinterManager type, got %T", manager)
	}

	if lm.projectDir != projectDir {
		t.Errorf("Expected projectDir to be %s, got %s", projectDir, lm.projectDir)
	}

	if lm.lintersPool == nil {
		t.Error("Expected lintersPool to be initialized")
	}

	if lm.languageMap == nil {
		t.Error("Expected languageMap to be initialized")
	}

	if lm.languageDetector == nil {
		t.Error("Expected languageDetector to be initialized")
	}

	if lm.initialized != false {
		t.Error("Expected initialized to be false initially")
	}
}

func TestGetLinterManager(t *testing.T) {
	// Reset the singleton instance for testing
	instance = nil
	once = sync.Once{}

	manager := GetLinterManager()
	if manager == nil {
		t.Fatal("Expected non-nil LinterManager from GetLinterManager")
	}

	// Call again to test singleton pattern
	manager2 := GetLinterManager()
	if manager != manager2 {
		t.Error("GetLinterManager did not return the same instance on second call")
	}
}

func TestManagerInitialize(t *testing.T) {
	projectDir := "/test/project"
	manager := NewLinterManager(projectDir)

	err := manager.ManagerInitialize()
	if err != nil {
		t.Fatalf("Initialize() returned unexpected error: %v", err)
	}

	// Type assertion to access unexported fields
	lm, ok := manager.(*LinterManager)
	if !ok {
		t.Fatalf("Expected *LinterManager type, got %T", manager)
	}

	if lm.installer == nil {
		t.Error("Expected installer to be set after initialization")
	}

	if !lm.initialized {
		t.Error("Expected initialized to be true after initialization")
	}

	// Test that second call to Initialize is a no-op
	err = manager.ManagerInitialize()
	if err != nil {
		t.Errorf("Second call to Initialize() returned unexpected error: %v", err)
	}
}

func TestRegisterLinter(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Create mock linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)

	// Test registration
	err := manager.RegisterLinter(eslintLinter)
	if err != nil {
		t.Errorf("RegisterLinter() returned unexpected error: %v", err)
	}

	err = manager.RegisterLinter(golangciLinter)
	if err != nil {
		t.Errorf("RegisterLinter() returned unexpected error: %v", err)
	}

	// Test registering nil linter
	err = manager.RegisterLinter(nil)
	if err == nil {
		t.Error("Expected error when registering nil linter, got nil")
	}

	// Test registering duplicate linter
	duplicateEslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	err = manager.RegisterLinter(duplicateEslintLinter)
	if err != nil {
		t.Errorf("RegisterLinter() returned unexpected error for duplicate linter: %v", err)
	}

	// Get linters for JavaScript and verify
	jsLinters, err := manager.GetLinterForLanguage(LanguageTypeJavaScript)
	if err != nil {
		t.Errorf("GetLinterForLanguage() returned unexpected error: %v", err)
	}

	if len(jsLinters) != 1 {
		t.Errorf("Expected 1 JavaScript linter, got %d", len(jsLinters))
	}

	if jsLinters[0].GetType() != LinterTypeESLint {
		t.Errorf("Expected ESLint linter, got %s", jsLinters[0].GetType())
	}

	// Get linters for Golang and verify
	goLinters, err := manager.GetLinterForLanguage(LanguageTypeGolang)
	if err != nil {
		t.Errorf("GetLinterForLanguage() returned unexpected error: %v", err)
	}

	if len(goLinters) != 1 {
		t.Errorf("Expected 1 Golang linter, got %d", len(goLinters))
	}

	if goLinters[0].GetType() != LinterTypeGolangCILint {
		t.Errorf("Expected GolangCILint linter, got %s", goLinters[0].GetType())
	}
}

func TestGetLinterForFile(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Register linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)
	pythonLinter := NewMockLinter(LinterTypeRuff, LanguageTypePython)

	_ = manager.RegisterLinter(eslintLinter)
	_ = manager.RegisterLinter(golangciLinter)
	_ = manager.RegisterLinter(pythonLinter)

	tests := []struct {
		name           string
		filePath       string
		expectedLinter LinterType
		expectError    bool
	}{
		{
			name:           "JavaScript file",
			filePath:       "test.js",
			expectedLinter: LinterTypeESLint,
			expectError:    false,
		},
		{
			name:           "Go file",
			filePath:       "test.go",
			expectedLinter: LinterTypeGolangCILint,
			expectError:    false,
		},
		{
			name:           "Python file",
			filePath:       "test.py",
			expectedLinter: LinterTypeRuff,
			expectError:    false,
		},
		{
			name:           "Unknown extension",
			filePath:       "test.unknown",
			expectedLinter: "",
			expectError:    true,
		},
		{
			name:           "No extension",
			filePath:       "testfile",
			expectedLinter: "",
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linters, err := manager.GetLinterForFile(tt.filePath)

			if (err != nil) != tt.expectError {
				t.Errorf("GetLinterForFile() error = %v, expectError %v", err, tt.expectError)
				return
			}

			if !tt.expectError {
				if len(linters) == 0 {
					t.Errorf("Expected at least one linter, got none")
					return
				}

				if linters[0].GetType() != tt.expectedLinter {
					t.Errorf("Expected linter type %s, got %s", tt.expectedLinter, linters[0].GetType())
				}
			}
		})
	}
}

func TestGetLinterForLanguage(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Register linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	tsLinter := NewMockLinter(LinterTypeESLint, LanguageTypeTypeScript) // ESLint can handle TS too

	_ = manager.RegisterLinter(eslintLinter)
	_ = manager.RegisterLinter(tsLinter)

	tests := []struct {
		name          string
		language      LanguageType
		expectedCount int
		expectError   bool
	}{
		{
			name:          "JavaScript language",
			language:      LanguageTypeJavaScript,
			expectedCount: 1,
			expectError:   false,
		},
		{
			name:          "TypeScript language",
			language:      LanguageTypeTypeScript,
			expectedCount: 1,
			expectError:   false,
		},
		{
			name:          "Unregistered language",
			language:      LanguageTypeRust,
			expectedCount: 0,
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linters, err := manager.GetLinterForLanguage(tt.language)

			if (err != nil) != tt.expectError {
				t.Errorf("GetLinterForLanguage() error = %v, expectError %v", err, tt.expectError)
				return
			}

			if !tt.expectError && len(linters) != tt.expectedCount {
				t.Errorf("Expected %d linters, got %d", tt.expectedCount, len(linters))
			}
		})
	}

	// Test default linters for language without explicit registration
	// First we need to update the LanguageToDefaultLinter map
	originalDefaults := LanguageToDefaultLinter
	defer func() { LanguageToDefaultLinter = originalDefaults }()

	LanguageToDefaultLinter = map[LanguageType][]LinterType{
		LanguageTypeGolang: {LinterTypeGolangCILint},
	}

	// Register the default linter
	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)
	_ = manager.RegisterLinter(golangciLinter)

	// Test getting the default linter
	goLinters, err := manager.GetLinterForLanguage(LanguageTypeGolang)
	if err != nil {
		t.Errorf("GetLinterForLanguage() for default linter returned unexpected error: %v", err)
	}

	if len(goLinters) != 1 || goLinters[0].GetType() != LinterTypeGolangCILint {
		t.Errorf("Expected default GolangCILint linter, got %v", goLinters)
	}
}

func TestGetLinterFromConfig(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Type assertion to access unexported methods
	lm, ok := manager.(*LinterManager)
	if !ok {
		t.Fatalf("Expected *LinterManager type, got %T", manager)
	}

	// Create a mock config for testing
	mockConfigs := []*config.LinterConfig{
		{
			Type:       "eslint",
			Language:   "javascript",
			ConfigPath: "/path/to/eslint.config.js",
		},
		{
			Type:       "ruff",
			Language:   "python",
			ConfigPath: "/path/to/ruff.toml",
		},
	}

	tests := []struct {
		name          string
		language      LanguageType
		expectedTypes []LinterType
		expectError   bool
	}{
		{
			name:          "JavaScript config",
			language:      LanguageTypeJavaScript,
			expectedTypes: []LinterType{LinterTypeESLint},
			expectError:   false,
		},
		{
			name:          "Python config",
			language:      LanguageTypePython,
			expectedTypes: []LinterType{LinterTypeRuff},
			expectError:   false,
		},
		{
			name:          "No config for language",
			language:      LanguageTypeGolang,
			expectedTypes: []LinterType{},
			expectError:   false,
		},
	}

	// 保存原始函数
	originalLoadEnvConfigFunc := LoadEnvConfigFunc
	defer func() { LoadEnvConfigFunc = originalLoadEnvConfigFunc }()

	LoadEnvConfigFunc = func() (*config.Config, error) {
		return &config.Config{
			LinterConfigs: mockConfigs,
		}, nil
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linters, err := lm.GetLinterFromConfig(tt.language)

			if (err != nil) != tt.expectError {
				t.Errorf("GetLinterFromConfig() error = %v, expectError %v", err, tt.expectError)
				return
			}

			if !tt.expectError {
				if len(linters) != len(tt.expectedTypes) {
					t.Errorf("Expected %d linters, got %d", len(tt.expectedTypes), len(linters))
					return
				}

				for i, linter := range linters {
					if linter.GetType() != tt.expectedTypes[i] {
						t.Errorf("Expected linter type %s at index %d, got %s",
							tt.expectedTypes[i], i, linter.GetType())
					}
				}
			}
		})
	}

	t.Run("error case", func(t *testing.T) {
		LoadEnvConfigFunc = func() (*config.Config, error) {
			return nil, errors.New("config load error")
		}
		linters, err := lm.GetLinterFromConfig(LanguageTypeJavaScript)
		if err != nil {
			t.Errorf("Expected no error from GetLinterFromConfig when getLinterConfig fails, got: %v", err)
		}
		if len(linters) != 0 {
			t.Errorf("Expected empty linters when getLinterConfig fails, got %d linters", len(linters))
		}
	})
}

func TestDetectAndInstallForFile(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Replace the installer with our mock
	lm, ok := manager.(*LinterManager)
	if !ok {
		t.Fatalf("Expected *LinterManager type, got %T", manager)
	}
	mockInstaller := NewMockInstaller()
	lm.installer = mockInstaller

	// Register linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	eslintLinter.IsInstalledValue = false

	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)
	golangciLinter.IsInstalledValue = true

	pythonLinter := NewMockLinter(LinterTypeRuff, LanguageTypePython)
	pythonLinter.IsInstalledValue = false
	pythonLinter.InstallError = errors.New("installation failed")

	_ = manager.RegisterLinter(eslintLinter)
	_ = manager.RegisterLinter(golangciLinter)
	_ = manager.RegisterLinter(pythonLinter)

	tests := []struct {
		name        string
		filePath    string
		expectError bool
		setupMock   func()
	}{
		{
			name:        "JavaScript file - needs installation",
			filePath:    "test.js",
			expectError: false,
			setupMock:   func() { mockInstaller.InstallForFileError = nil },
		},
		{
			name:        "Go file - already installed",
			filePath:    "test.go",
			expectError: false,
			setupMock:   func() { mockInstaller.InstallForFileError = nil },
		},
		{
			name:        "Python file - installation error",
			filePath:    "test.py",
			expectError: true,
			setupMock:   func() { mockInstaller.InstallForFileError = errors.New("installation failed") },
		},
		{
			name:        "Unknown file type",
			filePath:    "test.unknown",
			expectError: true,
			setupMock:   func() { mockInstaller.InstallForFileError = errors.New("could not determine language for file") },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()
			err := manager.DetectAndInstallForFile(tt.filePath)

			if (err != nil) != tt.expectError {
				t.Errorf("DetectAndInstallForFile() error = %v, expectError %v", err, tt.expectError)
			}
		})
	}
}

func TestLintFile(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Register linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	eslintLinter.LintResults = &LintResult{
		FilePath: "",
		Issues: []LintIssue{
			{
				Message: "Test issue 1",
				Location: Location{
					FilePath: "test.js",
					Range: Range{
						Start: Position{Line: 1, Character: 0},
						End:   Position{Line: 1, Character: 10},
					},
				},
				Severity: SeverityLevel("2"),
				Source:   "eslint",
			},
		},
		Success: true,
		Error:   "",
	}

	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)
	golangciLinter.LintError = errors.New("linting failed")

	// Register a linter that returns multiple issues
	typescriptLinter := NewMockLinter(LinterTypeESLint, LanguageTypeTypeScript)
	typescriptLinter.LintResults = &LintResult{
		FilePath: "",
		Issues: []LintIssue{
			{
				Message: "TS issue 1",
				Location: Location{
					FilePath: "test.ts",
					Range: Range{
						Start: Position{Line: 1, Character: 0},
						End:   Position{Line: 1, Character: 10},
					},
				},
				Severity: SeverityLevel("1"),
				Source:   "eslint",
			},
			{
				Message: "TS issue 2",
				Location: Location{
					FilePath: "test.ts",
					Range: Range{
						Start: Position{Line: 2, Character: 0},
						End:   Position{Line: 2, Character: 10},
					},
				},
				Severity: SeverityLevel("2"),
				Source:   "eslint",
			},
		},
		Success: true,
		Error:   "",
	}

	_ = manager.RegisterLinter(eslintLinter)
	_ = manager.RegisterLinter(golangciLinter)
	_ = manager.RegisterLinter(typescriptLinter)

	tests := []struct {
		name           string
		filePath       string
		expectedIssues int
		expectError    bool
	}{
		{
			name:           "JavaScript file - successful lint",
			filePath:       "test.js",
			expectedIssues: 1,
			expectError:    false,
		},
		{
			name:           "Go file - lint error (but continues with other linters)",
			filePath:       "test.go",
			expectedIssues: 0,
			expectError:    false,
		},
		{
			name:           "TypeScript file - multiple issues",
			filePath:       "test.ts",
			expectedIssues: 2,
			expectError:    false,
		},
		{
			name:           "Unknown file type",
			filePath:       "test.unknown",
			expectedIssues: 0,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := manager.LintFile(tt.filePath)

			if (err != nil) != tt.expectError {
				t.Errorf("LintFile() error = %v, expectError %v", err, tt.expectError)
				return
			}

			if !tt.expectError {
				totalIssues := 0
				for _, result := range results {
					totalIssues += len(result.Issues)
				}

				if totalIssues != tt.expectedIssues {
					t.Errorf("Expected %d issues, got %d", tt.expectedIssues, totalIssues)
				}
			}
		})
	}
}

func TestFixFile(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Register linters
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	eslintLinter.FixResults = &FixResult{
		FilePath: "",
		Fixed:    true,
		FixCount: 2,
		Error:    "",
	}

	golangciLinter := NewMockLinter(LinterTypeGolangCILint, LanguageTypeGolang)
	golangciLinter.FixError = errors.New("fixing failed")

	_ = manager.RegisterLinter(eslintLinter)
	_ = manager.RegisterLinter(golangciLinter)

	tests := []struct {
		name          string
		filePath      string
		expectedFixed bool
		expectError   bool
	}{
		{
			name:          "JavaScript file - successful fix",
			filePath:      "test.js",
			expectedFixed: true,
			expectError:   false,
		},
		{
			name:          "Go file - fix error (but continues with other linters)",
			filePath:      "test.go",
			expectedFixed: false,
			expectError:   false,
		},
		{
			name:          "Unknown file type",
			filePath:      "test.unknown",
			expectedFixed: false,
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := manager.FixFile(tt.filePath)

			if (err != nil) != tt.expectError {
				t.Errorf("FixFile() error = %v, expectError %v", err, tt.expectError)
				return
			}

			if !tt.expectError {
				anyFixed := false
				for _, result := range results {
					if result.Fixed {
						anyFixed = true
						break
					}
				}

				if anyFixed != tt.expectedFixed {
					t.Errorf("Expected fixed=%v, got %v", tt.expectedFixed, anyFixed)
				}
			}
		})
	}
}

// TestEdgeCases tests edge cases and unusual inputs
func TestEdgeCases(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Register a linter
	eslintLinter := NewMockLinter(LinterTypeESLint, LanguageTypeJavaScript)
	_ = manager.RegisterLinter(eslintLinter)

	tests := []struct {
		name        string
		filePath    string
		expectError bool
	}{
		{
			name:        "Empty file path",
			filePath:    "",
			expectError: true,
		},
		{
			name:        "File path with only extension",
			filePath:    ".js",
			expectError: false, // Should be detected as JavaScript
		},
		{
			name:        "File path with spaces",
			filePath:    "my file with spaces.js",
			expectError: false, // Should be handled correctly
		},
		{
			name:        "File path with special characters",
			filePath:    "special!@#$%^&*().js",
			expectError: false, // Should be handled correctly
		},
		{
			name:        "Very long file path",
			filePath:    "very/long/path/to/" + strings.Repeat("a", 200) + ".js",
			expectError: false, // Should be handled correctly
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := manager.LintFile(tt.filePath)

			if (err != nil) != tt.expectError {
				t.Errorf("LintFile() with edge case '%s' error = %v, expectError %v", tt.name, err, tt.expectError)
			}
		})
	}
}

// TestLanguageDetection tests that the manager correctly detects language for different file types
func TestLanguageDetection(t *testing.T) {
	manager := NewLinterManager("/test/project")

	// Initialize the manager
	_ = manager.ManagerInitialize()

	// Access the unexported languageDetector
	lm, ok := manager.(*LinterManager)
	if !ok {
		t.Fatalf("Expected *LinterManager type, got %T", manager)
	}

	tests := []struct {
		filePath         string
		expectedLanguage LanguageType
	}{
		{"test.js", LanguageTypeJavaScript},
		{"test.jsx", LanguageTypeJavaScript},
		{"test.ts", LanguageTypeTypeScript},
		{"test.tsx", LanguageTypeTypeScript},
		{"test.py", LanguageTypePython},
		{"test.go", LanguageTypeGolang},
		{"test.java", LanguageTypeJava},
		{"test.c", LanguageTypeC},
		{"test.cpp", LanguageTypeCPP},
		{"test.cs", LanguageTypeCSharp},
		{"test.rb", LanguageTypeRuby},
		{"test.php", LanguageTypePHP},
		{"test.rs", LanguageTypeRust},
		{"test.swift", LanguageTypeSwift},
		{"test.unknown", LanguageTypeUnknown},
	}

	for _, tt := range tests {
		t.Run(tt.filePath, func(t *testing.T) {
			detectedLanguage := lm.languageDetector.DetectLanguage(tt.filePath)

			if detectedLanguage != tt.expectedLanguage {
				t.Errorf("For file %s, expected language %s, got %s", tt.filePath, tt.expectedLanguage, detectedLanguage)
			}
		})
	}
}

func TestClosedManagerOperations(t *testing.T) {
	// Create a manager to test what happens when it hasn't been initialized
	uninitializedManager := &LinterManager{
		lintersPool:      make(map[LinterType]LinterIface),
		languageMap:      make(map[LanguageType][]LinterIface),
		languageDetector: GetLanguageDetector(),
		initialized:      false,
		projectDir:       "/test/project",
		cacheFile:        make(map[string]bool),
	}

	// Test operations on uninitialized manager
	_, err := uninitializedManager.LintFile("test.js")
	if err == nil {
		t.Error("Expected error when linting with uninitialized manager")
	}

	_, err = uninitializedManager.FixFile("test.js")
	if err == nil {
		t.Error("Expected error when fixing with uninitialized manager")
	}

	// Initialize but set installer to nil to simulate a partial initialization
	uninitializedManager.initialized = true
	uninitializedManager.installer = nil

	err = uninitializedManager.DetectAndInstallForFile("test.js")
	if err == nil {
		t.Error("Expected error when installing with nil installer")
	}
}

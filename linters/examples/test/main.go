//go:build ignore

// This file is an example and should not be included in tests or coverage

package main

import (
	"agent/linters"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// Sample test files for each linter type
const (
	eslintTestFile = `
// eslintTestFile.js
function example() {
  const x = 1;
  if (x == 1) {  // eslint prefers === for comparison
    console.log('Test');
  }
}
`

	ruffTestFile = `
# ruffTestFile.py
def example():
    unused_var = "test"  # Unused variable, will be flagged by Ruff
    print("Hello world!")
`

	mypyTestFile = `
# mypyTestFile.py
def add(a, b):  # Missing type annotations, will be flagged by MyPy
    return a + b
`

	golangciTestFile = `
// golangciTestFile.go
package main

import "fmt"

func main() {
	var x int
	// Unused variable, will be flagged by golangci-lint
	fmt.Println("Hello world!")
}
`
)

// createTestFile creates a test file with the given content
func createTestFile(content, path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %v", err)
	}
	return os.WriteFile(path, []byte(content), 0644)
}

// installLinter attempts to install the specified linter
func installLinter(linterType linters.LinterType) error {
	fmt.Printf("Installing %s...\n", linterType)
	var linter linters.Linter

	switch linterType {
	case linters.LinterTypeESLint:
		linter = linters.NewESLintLinter()
	case linters.LinterTypeRuff:
		linter = linters.NewRuffLinter()
	case linters.LinterTypeMyPy:
		linter = linters.NewMyPyLinter()
	case linters.LinterTypeGolangCILint:
		linter = linters.NewGolangCILintLinter()
	default:
		return fmt.Errorf("unsupported linter type: %s", linterType)
	}

	if linter.IsInstalled() {
		fmt.Printf("%s is already installed.\n", linterType)
		return nil
	}

	err := linter.Install()
	if err != nil {
		return fmt.Errorf("failed to install %s: %v", linterType, err)
	}

	fmt.Printf("%s installed successfully.\n", linterType)
	return nil
}

// lintFile runs the linter on the specified file
func lintFile(linterType linters.LinterType, filePath string) error {
	fmt.Printf("Linting %s with %s...\n", filePath, linterType)
	var linter linters.Linter

	switch linterType {
	case linters.LinterTypeESLint:
		linter = linters.NewESLintLinter()
	case linters.LinterTypeRuff:
		linter = linters.NewRuffLinter()
	case linters.LinterTypeMyPy:
		linter = linters.NewMyPyLinter()
	case linters.LinterTypeGolangCILint:
		linter = linters.NewGolangCILintLinter()
	default:
		return fmt.Errorf("unsupported linter type: %s", linterType)
	}

	if !linter.IsInstalled() {
		return fmt.Errorf("%s is not installed", linterType)
	}

	result, err := linter.Lint(filePath)
	if err != nil {
		return fmt.Errorf("linting failed: %v", err)
	}

	// Display results
	fmt.Printf("\n=== Lint Results for %s ===\n", filePath)
	if !result.Success {
		fmt.Printf("Linting failed: %s\n", result.Error)
		return nil
	}

	if len(result.Issues) == 0 {
		fmt.Printf("No issues found.\n")
	} else {
		fmt.Printf("Found %d issues:\n", len(result.Issues))
		fmt.Printf("result: %+v\n", result)
		for i, issue := range result.Issues {
			fmt.Printf("%d. [%s] Line %d:%d: %s\n",
				i+1,
				issue.Severity,
				issue.Location.Range.Start.Line,
				issue.Location.Range.Start.Character,
				issue.Message)
		}
	}

	return nil
}

// fixFile attempts to fix issues in the specified file
func fixFile(linterType linters.LinterType, filePath string) error {
	fmt.Printf("Fixing %s with %s...\n", filePath, linterType)
	var linter linters.Linter

	switch linterType {
	case linters.LinterTypeESLint:
		linter = linters.NewESLintLinter()
	case linters.LinterTypeRuff:
		linter = linters.NewRuffLinter()
	case linters.LinterTypeMyPy:
		linter = linters.NewMyPyLinter()
	case linters.LinterTypeGolangCILint:
		linter = linters.NewGolangCILintLinter()
	default:
		return fmt.Errorf("unsupported linter type: %s", linterType)
	}

	if !linter.IsInstalled() {
		return fmt.Errorf("%s is not installed", linterType)
	}

	// Read original content for comparison
	originalContent, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read original file content: %v", err)
	}

	result, err := linter.Fix(filePath)
	if err != nil {
		return fmt.Errorf("fixing failed: %v", err)
	}

	// Display results
	fmt.Printf("\n=== Fix Results for %s ===\n", filePath)
	if result.Fixed {
		fmt.Printf("Fixed %d issues.\n", result.FixCount)

		// Read new content for comparison
		newContent, err := os.ReadFile(filePath)
		if err != nil {
			return fmt.Errorf("failed to read new file content: %v", err)
		}

		if string(originalContent) != string(newContent) {
			fmt.Println("File was modified.")
		}
	} else if result.Error != "" {
		fmt.Printf("Fixing failed: %s\n", result.Error)
	} else {
		fmt.Printf("No issues fixed.\n")
	}

	return nil
}

// createTestFiles creates sample test files for different linters
func createTestFiles() (map[linters.LinterType]string, error) {
	testFiles := make(map[linters.LinterType]string)

	// Create temp directory for test files
	tmpDir, err := os.MkdirTemp("", "linter-test-*")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp directory: %v", err)
	}

	// Create ESLint test file
	eslintPath := filepath.Join(tmpDir, "eslintTestFile.js")
	if err := createTestFile(eslintTestFile, eslintPath); err != nil {
		return nil, err
	}
	testFiles[linters.LinterTypeESLint] = eslintPath

	// Create Ruff test file
	ruffPath := filepath.Join(tmpDir, "ruffTestFile.py")
	if err := createTestFile(ruffTestFile, ruffPath); err != nil {
		return nil, err
	}
	testFiles[linters.LinterTypeRuff] = ruffPath

	// Create MyPy test file
	mypyPath := filepath.Join(tmpDir, "mypyTestFile.py")
	if err := createTestFile(mypyTestFile, mypyPath); err != nil {
		return nil, err
	}
	testFiles[linters.LinterTypeMyPy] = mypyPath

	// Create GolangCI-Lint test file
	golangciPath := filepath.Join(tmpDir, "golangciTestFile.go")
	if err := createTestFile(golangciTestFile, golangciPath); err != nil {
		return nil, err
	}
	testFiles[linters.LinterTypeGolangCILint] = golangciPath

	fmt.Printf("Created test files in directory: %s\n", tmpDir)
	return testFiles, nil
}

// getSupportedLinters returns a list of all supported linters
func getSupportedLinters() []linters.LinterType {
	return []linters.LinterType{
		linters.LinterTypeESLint,
		linters.LinterTypeRuff,
		linters.LinterTypeMyPy,
		linters.LinterTypeGolangCILint,
	}
}

func main() {
	// Define command-line flags
	linterTypeFlag := flag.String("linter", "all", "Linter type to use (eslint, ruff, mypy, golangci-lint, or all)")
	operationFlag := flag.String("operation", "lint", "Operation to perform (install, lint, fix)")
	fileFlag := flag.String("file", "", "File to lint or fix (if empty, creates temp test files)")

	flag.Parse()

	// Validate linter type
	var linterTypes []linters.LinterType
	if *linterTypeFlag == "all" {
		linterTypes = getSupportedLinters()
	} else {
		linterType := linters.LinterType(*linterTypeFlag)
		switch linterType {
		case linters.LinterTypeESLint, linters.LinterTypeRuff, linters.LinterTypeMyPy, linters.LinterTypeGolangCILint:
			linterTypes = []linters.LinterType{linterType}
		default:
			log.Fatalf("Unsupported linter type: %s", *linterTypeFlag)
		}
	}

	// Validate operation
	operation := strings.ToLower(*operationFlag)
	if operation != "install" && operation != "lint" && operation != "fix" {
		log.Fatalf("Unsupported operation: %s", *operationFlag)
	}

	// Determine what file(s) to use
	var filePaths map[linters.LinterType]string
	var err error

	if *fileFlag != "" {
		// Use the specified file for all linters
		filePaths = make(map[linters.LinterType]string)
		for _, linterType := range linterTypes {
			filePaths[linterType] = *fileFlag
		}
	} else if operation == "lint" || operation == "fix" {
		// Create test files if no specific file is provided
		fmt.Println("No file specified, creating test files...")
		filePaths, err = createTestFiles()
		if err != nil {
			log.Fatalf("Failed to create test files: %v", err)
		}
	}

	// Perform the requested operation for each linter
	for _, linterType := range linterTypes {
		switch operation {
		case "install":
			if err := installLinter(linterType); err != nil {
				log.Printf("Error: %v", err)
			}
		case "lint":
			filePath, ok := filePaths[linterType]
			if !ok {
				log.Printf("No test file available for %s", linterType)
				continue
			}
			if err := lintFile(linterType, filePath); err != nil {
				log.Printf("Error: %v", err)
			}
		case "fix":
			filePath, ok := filePaths[linterType]
			if !ok {
				log.Printf("No test file available for %s", linterType)
				continue
			}
			if err := fixFile(linterType, filePath); err != nil {
				log.Printf("Error: %v", err)
			}
		}
	}
}

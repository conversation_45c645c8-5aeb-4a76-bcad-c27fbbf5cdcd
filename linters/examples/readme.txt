CGO_ENABLED=1 go build -o linter_test examples/main.go
一、install
1) eslint:
./linter_test -linter eslint -operation install
npm uninstall -g eslint

2) golang
./linter_test -linter eslint -operation install

3) python
./linter_test -linter mypy -operation install
pip uninstall mypy
./linter_test -linter ruff -operation install
pip uninstall ruff


二、lint
1）eslint
./linter_test -linter eslint -operation lint -file=./testfiles/js_test.js

2) golang
./linter_test -linter golangci-lint -operation lint -file=./testfiles/go_test.go

3) python
./linter_test -linter mypy -operation lint -file=./testfiles/python_test.py
./linter_test -linter ruff -operation lint -file=./testfiles/python_test.py

三、fix
1）eslint
./linter_test -linter eslint -operation fix -file=./js_test.js

2) golang
./linter_test -linter golangci-lint -operation fix -file=./testfiles/go_test.go

3) python
./linter_test -linter mypy -operation fix -file=./testfiles/python_test.py
./linter_test -linter ruff -operation fix -file=./testfiles/python_test.py

package linters

import (
	"agent/config"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

// 1. 定义接口 type MyPyExecutor interface { CombinedOutputWithBashrc(command string, args ...string) ([]byte, error) }

func TestNewMyPyLinter(t *testing.T) {
	// Create test config
	config := &config.LinterConfig{
		ConfigPath: "/path/to/mypy.ini",
	}

	// Create linter
	linter := NewMyPyLinter(config)

	// Check initialization
	if linter.mypyPath != "mypy" {
		t.Errorf("Expected mypyPath to be 'mypy', got '%s'", linter.mypyPath)
	}

	if linter.pipCmd != "pip" {
		t.<PERSON>rf("Expected pipCmd to be 'pip', got '%s'", linter.pipCmd)
	}

	if linter.Type != LinterTypeMyPy {
		t.<PERSON>("Expected Type to be '%s', got '%s'", LinterTypeMyPy, linter.Type)
	}

	if linter.Language != LanguageTypePython {
		t.Errorf("Expected Language to be '%s', got '%s'", LanguageTypePython, linter.Language)
	}

	if linter.Config != config {
		t.Errorf("Expected Config to be properly set")
	}

	if linter.cacheFile == nil {
		t.Errorf("Expected cacheFile to be initialized")
	}
}

func TestInstallMypy(t *testing.T) {
	tests := []struct {
		name             string
		pipExists        bool
		pip3Exists       bool
		pipExistsErr     error
		pip3ExistsErr    error
		pipInstallOutput string
		pipInstallErr    error
		expectError      bool
		expectedPipCmd   string
	}{
		{
			name:           "Success with pip",
			pipExists:      true,
			pip3Exists:     false,
			pipExistsErr:   nil,
			pip3ExistsErr:  os.ErrNotExist,
			expectError:    false,
			expectedPipCmd: "pip",
		},
		{
			name:           "Success with pip3",
			pipExists:      false,
			pip3Exists:     true,
			pipExistsErr:   os.ErrNotExist,
			pip3ExistsErr:  nil,
			expectError:    false,
			expectedPipCmd: "pip3",
		},
		{
			name:           "No pip or pip3 available",
			pipExists:      false,
			pip3Exists:     false,
			pipExistsErr:   os.ErrNotExist,
			pip3ExistsErr:  os.ErrNotExist,
			expectError:    true,
			expectedPipCmd: "pip",
		},
		{
			name:             "Pip install fails",
			pipExists:        true,
			pip3Exists:       false,
			pipExistsErr:     nil,
			pip3ExistsErr:    os.ErrNotExist,
			pipInstallOutput: "",
			pipInstallErr:    fmt.Errorf("installation failed"),
			expectError:      true,
			expectedPipCmd:   "pip",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "pip" {
					if tt.pipExists {
						return "/usr/bin/pip", nil
					}
					return "", tt.pipExistsErr
				}
				if cmd == "pip3" {
					if tt.pip3Exists {
						return "/usr/bin/pip3", nil
					}
					return "", tt.pip3ExistsErr
				}
				return "", fmt.Errorf("unknown command: %s", cmd)
			}

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "pip" || command == "pip3" {
					if len(args) >= 2 && args[0] == "install" && args[1] == "mypy" {
						if tt.pipInstallErr != nil {
							return []byte(tt.pipInstallOutput), tt.pipInstallErr
						}
						return []byte("Successfully installed mypy"), nil
					}
				}
				return []byte(""), nil
			}

			// Create linter and run test
			linter := NewMyPyLinter(nil)
			err := linter.Install()

			// Check results
			if (err != nil) != tt.expectError {
				t.Errorf("Install() error = %v, expectError %v", err, tt.expectError)
			}

			if !tt.expectError && linter.pipCmd != tt.expectedPipCmd {
				t.Errorf("Install() pipCmd = %v, want %v", linter.pipCmd, tt.expectedPipCmd)
			}
		})
	}
}

func TestIsInstalledMypy(t *testing.T) {
	tests := []struct {
		name       string
		mypyExists bool
		mypyErr    error
		expected   bool
	}{
		{
			name:       "MyPy installed",
			mypyExists: true,
			mypyErr:    nil,
			expected:   true,
		},
		{
			name:       "MyPy not installed",
			mypyExists: false,
			mypyErr:    errors.New("command not found"),
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "mypy" {
					if tt.mypyExists {
						return "/usr/bin/mypy", nil
					}
					return "", tt.mypyErr
				}
				return "", errors.New("command not found")
			}

			linter := NewMyPyLinter(nil)
			result := linter.IsInstalled()

			if result != tt.expected {
				t.Errorf("IsInstalled() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFindMyPyConfig(t *testing.T) {
	// Create temporary test directory structure
	tmpDir, err := os.MkdirTemp("", "mypy-config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// Create nested directories
	subDir := filepath.Join(tmpDir, "sub")
	subSubDir := filepath.Join(subDir, "subsub")
	if err := os.MkdirAll(subSubDir, 0755); err != nil {
		t.Fatalf("Failed to create nested directories: %v", err)
	}

	// Create config files
	rootConfig := filepath.Join(tmpDir, "mypy.ini")
	if err := os.WriteFile(rootConfig, []byte("[mypy]\ncheck_untyped_defs = True"), 0644); err != nil {
		t.Fatalf("Failed to create root config: %v", err)
	}

	subConfig := filepath.Join(subDir, "pyproject.toml")
	if err := os.WriteFile(subConfig, []byte("[tool.mypy]\nfollow_imports = 'silent'"), 0644); err != nil {
		t.Fatalf("Failed to create sub config: %v", err)
	}

	// Create test file in the deepest directory
	testFile := filepath.Join(subSubDir, "test.py")
	if err := os.WriteFile(testFile, []byte("# Python code"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name         string
		configPath   string
		filePath     string
		expectedPath string
	}{
		{
			name:         "Config in linter config",
			configPath:   "/explicit/config/path.ini",
			filePath:     testFile,
			expectedPath: "/explicit/config/path.ini",
		},
		{
			name:         "Find config in parent directory",
			configPath:   "",
			filePath:     testFile,
			expectedPath: subConfig, // Should find the nearest config (pyproject.toml)
		},
		{
			name:         "No config in path",
			configPath:   "",
			filePath:     "/some/random/path.py",
			expectedPath: "", // No config found
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewMyPyLinter(linterConfig)

			result := linter.findMyPyConfig(tt.filePath)

			if tt.name == "Config in linter config" && result != tt.expectedPath {
				t.Errorf("findMyPyConfig() = %v, want %v", result, tt.expectedPath)
			}

			if tt.name == "Find config in parent directory" && result != subConfig {
				t.Errorf("findMyPyConfig() = %v, want %v", result, subConfig)
			}

			if tt.name == "No config in path" && result != "" {
				t.Errorf("findMyPyConfig() = %v, want empty string", result)
			}
		})
	}
}

func TestParseMyPyOutput(t *testing.T) {
	linter := NewMyPyLinter(nil)
	testFilePath := "test.py"

	tests := []struct {
		name             string
		output           string
		expectedIssues   int
		expectedLine     int
		expectedCol      int
		expectedSeverity int
		expectedMessage  string
	}{
		{
			name: "Standard format",
			output: `test.py:10: error: Missing return statement
test.py:15: note: Function is missing a type annotation`,
			expectedIssues:   2,
			expectedLine:     10,
			expectedCol:      1, // Default column
			expectedSeverity: 1,
			expectedMessage:  "Missing return statement",
		},
		{
			name: "Format with column",
			output: `test.py:10:5: error: Missing return statement
test.py:15:8: note: Function is missing a type annotation`,
			expectedIssues:   2,
			expectedLine:     10,
			expectedCol:      5,
			expectedSeverity: 1,
			expectedMessage:  "Missing return statement",
		},
		{
			name: "New format with error classification",
			output: `test.py:10:5: error: Missing return statement [syntax]
test.py:15:8: note: Function is missing a type annotation [type-missing]`,
			expectedIssues:   2,
			expectedLine:     10,
			expectedCol:      5,
			expectedSeverity: 1,
			expectedMessage:  "Missing return statement [syntax]",
		},
		{
			name: "With summary line",
			output: `test.py:10: error: Missing return statement
Found 1 error in 1 file (checked 1 source file)`,
			expectedIssues:   1,
			expectedLine:     10,
			expectedCol:      1,
			expectedSeverity: 1,
			expectedMessage:  "Missing return statement",
		},
		{
			name: "With empty lines",
			output: `
test.py:10: error: Missing return statement

test.py:15: note: Function is missing a type annotation
`,
			expectedIssues:   2,
			expectedLine:     10,
			expectedCol:      1,
			expectedSeverity: 1,
			expectedMessage:  "Missing return statement",
		},
		{
			name: "Different file should be filtered",
			output: `other.py:10: error: Missing return statement
test.py:15: note: Function is missing a type annotation`,
			expectedIssues:   1,
			expectedLine:     15,
			expectedCol:      1,
			expectedSeverity: 1, // note is warning
			expectedMessage:  "Function is missing a type annotation",
		},
		{
			name: "Invalid format should be skipped",
			output: `This is not a valid format
test.py:15: note: Function is missing a type annotation`,
			expectedIssues:   1,
			expectedLine:     15,
			expectedCol:      1,
			expectedSeverity: 1,
			expectedMessage:  "Function is missing a type annotation",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues := linter.parseMyPyOutput(tt.output, testFilePath)

			if len(issues) != tt.expectedIssues {
				t.Errorf("Expected %d issues, got %d", tt.expectedIssues, len(issues))
				return
			}
		})
	}
}

// 2. 在 MyPyLinter 结构体中增加 executor 字段，类型为 MyPyExecutor

func TestLintMypy(t *testing.T) {
	// Use the test Python file
	testPyFile := "testfiles/python_test.py"

	// Sample MyPy output
	mypyOutput := `testfiles/python_test.py:11: error: Function is missing a type annotation
testfiles/python_test.py:15:1: error: Function is missing a return type annotation
testfiles/python_test.py:37:12: error: Function is missing a return type annotation`

	tests := []struct {
		name           string
		isInstalled    bool
		configPath     string
		mockOutput     string
		mockError      error
		expectedIssues int
		expectError    bool
	}{
		{
			name:           "Successful lint with issues",
			isInstalled:    true,
			configPath:     "testfiles/mypy.ini",
			mockOutput:     mypyOutput,
			mockError:      nil,
			expectedIssues: 3,
			expectError:    false,
		},
		{
			name:        "MyPy not installed",
			isInstalled: false,
			expectError: true,
		},
		{
			name:        "MyPy execution error",
			isInstalled: true,
			configPath:  "mypy.ini",
			mockOutput:  "",
			mockError:   errors.New("execution error"),
			expectError: true,
		},
		{
			name:           "No issues found",
			isInstalled:    true,
			configPath:     "testfiles/mypy.ini",
			mockOutput:     "Success: no issues found in 1 source file",
			mockError:      nil,
			expectedIssues: 0,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "mypy" {
					if tt.isInstalled {
						return "/usr/bin/mypy", nil
					}
					return "", errors.New("mypy not installed")
				}
				return "", errors.New("command not found")
			}
			defer func() { WhichExecFunc = originalWhichExec }()

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "mypy" {
					if tt.mockOutput != "" {
						return []byte(tt.mockOutput), tt.mockError
					}
					if tt.mockError != nil {
						return []byte(""), tt.mockError
					}
					return []byte("Success: no issues found in 1 source file"), nil
				}
				return []byte(""), nil
			}
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewMyPyLinter(linterConfig)
			linter.Lint(testPyFile)
		})
	}
}

func TestMyPyLinterType(t *testing.T) {
	linter := NewMyPyLinter(nil)
	if linter.GetType() != LinterTypeMyPy {
		t.Errorf("GetType() = %v, want %v", linter.GetType(), LinterTypeMyPy)
	}
}

func TestMyPyLanguageType(t *testing.T) {
	linter := NewMyPyLinter(nil)
	if linter.GetLanguage() != LanguageTypePython {
		t.Errorf("GetLanguage() = %v, want %v", linter.GetLanguage(), LanguageTypePython)
	}
}

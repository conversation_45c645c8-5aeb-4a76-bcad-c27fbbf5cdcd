package linters

import (
	config2 "agent/config"
	"agent/consts"
	"agent/utils/log"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
)

var (
	ErrorLintFileLintIsRunning = errors.New("LintFile lint is running")
)

type LinterManagerIface interface {
	RegisterLinter(linter LinterIface) error
	GetLinterForFile(filePath string) ([]LinterIface, error)
	GetLinterForLanguage(language LanguageType) ([]LinterIface, error)

	DetectAndInstallForFile(filePath string) error
	LintFile(filePath string) ([]*LintResult, error)
	FixFile(filePath string) ([]*FixResult, error)

	ManagerInitialize() error
}

// LinterManager manages all registered linters
type LinterManager struct {
	lintersPool      map[LinterType]LinterIface
	languageMap      map[LanguageType][]LinterIface
	mutex            sync.RWMutex
	languageDetector *LanguageDetector
	installer        InstallerIface
	initialized      bool
	projectDir       string

	// linter使用
	mu        sync.RWMutex
	cacheFile map[string]bool
}

// NewLinterManager creates a new linter manager
func NewLinterManager(projectDir string) LinterManagerIface {
	return &LinterManager{
		lintersPool:      make(map[LinterType]LinterIface),
		languageMap:      make(map[LanguageType][]LinterIface),
		languageDetector: GetLanguageDetector(),
		initialized:      false,
		projectDir:       projectDir,
		mu:               sync.RWMutex{},
		cacheFile:        make(map[string]bool),
	}
}

var (
	instance LinterManagerIface
	once     sync.Once
)

// GetLinterManagerFunc 包装GetLinterManager函数，便于测试时mock
var GetLinterManagerFunc = GetLinterManager

// GetLinterManager returns the singleton instance of LinterManager
func GetLinterManager() LinterManagerIface {
	once.Do(func() {
		instance = NewLinterManager(consts.AppRootDir)
		if err := instance.ManagerInitialize(); err != nil {
			log.Warnf("LangLinters, Failed to initialize linter manager: %v", err)
		}
	})
	return instance
}

// ManagerInitialize completes the initialization of the manager, setting up the installer
// and performing initial language detection
func (lm *LinterManager) ManagerInitialize() error {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	if lm.initialized {
		return nil
	}
	lm.installer = NewInstaller(lm)
	lm.initialized = true

	return nil
}

// RegisterLinter registers a new linter
func (lm *LinterManager) RegisterLinter(linter LinterIface) error {
	if linter == nil {
		return errors.New("cannot register nil linter")
	}

	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	linterType := linter.GetType()
	language := linter.GetLanguage()

	lm.lintersPool[linterType] = linter

	if _, exists := lm.languageMap[language]; !exists {
		lm.languageMap[language] = make([]LinterIface, 0)
	}
	found := false
	for _, registeredLinter := range lm.languageMap[language] {
		if registeredLinter.GetType() == linterType {
			found = true
			break
		}
	}
	if !found {
		lm.languageMap[language] = append(lm.languageMap[language], linter)
	}

	log.Infof("LangLinters, Registered linter %s for language %s", linterType, language)
	return nil
}

// GetLinterForFile returns the appropriate linter(s) for a file based on its detected language
func (lm *LinterManager) GetLinterForFile(filePath string) ([]LinterIface, error) {
	language := lm.languageDetector.DetectLanguage(filePath)

	if language == LanguageTypeUnknown {
		ext := strings.ToLower(filepath.Ext(filePath))
		if ext == "" {
			return nil, fmt.Errorf("cannot determine file type for: %s (no extension and language detector failed)", filePath)
		}

		var ok bool
		language, ok = FileExtToLanguage[ext]
		if !ok {
			return nil, fmt.Errorf("no language mapping found for file: %s (extension %s) and language detector failed", filePath, ext)
		}
	}

	// 优先从1024里获取插件配置信息
	linters, err := lm.GetLinterFromConfig(language)
	if err == nil && len(linters) != 0 {
		return linters, nil
	}

	// 获取默认插件
	return lm.GetLinterForLanguage(language)
}

// GetLinterForLanguage returns the appropriate linter(s) for a language
// It returns all linters registered for this language.
func (lm *LinterManager) GetLinterForLanguage(language LanguageType) ([]LinterIface, error) {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()

	linters, exists := lm.languageMap[language]
	if !exists || len(linters) == 0 {
		defaultLinterTypes, hasDefault := LanguageToDefaultLinter[language]
		if !hasDefault || len(defaultLinterTypes) == 0 {
			return nil, fmt.Errorf("no linters or default linter types found/registered for language: %s", language)
		}

		defaultLinters := make([]LinterIface, 0)
		for _, defaultType := range defaultLinterTypes {
			if linter, exists := lm.lintersPool[defaultType]; exists {
				defaultLinters = append(defaultLinters, linter)
			}
		}

		if len(defaultLinters) == 0 {
			return nil, fmt.Errorf("default linters (%v) for language %s are defined but not currently registered with the manager", defaultLinterTypes, language)
		}

		return defaultLinters, nil
	}

	return linters, nil
}

// GetLinterFromConfig
func (lm *LinterManager) GetLinterFromConfig(language LanguageType) ([]LinterIface, error) {
	configs, err := lm.getLinterConfig()
	if err != nil {
		return nil, err
	}

	linters := make([]LinterIface, 0)
	for _, c := range configs {
		if string(language) == c.Language {
			switch LinterType(c.Type) {
			case LinterTypeESLint:
				linters = append(linters, NewESLintLinter(c))
			case LinterTypeRuff:
				linters = append(linters, NewRuffLinter(c))
			case LinterTypeMyPy:
				linters = append(linters, NewMyPyLinter(c))
			case LinterTypeGolangCILint:
				linters = append(linters, NewGolangCILintLinter(c))
			}
		}
	}

	return linters, nil
}

var LoadEnvConfigFunc = config2.LoadEnvConfig

// GetLinterConfig loads linter configurations from the .1024 file
func (lm *LinterManager) getLinterConfig() ([]*config2.LinterConfig, error) {
	config, err := LoadEnvConfigFunc()
	if err != nil {
		// 配置文件不存在是正常情况，返回空配置而不是错误
		return []*config2.LinterConfig{}, nil
	}
	return config.LinterConfigs, nil
}

// DetectAndInstallForFile detects the language of a file and installs the appropriate default linter.
func (lm *LinterManager) DetectAndInstallForFile(filePath string) error {
	if lm.installer == nil {
		return errors.New("linter installer not initialized - cannot detect and install for file")
	}

	//log.Infof("LangLinters, Delegating detection and installation for file %s to installer...", filePath)
	return lm.installer.InstallForFile(filePath)
}

func (lm *LinterManager) checkCacheFile(filepath string) bool {
	lm.mu.Lock()
	defer lm.mu.Unlock()
	isExt, ok := lm.cacheFile[filepath]
	if ok && isExt {
		return true
	}

	lm.cacheFile[filepath] = true

	return false
}

func (lm *LinterManager) deleteCacheFile(filepath string) {
	lm.mu.Lock()
	defer lm.mu.Unlock()
	delete(lm.cacheFile, filepath)
}

// LintFile lints a file using the appropriate linter
func (lm *LinterManager) LintFile(filePath string) ([]*LintResult, error) {
	linters, err := lm.GetLinterForFile(filePath)
	if err != nil {
		installErr := lm.DetectAndInstallForFile(filePath)
		if installErr == nil {
			linters, err = lm.GetLinterForFile(filePath)
		} else {
			log.Warnf("LangLinters, Failed to auto-install linter for %s after initial lookup failed: %v", filePath, installErr)
		}
	}

	if err != nil || len(linters) == 0 {
		if err == nil && len(linters) == 0 {
			return nil, fmt.Errorf("no linters found for file after checking and attempting auto-install: %s", filePath)
		}
		return nil, err
	}

	// 所有插件执行完成后
	if lm.checkCacheFile(filePath) {
		return nil, ErrorLintFileLintIsRunning
	}
	defer lm.deleteCacheFile(filePath)

	result := make([]*LintResult, 0)
	for _, lint := range linters {
		rs, err := lint.Lint(filePath)
		if err != nil {
			log.Warnf("LangLinters, Error linting file %s with %s: %v", filePath, lint.GetType(), err)
			continue
		}

		result = append(result, rs)
	}

	return result, nil
}

// FixFile attempts to fix issues in a file using the appropriate linter
func (lm *LinterManager) FixFile(filePath string) ([]*FixResult, error) {
	linters, err := lm.GetLinterForFile(filePath)
	if err != nil {
		log.Warnf("LangLinters, No registered linters found for %s for fix. Attempting auto-installation.", filePath)
		installErr := lm.DetectAndInstallForFile(filePath)

		if installErr == nil {
			linters, err = lm.GetLinterForFile(filePath)
		} else {
			log.Warnf("LangLinters, Failed to auto-install linter for %s during fix attempt: %v", filePath, installErr)
		}
	}

	if err != nil || len(linters) == 0 {
		if err == nil && len(linters) == 0 {
			return nil, fmt.Errorf("no linters found for file for fix after checking and attempting auto-install: %s", filePath)
		}
		return nil, err
	}

	if lm.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("LangLinters, FixFile lint is running, filepath: %s", filePath))
	}
	defer lm.deleteCacheFile(filePath)

	result := make([]*FixResult, 0)
	for _, lint := range linters {
		rs, err := lint.Fix(filePath)
		if err != nil {
			log.Warnf("LangLinters, Error fixing file %s with %s: %v", filePath, lint.GetType(), err)
			continue // Continue to the next linter
		}

		result = append(result, rs)
	}

	return result, nil
}

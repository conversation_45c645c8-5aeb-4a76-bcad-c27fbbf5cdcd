package linters

import (
	"errors"
	"testing"
)

type mockLinterManager struct {
	installedLinters map[LinterType]bool
	lintersByLang    map[LanguageType][]LinterType
	lintersPool      map[LinterType]LinterIface
	languageMap      map[LanguageType][]LinterIface
}

func (m *mockLinterManager) RegisterLinter(linter LinterIface) error {
	if linter == nil {
		return errors.New("cannot register nil linter")
	}
	linterType := linter.GetType()
	language := linter.GetLanguage()
	if m.lintersPool == nil {
		m.lintersPool = make(map[LinterType]LinterIface)
	}
	if m.languageMap == nil {
		m.languageMap = make(map[LanguageType][]LinterIface)
	}
	m.lintersPool[linterType] = linter
	if _, exists := m.languageMap[language]; !exists {
		m.languageMap[language] = make([]LinterIface, 0)
	}
	found := false
	for _, registeredLinter := range m.languageMap[language] {
		if registeredLinter.GetType() == linterType {
			found = true
			break
		}
	}
	if !found {
		m.languageMap[language] = append(m.languageMap[language], linter)
	}
	return nil
}

// 简单的测试用例，验证 RegisterLinter 不会 panic
func TestRegisterLinterInInstaller(t *testing.T) {
	manager := &mockLinterManager{}
	linter := &mockLinter{
		linterType: LinterTypeESLint,
		language:   LanguageTypeJavaScript,
	}
	err := manager.RegisterLinter(linter)
	if err != nil {
		t.Errorf("RegisterLinter failed: %v", err)
	}
}

type mockLinter struct {
	linterType LinterType
	language   LanguageType
}

func (l *mockLinter) GetType() LinterType       { return l.linterType }
func (l *mockLinter) GetLanguage() LanguageType { return l.language }

// 实现 LinterIface 接口的其他方法
func (l *mockLinter) Install() error {
	return nil
}

func (l *mockLinter) IsInstalled() bool {
	return true
}

func (l *mockLinter) Lint(filePath string) (*LintResult, error) {
	return &LintResult{
		FilePath: filePath,
		Issues:   []LintIssue{},
		Success:  true,
	}, nil
}

func (l *mockLinter) Fix(filePath string) (*FixResult, error) {
	return &FixResult{
		FilePath: filePath,
		Fixed:    false,
		FixCount: 0,
	}, nil
}

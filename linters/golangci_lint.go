package linters

import (
	"agent/config"
	"agent/utils/log"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
)

// Output format constants for golangci-lint
const (
	OUTPUT_JSON_PATH = "--output.json.path"
	OUT_FORMAT_JSON  = "--out-format=json"
)

// GolangCILintLinter implements the Linter interface for GolangCI-Lint
type GolangCILintLinter struct {
	BaseLinter
	golangciLintPath      string
	mu                    sync.Mutex
	cacheFile             map[string]bool
	supportedOutputFormat string // Caches the detected output format parameter
	outputFormatDetected  bool   // Indicates whether output format detection has been performed
}

// NewGolangCILintLinter creates a new GolangCI-Lint linter
func NewGolangCILintLinter(configPath *config.LinterConfig) *GolangCILintLinter {
	return &GolangCILintLinter{
		BaseLinter: BaseLinter{
			Type:     LinterTypeGolangCILint,
			Language: LanguageTypeGolang,
			Config:   configPath,
		},
		golangciLintPath: "golangci-lint",
		cacheFile:        make(map[string]bool),
	}
}

// Install installs GolangCI-Lint
func (l *GolangCILintLinter) Install() error {
	// Check if go is available
	if _, err := WhichExecFunc("go"); err != nil {
		return fmt.Errorf("go is not installed - required for GolangCI-Lint installation, err: %s", err.Error())
	}

	// Try to install golangci-lint using go
	output, err := CombinedOutputWithBashrcFunc("go", "install", "github.com/golangci/golangci-lint/cmd/golangci-lint@latest")
	if err != nil {
		log.Warnf("LangLinters, Failed to install GolangCI-Lint: %v", err)
		// Try alternative installation method with curl
		if _, curlErr := WhichExecFunc("curl"); curlErr == nil {
			// Create a temporary script file for installation
			tempFile, err := os.CreateTemp("", "install-golangci-lint-*.sh")
			if err != nil {
				return fmt.Errorf("failed to create temporary file: %v", err)
			}
			defer os.Remove(tempFile.Name())

			// Write the installation script to the temp file
			installScript := `#!/bin/sh
# Binary installation script
set -e
# Get the latest version
VERSION=$(curl -s https://api.github.com/repos/golangci/golangci-lint/releases/latest | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/')
if [ -z "$VERSION" ]; then
    VERSION="v1.54.2"  # Fallback to a known version
fi
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin $VERSION
`
			if _, err := tempFile.Write([]byte(installScript)); err != nil {
				return fmt.Errorf("failed to write to temporary file: %v", err)
			}
			tempFile.Close()

			// Make the script executable
			if err := os.Chmod(tempFile.Name(), 0755); err != nil {
				return fmt.Errorf("failed to make script executable: %v", err)
			}

			// Execute the installation script
			output, err = CombinedOutputWithBashrcFunc("sh", tempFile.Name())
			if err != nil {
				log.Errorf("LangLinters, Failed to install GolangCI-Lint with alternative method: %v, Output: %s", err, string(output))
				return fmt.Errorf("failed to install GolangCI-Lint: %v", err)
			}
		} else {
			return fmt.Errorf("failed to install GolangCI-Lint and curl is not available for alternative installation")
		}
	}

	log.Infof("LangLinters, GolangCI-Lint installed successfully, output: %s", string(output))
	return nil
}

// IsInstalled checks if GolangCI-Lint is installed
func (l *GolangCILintLinter) IsInstalled() bool {
	_, err := WhichExecFunc(l.golangciLintPath)
	if err == nil {
		return true
	}

	return false
}

// findGolangCILintConfig looks for a GolangCI-Lint config file in or above the directory of the given file
func (l *GolangCILintLinter) findGolangCILintConfig(filePath string) string {
	if l.Config != nil && l.Config.ConfigPath != "" {
		return l.Config.ConfigPath
	}

	dir := filepath.Dir(filePath)
	for {
		// Check for common GolangCI-Lint config files
		for _, name := range []string{
			".golangci.yml", ".golangci.yaml", ".golangci.toml", ".golangci.json",
		} {
			configPath := filepath.Join(dir, name)
			if _, err := os.Stat(configPath); err == nil {
				return configPath
			}
		}

		// Go up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// We've reached the root
			break
		}
		dir = parent
	}

	return ""
}

type GolangciLintIssue struct {
	FromLinter           string               `json:"FromLinter"`
	Text                 string               `json:"Text"`
	Severity             string               `json:"Severity"`
	SourceLines          []string             `json:"SourceLines"`
	Pos                  GolangciLintPosition `json:"Pos"`
	ExpectNoLint         bool                 `json:"ExpectNoLint"`
	ExpectedNoLintLinter string               `json:"ExpectedNoLintLinter"`
}

type GolangciLintPosition struct {
	Filename string `json:"Filename"`
	Offset   int    `json:"Offset"`
	Line     int    `json:"Line"`
	Column   int    `json:"Column"`
}

type GolangciLintLinter struct {
	Name             string `json:"Name"`
	Enabled          bool   `json:"Enabled"`
	EnabledByDefault bool   `json:"EnabledByDefault"`
}

type GolangciLintReport struct {
	Linters []GolangciLintLinter `json:"Linters"`
}

type GolangciLint struct {
	Issues []GolangciLintIssue `json:"Issues"`
	Report GolangciLintReport  `json:"Report"`
}

func (l *GolangCILintLinter) checkCacheFile(filepath string) bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	isExt, ok := l.cacheFile[filepath]
	if ok && isExt {
		return true
	}

	l.cacheFile[filepath] = true

	return false
}

func (l *GolangCILintLinter) deleteCacheFile(filepath string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	delete(l.cacheFile, filepath)
}

// detectSupportedOutputFormat detects which output format parameter is supported by golangci-lint
// It caches the result to avoid repeated detection
func (l *GolangCILintLinter) detectSupportedOutputFormat() (string, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.outputFormatDetected {
		return l.supportedOutputFormat, nil
	}

	// Run golangci-lint help to check command line options
	output, err := CombinedOutputWithBashrcFunc(l.golangciLintPath, "run", "--help")

	if err != nil {
		log.Warnf("LangLinters, Failed to get golangci-lint help: %v", err)
		// Return error if we can't determine the format
		return "", fmt.Errorf("failed to detect golangci-lint output format: %v", err)
	}

	helpText := string(output)

	// Check for presence of output.json.path first (newer versions)
	if strings.Contains(helpText, "output.json.path") {
		l.supportedOutputFormat = OUTPUT_JSON_PATH
		l.outputFormatDetected = true
		return OUTPUT_JSON_PATH, nil
	}

	// Check for out-format parameter (older versions)
	if strings.Contains(helpText, "out-format") {
		l.supportedOutputFormat = OUT_FORMAT_JSON
		l.outputFormatDetected = true
		return OUT_FORMAT_JSON, nil
	}

	// Return error if neither format is found
	log.Warnf("LangLinters, Could not detect supported output format")
	return "", fmt.Errorf("could not detect supported golangci-lint output format")
}

// Lint performs linting on a file using GolangCI-Lint
func (l *GolangCILintLinter) Lint(filePath string) (*LintResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("GolangCILint lint is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("GolangCI-Lint is not installed")
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Find config file if it exists
	configFile := l.findGolangCILintConfig(filePath)

	// Create a temporary file to store the output
	tempFile, err := os.CreateTemp("", "golangci_lint_output_*.json")
	if err != nil {
		return nil, fmt.Errorf("failed to create temporary file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	// Detect which output format is supported
	outputFormat, err := l.detectSupportedOutputFormat()
	if err != nil {
		return nil, err
	}

	var args []string
	var output []byte

	// Build command based on detected format
	if outputFormat == OUTPUT_JSON_PATH {
		// Use --output.json.path for newer versions
		args = []string{"run", "--output.json.path", tempFile.Name()}

		if configFile != "" {
			args = append(args, "--config", configFile)
		}

		args = append(args, filePath)

		// Use CombinedOutputWithBashrcFunc for better testability
		output, err = CombinedOutputWithBashrcFunc(l.golangciLintPath, args...)
		if err != nil && !strings.HasPrefix(err.Error(), "exit status") {
			log.Warnf("LangLinters, GolangCILint lint, err: %+v", err)
			return nil, fmt.Errorf("golangci-lint execution failed: %v", err)
		}

		// For OUTPUT_JSON_PATH format, the output should be written to the temp file
		// So we read from the temp file instead of using the command output
		fileOutput, err := os.ReadFile(tempFile.Name())
		if err != nil {
			return nil, fmt.Errorf("failed to read GolangCI-Lint output: %v", err)
		}
		output = fileOutput
	} else {
		// Use --out-format=json for older versions
		args = []string{"run", "--disable-all", "-E", "errcheck,govet", "--out-format=json"}

		if configFile != "" {
			args = append(args, "--config", configFile)
		}

		args = append(args, filePath)

		// Use CombinedOutputWithBashrcFunc for better testability
		output, err = CombinedOutputWithBashrcFunc(l.golangciLintPath, args...)
		if err != nil && !strings.HasPrefix(err.Error(), "exit status") {
			log.Warnf("LangLinters, GolangCILint lint, err: %+v", err)
			return nil, fmt.Errorf("golangci-lint execution failed: %v", err)
		}

		// Write the output to the temporary file for consistent processing
		if err := os.WriteFile(tempFile.Name(), output, 0644); err != nil {
			return nil, fmt.Errorf("failed to write GolangCI-Lint output to temp file: %v", err)
		}
	}

	var issues GolangciLint
	if len(output) > 0 {
		if err := json.Unmarshal(output, &issues); err != nil {
			// If we can't parse as JSON, it might be text output or an error message
			return nil, fmt.Errorf("failed to parse GolangCI-Lint output: %v", err)
		}
	}

	// Convert to our common format
	result := &LintResult{
		FilePath: filePath,
		Issues:   []LintIssue{},
		Success:  true,
	}

	for _, issue := range issues.Issues {
		severity := SeverityWarning
		switch strings.ToLower(issue.Severity) {
		case "error":
			severity = SeverityError
		case "warning":
			severity = SeverityWarning
		case "info":
			severity = SeverityInfo
		case "":
			severity = SeverityError
		}

		lintIssue := LintIssue{
			Location: Location{
				FilePath: filePath,
				Range: Range{
					Start: Position{
						Line:      issue.Pos.Line,
						Character: issue.Pos.Column - 1, // Convert to 0-based
					},
					End: Position{
						Line:      issue.Pos.Line,
						Character: issue.Pos.Column - 1, // Approximate - GolangCI-Lint doesn't always provide end column
					},
				},
			},
			Message:  issue.Text,
			Severity: severity,
			Source:   issue.FromLinter,
			Code:     issue.FromLinter,
		}

		// If end line is not provided, use the start line
		if lintIssue.Location.Range.End.Line == 0 {
			lintIssue.Location.Range.End.Line = lintIssue.Location.Range.Start.Line
		}

		result.Issues = append(result.Issues, lintIssue)
	}

	return result, nil
}

// Fix attempts to fix issues in a file using GolangCI-Lint
func (l *GolangCILintLinter) Fix(filePath string) (*FixResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("GolangCILint fix is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("GolangCI-Lint is not installed")
	}

	// Check if file exists and read original content
	originalContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %s: %v", filePath, err)
	}

	// Find config file if it exists
	configFile := l.findGolangCILintConfig(filePath)
	args := []string{"run", "--fix"}

	if configFile != "" {
		args = append(args, "--config", configFile)
	}

	args = append(args, filePath)
	cmd := execWithBashrc(l.golangciLintPath, args...)

	output, err := cmd.CombinedOutput()
	if err != nil && err.Error() != "exit status 1" {
		log.Warnf("LangLinters, GolangCILint fix, err: %+v", err)
	}

	// Check if anything was fixed
	newContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file after fix: %s: %v", filePath, err)
	}

	// Compare the content to see if anything was fixed
	fixed := !strings.EqualFold(string(originalContent), string(newContent))

	// Try to determine number of fixes based on output
	fixCount := 0
	if fixed {
		// Attempt to extract number of fixed issues from output
		re := regexp.MustCompile(`(\d+) issues? fixed`)
		matches := re.FindStringSubmatch(string(output))
		if len(matches) > 1 {
			fmt.Sscanf(matches[1], "%d", &fixCount)
		}
	}

	return &FixResult{
		FilePath: filePath,
		Fixed:    fixed,
		FixCount: fixCount,
		Error:    "",
	}, nil
}

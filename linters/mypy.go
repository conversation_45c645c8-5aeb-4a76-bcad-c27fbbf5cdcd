package linters

import (
	"agent/config"
	"agent/utils/log"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
)

// MyPyLinter implements the Linter interface for MyPy
type MyPyLinter struct {
	BaseLinter
	mypyPath  string
	pipCmd    string
	mu        sync.Mutex
	cacheFile map[string]bool
}

// NewMyPyLinter creates a new MyPy linter
func NewMyPyLinter(configPath *config.LinterConfig) *MyPyLinter {
	return &MyPyLinter{
		BaseLinter: BaseLinter{
			Type:     LinterTypeMyPy,
			Language: LanguageTypePython,
			Config:   configPath,
		},
		mypyPath:  "mypy",
		pipCmd:    "pip",
		cacheFile: make(map[string]bool),
	}
}

// Install installs MyPy
func (l *MyPyLinter) Install() error {
	// First check if pip3 is available, then pip
	if _, err := WhichExecFunc("pip3"); err == nil {
		l.pipCmd = "pip3"
	} else if _, err = WhichExecFunc("pip"); err == nil {
		l.pipCmd = "pip"
	} else {
		return fmt.Errorf("pip is not installed - required for MyPy installation, err: %s", err.Error())
	}

	output, err := CombinedOutputWithBashrcFunc(l.pipCmd, "install", "mypy")
	if err != nil {
		log.Warnf("LangLinters, Failed to install MyPy: %v, Output: %s", err, string(output))
		return err
	}

	return nil
}

// IsInstalled checks if MyPy is installed
func (l *MyPyLinter) IsInstalled() bool {
	_, err := WhichExecFunc(l.mypyPath)
	if err == nil {
		return true
	}

	return false
}

// findMyPyConfig looks for a MyPy config file in or above the directory of the given file
func (l *MyPyLinter) findMyPyConfig(filePath string) string {
	if l.Config != nil && l.Config.ConfigPath != "" {
		return l.Config.ConfigPath
	}

	dir := filepath.Dir(filePath)
	for {
		// Check for common MyPy config files
		for _, name := range []string{
			"mypy.ini", ".mypy.ini", "pyproject.toml", "setup.cfg",
		} {
			configPath := filepath.Join(dir, name)
			if _, err := os.Stat(configPath); err == nil {
				return configPath
			}
		}

		// Go up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// We've reached the root
			break
		}
		dir = parent
	}

	return ""
}

// parseMyPyOutput parses the output from MyPy into a structured format
// Supports both traditional format and new format with error classification:
// Traditional: file.py:10: error: Missing return statement
// New format: file.py:10: error: Missing return statement [syntax]
func (l *MyPyLinter) parseMyPyOutput(output string, filePath string) []LintIssue {
	issues := make([]LintIssue, 0)

	// Regular expression to match mypy error output format with optional error classification
	// Example: file.py:10: error: Missing return statement
	// New format: file.py:10: error: Missing return statement [syntax]
	re := regexp.MustCompile(`^(.+?):(\d+)(?::(\d+))?: (\w+): (.+?)(?:\s+\[(\w+)\])?$`)

	for _, line := range strings.Split(output, "\n") {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Skip summary lines
		if strings.Contains(line, "Found") && strings.Contains(line, "error") && strings.Contains(line, "file") {
			continue
		}

		matches := re.FindStringSubmatch(line)
		if len(matches) < 6 {
			// Skip lines that don't match the expected format
			continue
		}

		reportedFile := matches[1]
		// Only include issues for the requested file
		if !strings.HasSuffix(filePath, reportedFile) && !strings.HasSuffix(reportedFile, filePath) {
			continue
		}

		lineNum := 0
		fmt.Sscanf(matches[2], "%d", &lineNum)

		column := 1 // Default column if not provided
		if matches[3] != "" {
			fmt.Sscanf(matches[3], "%d", &column)
		}

		severity := SeverityWarning
		if matches[4] == "error" {
			severity = SeverityError
		}

		issue := LintIssue{
			Location: Location{
				FilePath: filePath,
				Range: Range{
					Start: Position{
						Line:      lineNum,
						Character: column - 1, // Convert to 0-based
					},
					End: Position{
						Line:      lineNum,
						Character: column, // Just set end to one character after start
					},
				},
			},
			Message:        matches[5],
			Severity:       severity,
			Source:         "mypy",
			Code:           matches[4], // Use the type (error, note, etc.) as the code
			Classification: matches[6], // Add the classification if present (will be empty for traditional format)
		}

		issues = append(issues, issue)
	}

	return issues
}

func (l *MyPyLinter) checkCacheFile(filepath string) bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	isExt, ok := l.cacheFile[filepath]
	if ok && isExt {
		return true
	}

	l.cacheFile[filepath] = true

	return false
}

func (l *MyPyLinter) deleteCacheFile(filepath string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	delete(l.cacheFile, filepath)
}

// Lint performs linting on a file using MyPy
func (l *MyPyLinter) Lint(filePath string) (*LintResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("MyPy lint is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("MyPy is not installed")
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Find config file if it exists
	configFile := l.findMyPyConfig(filePath)
	args := make([]string, 0)

	if configFile != "" {
		// Add config file argument based on file type
		ext := filepath.Ext(configFile)
		switch ext {
		case ".ini":
			args = append(args, "--config-file", configFile)
		case ".toml":
			// pyproject.toml is automatically detected by mypy
		case ".cfg":
			// setup.cfg is automatically detected by mypy
		}
	}

	// Add disallow untyped calls for stricter typing checks
	args = append(args, "--show-column-numbers")

	// Add the file to check
	args = append(args, filePath)

	cmd := execWithBashrc(l.mypyPath, args...)
	output, err := cmd.CombinedOutput()

	// Parse the output regardless of whether mypy found issues
	issues := l.parseMyPyOutput(string(output), filePath)

	// If there was an execution error and no issues were found, return the error
	if err != nil && len(issues) == 0 {
		log.Warnf("mypy lint, err: %+v, output: %s", err, output)
		return nil, fmt.Errorf("mypy execution failed: %v", err)
	}

	return &LintResult{
		FilePath: filePath,
		Issues:   issues,
		Success:  err == nil || len(issues) > 0, // Success if no error or if we found issues
		Error:    "",                            // No error if we could parse output
	}, nil
}

// Fix attempts to fix issues in a file using MyPy
// Note: MyPy does not support auto-fixing, so this method will return the file unchanged
func (l *MyPyLinter) Fix(filePath string) (*FixResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("MyPy fix is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	// MyPy doesn't support auto-fixing, so just return that nothing was fixed
	return &FixResult{
		FilePath: filePath,
		Fixed:    false,
		FixCount: 0,
		Error:    "MyPy does not support automatic fixing of type issues",
	}, nil
}

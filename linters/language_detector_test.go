package linters

import (
	"agent/treesitter"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
)

// TestMain 设置测试环境变量
func TestMain(m *testing.M) {
	// 设置必要的环境变量
	if os.Getenv("paas_app_path") == "" {
		// 获取项目根目录（当前目录的父目录，因为我们在 linters 子目录中）
		if cwd, err := os.Getwd(); err == nil {
			// 如果当前在 linters 目录中，则设置父目录为项目根目录
			if strings.HasSuffix(cwd, "/linters") {
				projectRoot := filepath.Dir(cwd)
				os.Setenv("paas_app_path", projectRoot)
			} else {
				os.Setenv("paas_app_path", cwd)
			}
		}
	}

	if os.Getenv("paas_config_file_name") == "" {
		// 设置配置文件名为 .1024
		os.Setenv("paas_config_file_name", ".1024")
	}

	// 运行测试
	os.Exit(m.Run())
}

func TestNewLanguageDetector(t *testing.T) {
	detector := NewLanguageDetector()

	// Check that detector is initialized with FileExtToLanguage
	if len(detector.languageExtensions) != len(FileExtToLanguage) {
		t.Errorf("Expected languageExtensions to have %d entries, got %d",
			len(FileExtToLanguage), len(detector.languageExtensions))
	}

	// Check a few key extensions
	if detector.languageExtensions[".js"] != LanguageTypeJavaScript {
		t.Errorf("Expected .js to map to %s, got %s",
			LanguageTypeJavaScript, detector.languageExtensions[".js"])
	}

	if detector.languageExtensions[".py"] != LanguageTypePython {
		t.Errorf("Expected .py to map to %s, got %s",
			LanguageTypePython, detector.languageExtensions[".py"])
	}

	if detector.languageExtensions[".go"] != LanguageTypeGolang {
		t.Errorf("Expected .go to map to %s, got %s",
			LanguageTypeGolang, detector.languageExtensions[".go"])
	}
}

func TestDetectLanguage(t *testing.T) {
	detector := NewLanguageDetector()

	tests := []struct {
		filePath     string
		expectedLang LanguageType
	}{
		// JavaScript and related
		{"test.js", LanguageTypeJavaScript},
		{"test.jsx", LanguageTypeJavaScript},
		{"test.mjs", LanguageTypeJavaScript},
		{"test.cjs", LanguageTypeJavaScript},
		{"some/path/to/app.js", LanguageTypeJavaScript},

		// TypeScript
		{"test.ts", LanguageTypeTypeScript},
		{"test.tsx", LanguageTypeTypeScript},
		{"/absolute/path/to/service.ts", LanguageTypeTypeScript},

		// Python
		{"test.py", LanguageTypePython},
		{"test.pyi", LanguageTypePython},
		{"test.pyx", LanguageTypePython},
		{"../relative/path/script.py", LanguageTypePython},

		// Go
		{"test.go", LanguageTypeGolang},
		{"package/main.go", LanguageTypeGolang},

		// Ruby
		{"test.rb", LanguageTypeRuby},
		{"test.rake", LanguageTypeRuby},

		// Java
		{"test.java", LanguageTypeJava},
		{"com/example/Main.java", LanguageTypeJava},

		// Swift
		{"test.swift", LanguageTypeSwift},

		// Kotlin
		{"test.kt", LanguageTypeKotlin},
		{"test.kts", LanguageTypeKotlin},

		// Scala
		{"test.scala", LanguageTypeScala},

		// PHP
		{"test.php", LanguageTypePHP},
		{"index.php", LanguageTypePHP},

		// Rust
		{"test.rs", LanguageTypeRust},
		{"src/main.rs", LanguageTypeRust},

		// CSS
		{"test.css", LanguageTypeCSS},
		{"styles.css", LanguageTypeCSS},
		{"themes/dark/styles.css", LanguageTypeCSS},

		// HTML
		{"test.html", LanguageTypeHTML},
		{"test.htm", LanguageTypeHTML},
		{"public/index.html", LanguageTypeHTML},

		// Shell
		{"test.sh", LanguageTypeShell},
		{"test.bash", LanguageTypeShell},
		{"test.zsh", LanguageTypeShell},
		{"scripts/deploy.sh", LanguageTypeShell},

		// CSharp
		{"test.cs", LanguageTypeCSharp},
		{"Models/User.cs", LanguageTypeCSharp},

		// C++
		{"test.cpp", LanguageTypeCPP},
		{"test.cc", LanguageTypeCPP},
		{"test.cxx", LanguageTypeCPP},
		{"test.hpp", LanguageTypeCPP},
		{"src/utils.cpp", LanguageTypeCPP},

		// C
		{"test.c", LanguageTypeC},
		{"test.h", LanguageTypeC},
		{"lib/helper.c", LanguageTypeC},

		// Dart
		{"test.dart", LanguageTypeDart},
		{"lib/widgets/button.dart", LanguageTypeDart},

		// Edge cases
		{"file_with_no_extension", LanguageTypeUnknown},
		{"file.unknown_extension", LanguageTypeUnknown},
		{".hidden", LanguageTypeUnknown},
		{"", LanguageTypeUnknown},
		{".bashrc", LanguageTypeUnknown}, // Just dot and extension
		{"/path/with/trailing/dot.", LanguageTypeUnknown},
		{"file with spaces.js", LanguageTypeJavaScript},
	}

	for _, tt := range tests {
		t.Run(tt.filePath, func(t *testing.T) {
			lang := detector.DetectLanguage(tt.filePath)
			if lang != tt.expectedLang {
				t.Errorf("DetectLanguage(%s) = %s, want %s", tt.filePath, lang, tt.expectedLang)
			}
		})
	}
}

func TestDetectLanguageWithTreesitter(t *testing.T) {
	// Create a temporary file with unknown extension but recognizable content
	tempDir, err := os.MkdirTemp("", "language-detector-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Python content with .xyz extension
	pythonFile := filepath.Join(tempDir, "script.xyz")
	pythonContent := `#!/usr/bin/env python3
import os
import sys

def main():
    print("Hello, world!")

if __name__ == "__main__":
    main()
`
	if err := os.WriteFile(pythonFile, []byte(pythonContent), 0644); err != nil {
		t.Fatalf("Failed to write Python test file: %v", err)
	}

	// JavaScript content with .xyz extension
	jsFile := filepath.Join(tempDir, "code.xyz")
	jsContent := `const hello = () => {
    console.log("Hello, world!");
};

hello();
`
	if err := os.WriteFile(jsFile, []byte(jsContent), 0644); err != nil {
		t.Fatalf("Failed to write JavaScript test file: %v", err)
	}

	// Skip if treesitter is not available or CI environment
	if os.Getenv("CI") != "" {
		t.Skip("Skipping treesitter test in CI environment")
	}

	ts := treesitter.GetInstance()
	if ts == nil {
		t.Skip("TreeSitter not available, skipping test")
	}

	detector := NewLanguageDetector()

	// Test the files
	// Note: This test may be flaky as it depends on treesitter's ability to detect language
	// from content, which is not always reliable for small snippets
	lang := detector.DetectLanguage(pythonFile)
	if lang != LanguageTypePython && lang != LanguageTypeUnknown {
		t.Logf("TreeSitter detection for Python content returned: %s", lang)
	}

	lang = detector.DetectLanguage(jsFile)
	if lang != LanguageTypeJavaScript && lang != LanguageTypeUnknown {
		t.Logf("TreeSitter detection for JavaScript content returned: %s", lang)
	}
}

func TestIsLanguageInstalled(t *testing.T) {
	detector := NewLanguageDetector()

	// Skip this test if running in CI environment without language tools
	if os.Getenv("CI") != "" {
		t.Skip("Skipping language installation test in CI environment")
	}

	// Test for a language that's likely to be installed on the test machine (Go)
	// This is a reasonable assumption since we're running Go tests
	if !detector.IsLanguageInstalled(LanguageTypeGolang) {
		t.Error("Expected Go to be installed on the testing machine")
	}

	// For unknown language types, should return false
	if detector.IsLanguageInstalled(LanguageTypeUnknown) {
		t.Error("Expected unknown language type to return false for IsLanguageInstalled")
	}

	// Test for languages that have multiple possible binaries
	pythonInstalled := detector.IsLanguageInstalled(LanguageTypePython)
	t.Logf("Python installed: %v (informational only, not an error if false)", pythonInstalled)

	jsInstalled := detector.IsLanguageInstalled(LanguageTypeJavaScript)
	t.Logf("JavaScript (Node.js) installed: %v (informational only, not an error if false)", jsInstalled)
}

// Mock exec.Command for testing
type mockCommand struct {
	cmd  string
	args []string
}

// TestDetectAndGetLanguageVersion tests the language version detection.
// This is a more complex test that mocks exec.Command to avoid actual system calls.
func TestGetLanguageVersion(t *testing.T) {
	detector := NewLanguageDetector()

	// Skip full version detection if in CI
	if os.Getenv("CI") != "" {
		t.Skip("Skipping language version detection test in CI environment")
	}

	// Test Go version detection - should work since we're running in Go
	version, err := detector.GetLanguageVersion(LanguageTypeGolang)
	if err != nil {
		t.Fatalf("Failed to detect Go version: %v", err)
	}

	// Check format (e.g., "1.16", "1.17", etc.)
	if !strings.Contains(version, ".") {
		t.Errorf("Go version does not match expected format: %s", version)
	}

	// Test unsupported language
	_, err = detector.GetLanguageVersion(LanguageTypeUnknown)
	if err == nil {
		t.Error("Expected error when getting version for unsupported language")
	}

	// Test error case with unsupported language
	_, err = detector.GetLanguageVersion(LanguageType("UnsupportedLanguage"))
	if err == nil {
		t.Error("Expected error for unsupported language type")
	}
}

func TestDetectLanguageVersion(t *testing.T) {
	// Create temp directory for test project files
	tempDir, err := os.MkdirTemp("", "language-detector-test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	detector := NewLanguageDetector()

	// Test Go project
	goModContent := []byte("module testproject\n\ngo 1.18\n")
	if err := os.WriteFile(filepath.Join(tempDir, "go.mod"), goModContent, 0644); err != nil {
		t.Fatalf("Failed to write go.mod: %v", err)
	}

	version, err := detector.DetectLanguageVersion(tempDir, LanguageTypeGolang)
	if err != nil {
		t.Fatalf("Failed to detect Go version from go.mod: %v", err)
	}
	if version != "1.18" {
		t.Errorf("Expected Go version 1.18, got %s", version)
	}

	// Test Python project
	pyProjectContent := []byte(`[tool.poetry.dependencies]
python = "^3.8"
`)
	if err := os.WriteFile(filepath.Join(tempDir, "pyproject.toml"), pyProjectContent, 0644); err != nil {
		t.Fatalf("Failed to write pyproject.toml: %v", err)
	}

	version, err = detector.DetectLanguageVersion(tempDir, LanguageTypePython)
	if err != nil {
		t.Fatalf("Failed to detect Python version from pyproject.toml: %v", err)
	}
	if version != "3.8.0" {
		t.Errorf("Expected Python version 3.8.0, got %s", version)
	}

	// Test TypeScript project
	packageJsonContent := []byte(`{
  "name": "test-project",
  "dependencies": {
    "typescript": "^4.5.4"
  },
  "engines": {
    "node": "16.13.0"
  }
}`)
	if err := os.WriteFile(filepath.Join(tempDir, "package.json"), packageJsonContent, 0644); err != nil {
		t.Fatalf("Failed to write package.json: %v", err)
	}

	version, err = detector.DetectLanguageVersion(tempDir, LanguageTypeTypeScript)
	if err != nil {
		t.Fatalf("Failed to detect TypeScript version from package.json: %v", err)
	}
	if version != "4.5.4" {
		t.Errorf("Expected TypeScript version 4.5.4, got %s", version)
	}

	// Test Node.js version detection for JavaScript project
	version, err = detector.DetectLanguageVersion(tempDir, LanguageTypeJavaScript)
	if err != nil {
		t.Fatalf("Failed to detect Node version from package.json: %v", err)
	}
	if version != "16.13.0" {
		t.Errorf("Expected Node version 16.13.0, got %s", version)
	}

	// Test missing configuration files
	subDir := filepath.Join(tempDir, "empty-project")
	if err := os.Mkdir(subDir, 0755); err != nil {
		t.Fatalf("Failed to create subdirectory: %v", err)
	}

	_, err = detector.DetectLanguageVersion(subDir, LanguageTypeGolang)
	if err == nil {
		t.Error("Expected error when no go.mod found")
	}

	_, err = detector.DetectLanguageVersion(subDir, LanguageTypePython)
	if err == nil {
		t.Error("Expected error when no Python config found")
	}

	_, err = detector.DetectLanguageVersion(subDir, LanguageTypeJavaScript)
	if err == nil {
		t.Error("Expected error when no package.json found")
	}
}

func TestGetDetectedLanguagesInProject(t *testing.T) {
	// Create temp directory for test project with multiple languages
	tempDir, err := os.MkdirTemp("", "multi-language-project")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create files for different languages
	files := map[string]LanguageType{
		"main.go":     LanguageTypeGolang,
		"script.py":   LanguageTypePython,
		"index.js":    LanguageTypeJavaScript,
		"styles.css":  LanguageTypeCSS,
		"index.html":  LanguageTypeHTML,
		"config.json": LanguageTypeUnknown, // No specific language type for JSON
	}

	for filename, _ := range files {
		err := os.WriteFile(filepath.Join(tempDir, filename), []byte("// Test content"), 0644)
		if err != nil {
			t.Fatalf("Failed to create file %s: %v", filename, err)
		}
	}

	// Create a subdirectory
	subDir := filepath.Join(tempDir, "src")
	if err := os.Mkdir(subDir, 0755); err != nil {
		t.Fatalf("Failed to create subdirectory: %v", err)
	}

	// Add some files in the subdirectory
	if err := os.WriteFile(filepath.Join(subDir, "app.ts"), []byte("// TypeScript content"), 0644); err != nil {
		t.Fatalf("Failed to create file: %v", err)
	}

	// Create hidden directory and file (should be ignored)
	if err := os.Mkdir(filepath.Join(tempDir, ".git"), 0755); err != nil {
		t.Fatalf("Failed to create hidden directory: %v", err)
	}
	if err := os.WriteFile(filepath.Join(tempDir, ".gitignore"), []byte("node_modules/"), 0644); err != nil {
		t.Fatalf("Failed to create hidden file: %v", err)
	}

	// Detect languages
	detector := NewLanguageDetector()
	languages, err := detector.GetDetectedLanguagesInProject(tempDir)
	if err != nil {
		t.Fatalf("Failed to detect languages in project: %v", err)
	}

	// Check that we found the expected languages
	expectedLanguages := map[LanguageType]bool{
		LanguageTypeGolang:     true,
		LanguageTypePython:     true,
		LanguageTypeJavaScript: true,
		LanguageTypeCSS:        true,
		LanguageTypeHTML:       true,
		LanguageTypeTypeScript: true,
	}

	// Check all expected languages were found
	for lang := range expectedLanguages {
		if !languages[lang] {
			t.Errorf("Expected language %s not found in project", lang)
		}
	}

	// Check we don't have unexpected languages
	for lang := range languages {
		if !expectedLanguages[lang] {
			t.Errorf("Unexpected language %s found in project", lang)
		}
	}

	// Verify hidden files were ignored
	if languages[LanguageTypeUnknown] {
		t.Error("Unknown language type should not be included in detected languages")
	}

	// Test with non-existent directory
	_, err = detector.GetDetectedLanguagesInProject("/path/that/does/not/exist")
	if err == nil {
		t.Error("Expected error when directory does not exist")
	}

	// Test with empty directory
	emptyDir, err := os.MkdirTemp("", "empty-project")
	if err != nil {
		t.Fatalf("Failed to create empty directory: %v", err)
	}
	defer os.RemoveAll(emptyDir)

	emptyLangs, err := detector.GetDetectedLanguagesInProject(emptyDir)
	if err != nil {
		t.Fatalf("Failed to detect languages in empty project: %v", err)
	}

	if len(emptyLangs) != 0 {
		t.Errorf("Expected no languages in empty directory, got %d", len(emptyLangs))
	}
}

func TestGetLanguageDetectorSingleton(t *testing.T) {
	// Get singleton instance twice
	detector1 := GetLanguageDetector()
	detector2 := GetLanguageDetector()

	// Test that we get the same instance
	if detector1 != detector2 {
		t.Error("GetLanguageDetector() did not return the same instance")
	}
}

func TestProjectWithSpecialCharacters(t *testing.T) {
	// Create temp directory with special characters in the name
	tempDir, err := os.MkdirTemp("", "project with spaces-and-!special@chars#")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a file with special characters in the name
	specialFile := filepath.Join(tempDir, "file with spaces & special chars!.js")
	if err := os.WriteFile(specialFile, []byte("console.log('test');"), 0644); err != nil {
		t.Fatalf("Failed to create special file: %v", err)
	}

	// Test that language detection works with special characters
	detector := NewLanguageDetector()
	lang := detector.DetectLanguage(specialFile)
	if lang != LanguageTypeJavaScript {
		t.Errorf("Failed to detect JavaScript for file with special characters")
	}

	// Try to detect languages in the project
	languages, err := detector.GetDetectedLanguagesInProject(tempDir)
	if err != nil {
		t.Fatalf("Failed to detect languages in project with special characters: %v", err)
	}

	if !languages[LanguageTypeJavaScript] {
		t.Error("Failed to detect JavaScript in project with special characters")
	}
}

func TestExtremelyLongPaths(t *testing.T) {
	// Skip on Windows where path length limitations are stricter
	if runtime.GOOS == "windows" {
		t.Skip("Skipping long path test on Windows")
	}

	// Create a temp directory
	tempDir, err := os.MkdirTemp("", "long-paths")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a deeply nested directory structure
	currentDir := tempDir
	for i := 0; i < 15; i++ {
		dirName := "subdir_" + strings.Repeat(string(rune('a'+i%26)), 10)
		currentDir = filepath.Join(currentDir, dirName)
		if err := os.MkdirAll(currentDir, 0755); err != nil {
			t.Fatalf("Failed to create nested directory: %v", err)
		}
	}

	// Create a file with an extremely long path
	longPathFile := filepath.Join(currentDir, "test_file_with_long_name_"+strings.Repeat("a", 50)+".go")
	if err := ioutil.WriteFile(longPathFile, []byte("package main"), 0644); err != nil {
		t.Logf("Could not create extremely long path file: %v", err)
		return // Skip the rest if we can't create the file
	}

	// Test language detection with the long path
	detector := NewLanguageDetector()
	lang := detector.DetectLanguage(longPathFile)
	if lang != LanguageTypeGolang {
		t.Errorf("Failed to detect Go language for extremely long path")
	}
}

func TestLanguageDetectorIntegrationWithManager(t *testing.T) {
	// Skip this integration test in CI environments
	if os.Getenv("CI") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Get linter manager instance
	manager := GetLinterManager()

	// Create a test file for testing
	tempDir, err := os.MkdirTemp("", "language-detector-integration")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a test Go file
	testFile := filepath.Join(tempDir, "main.go")
	if err := os.WriteFile(testFile, []byte("package main\n\nfunc main() {\n}\n"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Detect and install linter for Go file
	err = manager.DetectAndInstallForFile(testFile)
	linters, _ := manager.GetLinterForFile(testFile)

	// We're not asserting installation success (which is system-dependent)
	// but checking that detection works correctly
	if len(linters) == 0 {
		t.Logf("Note: No linters found for Go file")
	} else {
		t.Logf("Found %d linters for Go file", len(linters))
		for _, linter := range linters {
			t.Logf("Found linter: %s", linter.GetType())
			if linter.GetType() != LinterTypeGolangCILint {
				t.Errorf("Expected linter type %s for Go file, got %s",
					LinterTypeGolangCILint, linter.GetType())
			}
		}
	}

	// Check the project languages - should detect Go
	langs, err := GetLanguageDetector().GetDetectedLanguagesInProject(tempDir)
	if err != nil {
		t.Fatalf("Failed to detect languages in project: %v", err)
	}

	if !langs[LanguageTypeGolang] {
		t.Error("Golang should be detected in the test project")
	}
}

func TestUnicodeFilenames(t *testing.T) {
	// Create temp directory for Unicode filename tests
	tempDir, err := os.MkdirTemp("", "unicode-filenames")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test files with Unicode characters in different scripts
	unicodeFiles := []struct {
		filename string
		content  string
		lang     LanguageType
	}{
		{"你好.py", "print('Hello')", LanguageTypePython},
		{"привет.js", "console.log('Hello');", LanguageTypeJavaScript},
		{"こんにちは.go", "package main", LanguageTypeGolang},
		{"안녕하세요.html", "<html></html>", LanguageTypeHTML},
		{"ñandú.css", "body { color: red; }", LanguageTypeCSS},
	}

	for _, uf := range unicodeFiles {
		filePath := filepath.Join(tempDir, uf.filename)
		// Skip if the OS/filesystem doesn't support the Unicode filename
		err := os.WriteFile(filePath, []byte(uf.content), 0644)
		if err != nil {
			t.Logf("Skipping Unicode filename test for %s: %v", uf.filename, err)
			continue
		}

		detector := NewLanguageDetector()
		lang := detector.DetectLanguage(filePath)
		if lang != uf.lang {
			t.Errorf("Expected %s for file %s, got %s", uf.lang, uf.filename, lang)
		}
	}

	// Test with non-printable characters (should be handled gracefully)
	// Using a filename with some control characters (if allowed by the OS)
	nonPrintable := string([]byte{0x61, 0x62, 0x07, 0x08, 0x0A, 0x63, 0x2E, 0x70, 0x79}) // "ab<bell><backspace><LF>c.py"
	nonPrintablePath := filepath.Join(tempDir, nonPrintable)

	// Just attempt to write, but don't fail the test if the OS doesn't allow it
	if err := os.WriteFile(nonPrintablePath, []byte("print('test')"), 0644); err == nil {
		detector := NewLanguageDetector()
		lang := detector.DetectLanguage(nonPrintablePath)
		if lang != LanguageTypePython {
			t.Errorf("Expected Python for non-printable filename, got %s", lang)
		}
	} else {
		t.Logf("OS doesn't support non-printable characters in filenames (expected): %v", err)
	}
}

func TestDetectLanguageWithEdgeCases(t *testing.T) {
	detector := NewLanguageDetector()

	// Test edge cases with empty or invalid paths
	edgeCases := []struct {
		name     string
		path     string
		expected LanguageType
	}{
		{"Empty string", "", LanguageTypeUnknown},
		{"Single dot", ".", LanguageTypeUnknown},
		{"Double dot", "..", LanguageTypeUnknown},
		{"Just path separators", string(filepath.Separator), LanguageTypeUnknown},
		{"Multiple path separators", string(filepath.Separator) + string(filepath.Separator), LanguageTypeUnknown},
		{"Extension only", ".go", LanguageTypeGolang}, // Should detect language from extension
		{"Hidden file without extension", ".gitignore", LanguageTypeUnknown},
		{"Hidden directory", ".git/HEAD", LanguageTypeUnknown},

		// Test with control characters (these might not be valid filenames on some systems)
		{"Path with tab", "file\t.js", LanguageTypeJavaScript},
		{"Path with newline", "file\n.py", LanguageTypePython},

		// Test with URLs (should handle just the filename part)
		{"URL-like path", "http://example.com/file.js", LanguageTypeJavaScript},
		{"Query parameters", "file.php?id=123", LanguageTypePHP},
		{"Fragment identifier", "file.html#section", LanguageTypeHTML},

		// Test with archive paths
		{"ZIP path", "archive.zip/file.cs", LanguageTypeCSharp},
		{"Tar path", "archive.tar.gz/folder/file.rs", LanguageTypeRust},

		// Test with case insensitivity
		{"Uppercase extension", "FILE.JS", LanguageTypeJavaScript},
		{"Mixed case extension", "File.Py", LanguageTypePython},

		// Test with multiple dots
		{"Multiple dots", "file.min.js", LanguageTypeJavaScript},
		{"Multiple dots with directory", "path/to/styles.min.css", LanguageTypeCSS},

		// Test with unusual but valid extensions
		{"Valid unusual extension", "file.jsx", LanguageTypeJavaScript},
		{"Another unusual extension", "file.tsx", LanguageTypeTypeScript},
	}

	for _, tc := range edgeCases {
		t.Run(tc.name, func(t *testing.T) {
			result := detector.DetectLanguage(tc.path)
			if result != tc.expected {
				t.Errorf("DetectLanguage(%q) = %s, want %s", tc.path, result, tc.expected)
			}
		})
	}
}

func TestGetLinterForFileBasic(t *testing.T) {
	// 获取 linter manager 实例
	manager := GetLinterManager()

	// 创建一个临时的 Go 文件进行测试
	tempDir, err := os.MkdirTemp("", "linter-test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建一个测试 Go 文件
	testFile := filepath.Join(tempDir, "main.go")
	if err := os.WriteFile(testFile, []byte("package main\n\nfunc main() {\n}\n"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 测试 GetLinterForFile 方法
	linters, err := manager.GetLinterForFile(testFile)

	// 在测试环境中，我们期望以下情况之一：
	// 1. 配置文件加载失败（预期的）
	// 2. 默认 linter 没有注册（预期的，因为测试环境没有安装实际的 linter）
	if err != nil {
		// 检查是否是预期的错误类型
		if strings.Contains(err.Error(), ".environments.yaml configuration file was not found") {
			t.Logf("Expected error due to missing configuration: %v", err)
		} else if strings.Contains(err.Error(), "default linters") && strings.Contains(err.Error(), "are defined but not currently registered") {
			t.Logf("Expected error due to unregistered default linters: %v", err)
		} else {
			t.Errorf("Unexpected error: %v", err)
		}
	} else {
		t.Logf("Successfully got %d linters for Go file", len(linters))
	}
}

func TestEnvironmentVariablesSetup(t *testing.T) {
	// 验证环境变量是否正确设置
	appPath := os.Getenv("paas_app_path")
	configFileName := os.Getenv("paas_config_file_name")

	if appPath == "" {
		t.Error("paas_app_path environment variable is not set")
	} else {
		t.Logf("paas_app_path is set to: %s", appPath)
	}

	if configFileName == "" {
		t.Error("paas_config_file_name environment variable is not set")
	} else {
		t.Logf("paas_config_file_name is set to: %s", configFileName)
	}

	// 验证配置文件路径是否正确构建
	expectedConfigPath := appPath + "/" + configFileName
	t.Logf("Expected config path: %s", expectedConfigPath)

	// 检查配置文件是否存在
	if _, err := os.Stat(expectedConfigPath); os.IsNotExist(err) {
		t.Logf("Config file does not exist at: %s (this is expected in some test environments)", expectedConfigPath)
	} else {
		t.Logf("Config file exists at: %s", expectedConfigPath)
	}
}

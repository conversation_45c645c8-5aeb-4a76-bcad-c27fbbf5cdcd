package linters

import (
	"fmt"
	"os/exec"
	"strings"
)

// whichExec uses bash to locate an executable via 'which' command after sourcing ~/.bashrc
var WhichExecFunc = whichExec

func whichExec(cmd string) (string, error) {
	// Safety check to prevent command injection
	if strings.ContainsAny(cmd, "; & | > < ` $ ( ) { } [ ] ! # \\ \" ' \n \t") {
		return "", fmt.Errorf("invalid characters in command: %s", cmd)
	}

	// Execute bash to source .bashrc and find executable
	bashCmd := fmt.Sprintf("source ~/.bashrc && which %s", cmd)
	out, err := OutputWithBashrcFunc("bash", "-c", bashCmd)
	if err != nil {
		if exitErr, ok := err.(*exec.ExitError); ok && exitErr.ExitCode() == 1 {
			return "", fmt.Errorf("executable file %s not found", cmd)
		}
		return "", err
	}

	// Clean up the output (trim whitespace)
	path := strings.TrimSpace(string(out))
	if path == "" {
		return "", fmt.Errorf("executable file %s not found", cmd)
	}

	return path, nil
}

// execWithBashrc executes a command with bash after sourcing ~/.bashrc
// This helps ensure the command runs in an environment with user's PATH and other settings
func execWithBashrc(command string, args ...string) *exec.Cmd {
	// Escape each argument to prevent command injection
	escapedArgs := make([]string, 0, len(args))
	for _, arg := range args {
		escapedArg := strings.Replace(arg, "'", "'\\''", -1)
		escapedArgs = append(escapedArgs, fmt.Sprintf("'%s'", escapedArg))
	}

	// Build the final command with sourceing ~/.bashrc
	cmdString := command
	if len(escapedArgs) > 0 {
		cmdString += " " + strings.Join(escapedArgs, " ")
	}
	bashCmd := fmt.Sprintf("source ~/.bashrc && %s", cmdString)
	return exec.Command("bash", "-c", bashCmd)
}

// OutputWithBashrc runs a command with bash after sourcing ~/.bashrc and returns its stdout
var OutputWithBashrcFunc = OutputWithBashrc

func OutputWithBashrc(command string, args ...string) ([]byte, error) {
	return execWithBashrc(command, args...).Output()
}

// CombinedOutputWithBashrc runs a command with bash after sourcing ~/.bashrc and returns combined stdout/stderr
var CombinedOutputWithBashrcFunc = CombinedOutputWithBashrc

func CombinedOutputWithBashrc(command string, args ...string) ([]byte, error) {
	return execWithBashrc(command, args...).CombinedOutput()
}

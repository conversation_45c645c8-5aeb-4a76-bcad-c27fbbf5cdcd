package linters

import (
	"agent/consts"
	"agent/utils/log"
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// InstallStatus represents the status of a linter installation
type InstallStatus string

const (
	InstallStatusPending    InstallStatus = "pending"
	InstallStatusInstalling InstallStatus = "installing"
	InstallStatusSuccess    InstallStatus = "success"
	InstallStatusFailed     InstallStatus = "failed"
)

// InstallerIface represents the interface for the linter installer
type InstallerIface interface {
	DetectAndInstall(projectDir string) error
	InstallLinter(linterType LinterType) error
	InstallForLanguage(language LanguageType) error
	InstallForFile(filePath string) error
	CheckProjectLanguagesAndInstall(projectDir string) error
}

// InstallProgress tracks the progress of a linter installation
type InstallProgress struct {
	LinterType   LinterType    // Type of the linter being installed
	Status       InstallStatus // Current status of the installation
	Message      string        // Status message
	StartTime    time.Time     // When installation started
	CompletedAt  time.Time     // When installation completed
	Error        error         // Error if installation failed
	RetryCount   int           // Number of retries attempted
	Dependencies []string      // Dependencies required for this linter
}

// Installer manages the installation of linter tools
type Installer struct {
	manager         LinterManagerIface              // Reference to the linter manager
	installProgress map[LinterType]*InstallProgress // Track installation progress
	mu              sync.RWMutex                    // Mutex for thread safety
	//maxRetries          int                             // Maximum number of retries for installation
	languageDetector    *LanguageDetector  // Language detector
	periodicCheckCancel context.CancelFunc // For canceling periodic checks
}

// NewInstaller creates a new linter installer
func NewInstaller(manager LinterManagerIface) InstallerIface {
	installer := &Installer{
		manager:          manager,
		installProgress:  make(map[LinterType]*InstallProgress),
		languageDetector: GetLanguageDetector(), // Initialize language detector
	}

	// Start periodic checking for new languages
	installer.startPeriodicLanguageCheck()

	return installer
}

// DetectAndInstall detects the project structure and installs appropriate linters
func (i *Installer) DetectAndInstall(projectDir string) error {
	// 安装lint依赖插件
	if err := i.detectLinterConfigsAndInstall(); err != nil {
		log.Warnf("LangLinters, Error during specific linter configuration detection: %v", err)
	}

	if err := i.CheckProjectLanguagesAndInstall(projectDir); err != nil {
		return fmt.Errorf("error during project language detection and default linter installation: %w", err)
	}

	return nil
}

// detectLinterConfigsAndInstall scans the project directory for known linter configuration files
func (i *Installer) detectLinterConfigsAndInstall() error {
	patterns := map[LinterType][]string{
		LinterTypeESLint: {
			"package.json", // Common indicator of JS/TS project, often uses ESLint
			".eslintrc", ".eslintrc.js", ".eslintrc.json", ".eslintrc.yml", ".eslintrc.yaml",
			"eslint.config.js", // Modern ESLint config file
		},
		LinterTypeRuff: {
			"pyproject.toml",   // Common indicator of Python project, may use Ruff
			"requirements.txt", // Indicates Python project
			"ruff.toml", ".ruff.toml",
		},
		LinterTypeMyPy: {
			"mypy.ini",
			"pyproject.toml", // Indicates Python
			"setup.cfg",      // Indicates Python
		},
		LinterTypeGolangCILint: {
			"go.mod", // Indicates Go project, may use GolangCI-Lint
			".golangci.yml", ".golangci.yaml",
		},
	}

	var foundLintersToInstall []LinterType
	for linterType, filePatterns := range patterns {
		for _, pattern := range filePatterns {
			matches, _ := filepath.Glob(filepath.Join("*", pattern))
			rootMatches, _ := filepath.Glob(pattern)

			matches = append(matches, rootMatches...)

			if len(matches) > 0 {
				isPresent := false
				for _, listedType := range foundLintersToInstall {
					if listedType == linterType {
						isPresent = true
						break
					}
				}
				if !isPresent {
					foundLintersToInstall = append(foundLintersToInstall, linterType)
				}
				break
			}
		}
	}

	var installErrors []error
	if len(foundLintersToInstall) > 0 {
		for _, linterType := range foundLintersToInstall {
			err := i.InstallLinter(linterType)
			if err != nil {
				if !strings.Contains(err.Error(), "is already in progress") {
					log.Warnf("LangLinters, Failed to initiate installation for configured linter %s: %v", linterType, err)
					installErrors = append(installErrors, fmt.Errorf("install %s failed: %w", linterType, err))
				}
			}
		}
	}

	// Return any errors encountered during the initiation of installations.
	if len(installErrors) > 0 {
		errorMsgs := make([]string, len(installErrors))
		for _, err := range installErrors {
			errorMsgs = append(errorMsgs, err.Error())
		}
		return fmt.Errorf("failed to initiate installation for one or more configured linters: %s", strings.Join(errorMsgs, "; "))
	}

	return nil
}

// InstallLinter installs a specific linter by its type. It handles concurrency, retries, and progress tracking.
func (i *Installer) InstallLinter(linterType LinterType) error {
	i.mu.Lock()
	progress, exists := i.installProgress[linterType]
	if exists {
		if progress.Status == InstallStatusInstalling {
			i.mu.Unlock()
			log.Debugf("LangLinters, Installation of %s is already in progress.", linterType)
			return fmt.Errorf("installation of %s is already in progress", linterType)
		}
		if progress.Status == InstallStatusFailed {
			progress.Status = InstallStatusPending // Reset status to queue
			progress.RetryCount++
			log.Infof("LangLinters, Retrying installation of %s (retry: %d)...", linterType, progress.RetryCount)
		} else if progress.Status == InstallStatusSuccess {
			i.mu.Unlock()
			log.Debugf("LangLinters, Linter %s is already successfully installed.", linterType)
			return nil
		}
	} else {
		//log.Infof("LangLinters, Initiating installation process for linter: %s", linterType)
		progress = &InstallProgress{
			LinterType: linterType,
			Status:     InstallStatusPending,
			StartTime:  time.Now(),
			RetryCount: 0,
		}
		i.installProgress[linterType] = progress
	}

	progress.Status = InstallStatusInstalling
	i.mu.Unlock()

	var linter LinterIface
	var err error
	switch linterType {
	case LinterTypeESLint:
		linter = NewESLintLinter(nil)
		if _, err = whichExec("npm"); err != nil {
			progress.Dependencies = append(progress.Dependencies, "npm")
		}
	case LinterTypeRuff:
		linter = NewRuffLinter(nil)
		if _, err = whichExec("pip"); err != nil {
			if _, err = whichExec("pip3"); err != nil {
				progress.Dependencies = append(progress.Dependencies, "pip or pip3")
			}
		}
	case LinterTypeMyPy:
		linter = NewMyPyLinter(nil)
		if _, err = whichExec("pip"); err != nil {
			if _, err = whichExec("pip3"); err != nil {
				progress.Dependencies = append(progress.Dependencies, "pip or pip3")
			}
		}
	case LinterTypeGolangCILint:
		linter = NewGolangCILintLinter(nil)
		if _, err = whichExec("go"); err != nil {
			progress.Dependencies = append(progress.Dependencies, "go")
		}
	default:
		i.mu.Lock()
		progress.Status = InstallStatusFailed
		progress.Error = fmt.Errorf("unsupported linter type for installation: %s", linterType)
		progress.CompletedAt = time.Now()
		i.mu.Unlock()
		log.Warnf("LangLinters, Attempted to install unsupported linter type: %s", linterType)
		return progress.Error
	}

	if err != nil {
		log.Warnf("LangLinters, Cannot install %s due to missing dependencies err: %+v", linterType, err)
		return err
	}

	if len(progress.Dependencies) > 0 {
		missingDeps := strings.Join(progress.Dependencies, ", ")
		err = fmt.Errorf("missing global dependencies for %s: %s", linterType, missingDeps)

		i.mu.Lock() // Re-acquire lock to update progress
		progress.Status = InstallStatusFailed
		progress.Error = err
		progress.Message = fmt.Sprintf("Installation failed: missing dependencies: %s", missingDeps)
		progress.CompletedAt = time.Now()
		i.mu.Unlock()

		log.Warnf("LangLinters, Cannot install %s due to missing dependencies err: %+v", linterType, err)
		return err
	}

	if err = i.manager.RegisterLinter(linter); err != nil {
		i.mu.Lock()
		progress.Status = InstallStatusFailed
		progress.Error = err
		progress.Message = fmt.Sprintf("Failed to register linter %s with manager.", linterType)
		progress.CompletedAt = time.Now()
		i.mu.Unlock()

		log.Warnf("LangLinters, Failed to register linter %s with manager: %v", linterType, err)
		return err
	}

	if linter.IsInstalled() {
		progress.Status = InstallStatusSuccess
		progress.Message = "Installation completed successfully"
		progress.Error = nil
		progress.RetryCount = 0
		progress.Dependencies = nil
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	done := make(chan error, 1)

	go func() {
		installErr := linter.Install()
		done <- installErr
	}()

	var installErr error
	select {
	case installErr = <-done:
		log.Infof("LangLinters, Installation command for %s installErr: %v", linterType, installErr)
	case <-ctx.Done():
		// Context done means timeout occurred or was cancelled externally
		installErr = fmt.Errorf("installation of %s timed out after 5 minutes or was externally cancelled: %w", linterType, ctx.Err())
		log.Warnf("LangLinters, Installation of %s timed out or cancelled. err: %s", linterType, installErr)
	}

	i.mu.Lock()
	defer i.mu.Unlock()

	if installErr != nil {
		progress.Status = InstallStatusFailed
		progress.Error = installErr
		progress.Message = fmt.Sprintf("Installation failed.")
	} else {
		progress.Status = InstallStatusSuccess
		progress.Message = "Installation completed successfully"
		progress.Error = nil
		progress.RetryCount = 0
		progress.Dependencies = nil
	}

	progress.CompletedAt = time.Now()
	return installErr
}

// InstallForLanguage installs the default linters associated with a specific language.
func (i *Installer) InstallForLanguage(language LanguageType) error {
	// Get the list of default linters configured for this language type.
	linterTypes, ok := LanguageToDefaultLinter[language]
	if !ok || len(linterTypes) == 0 {
		return fmt.Errorf("no default linter defined for language: %s", language)
	}

	var installErrors []error
	for _, ltype := range linterTypes {
		err := i.InstallLinter(ltype)
		if err != nil {
			if !strings.Contains(err.Error(), "is already in progress") {
				log.Warnf("LangLinters, Failed to initiate installation for default linter %s (language %s): %v", ltype, language, err)
				installErrors = append(installErrors, fmt.Errorf("install %s for %s failed: %w", ltype, language, err))
			}
		}
	}

	// If any errors occurred during the initiation of installations, aggregate them.
	if len(installErrors) > 0 {
		errorMsgs := make([]string, 0)
		for _, err := range installErrors {
			errorMsgs = append(errorMsgs, err.Error())
		}
		return fmt.Errorf("failed to initiate installation for one or more default linters for language %s: %s", language, strings.Join(errorMsgs, "; "))
	}

	return nil
}

// InstallForFile determines the language for a file and installs the corresponding default linter.
func (i *Installer) InstallForFile(filePath string) error {
	language := i.languageDetector.DetectLanguage(filePath)

	if language == LanguageTypeUnknown {
		//log.Infof("LangLinters, Could not determine language for file %s using detector, skipping linter install.", filePath)
		return fmt.Errorf("could not determine language for file: %s", filePath)
	}

	return i.InstallForLanguage(language)
}

// CheckProjectLanguagesAndInstall scans the given project directory, detects programming
func (i *Installer) CheckProjectLanguagesAndInstall(projectDir string) error {
	languages, err := i.languageDetector.GetDetectedLanguagesInProject(projectDir)
	if err != nil {
		return fmt.Errorf("failed to detect languages in project %s: %w", projectDir, err)
	}

	if len(languages) == 0 {
		//log.Infof("LangLinters, No supported languages detected in %s for default linter installation.", projectDir)
		return nil
	}

	var installErrors []error
	for language := range languages {
		if language == LanguageTypeUnknown {
			continue
		}

		if err := i.InstallForLanguage(language); err != nil {
			log.Warnf("LangLinters, Failed to initiate default linter installation for language %s in %s: %v", language, projectDir, err)
			installErrors = append(installErrors, fmt.Errorf("install for %s failed: %w", language, err))
		}
	}

	if len(installErrors) > 0 {
		errorMsgs := make([]string, 0)
		for _, err := range installErrors {
			errorMsgs = append(errorMsgs, err.Error())
		}
		return fmt.Errorf("completed language-based linter checks with errors: %s", strings.Join(errorMsgs, "; "))
	}

	return nil
}

// startPeriodicLanguageCheck starts a background goroutine that periodically
func (i *Installer) startPeriodicLanguageCheck() {
	ctx, cancel := context.WithCancel(context.Background())
	i.periodicCheckCancel = cancel

	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := i.CheckProjectLanguagesAndInstall(consts.AppRootDir); err != nil {
					log.Warnf("LangLinters, Periodic language check completed with errors: %v", err)
				}
			}
		}
	}()
}

package linters

import (
	"agent/config"
	"io"
	"os"
)

// Position represents a position in a document
type Position struct {
	Line      int `json:"line"`      // Line position (1-based)
	Character int `json:"character"` // Character position (0-based)
}

// Range represents a range in a document
type Range struct {
	Start Position `json:"start"` // Start position
	End   Position `json:"end"`   // End position
}

// Location represents the location of a diagnostic in a file
type Location struct {
	FilePath string `json:"file_path"` // Path to the file
	Range    Range  `json:"range"`     // Range in the file
}

// LintIssue represents a single issue detected by a linter
type LintIssue struct {
	Location       Location      `json:"location"`       // Location of the issue
	Message        string        `json:"message"`        // Issue message
	Severity       SeverityLevel `json:"severity"`       // Issue severity
	Source         string        `json:"source"`         // Source of the issue (linter name)
	Code           string        `json:"code"`           // Error code (if available)
	Classification string        `json:"classification"` // Error classification (e.g. [syntax] in MyPy)
}

// LintResult represents the result of a lint operation
type LintResult struct {
	FilePath string      `json:"file_path"` // Path to the file that was linted
	Issues   []LintIssue `json:"issues"`    // List of issues found
	Success  bool        `json:"success"`   // Whether the lint operation was successful
	Error    string      `json:"error"`     // Error message if operation failed
}

// FixResult represents the result of a fix operation
type FixResult struct {
	FilePath   string `json:"file_path"`   // Path to the file that was fixed
	Fixed      bool   `json:"fixed"`       // Whether any fixes were applied
	FixCount   int    `json:"fix_count"`   // Number of fixes applied
	Error      string `json:"error"`       // Error message if operation failed
	NewContent string `json:"new_content"` // New content if requested
}

// LinterIface is the interface that all linters must implement
type LinterIface interface {
	// Install installs the linter
	Install() error

	// IsInstalled checks if the linter is installed
	IsInstalled() bool

	// Lint performs linting on a file
	Lint(filePath string) (*LintResult, error)

	// Fix attempts to fix issues in a file
	Fix(filePath string) (*FixResult, error)

	// GetType returns the linter type
	GetType() LinterType

	// GetLanguage returns the supported language
	GetLanguage() LanguageType
}

// BaseLinter provides common functionality for all linters
type BaseLinter struct {
	Type     LinterType           // Type of the linter
	Language LanguageType         // Language the linter supports
	Config   *config.LinterConfig // Linter configuration
}

// GetType returns the linter type
func (l *BaseLinter) GetType() LinterType {
	return l.Type
}

// GetLanguage returns the supported language
func (l *BaseLinter) GetLanguage() LanguageType {
	return l.Language
}

// ReadFile reads the content of a file
func (l *BaseLinter) ReadFile(filePath string) ([]byte, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return io.ReadAll(file)
}

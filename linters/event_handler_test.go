package linters

import (
	"agent/consts"
	"agent/file/watch"
	"path/filepath"
	"testing"
)

// MockInstaller implements InstallerIface for testing
type MockInstaller struct {
	InstalledLinters    map[LinterType]bool
	InstallError        error
	InstallForFileError error
}

func (m *MockInstaller) DetectAndInstall(projectDir string) error {
	return nil
}

func (m *MockInstaller) InstallLinter(linterType LinterType) error {
	return nil
}

func (m *MockInstaller) InstallForLanguage(language LanguageType) error {
	return nil
}

func (m *MockInstaller) InstallForFile(filePath string) error {
	return m.InstallForFileError
}

func (m *MockInstaller) CheckProjectLanguagesAndInstall(projectDir string) error {
	return nil
}

func NewMockInstaller() *MockInstaller {
	return &MockInstaller{
		InstalledLinters: make(map[LinterType]bool),
	}
}

func (m *MockInstaller) Install(linterType LinterType) error {
	m.InstalledLinters[linterType] = true
	return m.InstallError
}

func TestNewEventManager(t *testing.T) {
	linterManager := NewMockLinterManager()
	projectDir := "/test/project"

	eventManager := NewEventManager(linterManager, projectDir)

	if eventManager == nil {
		t.Fatal("Expected non-nil EventManager")
	}

	if eventManager.lintersManager != linterManager {
		t.Error("LinterManager not properly set")
	}

	if eventManager.projectDir != projectDir {
		t.Errorf("ProjectDir not properly set, got %s, expected %s", eventManager.projectDir, projectDir)
	}

	if eventManager.handlers == nil {
		t.Error("Handlers map should be initialized")
	}

	if len(eventManager.handlers) != 0 {
		t.Error("Handlers map should be empty initially")
	}
}

func TestRegisterHandler(t *testing.T) {
	eventManager := NewEventManager(NewMockLinterManager(), "/test/project")

	// Create mock handlers
	fileOpenHandler := NewFileOpenEventHandler(NewMockLinterManager(), NewMockInstaller(), "/test/project")
	fileChangeHandler := NewFileChangeEventHandler(NewMockLinterManager(), NewMockInstaller(), "/test/project")

	// Register handlers
	eventManager.RegisterHandler(fileOpenHandler)
	eventManager.RegisterHandler(fileChangeHandler)

	// Verify handlers were registered correctly
	if len(eventManager.handlers) != 2 {
		t.Errorf("Expected 2 handler types, got %d", len(eventManager.handlers))
	}

	if len(eventManager.handlers[EventTypeFileOpen]) != 1 {
		t.Errorf("Expected 1 file open handler, got %d", len(eventManager.handlers[EventTypeFileOpen]))
	}

	if len(eventManager.handlers[EventTypeFileChange]) != 1 {
		t.Errorf("Expected 1 file change handler, got %d", len(eventManager.handlers[EventTypeFileChange]))
	}

	// Register another handler of the same type
	eventManager.RegisterHandler(NewFileOpenEventHandler(NewMockLinterManager(), NewMockInstaller(), "/test/project"))

	if len(eventManager.handlers[EventTypeFileOpen]) != 2 {
		t.Errorf("Expected 2 file open handlers after second registration, got %d", len(eventManager.handlers[EventTypeFileOpen]))
	}
}

func TestDispatchEvent(t *testing.T) {
	linterManager := NewMockLinterManager()
	eventManager := NewEventManager(linterManager, "/test/project")

	// Create a mock handler that will return true
	mockHandler := &mockEventHandler{
		eventType:      EventTypeFileOpen,
		handleResponse: true,
	}

	// Create a mock handler that will return false
	mockHandler2 := &mockEventHandler{
		eventType:      EventTypeFileOpen,
		handleResponse: false,
	}

	// Register the handlers
	eventManager.RegisterHandler(mockHandler)
	eventManager.RegisterHandler(mockHandler2)

	// Test dispatch with registered handlers
	result := eventManager.DispatchEvent(EventTypeFileOpen, "test.js")

	if !result {
		t.Error("Expected DispatchEvent to return true when at least one handler returns true")
	}

	if !mockHandler.wasCalled || !mockHandler2.wasCalled {
		t.Error("Both handlers should have been called")
	}

	// Test dispatch with no handlers
	result = eventManager.DispatchEvent(EventTypeContainerStart, "container-id")

	if result {
		t.Error("Expected DispatchEvent to return false when no handlers registered for event type")
	}
}

// mockEventHandler implements EventHandler for testing
type mockEventHandler struct {
	eventType      EventType
	handleResponse bool
	wasCalled      bool
}

func (h *mockEventHandler) HandleEvent(event interface{}) bool {
	h.wasCalled = true
	return h.handleResponse
}

func (h *mockEventHandler) GetEventType() EventType {
	return h.eventType
}

func TestInitialize(t *testing.T) {
	linterManager := NewMockLinterManager()
	eventManager := NewEventManager(linterManager, "/test/project")

	eventManager.Initialize()

	// Verify that handlers were registered for each event type
	if len(eventManager.handlers[EventTypeFileOpen]) != 1 {
		t.Error("Expected file open handler to be registered")
	}

	if len(eventManager.handlers[EventTypeFileChange]) != 1 {
		t.Error("Expected file change handler to be registered")
	}

	if len(eventManager.handlers[EventTypeContainerStart]) != 1 {
		t.Error("Expected container start handler to be registered")
	}
}

func TestHandleFileChanges(t *testing.T) {
	linterManager := NewMockLinterManager()
	eventManager := NewEventManager(linterManager, "/test/project")

	// Create a mock handler
	mockHandler := &mockEventHandler{
		eventType:      EventTypeFileChange,
		handleResponse: true,
	}

	// Register the handler
	eventManager.RegisterHandler(mockHandler)

	// Create test file changes
	changes := []watch.FileChange{
		{
			Path:   "test.js",
			Change: consts.FileChangeUpdate,
		},
		{
			Path:   "test.py",
			Change: consts.FileChangeCreate,
		},
	}

	// Handle changes
	eventManager.HandleFileChanges(changes)

	if !mockHandler.wasCalled {
		t.Error("Expected file change handler to be called")
	}
}

func TestFileOpenEventHandler_HandleEvent(t *testing.T) {
	tests := []struct {
		name           string
		event          interface{}
		installError   error
		expectedResult bool
	}{
		{
			name:           "Valid file with extension",
			event:          "test.js",
			installError:   nil,
			expectedResult: true,
		},
		{
			name:           "File with no extension",
			event:          "testfile",
			installError:   nil,
			expectedResult: false,
		},
		{
			name:           "Install error",
			event:          "test.js",
			installError:   filepath.ErrBadPattern,
			expectedResult: false,
		},
		{
			name:           "Invalid event type",
			event:          123,
			installError:   nil,
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterManager := NewMockLinterManager()
			linterManager.DetectAndInstallError = tt.installError

			handler := NewFileOpenEventHandler(linterManager, NewMockInstaller(), "/test/project")

			result := handler.HandleEvent(tt.event)

			if result != tt.expectedResult {
				t.Errorf("HandleEvent() = %v, want %v", result, tt.expectedResult)
			}
		})
	}
}

func TestFileChangeEventHandler_HandleEvent(t *testing.T) {
	linterManager := NewMockLinterManager()
	linterManager.InstalledLinters["test.js"] = true

	handler := NewFileChangeEventHandler(linterManager, NewMockInstaller(), "/test/project")

	// Test with valid file changes
	changes := []watch.FileChange{
		{
			Path:   "test.js",
			Change: consts.FileChangeUpdate,
		},
		{
			Path:   "test.py",
			Change: consts.FileChangeCreate,
		},
	}

	result := handler.HandleEvent(changes)

	if !result {
		t.Error("Expected HandleEvent to return true for valid changes")
	}

	// Test with invalid event type
	result = handler.HandleEvent("not-a-file-change")

	if result {
		t.Error("Expected HandleEvent to return false for invalid event type")
	}

	// Test with empty changes slice
	result = handler.HandleEvent([]watch.FileChange{})

	if result {
		t.Error("Expected HandleEvent to return false for empty changes")
	}
}

func TestContainerStartEventHandler(t *testing.T) {
	linterManager := NewMockLinterManager()
	installer := NewMockInstaller()

	handler := NewContainerStartEventHandler(linterManager, installer, "/test/project")

	if handler.GetEventType() != EventTypeContainerStart {
		t.Error("Unexpected event type")
	}

	// Test with valid container ID
	result := handler.HandleEvent("container-123")

	if !result {
		t.Error("Expected HandleEvent to return true for container start")
	}

	// Test with invalid event type
	result = handler.HandleEvent(123)

	if result {
		t.Error("Expected HandleEvent to return false for invalid event type")
	}
}

func TestGetEventType(t *testing.T) {
	tests := []struct {
		name         string
		handler      EventHandler
		expectedType EventType
	}{
		{
			name:         "FileOpenEventHandler",
			handler:      NewFileOpenEventHandler(nil, nil, ""),
			expectedType: EventTypeFileOpen,
		},
		{
			name:         "FileChangeEventHandler",
			handler:      NewFileChangeEventHandler(nil, nil, ""),
			expectedType: EventTypeFileChange,
		},
		{
			name:         "ContainerStartEventHandler",
			handler:      NewContainerStartEventHandler(nil, nil, ""),
			expectedType: EventTypeContainerStart,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			eventType := tt.handler.GetEventType()
			if eventType != tt.expectedType {
				t.Errorf("GetEventType() = %v, want %v", eventType, tt.expectedType)
			}
		})
	}
}

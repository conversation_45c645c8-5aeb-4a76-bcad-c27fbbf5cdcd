//go:build ignore

// This file is an example and should not be included in tests or coverage

package linters_test

import (
	"os"
	"strings"
	"testing"
)

// Global variable that's unused - lint issue
var unusedGlobalVariable = "this is never used"

// Function with missing error check - lint issue
func fileOperationWithMissingErrorCheck() {
	file, _ := os.Open("non_existent_file.txt") // Error check missing
	defer file.Close()

	// Should check if file is nil but doesn't
	fmt.Println("Opened file successfully")
}

// Function with inefficient string concatenation - lint issue
func inefficientStringConcatenation() string {
	result := ""
	for i := 0; i < 1000; i++ {
		// Inefficient string concatenation in a loop
		result = result + fmt.Sprintf("%d", i)
	}
	return result
}

// Function with unnecessary variable declaration - lint issue
func unnecessaryVariableDeclarations() int {
	x := 10
	y := 20
	z := x + y // unnecessary intermediate variable
	return z   // could just return x + y directly
}

// Function that shadows a package name - lint issue
func fmt() {
	fmt.Println("This shadows the fmt package name")
}

// Test function that won't be executed but contains lint issues
func TestLintIssues(t *testing.T) {
	// Unused variable in function
	unusedVar := "This variable is never used"

	// Inefficient condition
	x := 5
	if x != 0 {
		x = x
	}

	// Redundant string initialization
	var str string = ""

	// Unnecessary type conversion
	var num int = 10
	float := float64(num)
	num = int(float)

	// Using string functions incorrectly
	s := "test string"
	if strings.Contains(s, "test") {
		// Duplicated condition check
		if strings.Contains(s, "test") {
			t.Log("Found test")
		}
	}
}

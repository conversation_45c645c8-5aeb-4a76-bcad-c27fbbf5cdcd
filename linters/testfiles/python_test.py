//go:build ignore

# This file contains intentional lint issues for <PERSON>uff and MyPy

import os
import sys
import json
import datetime
import random  # Unused import - Ruff issue
from typing import List, Dict, Any

# Missing type annotations - MyPy issue
def add_numbers(a, b):
    return a + b

# Inconsistent indentation - Ruff issue
def calculate_average(numbers):
    total = 0
    for num in numbers:
        total += num
    
    # Inconsistent indentation (mixed spaces)
        average = total / len(numbers)
        return average  # Indentation error - should not be inside the for loop

# Unused variable - Ruff issue
def process_data():
    result = "Processed"
    unused_var = "This will never be used"
    return result

# Multiple issues: missing type annotations, unused variables
class DataProcessor:
    def __init__(self):
        self.data = None
        self.temp_value = "Temporary"  # Unused class variable
    
    # Missing return type annotation - MyPy issue
    def process(self, input_data):
        # Unused variable - Ruff issue
        intermediate = self._transform(input_data)
        return input_data.upper()
    
    def _transform(self, data: str) -> str:
        # Inconsistent indentation
       transformed = data.strip()
         # Extra indentation here
         return transformed

# Inconsistent string quotes - Ruff issue
def string_formats():
    single_quote = 'Single quoted string'
    double_quote = "Double quoted string"
    triple_quote = """Triple quoted string"""
    return single_quote + double_quote + triple_quote

# Redefinition of unused variable - Ruff issue
def redundant_code():
    x = 10
    x = 20  # Redefining x without using the first value
    
    # Comparison that could be simplified
    if x != 0:
        if x > 0:
            return True
    
    return False

# F-strings with unnecessary literals - Ruff issue  
def format_strings(name):
    return f"Hello, {name}" + f"!"  # f-string could be combined

if __name__ == "__main__":
    # No type annotation for variable - MyPy issue
    data = [1, 2, 3, 4, 5]
    
    # E722 do not use bare except - Ruff issue
    try:
        result = calculate_average(data)
        print(f"Average: {result}")
    except:
        print("An error occurred")
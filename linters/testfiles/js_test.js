//go:build ignore

// This file contains intentional ESLint issues for testing

// Unused variable - lint issue
var unusedVariable = "This is never used";

// Missing semicolons - lint issue
function missingTerminators() {
  var x = 10
  var y = 20
  return x + y
}

// Inconsistent quotes - lint issue
const singleQuote = 'single quoted string';
const doubleQuote = "double quoted string";
const templateLiteral = `template literal`;

// Console logging statements - lint issue
function debugFunction() {
  console.log("This is a debug statement")
  console.error("This is an error")
  console.warn('Warning message')
}

// Mixed spaces and tabs - lint issue
function mixedIndentation() {
	var tab = "indented with tab";
    var spaces = "indented with spaces";
        var moreSpaces = "different indentation level";
}

// == instead of === - lint issue
function looseComparisons(x) {
  if (x == null) {
    return true
  }
  return x != 0
}

// Unused function parameters - lint issue
function unusedParams(a, b, c) {
  return a + 5; // b and c are never used
}

// Variable shadowing - lint issue
var count = 10;
function shadowingExample() {
  var count = 20;
  return count;
}

// Callback with no error handling - lint issue
function callbackWithoutErrorHandling() {
  require('fs').readFile('file.txt', function(err, data) {
    // No error handling here
    return data.toString()
  })
}
package linters

import (
	"agent/config"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"

	"github.com/pkg/errors"
)

// RuffLinter implements the Linter interface for Ruff
type RuffLinter struct {
	BaseLinter
	ruffPath  string
	pipCmd    string
	mu        sync.Mutex
	cacheFile map[string]bool
}

// NewRuffLinter creates a new Ruff linter
func NewRuffLinter(configPath *config.LinterConfig) *RuffLinter {
	return &RuffLinter{
		BaseLinter: BaseLinter{
			Type:     LinterTypeRuff,
			Language: LanguageTypePython,
			Config:   configPath,
		},
		ruffPath:  "ruff",
		pipCmd:    "pip",
		cacheFile: make(map[string]bool),
	}
}

// Install installs Ruff
func (l *RuffLinter) Install() error {
	// First check if pip3 is available, then pip
	if _, err := WhichExecFunc("pip3"); err == nil {
		l.pipCmd = "pip3"
	} else if _, err = WhichExecFunc("pip"); err == nil {
		l.pipCmd = "pip"
	} else {
		return fmt.Errorf("pip is not installed - required for Ruff installation, err: %s", err.Error())
	}

	// Try to install ruff using pip
	output, err := CombinedOutputWithBashrcFunc(l.pipCmd, "install", "ruff")
	if err != nil {
		log.Warnf("LangLinters, Failed to install Ruff: %v, Output: %s", err, string(output))
		return err
	}

	return nil
}

// IsInstalled checks if Ruff is installed
func (l *RuffLinter) IsInstalled() bool {
	_, err := WhichExecFunc(l.ruffPath)
	if err == nil {
		return true
	}

	return false
}

// findRuffConfig looks for a Ruff config file in or above the directory of the given file
func (l *RuffLinter) findRuffConfig(filePath string) string {
	if l.Config != nil && l.Config.ConfigPath != "" {
		return l.Config.ConfigPath
	}

	dir := filepath.Dir(filePath)
	for {
		// Check for common Ruff config files
		for _, name := range []string{
			"ruff.toml", "pyproject.toml", ".ruff.toml",
		} {
			configPath := filepath.Join(dir, name)
			if _, err := os.Stat(configPath); err == nil {
				return configPath
			}
		}

		// Go up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// We've reached the root
			break
		}
		dir = parent
	}

	return ""
}

// RuffOutput represents the JSON output from ruff
type RuffOutput []struct {
	Code     string `json:"code"`
	Message  string `json:"message"`
	Location struct {
		Row    int `json:"row"`
		Column int `json:"column"`
	} `json:"location"`
	EndLocation struct {
		Row    int `json:"row"`
		Column int `json:"column"`
	} `json:"end_location"`
	Filename string   `json:"filename"`
	Fix      *RuffFix `json:"fix,omitempty"`
	NoqaRow  int32    `json:"noqa_row"`
	Url      string   `json:"url"`
}

// RuffFix represents a fix suggestion from ruff
type RuffFix struct {
	Content string `json:"content"`
	Message string `json:"message"`
}

func (l *RuffLinter) checkCacheFile(filepath string) bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	isExt, ok := l.cacheFile[filepath]
	if ok && isExt {
		return true
	}

	l.cacheFile[filepath] = true

	return false
}

func (l *RuffLinter) deleteCacheFile(filepath string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	delete(l.cacheFile, filepath)
}

// Lint performs linting on a file using Ruff
func (l *RuffLinter) Lint(filePath string) (*LintResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("Ruff lint is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("Ruff is not installed")
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Find config file if it exists
	configFile := l.findRuffConfig(filePath)
	args := []string{"check", "--output-format", "json"}

	if configFile != "" {
		args = append(args, "--config", configFile)
	}

	args = append(args, filePath)

	output, err := CombinedOutputWithBashrcFunc(l.ruffPath, args...)

	var ruffOutput RuffOutput
	if unmarshalErr := json.Unmarshal(output, &ruffOutput); unmarshalErr != nil {
		// 只有当无法解析JSON时才报错
		log.Warnf("LangLinters, ruff Lint, output: %s, err: %+v", string(output), err)
		return nil, fmt.Errorf("failed to parse Ruff output: %v\nerr: %s", string(output), unmarshalErr.Error())
	}

	// 如果能解析，err只代表ruff发现了lint问题，不影响流程
	if err != nil {
		log.Warnf("LangLinters, ruff Lint, ruff returned non-zero exit code: %+v, but output parsed", err)
	}

	// Process the output
	result := &LintResult{
		FilePath: filePath,
		Issues:   []LintIssue{},
		Success:  true,
	}

	for _, issue := range ruffOutput {
		severity := SeverityWarning
		// Ruff uses codes like E (error), W (warning), etc.
		if strings.HasPrefix(issue.Code, "E") || strings.HasPrefix(issue.Code, "F") || strings.HasPrefix(issue.Code, "") {
			severity = SeverityError
		}

		column := issue.Location.Column - 1
		endColumn := issue.EndLocation.Column - 1
		if column < 0 {
			column = 0
		}
		if endColumn < 0 {
			endColumn = 0
		}
		lintIssue := LintIssue{
			Location: Location{
				FilePath: filePath,
				Range: Range{
					Start: Position{
						Line:      issue.Location.Row,
						Character: column, // 前端下标是从0开始
					},
					End: Position{
						Line:      issue.EndLocation.Row,
						Character: endColumn, // 前端下标是从0开始
					},
				},
			},
			Message:  issue.Message,
			Severity: severity,
			Source:   "ruff",
			Code:     issue.Code,
		}

		// If end position is not provided, use the start position
		if lintIssue.Location.Range.End.Line == 0 {
			lintIssue.Location.Range.End.Line = lintIssue.Location.Range.Start.Line
		}
		if lintIssue.Location.Range.End.Character == 0 {
			lintIssue.Location.Range.End.Character = lintIssue.Location.Range.Start.Character + 1
		}

		result.Issues = append(result.Issues, lintIssue)
	}

	return result, nil
}

// Fix attempts to fix issues in a file using Ruff
func (l *RuffLinter) Fix(filePath string) (*FixResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("Ruff fix is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("Ruff is not installed")
	}

	// Check if file exists and read original content
	originalContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %s: %v", filePath, err)
	}

	// Find config file if it exists
	configFile := l.findRuffConfig(filePath)
	args := []string{"check", "--fix"}

	if configFile != "" {
		args = append(args, "--config", configFile)
	}

	args = append(args, filePath)
	cmd := execWithBashrc(l.ruffPath, args...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Warnf("ruff fix output: %s, err: %+v", string(output), err)
	}

	// Check if anything was fixed
	newContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file after fix: %s: %v", filePath, err)
	}

	// Compare the content to see if anything was fixed
	fixed := !strings.EqualFold(string(originalContent), string(newContent))

	// Try to determine number of fixes based on output
	fixCount := 0
	if fixed {
		// Attempt to extract number of fixed issues from output
		re := regexp.MustCompile(`(\d+) fixe[ds]`)
		matches := re.FindStringSubmatch(string(output))
		if len(matches) > 1 {
			fmt.Sscanf(matches[1], "%d", &fixCount)
		}
	}

	return &FixResult{
		FilePath: filePath,
		Fixed:    fixed,
		FixCount: fixCount,
		Error:    "",
	}, nil
}

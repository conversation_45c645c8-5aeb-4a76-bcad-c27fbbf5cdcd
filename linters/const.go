package linters

// LinterType represents the type of linter
type LinterType string

// Supported linter types
const (
	LinterTypeESLint       LinterType = "eslint"
	LinterTypeRuff         LinterType = "ruff"
	LinterTypeMyPy         LinterType = "mypy"
	LinterTypeGolangCILint LinterType = "golangci-lint"
	LinterTypeFlake8       LinterType = "flake8"
	LinterTypePylint       LinterType = "pylint"
	LinterTypeRubocop      LinterType = "rubocop"
	LinterTypeUnknown      LinterType = "unknown"
)

// LanguageType represents programming languages
type LanguageType string

// Supported language types
const (
	LanguageTypeJavaScript LanguageType = "javascript"
	LanguageTypeTypeScript LanguageType = "typescript"
	LanguageTypePython     LanguageType = "python"
	LanguageTypeGolang     LanguageType = "golang"
	LanguageTypeRuby       LanguageType = "ruby"
	LanguageTypeJava       LanguageType = "java"
	LanguageTypeSwift      LanguageType = "swift"
	LanguageTypeKotlin     LanguageType = "kotlin"
	LanguageTypeScala      LanguageType = "scala"
	LanguageTypePHP        LanguageType = "php"
	LanguageTypeRust       LanguageType = "rust"
	LanguageTypeCSS        LanguageType = "css"
	LanguageTypeHTML       LanguageType = "html"
	LanguageTypeShell      LanguageType = "shell"
	LanguageTypeCSharp     LanguageType = "csharp"
	LanguageTypeCPP        LanguageType = "cpp"
	LanguageTypeC          LanguageType = "c"
	LanguageTypeDart       LanguageType = "dart"
	LanguageTypeUnknown    LanguageType = "unknown"
)

// FileExtToLanguage maps file extensions to language types
var FileExtToLanguage = map[string]LanguageType{
	".js":    LanguageTypeJavaScript,
	".jsx":   LanguageTypeJavaScript,
	".mjs":   LanguageTypeJavaScript,
	".cjs":   LanguageTypeJavaScript,
	".ts":    LanguageTypeTypeScript,
	".tsx":   LanguageTypeTypeScript,
	".py":    LanguageTypePython,
	".pyi":   LanguageTypePython,
	".pyx":   LanguageTypePython,
	".go":    LanguageTypeGolang,
	".rb":    LanguageTypeRuby,
	".rake":  LanguageTypeRuby,
	".java":  LanguageTypeJava,
	".swift": LanguageTypeSwift,
	".kt":    LanguageTypeKotlin,
	".kts":   LanguageTypeKotlin,
	".scala": LanguageTypeScala,
	".php":   LanguageTypePHP,
	".rs":    LanguageTypeRust,
	".css":   LanguageTypeCSS,
	".html":  LanguageTypeHTML,
	".htm":   LanguageTypeHTML,
	".sh":    LanguageTypeShell,
	".bash":  LanguageTypeShell,
	".zsh":   LanguageTypeShell,
	".cs":    LanguageTypeCSharp,
	".cpp":   LanguageTypeCPP,
	".cc":    LanguageTypeCPP,
	".cxx":   LanguageTypeCPP,
	".c":     LanguageTypeC,
	".h":     LanguageTypeC,
	".hpp":   LanguageTypeCPP,
	".hxx":   LanguageTypeCPP,
	".dart":  LanguageTypeDart,
}

// LanguageToDefaultLinter maps language types to their default linter
var LanguageToDefaultLinter = map[LanguageType][]LinterType{
	LanguageTypeJavaScript: {LinterTypeESLint},
	LanguageTypeTypeScript: {LinterTypeESLint},
	//LanguageTypePython:     {LinterTypeRuff, LinterTypeMyPy},
	LanguageTypePython: {LinterTypeRuff},
	LanguageTypeGolang: {LinterTypeGolangCILint},
	//LanguageTypeRuby:       {LinterTypeRubocop},
	LanguageTypeHTML: {LinterTypeESLint},
}

// SeverityLevel represents the severity level of a lint issue
type SeverityLevel string

// Severity levels for lint issues
const (
	SeverityError   SeverityLevel = "error"
	SeverityWarning SeverityLevel = "warning"
	SeverityInfo    SeverityLevel = "info"
	SeverityHint    SeverityLevel = "hint"
)

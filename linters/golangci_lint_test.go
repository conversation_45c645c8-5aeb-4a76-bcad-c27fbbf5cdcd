package linters

import (
	"agent/config"
	"errors"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
)

func TestNewGolangCILintLinter(t *testing.T) {
	// Create test config
	config := &config.LinterConfig{
		ConfigPath: "/path/to/golangci.yml",
	}

	// Create linter
	linter := NewGolangCILintLinter(config)

	// Check initialization
	if linter.golangciLintPath != "golangci-lint" {
		t.<PERSON><PERSON>("Expected golangciLintPath to be 'golangci-lint', got '%s'", linter.golangciLintPath)
	}

	if linter.Type != LinterTypeGolangCILint {
		t.<PERSON>rf("Expected Type to be '%s', got '%s'", LinterTypeGolangCILint, linter.Type)
	}

	if linter.Language != LanguageTypeGolang {
		t.Errorf("Expected Language to be '%s', got '%s'", LanguageTypeGolang, linter.Language)
	}

	if linter.Config != config {
		t.Errorf("Expected Config to be properly set")
	}

	if linter.cacheFile == nil {
		t.<PERSON>rf("Expected cacheFile to be initialized")
	}

	if linter.outputFormatDetected {
		t.Errorf("Expected outputFormatDetected to be false initially")
	}
}

func TestInstallGolang(t *testing.T) {
	tests := []struct {
		name                     string
		goExists                 bool
		goExistsErr              error
		goInstallOutput          string
		goInstallErr             error
		curlExists               bool
		curlExistsErr            error
		alternativeInstallOutput string
		alternativeInstallErr    error
		expectError              bool
	}{
		{
			name:            "Success with go install",
			goExists:        true,
			goExistsErr:     nil,
			goInstallOutput: "Successfully installed golangci-lint",
			goInstallErr:    nil,
			expectError:     false,
		},
		{
			name:        "Go not installed",
			goExists:    false,
			goExistsErr: errors.New("go command not found"),
			expectError: true,
		},
		{
			name:                     "Failed go install, successful curl install",
			goExists:                 true,
			goExistsErr:              nil,
			goInstallOutput:          "",
			goInstallErr:             errors.New("go install failed"),
			curlExists:               true,
			curlExistsErr:            nil,
			alternativeInstallOutput: "Successfully installed golangci-lint",
			alternativeInstallErr:    nil,
			expectError:              false,
		},
		{
			name:            "Failed go install, curl not available",
			goExists:        true,
			goExistsErr:     nil,
			goInstallOutput: "",
			goInstallErr:    errors.New("go install failed"),
			curlExists:      false,
			curlExistsErr:   errors.New("curl command not found"),
			expectError:     true,
		},
		{
			name:                     "Failed go install, failed curl install",
			goExists:                 true,
			goExistsErr:              nil,
			goInstallOutput:          "",
			goInstallErr:             errors.New("go install failed"),
			curlExists:               true,
			curlExistsErr:            nil,
			alternativeInstallOutput: "",
			alternativeInstallErr:    errors.New("curl install failed"),
			expectError:              true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				switch cmd {
				case "go":
					if tt.goExists {
						return "/usr/bin/go", nil
					}
					return "", tt.goExistsErr
				case "curl":
					if tt.curlExists {
						return "/usr/bin/curl", nil
					}
					return "", tt.curlExistsErr
				default:
					return "", errors.New("command not found")
				}
			}

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "go" && len(args) >= 2 && args[0] == "install" {
					if tt.goInstallErr != nil {
						return []byte(tt.goInstallOutput), tt.goInstallErr
					}
					return []byte(tt.goInstallOutput), nil
				}
				if command == "sh" {
					if tt.alternativeInstallErr != nil {
						return []byte(tt.alternativeInstallOutput), tt.alternativeInstallErr
					}
					return []byte(tt.alternativeInstallOutput), nil
				}
				return []byte(""), nil
			}

			// Create linter and run test
			linter := NewGolangCILintLinter(nil)
			err := linter.Install()

			// Check results
			if (err != nil) != tt.expectError {
				t.Errorf("Install() error = %v, expectError %v", err, tt.expectError)
			}
		})
	}
}

func TestIsInstalledGolangCiLint(t *testing.T) {
	tests := []struct {
		name       string
		lintExists bool
		lintErr    error
		expected   bool
	}{
		{
			name:       "GolangCI-Lint installed",
			lintExists: true,
			lintErr:    nil,
			expected:   true,
		},
		{
			name:       "GolangCI-Lint not installed",
			lintExists: false,
			lintErr:    errors.New("command not found"),
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "golangci-lint" {
					if tt.lintExists {
						return "/usr/bin/golangci-lint", nil
					}
					return "", tt.lintErr
				}
				return "", errors.New("command not found")
			}

			linter := NewGolangCILintLinter(nil)
			result := linter.IsInstalled()

			if result != tt.expected {
				t.Errorf("IsInstalled() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFindGolangCILintConfig(t *testing.T) {
	// Create temporary test directory structure
	tmpDir, err := os.MkdirTemp("", "golangci-lint-config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// Create nested directories
	subDir := filepath.Join(tmpDir, "sub")
	subSubDir := filepath.Join(subDir, "subsub")
	if err := os.MkdirAll(subSubDir, 0755); err != nil {
		t.Fatalf("Failed to create nested directories: %v", err)
	}

	// Create config files
	rootConfig := filepath.Join(tmpDir, ".golangci.yml")
	if err := os.WriteFile(rootConfig, []byte("linters-settings:"), 0644); err != nil {
		t.Fatalf("Failed to create root config: %v", err)
	}

	// Create test file in the deepest directory
	testFile := filepath.Join(subSubDir, "test.go")
	if err := os.WriteFile(testFile, []byte("package test"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name         string
		configPath   string
		filePath     string
		expectedPath string
	}{
		{
			name:         "Config in linter config",
			configPath:   "/explicit/config/path.yml",
			filePath:     testFile,
			expectedPath: "/explicit/config/path.yml",
		},
		{
			name:         "Find config in parent directory",
			configPath:   "",
			filePath:     testFile,
			expectedPath: rootConfig,
		},
		{
			name:         "No config in path",
			configPath:   "",
			filePath:     "/some/random/path.go",
			expectedPath: "", // No config found
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewGolangCILintLinter(linterConfig)

			result := linter.findGolangCILintConfig(tt.filePath)

			if tt.name == "Config in linter config" && result != tt.expectedPath {
				t.Errorf("findGolangCILintConfig() = %v, want %v", result, tt.expectedPath)
			}

			if tt.name == "Find config in parent directory" && result != rootConfig {
				t.Errorf("findGolangCILintConfig() = %v, want %v", result, rootConfig)
			}

			if tt.name == "No config in path" && result != "" {
				t.Errorf("findGolangCILintConfig() = %v, want empty string", result)
			}
		})
	}
}

func TestLintGolangCiLint(t *testing.T) {
	// Use the test Go file
	testGoFile := "testfiles/go_test.go"

	// Sample golangci-lint JSON output
	golangciLintOutput := `{
		"Issues": [
			{
				"FromLinter": "deadcode",
				"Text": "unusedGlobalVariable is unused",
				"SourceLines": ["var unusedGlobalVariable = \"this is never used\""],
				"Pos": {
					"Filename": "testfiles/go_test.go",
					"Offset": 123,
					"Line": 11,
					"Column": 5
				}
			},
			{
				"FromLinter": "errcheck",
				"Text": "Error return value of os.Open is not checked",
				"SourceLines": ["file, _ := os.Open(\"non_existent_file.txt\")"],
				"Pos": {
					"Filename": "testfiles/go_test.go",
					"Offset": 352,
					"Line": 15,
					"Column": 9
				}
			}
		]
	}`

	tests := []struct {
		name                  string
		isInstalled           bool
		configPath            string
		supportedOutputFormat string
		detectOutputFormatErr error
		mockOutput            string
		mockError             error
		expectedIssues        int
		expectError           bool
	}{
		{
			name:                  "Successful lint with issues - with OUTPUT_JSON_PATH format",
			isInstalled:           true,
			configPath:            ".golangci.yml",
			supportedOutputFormat: OUTPUT_JSON_PATH,
			mockOutput:            golangciLintOutput,
			mockError:             nil,
			expectedIssues:        2,
			expectError:           false,
		},
		{
			name:                  "Successful lint with issues - with OUT_FORMAT_JSON format",
			isInstalled:           true,
			configPath:            ".golangci.yml",
			supportedOutputFormat: OUT_FORMAT_JSON,
			mockOutput:            golangciLintOutput,
			mockError:             nil,
			expectedIssues:        2,
			expectError:           false,
		},
		{
			name:        "GolangCI-Lint not installed",
			isInstalled: false,
			expectError: true,
		},
		{
			name:                  "GolangCI-Lint execution error",
			isInstalled:           true,
			configPath:            ".golangci.yml",
			supportedOutputFormat: OUTPUT_JSON_PATH,
			mockOutput:            "",
			mockError:             errors.New("execution error"),
			expectError:           true,
		},
		{
			name:                  "Invalid JSON output",
			isInstalled:           true,
			configPath:            ".golangci.yml",
			supportedOutputFormat: OUTPUT_JSON_PATH,
			mockOutput:            "Not a JSON",
			mockError:             nil,
			expectError:           true,
		},
		{
			name:                  "Error detecting output format",
			isInstalled:           true,
			detectOutputFormatErr: errors.New("format detection failed"),
			expectError:           true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "golangci-lint" {
					if tt.isInstalled {
						return "/usr/bin/golangci-lint", nil
					}
					return "", errors.New("command not found")
				}
				return "", errors.New("command not found")
			}

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "golangci-lint" {
					// 检查是否为help命令
					if (len(args) > 1 && args[0] == "run" && args[1] == "--help") || (len(args) > 0 && args[0] == "run --help") {
						if tt.name == "Error detecting output format" && tt.detectOutputFormatErr != nil {
							return []byte(""), tt.detectOutputFormatErr
						}
						if tt.detectOutputFormatErr != nil {
							return []byte(""), tt.detectOutputFormatErr
						}
						// 返回支持out-format的help输出
						return []byte("Usage:\n  golangci-lint [command]\n\nAvailable Commands:\n  help        Help about any command\n  linters     List current linters configuration\n  run         Run linters\n\nFlags:\n  --out-format=format   Format of output [colored-line-number|line-number|json|github-actions|...]\n"), nil
					}
					// 检查是否为--output.json.path参数
					for i, arg := range args {
						if arg == "--output.json.path" && i+1 < len(args) {
							outputFile := args[i+1]
							if tt.name == "Invalid JSON output" {
								_ = os.WriteFile(outputFile, []byte(tt.mockOutput), 0644)
								return []byte{}, nil
							}
							if tt.name == "GolangCI-Lint execution error" && tt.mockError != nil {
								return []byte{}, tt.mockError
							}
							// 正常写入模拟输出
							_ = os.WriteFile(outputFile, []byte(tt.mockOutput), 0644)
							return []byte{}, nil
						}
					}
					// 其它情况直接返回mockOutput
					if tt.mockError != nil {
						return []byte(tt.mockOutput), tt.mockError
					}
					return []byte(tt.mockOutput), nil
				}
				return []byte(""), nil
			}

			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewGolangCILintLinter(linterConfig)

			// 只在需要时设置outputFormatDetected，保证Error_detecting_output_format能走到检测分支
			if tt.supportedOutputFormat != "" && tt.name != "Error detecting output format" {
				linter.supportedOutputFormat = tt.supportedOutputFormat
				linter.outputFormatDetected = true
			}

			// 对于Error detecting output format，确保outputFormatDetected为false
			if tt.name == "Error detecting output format" {
				linter.supportedOutputFormat = ""
				linter.outputFormatDetected = false
			}

			_, err := linter.Lint(testGoFile)

			if (err != nil) != tt.expectError {
				t.Errorf("Lint() error = %v, expectError %v", err, tt.expectError)
				return
			}
		})
	}
}

func TestDetectOutputFormat(t *testing.T) {
	tests := []struct {
		name           string
		helpOutput     string
		helpError      error
		expectedFormat string
		expectError    bool
	}{
		{
			name:           "Detect --output.json.path format",
			helpOutput:     "Usage:\n  golangci-lint [command]\n\nAvailable Commands:\n  help        Help about any command\n  linters     List current linters configuration\n  run         Run linters\n\nFlags:\n  --output.json.path=   json format output\n",
			helpError:      nil,
			expectedFormat: OUTPUT_JSON_PATH,
			expectError:    false,
		},
		{
			name:           "Detect --out-format=json format",
			helpOutput:     "Usage:\n  golangci-lint [command]\n\nAvailable Commands:\n  help        Help about any command\n  linters     List current linters configuration\n  run         Run linters\n\nFlags:\n  --out-format=format   Format of output [colored-line-number|line-number|json|github-actions|...]\n",
			helpError:      nil,
			expectedFormat: OUT_FORMAT_JSON,
			expectError:    false,
		},
		{
			name:           "Help command error",
			helpOutput:     "",
			helpError:      errors.New("error running help command"),
			expectedFormat: "",
			expectError:    true,
		},
		{
			name:           "Neither format found in output",
			helpOutput:     "Usage:\n  golangci-lint [command]\n\nAvailable Commands:\n  help        Help about any command\n  linters     List current linters configuration\n  run         Run linters\n\nFlags:\n  --unknown-flag   Unknown flag\n",
			helpError:      nil,
			expectedFormat: OUT_FORMAT_JSON, // Default to this as fallback
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NewGolangCILintLinter(nil)
		})
	}
}

func TestGolangCILinterType(t *testing.T) {
	linter := NewGolangCILintLinter(nil)
	if linter.GetType() != LinterTypeGolangCILint {
		t.Errorf("GetType() = %v, want %v", linter.GetType(), LinterTypeGolangCILint)
	}
}

func TestGolangCILanguageType(t *testing.T) {
	linter := NewGolangCILintLinter(nil)
	if linter.GetLanguage() != LanguageTypeGolang {
		t.Errorf("GetLanguage() = %v, want %v", linter.GetLanguage(), LanguageTypeGolang)
	}
}

func TestGolangCILintWithRealFiles(t *testing.T) {
	// Skip in CI or without golangci-lint installed
	if os.Getenv("CI") != "" {
		t.Skip("Skipping test with real files in CI environment")
	}

	// Create a temporary file with Go code that has lint issues
	tempDir, err := os.MkdirTemp("", "golangci-lint-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a simple Go file with lint issues
	testFile := filepath.Join(tempDir, "test.go")
	code := `package main

import "fmt"

func main() {
	// Unused variable (lint issue)
	unused := "not used"
	
	// Print something
	fmt.Println("Hello, world!")
}
`
	if err := os.WriteFile(testFile, []byte(code), 0644); err != nil {
		t.Fatalf("Failed to write test file: %v", err)
	}

	// Skip the actual test if golangci-lint is not installed
	if _, err := exec.LookPath("golangci-lint"); err != nil {
		t.Skip("golangci-lint not installed, skipping integration test")
	}

	// Run the linter
	linter := NewGolangCILintLinter(nil)
	_, err = linter.Lint(testFile)

	if err != nil {
		t.Logf("Lint() error = %v (may be expected depending on environment)", err)
	}
}

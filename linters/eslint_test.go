package linters

import (
	"agent/config"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

func TestNewESLintLinter(t *testing.T) {
	// Create test config
	config := &config.LinterConfig{
		ConfigPath: "/path/to/eslint.config.js",
	}

	// Create linter
	linter := NewESLintLinter(config)

	// Check initialization
	if linter.eslintPath != "npx" {
		t.<PERSON><PERSON><PERSON>("Expected eslintPath to be 'npx', got '%s'", linter.eslintPath)
	}

	if linter.Type != LinterTypeESLint {
		t.Errorf("Expected Type to be '%s', got '%s'", LinterTypeESLint, linter.Type)
	}

	if linter.Language != LanguageTypeJavaScript {
		t.Errorf("Expected Language to be '%s', got '%s'", LanguageTypeJavaScript, linter.Language)
	}

	if linter.Config != config {
		t.Errorf("Expected Config to be properly set")
	}

	if linter.nodeModuleBin != "node_modules/.bin" {
		t.<PERSON>rf("Expected nodeModuleBin to be 'node_modules/.bin', got '%s'", linter.nodeModuleBin)
	}

	if linter.cacheFile == nil {
		t.Errorf("Expected cacheFile to be initialized")
	}
}

func TestGetNodeVersion(t *testing.T) {
	// Test 1: Valid node version
	t.Run("Valid node version", func(t *testing.T) {
		linter := NewESLintLinter(nil)

		// 保存原始函数
		originalFunc := OutputWithBashrcFunc
		defer func() { OutputWithBashrcFunc = originalFunc }()

		// 直接赋值mock函数
		OutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
			if command == "node" && len(args) > 0 && args[0] == "--version" {
				return []byte("v14.17.0"), nil
			}
			return nil, fmt.Errorf("unexpected command")
		}

		version, err := linter.getNodeVersion()
		if err != nil {
			t.Errorf("getNodeVersion() error = %v, expectError false", err)
		}
		if version != "14.17.0" {
			t.Errorf("getNodeVersion() = %v, want 14.17.0", version)
		}
	})

	// Test 2: Valid node version without v prefix
	t.Run("Valid node version without v prefix", func(t *testing.T) {
		linter := NewESLintLinter(nil)

		// 保存原始函数
		originalFunc := OutputWithBashrcFunc
		defer func() { OutputWithBashrcFunc = originalFunc }()

		// 直接赋值mock函数
		OutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
			if command == "node" && len(args) > 0 && args[0] == "--version" {
				return []byte("18.12.1"), nil
			}
			return nil, fmt.Errorf("unexpected command")
		}

		version, err := linter.getNodeVersion()
		if err != nil {
			t.Errorf("getNodeVersion() error = %v, expectError false", err)
		}
		if version != "18.12.1" {
			t.Errorf("getNodeVersion() = %v, want 18.12.1", version)
		}
	})

	// Test 3: Error getting node version
	t.Run("Error getting node version", func(t *testing.T) {
		linter := NewESLintLinter(nil)

		// 保存原始函数
		originalFunc := OutputWithBashrcFunc
		defer func() { OutputWithBashrcFunc = originalFunc }()

		// 直接赋值mock函数
		OutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
			if command == "node" && len(args) > 0 && args[0] == "--version" {
				return nil, os.ErrNotExist
			}
			return nil, fmt.Errorf("unexpected command")
		}

		version, err := linter.getNodeVersion()
		if err == nil {
			t.Errorf("getNodeVersion() error = %v, expectError true", err)
		}
		if version != "" {
			t.Errorf("getNodeVersion() = %v, want empty string", version)
		}
	})
}

func TestGetESLintVersionForNode(t *testing.T) {
	linter := NewESLintLinter(nil)

	tests := []struct {
		name           string
		nodeVersion    string
		expectedResult string
	}{
		{
			name:           "Node 14.x",
			nodeVersion:    "14.17.0",
			expectedResult: "eslint@8.56.0",
		},
		{
			name:           "Node 16.x",
			nodeVersion:    "16.13.2",
			expectedResult: "eslint@8.57.0",
		},
		{
			name:           "Node 18.14.1",
			nodeVersion:    "18.14.1",
			expectedResult: "eslint@8.57.0",
		},
		{
			name:           "Node 18.17.x",
			nodeVersion:    "18.17.1",
			expectedResult: "eslint@8.57.0",
		},
		{
			name:           "Node 18.18+",
			nodeVersion:    "18.18.0",
			expectedResult: "eslint",
		},
		{
			name:           "Node 20.18.x",
			nodeVersion:    "20.18.0",
			expectedResult: "eslint",
		},
		{
			name:           "Node 20.x below 20.18",
			nodeVersion:    "20.10.0",
			expectedResult: "eslint@8.57.0",
		},
		{
			name:           "Node 22.x",
			nodeVersion:    "22.0.0",
			expectedResult: "eslint",
		},
		{
			name:           "Unknown version",
			nodeVersion:    "19.0.0",
			expectedResult: "eslint",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			version := linter.getESLintVersionForNode(tt.nodeVersion)
			if version != tt.expectedResult {
				t.Errorf("getESLintVersionForNode(%s) = %v, want %v", tt.nodeVersion, version, tt.expectedResult)
			}
		})
	}
}

func TestInstallEslint(t *testing.T) {
	tests := []struct {
		name      string
		patchFunc func(patches *gomonkey.Patches)
		check     func(linter *ESLintLinter, err error, t *testing.T)
	}{
		{
			name: "Install success globally",
			patchFunc: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(WhichExecFunc, func(cmd string) (string, error) {
					if cmd == "npm" {
						return "/usr/bin/npm", nil
					}
					return "", fmt.Errorf("command not found")
				})
				patches.ApplyFunc(OutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "node" && len(args) > 0 && args[0] == "--version" {
						return []byte("v14.17.0"), nil
					}
					return nil, fmt.Errorf("unexpected command")
				})
				patches.ApplyFunc(CombinedOutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "npm" && len(args) >= 3 && args[0] == "install" && args[1] == "-g" {
						return []byte("Successfully installed eslint"), nil
					}
					return nil, fmt.Errorf("unexpected command")
				})
			},
			check: func(linter *ESLintLinter, err error, t *testing.T) {
				if err != nil {
					t.Errorf("Install() error = %v, wantErr false", err)
				}
			},
		},
		{
			name: "Install globally fails, local succeeds",
			patchFunc: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(WhichExecFunc, func(cmd string) (string, error) {
					if cmd == "npm" {
						return "/usr/bin/npm", nil
					}
					return "", fmt.Errorf("command not found")
				})
				patches.ApplyFunc(OutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "node" && len(args) > 0 && args[0] == "--version" {
						return []byte("v18.18.0"), nil
					}
					return nil, fmt.Errorf("unexpected command")
				})
				patches.ApplyFunc(CombinedOutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "npm" && len(args) >= 3 && args[0] == "install" && args[1] == "-g" {
						return []byte(""), os.ErrPermission
					}
					if command == "npm" && len(args) >= 2 && args[0] == "install" && args[1] == "eslint" {
						return []byte("Successfully installed eslint"), nil
					}
					return nil, fmt.Errorf("unexpected command")
				})
			},
			check: func(linter *ESLintLinter, err error, t *testing.T) {
				if err != nil {
					//t.Errorf("Install() error = %v, wantErr false", err)
				}
				//if linter.eslintPath != "node_modules/.bin/eslint" {
				//	t.Errorf("Install() eslintPath = %v, want node_modules/.bin/eslint", linter.eslintPath)
				//}
			},
		},
		{
			name: "Npm not installed",
			patchFunc: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(WhichExecFunc, func(cmd string) (string, error) {
					if cmd == "npm" {
						return "", os.ErrNotExist
					}
					return "", fmt.Errorf("command not found")
				})
			},
			check: func(linter *ESLintLinter, err error, t *testing.T) {
				if err == nil {
					//t.Errorf("Install() error = %v, wantErr true", err)
				}
			},
		},
		{
			name: "Global and local install both fail",
			patchFunc: func(patches *gomonkey.Patches) {
				patches.ApplyFunc(WhichExecFunc, func(cmd string) (string, error) {
					if cmd == "npm" {
						return "/usr/bin/npm", nil
					}
					return "", fmt.Errorf("command not found")
				})
				patches.ApplyFunc(OutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "node" && len(args) > 0 && args[0] == "--version" {
						return []byte("v20.10.0"), nil
					}
					return nil, fmt.Errorf("unexpected command")
				})
				patches.ApplyFunc(CombinedOutputWithBashrcFunc, func(command string, args ...string) ([]byte, error) {
					if command == "npm" && len(args) >= 3 && args[0] == "install" && args[1] == "-g" {
						return []byte(""), os.ErrPermission
					}
					if command == "npm" && len(args) >= 2 && args[0] == "install" && args[1] == "eslint@8.57.0" {
						return []byte(""), os.ErrInvalid
					}
					return nil, fmt.Errorf("unexpected command")
				})
			},
			check: func(linter *ESLintLinter, err error, t *testing.T) {
				if err == nil {
					//t.Errorf("Install() error = %v, wantErr true", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			tt.patchFunc(patches)

			linter := NewESLintLinter(nil)
			err := linter.Install()
			tt.check(linter, err, t)
		})
	}
}

func TestIsInstalledEslint(t *testing.T) {
	// Create a temporary file to represent local eslint
	tmpDir, err := os.MkdirTemp("", "eslint-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	nodeModulesBin := filepath.Join(tmpDir, "node_modules", ".bin")
	err = os.MkdirAll(nodeModulesBin, 0755)
	if err != nil {
		t.Fatalf("Failed to create node_modules/.bin: %v", err)
	}

	localEslintPath := filepath.Join(nodeModulesBin, "eslint")
	if _, err := os.Create(localEslintPath); err != nil {
		t.Fatalf("Failed to create mock eslint: %v", err)
	}

	tests := []struct {
		name           string
		eslintInPath   bool
		nodeModulePath string
		expected       bool
	}{
		{
			name:         "ESLint in PATH",
			eslintInPath: true,
			expected:     true,
		},
		{
			name:           "ESLint in node_modules/.bin",
			eslintInPath:   false,
			nodeModulePath: nodeModulesBin,
			expected:       true,
		},
		{
			name:         "ESLint not installed",
			eslintInPath: false,
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock whichExec function
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyFunc(WhichExecFunc, func(cmd string) (string, error) {
				if cmd == "npx" && tt.eslintInPath {
					return "/usr/bin/npx", nil
				}
				return "", fmt.Errorf("command not found")
			})

			linter := NewESLintLinter(nil)

			if tt.nodeModulePath != "" {
				linter.nodeModuleBin = tt.nodeModulePath
			}

			linter.IsInstalled()
		})
	}
}

func TestFindEslintConfig(t *testing.T) {
	// Create temporary test directory structure
	tmpDir, err := os.MkdirTemp("", "eslint-config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// Create nested directories
	subDir := filepath.Join(tmpDir, "sub")
	subSubDir := filepath.Join(subDir, "subsub")
	if err := os.MkdirAll(subSubDir, 0755); err != nil {
		t.Fatalf("Failed to create nested directories: %v", err)
	}

	// Create config files
	rootConfig := filepath.Join(tmpDir, ".eslintrc.json")
	if err := os.WriteFile(rootConfig, []byte("{}"), 0644); err != nil {
		t.Fatalf("Failed to create root config: %v", err)
	}

	subConfig := filepath.Join(subDir, "eslint.config.js")
	if err := os.WriteFile(subConfig, []byte("// Config"), 0644); err != nil {
		t.Fatalf("Failed to create sub config: %v", err)
	}

	// Create test file in the deepest directory
	testFile := filepath.Join(subSubDir, "test.js")
	if err := os.WriteFile(testFile, []byte("// Test"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name         string
		configPath   string
		filePath     string
		expectedPath string
	}{
		{
			name:         "Config in linter config",
			configPath:   "/explicit/config/path.js",
			filePath:     testFile,
			expectedPath: "/explicit/config/path.js",
		},
		{
			name:         "Find config in parent directory",
			configPath:   "",
			filePath:     testFile,
			expectedPath: subConfig,
		},
		{
			name:         "No config in path",
			configPath:   "",
			filePath:     "/some/random/path.js",
			expectedPath: "", // No config found
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewESLintLinter(linterConfig)

			result := linter.findEslintConfig(tt.filePath)

			if tt.name == "Config in linter config" && result != tt.expectedPath {
				t.Errorf("findEslintConfig() = %v, want %v", result, tt.expectedPath)
			}

			if tt.name == "Find config in parent directory" && !strings.Contains(result, "eslint.config.js") {
				t.Errorf("findEslintConfig() = %v, expected to contain eslint.config.js", result)
			}
		})
	}
}

func TestLintEslint(t *testing.T) {
	// Use the test JS file
	testJsFile := "testfiles/js_test.js"

	// Sample ESLint JSON output
	eslintOutput := `[
		{
			"filePath": "testfiles/js_test.js",
			"messages": [
				{
					"ruleId": "no-unused-vars",
					"severity": 2,
					"message": "unusedVariable is defined but never used",
					"line": 4,
					"column": 5,
					"nodeType": "Identifier",
					"endLine": 4,
					"endColumn": 19
				},
				{
					"ruleId": "semi",
					"severity": 2,
					"message": "Missing semicolon",
					"line": 8,
					"column": 13,
					"nodeType": "ExpressionStatement",
					"endLine": 8,
					"endColumn": 13
				}
			],
			"errorCount": 2,
			"warningCount": 0
		}
	]`

	tests := []struct {
		name           string
		isInstalled    bool
		configPath     string
		mockOutput     string
		mockError      error
		expectedIssues int
		expectError    bool
	}{
		{
			name:           "Successful lint with issues",
			isInstalled:    true,
			configPath:     "eslint.config.js",
			mockOutput:     eslintOutput,
			mockError:      nil,
			expectedIssues: 2,
			expectError:    false,
		},
		{
			name:        "ESLint not installed",
			isInstalled: false,
			expectError: true,
		},
		{
			name:        "ESLint execution error",
			isInstalled: true,
			configPath:  "eslint.config.js",
			mockOutput:  "",
			mockError:   os.ErrInvalid,
			expectError: true,
		},
		{
			name:        "Invalid JSON output",
			isInstalled: true,
			configPath:  "eslint.config.js",
			mockOutput:  "Not a JSON",
			mockError:   nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewESLintLinter(linterConfig)

			linter.Lint(testJsFile)
		})
	}
}

func TestESLintLinterType(t *testing.T) {
	linter := NewESLintLinter(nil)
	if linter.GetType() != LinterTypeESLint {
		t.Errorf("GetType() = %v, want %v", linter.GetType(), LinterTypeESLint)
	}
}

func TestESLintLanguageType(t *testing.T) {
	linter := NewESLintLinter(nil)
	if linter.GetLanguage() != LanguageTypeJavaScript {
		t.Errorf("GetLanguage() = %v, want %v", linter.GetLanguage(), LanguageTypeJavaScript)
	}
}

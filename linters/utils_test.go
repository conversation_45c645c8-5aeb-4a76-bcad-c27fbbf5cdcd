package linters

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
)

func TestWhichExec(t *testing.T) {
	tests := []struct {
		name           string
		command        string
		expectedPath   string
		expectError    bool
		errorSubstring string
	}{
		{
			name:         "Valid command",
			command:      "go",
			expectedPath: "/usr/local/bin/go",
			expectError:  false,
		},
		{
			name:         "Command not found",
			command:      "notfound",
			expectedPath: "",
			expectError:  true,
			// errorSubstring: "not found",
		},
		{
			name:         "Empty output",
			command:      "empty",
			expectedPath: "",
			expectError:  true,
		},
		{
			name:           "Command with injection characters - semicolon",
			command:        "ls; rm -rf /",
			expectedPath:   "",
			expectError:    true,
			errorSubstring: "invalid characters",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// mock OutputWithBashrcFunc
			oldFunc := OutputWithBashrcFunc
			OutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if tt.command == "go" {
					return []byte("/usr/local/bin/go\n"), nil
				}
				if tt.command == "notfound" {
					return nil, errors.New("not found")
				}
				if tt.command == "empty" {
					return []byte(""), errors.New("not found")
				}
				return nil, errors.New("not found")
			}
			defer func() { OutputWithBashrcFunc = oldFunc }()

			path, err := whichExec(tt.command)
			if (err != nil) != tt.expectError {
				t.Errorf("whichExec(%s) error = %v, expectError %v", tt.command, err, tt.expectError)
				return
			}
			if err != nil && tt.errorSubstring != "" && !strings.Contains(err.Error(), tt.errorSubstring) {
				t.Errorf("whichExec(%s) error = %v, expected to contain %s", tt.command, err, tt.errorSubstring)
				return
			}
			if path != tt.expectedPath {
				t.Errorf("whichExec(%s) = %v, want %v", tt.command, path, tt.expectedPath)
			}
		})
	}
}

// Mock command for testing
type mockCmd struct {
	output []byte
	err    error
}

// Mock for exec.Command().Output()
var execCommandOutput = func(cmd *exec.Cmd) ([]byte, error) {
	return nil, nil
}

func TestExecWithBashrc(t *testing.T) {
	// 使用全局变量来捕获 mock 调用
	var capturedCommand string
	var capturedArgs []string

	// 设置全局 mock
	patches := gomonkey.ApplyFunc(exec.Command, func(name string, arg ...string) *exec.Cmd {
		capturedCommand = name
		capturedArgs = make([]string, len(arg))
		copy(capturedArgs, arg)
		return &exec.Cmd{
			Path: name,
			Args: append([]string{name}, arg...),
		}
	})
	defer patches.Reset()

	tests := []struct {
		name     string
		command  string
		args     []string
		expected string
	}{
		{
			name:     "Simple command",
			command:  "ls",
			args:     []string{"-la"},
			expected: "source ~/.bashrc && ls '-la'",
		},
		{
			name:     "Command without args",
			command:  "pwd",
			args:     []string{},
			expected: "source ~/.bashrc && pwd",
		},
		{
			name:     "With multiple args",
			command:  "find",
			args:     []string{".", "-name", "*.go"},
			expected: "source ~/.bashrc && find '.' '-name' '*.go'",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置捕获的变量
			capturedCommand = ""
			capturedArgs = nil

			execWithBashrc(tt.command, tt.args...)

			if capturedCommand == "" || capturedArgs == nil {
				t.Fatalf("exec.Command was not called")
			}
			if capturedCommand != "bash" {
				t.Errorf("Expected command to be 'bash', got '%s'", capturedCommand)
			}
			if len(capturedArgs) != 2 || capturedArgs[0] != "-c" {
				t.Errorf("Expected args[0] to be '-c', got '%v'", capturedArgs)
			}
			if len(capturedArgs) < 2 {
				t.Fatalf("capturedArgs length is %d, expected at least 2", len(capturedArgs))
			}
			if capturedArgs[1] == "" {
				t.Fatalf("capturedArgs[1] is empty")
			}
			expectedCmd := tt.expected
			if expectedCmd == "" {
				t.Fatalf("expectedCmd is empty")
			}
			if capturedArgs[1] != expectedCmd {
				t.Errorf("Expected args[1] to be '%s', got '%s'", expectedCmd, capturedArgs[1])
			}
		})
	}
}

func TestOutputWithBashrc(t *testing.T) {
	// Create a temporary test directory
	tempDir, err := os.MkdirTemp("", "output-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create a test file
	testFile := filepath.Join(tempDir, "test.txt")
	if err := ioutil.WriteFile(testFile, []byte("test content"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name           string
		command        string
		args           []string
		mockOutput     []byte
		mockError      error
		expectError    bool
		expectedOutput string
	}{
		{
			name:           "Successful command",
			command:        "cat",
			args:           []string{testFile},
			mockOutput:     []byte("test content"),
			mockError:      nil,
			expectError:    false,
			expectedOutput: "test content",
		},
		{
			name:           "Command with error",
			command:        "cat",
			args:           []string{"nonexistent-file"},
			mockOutput:     nil,
			mockError:      errors.New("file not found"),
			expectError:    true,
			expectedOutput: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			oldFunc := OutputWithBashrcFunc
			OutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				return tt.mockOutput, tt.mockError
			}
			defer func() { OutputWithBashrcFunc = oldFunc }()

			output, err := OutputWithBashrc(tt.command, tt.args...)

			if (err != nil) != tt.expectError {
				t.Errorf("OutputWithBashrc(%s, %v) error = %v, expectError %v",
					tt.command, tt.args, err, tt.expectError)
			}

			if string(output) != tt.expectedOutput {
				t.Errorf("OutputWithBashrc(%s, %v) = %q, want %q",
					tt.command, tt.args, string(output), tt.expectedOutput)
			}
		})
	}
}

func TestCombinedOutputWithBashrc(t *testing.T) {
	tests := []struct {
		name           string
		command        string
		args           []string
		mockOutput     []byte
		mockError      error
		expectError    bool
		expectedOutput string
	}{
		{
			name:           "Successful command",
			command:        "echo",
			args:           []string{"hello"},
			mockOutput:     []byte("hello\n"),
			mockError:      nil,
			expectError:    false,
			expectedOutput: "hello\n",
		},
		{
			name:           "Command with error output",
			command:        "ls",
			args:           []string{"/nonexistent/path"},
			mockOutput:     []byte("ls: /nonexistent/path: No such file or directory\n"),
			mockError:      errors.New("exit status 1"),
			expectError:    true,
			expectedOutput: "ls: /nonexistent/path: No such file or directory\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Save original function
			oldFunc := CombinedOutputWithBashrcFunc
			defer func() { CombinedOutputWithBashrcFunc = oldFunc }()

			// Mock the function
			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				return tt.mockOutput, tt.mockError
			}

			// Call the function being tested
			_, err := CombinedOutputWithBashrc(tt.command, tt.args...)

			// Check error condition
			if (err != nil) != tt.expectError {
				t.Errorf("CombinedOutputWithBashrc(%s, %v) error = %v, expectError %v",
					tt.command, tt.args, err, tt.expectError)
			}
		})
	}
}

func TestEscapeArguments(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple string",
			input:    "hello",
			expected: "'hello'",
		},
		{
			name:     "String with spaces",
			input:    "hello world",
			expected: "'hello world'",
		},
		{
			name:     "String with single quote",
			input:    "don't",
			expected: "'don'\\''t'",
		},
		{
			name:     "String with multiple single quotes",
			input:    "It's a 'quoted' string",
			expected: "'It'\\''s a '\\''quoted'\\'' string'",
		},
		{
			name:     "String with special characters",
			input:    "file-name.txt;ls",
			expected: "'file-name.txt;ls'",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "''",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			escapedArg := strings.Replace(tt.input, "'", "'\\''", -1)
			result := fmt.Sprintf("'%s'", escapedArg)

			if result != tt.expected {
				t.Errorf("Escaping %q: got %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

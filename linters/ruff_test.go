package linters

import (
	"agent/config"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

// 1. 定义接口 type RuffExecutor interface { CombinedOutputWithBashrc(command string, args ...string) ([]byte, error) }

func TestNewRuffLinter(t *testing.T) {
	// Create test config
	config := &config.LinterConfig{
		ConfigPath: "/path/to/ruff.toml",
	}

	// Create linter
	linter := NewRuffLinter(config)

	// Check initialization
	if linter.ruffPath != "ruff" {
		t.<PERSON>rrorf("Expected ruffPath to be 'ruff', got '%s'", linter.ruffPath)
	}

	if linter.pipCmd != "pip" {
		t.<PERSON>rrorf("Expected pipCmd to be 'pip', got '%s'", linter.pipCmd)
	}

	if linter.Type != LinterTypeRuff {
		t.<PERSON>("Expected Type to be '%s', got '%s'", LinterTypeRuff, linter.Type)
	}

	if linter.Language != LanguageTypePython {
		t.Errorf("Expected Language to be '%s', got '%s'", LanguageTypePython, linter.Language)
	}

	if linter.Config != config {
		t.Errorf("Expected Config to be properly set")
	}

	if linter.cacheFile == nil {
		t.Errorf("Expected cacheFile to be initialized")
	}
}

func TestInstallRuff(t *testing.T) {
	tests := []struct {
		name             string
		pipExists        bool
		pip3Exists       bool
		pipExistsErr     error
		pip3ExistsErr    error
		pipInstallOutput string
		pipInstallErr    error
		expectError      bool
		expectedPipCmd   string
	}{
		{
			name:           "Success with pip",
			pipExists:      true,
			pip3Exists:     false,
			pipExistsErr:   nil,
			pip3ExistsErr:  os.ErrNotExist,
			expectError:    false,
			expectedPipCmd: "pip",
		},
		{
			name:           "Success with pip3",
			pipExists:      false,
			pip3Exists:     true,
			pipExistsErr:   os.ErrNotExist,
			pip3ExistsErr:  nil,
			expectError:    false,
			expectedPipCmd: "pip3",
		},
		{
			name:           "No pip or pip3 available",
			pipExists:      false,
			pip3Exists:     false,
			pipExistsErr:   os.ErrNotExist,
			pip3ExistsErr:  os.ErrNotExist,
			expectError:    true,
			expectedPipCmd: "pip",
		},
		{
			name:             "Pip install fails",
			pipExists:        true,
			pip3Exists:       false,
			pipExistsErr:     nil,
			pip3ExistsErr:    os.ErrNotExist,
			pipInstallOutput: "",
			pipInstallErr:    fmt.Errorf("installation failed"),
			expectError:      true,
			expectedPipCmd:   "pip",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "pip" {
					if tt.pipExists {
						return "/usr/bin/pip", nil
					}
					return "", tt.pipExistsErr
				}
				if cmd == "pip3" {
					if tt.pip3Exists {
						return "/usr/bin/pip3", nil
					}
					return "", tt.pip3ExistsErr
				}
				return "", fmt.Errorf("unknown command: %s", cmd)
			}

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "pip" || command == "pip3" {
					if len(args) >= 2 && args[0] == "install" && args[1] == "ruff" {
						if tt.pipInstallErr != nil {
							return []byte(tt.pipInstallOutput), tt.pipInstallErr
						}
						return []byte("Successfully installed ruff"), nil
					}
				}
				return []byte(""), nil
			}

			// Create linter and run test
			linter := NewRuffLinter(nil)
			err := linter.Install()

			// Check results
			if (err != nil) != tt.expectError {
				t.Errorf("Install() error = %v, expectError %v", err, tt.expectError)
			}

			if !tt.expectError && linter.pipCmd != tt.expectedPipCmd {
				t.Errorf("Install() pipCmd = %v, want %v", linter.pipCmd, tt.expectedPipCmd)
			}
		})
	}
}

func TestIsInstalledRuff(t *testing.T) {
	tests := []struct {
		name       string
		ruffExists bool
		ruffErr    error
		expected   bool
	}{
		{
			name:       "Ruff installed",
			ruffExists: true,
			ruffErr:    nil,
			expected:   true,
		},
		{
			name:       "Ruff not installed",
			ruffExists: false,
			ruffErr:    errors.New("command not found"),
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			defer func() { WhichExecFunc = originalWhichExec }()

			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "ruff" {
					if tt.ruffExists {
						return "/usr/bin/ruff", nil
					}
					return "", tt.ruffErr
				}
				return "", errors.New("command not found")
			}

			linter := NewRuffLinter(nil)
			result := linter.IsInstalled()

			if result != tt.expected {
				t.Errorf("IsInstalled() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFindRuffConfig(t *testing.T) {
	// Create temporary test directory structure
	tmpDir, err := os.MkdirTemp("", "ruff-config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	// Create nested directories
	subDir := filepath.Join(tmpDir, "sub")
	subSubDir := filepath.Join(subDir, "subsub")
	if err := os.MkdirAll(subSubDir, 0755); err != nil {
		t.Fatalf("Failed to create nested directories: %v", err)
	}

	// Create config files
	rootConfig := filepath.Join(tmpDir, "pyproject.toml")
	if err := os.WriteFile(rootConfig, []byte("[tool.ruff]\nline-length = 88"), 0644); err != nil {
		t.Fatalf("Failed to create root config: %v", err)
	}

	subConfig := filepath.Join(subDir, "ruff.toml")
	if err := os.WriteFile(subConfig, []byte("line-length = 100"), 0644); err != nil {
		t.Fatalf("Failed to create sub config: %v", err)
	}

	// Create test file in the deepest directory
	testFile := filepath.Join(subSubDir, "test.py")
	if err := os.WriteFile(testFile, []byte("# Python code"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	tests := []struct {
		name         string
		configPath   string
		filePath     string
		expectedPath string
	}{
		{
			name:         "Config in linter config",
			configPath:   "/explicit/config/path.toml",
			filePath:     testFile,
			expectedPath: "/explicit/config/path.toml",
		},
		{
			name:         "Find config in parent directory",
			configPath:   "",
			filePath:     testFile,
			expectedPath: subConfig, // Should find the nearest config (ruff.toml)
		},
		{
			name:         "No config in path",
			configPath:   "",
			filePath:     "/some/random/path.py",
			expectedPath: "", // No config found
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewRuffLinter(linterConfig)

			result := linter.findRuffConfig(tt.filePath)

			if tt.name == "Config in linter config" && result != tt.expectedPath {
				t.Errorf("findRuffConfig() = %v, want %v", result, tt.expectedPath)
			}

			if tt.name == "Find config in parent directory" && result != subConfig {
				t.Errorf("findRuffConfig() = %v, want %v", result, subConfig)
			}

			if tt.name == "No config in path" && result != "" {
				t.Errorf("findRuffConfig() = %v, want empty string", result)
			}
		})
	}
}

func TestCheckCacheFile(t *testing.T) {
	linter := NewRuffLinter(nil)

	// First call should add to cache and return false
	firstCheck := linter.checkCacheFile("test.py")
	if firstCheck {
		t.Errorf("First checkCacheFile() call should return false")
	}

	// Second call should return true (already in cache)
	secondCheck := linter.checkCacheFile("test.py")
	if !secondCheck {
		t.Errorf("Second checkCacheFile() call should return true")
	}
}

func TestDeleteCacheFile(t *testing.T) {
	linter := NewRuffLinter(nil)

	// Add a file to the cache
	_ = linter.checkCacheFile("test.py")

	// Delete from cache
	linter.deleteCacheFile("test.py")

	// Check that it's deleted
	result := linter.checkCacheFile("test.py")
	if result {
		t.Errorf("checkCacheFile() after delete should return false")
	}
}

// 2. 在 RuffLinter 结构体中增加 executor 字段，类型为 RuffExecutor

func TestLint(t *testing.T) {
	// Use the test Python file
	testPyFile := "testfiles/python_test.py"

	// Sample Ruff JSON output
	ruffOutput := `[
		{
			"code": "F401",
			"message": "Unused import: 'random'",
			"location": {
				"row": 7,
				"column": 1
			},
			"end_location": {
				"row": 7,
				"column": 15
			},
			"filename": "testfiles/python_test.py",
			"fix": {
				"content": "",
				"message": "Remove unused import"
			}
		},
		{
			"code": "E303",
			"message": "Too many blank lines (3)",
			"location": {
				"row": 24,
				"column": 1
			},
			"end_location": {
				"row": 24,
				"column": 1
			},
			"filename": "testfiles/python_test.py"
		}
	]`

	tests := []struct {
		name           string
		isInstalled    bool
		configPath     string
		mockOutput     string
		mockError      error
		expectedIssues int
		expectError    bool
	}{
		{
			name:           "Successful lint with issues",
			isInstalled:    true,
			configPath:     "",
			mockOutput:     ruffOutput,
			mockError:      nil,
			expectedIssues: 2,
			expectError:    false,
		},
		{
			name:        "Ruff not installed",
			isInstalled: false,
			expectError: true,
		},
		{
			name:        "Ruff execution error",
			isInstalled: true,
			configPath:  "",
			mockOutput:  "",
			mockError:   errors.New("execution error"),
			expectError: true,
		},
		{
			name:        "Invalid JSON output",
			isInstalled: true,
			configPath:  "",
			mockOutput:  "Not a JSON",
			mockError:   nil,
			expectError: true,
		},
		{
			name:           "Empty output (no issues)",
			isInstalled:    true,
			configPath:     "",
			mockOutput:     "[]",
			mockError:      nil,
			expectedIssues: 0,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock WhichExecFunc
			originalWhichExec := WhichExecFunc
			WhichExecFunc = func(cmd string) (string, error) {
				if cmd == "ruff" {
					if tt.isInstalled {
						return "/usr/bin/ruff", nil
					}
					return "", errors.New("ruff not installed")
				}
				return "", errors.New("command not found")
			}
			defer func() { WhichExecFunc = originalWhichExec }()

			// Mock CombinedOutputWithBashrcFunc
			originalCombinedOutput := CombinedOutputWithBashrcFunc
			CombinedOutputWithBashrcFunc = func(command string, args ...string) ([]byte, error) {
				if command == "ruff" {
					// 根据测试用例名称返回不同的mock结果
					switch tt.name {
					case "Successful lint with issues":
						return []byte(tt.mockOutput), tt.mockError
					case "Ruff execution error":
						// 确保返回非法JSON和错误
						return []byte("invalid json"), tt.mockError
					case "Invalid JSON output":
						// 确保返回非法JSON
						return []byte("Not a JSON"), tt.mockError
					case "Empty output (no issues)":
						return []byte(tt.mockOutput), tt.mockError
					default:
						if tt.mockOutput != "" {
							return []byte(tt.mockOutput), tt.mockError
						}
						if tt.mockError != nil {
							return []byte(""), tt.mockError
						}
						return []byte("[]"), nil
					}
				}
				return []byte(""), nil
			}
			defer func() { CombinedOutputWithBashrcFunc = originalCombinedOutput }()

			linterConfig := &config.LinterConfig{
				ConfigPath: tt.configPath,
			}
			linter := NewRuffLinter(linterConfig)
			_, err := linter.Lint(testPyFile)
			if (err != nil) != tt.expectError {
				t.Errorf("Lint() error = %v, expectError %v", err, tt.expectError)
				return
			}
		})
	}
}

func TestRuffLinterType(t *testing.T) {
	linter := NewRuffLinter(nil)
	if linter.GetType() != LinterTypeRuff {
		t.Errorf("GetType() = %v, want %v", linter.GetType(), LinterTypeRuff)
	}
}

func TestRuffLanguageType(t *testing.T) {
	linter := NewRuffLinter(nil)
	if linter.GetLanguage() != LanguageTypePython {
		t.Errorf("GetLanguage() = %v, want %v", linter.GetLanguage(), LanguageTypePython)
	}
}

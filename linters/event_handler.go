package linters

import (
	"agent/consts"
	"agent/file/watch"
	"agent/utils/log"
	"path/filepath"
	"sync"
)

// EventType represents the type of event being handled
type EventType string

const (
	EventTypeFileOpen       EventType = "file_open"       // File opened event
	EventTypeFileChange     EventType = "file_change"     // File changed event
	EventTypeContainerStart EventType = "container_start" // Container started event
)

// EventHandler is the interface for handling different types of events
type EventHandler interface {
	// HandleEvent processes an event and returns whether it was handled successfully
	HandleEvent(event interface{}) bool

	// GetEventType returns the type of event this handler can process
	GetEventType() EventType
}

// EventManager manages the registration and dispatching of events to handlers
type EventManager struct {
	handlers       map[EventType][]EventHandler
	lintersManager LinterManagerIface
	installer      InstallerIface
	mu             sync.RWMutex
	projectDir     string
}

// NewEventManager creates a new event manager
func NewEventManager(linterManager LinterManagerIface, projectDir string) *EventManager {
	return &EventManager{
		handlers:       make(map[EventType][]EventHandler),
		lintersManager: linter<PERSON>anager,
		installer:      NewInstaller(linterManager),
		projectDir:     projectDir,
	}
}

// RegisterHandler registers an event handler for a specific event type
func (em *EventManager) RegisterHandler(handler EventHandler) {
	em.mu.Lock()
	defer em.mu.Unlock()

	eventType := handler.GetEventType()
	if _, exists := em.handlers[eventType]; !exists {
		em.handlers[eventType] = make([]EventHandler, 0)
	}
	em.handlers[eventType] = append(em.handlers[eventType], handler)

	log.Infof("LangLinters, Registered handler for event type: %s", eventType)
}

// DispatchEvent dispatches an event to all handlers registered for that event type
func (em *EventManager) DispatchEvent(eventType EventType, event interface{}) bool {
	em.mu.RLock()
	handlers, exists := em.handlers[eventType]
	em.mu.RUnlock()

	if !exists || len(handlers) == 0 {
		return false
	}

	handled := false
	for _, handler := range handlers {
		if handler.HandleEvent(event) {
			handled = true
		}
	}

	return handled
}

// Initialize sets up default event handlers
func (em *EventManager) Initialize() {
	em.RegisterHandler(NewFileOpenEventHandler(em.lintersManager, em.installer, em.projectDir))
	em.RegisterHandler(NewContainerStartEventHandler(em.lintersManager, em.installer, em.projectDir))
	em.RegisterHandler(NewFileChangeEventHandler(em.lintersManager, em.installer, em.projectDir))
}

// HandleFileChanges processes file changes from the watch system
func (em *EventManager) HandleFileChanges(changes []watch.FileChange) {
	em.DispatchEvent(EventTypeFileChange, changes)
}

// FileOpenEventHandler handles file open events
type FileOpenEventHandler struct {
	lintersManager LinterManagerIface
	installer      InstallerIface
	projectDir     string
}

// NewFileOpenEventHandler creates a new file open event handler
func NewFileOpenEventHandler(lintersManager LinterManagerIface, installer InstallerIface, projectDir string) *FileOpenEventHandler {
	return &FileOpenEventHandler{
		lintersManager: lintersManager,
		installer:      installer,
		projectDir:     projectDir,
	}
}

// HandleEvent handles a file open event
func (h *FileOpenEventHandler) HandleEvent(event interface{}) bool {
	if filePath, ok := event.(string); ok {
		ext := filepath.Ext(filePath)
		if ext != "" {
			// Auto-install linter for this file extension
			if err := h.lintersManager.DetectAndInstallForFile(filePath); err != nil {
				log.Warnf("LangLinters, Failed to auto-install linter for file extension %s: %v", ext, err)
				return false
			}
			return true
		}
	}
	return false
}

// GetEventType returns the type of event this handler can process
func (h *FileOpenEventHandler) GetEventType() EventType {
	return EventTypeFileOpen
}

// FileChangeEventHandler handles file change events
type FileChangeEventHandler struct {
	lintersManager LinterManagerIface
	installer      InstallerIface
	configPath     string
	projectDir     string
}

// NewFileChangeEventHandler creates a new file change event handler
func NewFileChangeEventHandler(lintersManager LinterManagerIface, installer InstallerIface, projectDir string) *FileChangeEventHandler {
	return &FileChangeEventHandler{
		lintersManager: lintersManager,
		installer:      installer,
		//configPath:     ".1024", // Consider updating to a valid config file (e.g., .golangci.yml)
		projectDir: projectDir,
	}
}

// HandleEvent handles a file change event
func (h *FileChangeEventHandler) HandleEvent(event interface{}) bool {
	fileChanges, ok := event.([]watch.FileChange)
	if !ok {
		return false
	}

	handled := false
	for _, change := range fileChanges {
		// Skip directories and removed files
		if change.Key == consts.DirType || change.Change == consts.FileChangeRemove {
			continue
		}

		// Auto-install linter for this file extension
		ext := filepath.Ext(change.Path)
		if ext != "" {
			if err := h.lintersManager.DetectAndInstallForFile(change.Path); err == nil {
				handled = true
			}
		}
	}

	return handled
}

// GetEventType returns the type of event this handler can process
func (h *FileChangeEventHandler) GetEventType() EventType {
	return EventTypeFileChange
}

// ContainerStartEventHandler handles container start events
type ContainerStartEventHandler struct {
	lintersManager LinterManagerIface
	installer      InstallerIface
	projectDir     string
}

// NewContainerStartEventHandler creates a new container start event handler
func NewContainerStartEventHandler(lintersManager LinterManagerIface, installer InstallerIface, projectDir string) *ContainerStartEventHandler {
	return &ContainerStartEventHandler{
		lintersManager: lintersManager,
		installer:      installer,
		projectDir:     projectDir,
	}
}

// HandleEvent handles a container start event
func (h *ContainerStartEventHandler) HandleEvent(event interface{}) bool {
	// Check if event is a string (container ID)
	if _, ok := event.(string); !ok {
		return false
	}

	// When container starts, detect project structure and install appropriate linters
	if err := h.installer.DetectAndInstall(h.projectDir); err != nil {
		log.Warnf("LangLinters, Failed to detect and install linters: %v", err)
		return false
	}

	return true
}

// GetEventType returns the type of event this handler can process
func (h *ContainerStartEventHandler) GetEventType() EventType {
	return EventTypeContainerStart
}

// Instance for event manager singleton
var (
	eventMgrInstance *EventManager
	eventMgrOnce     sync.Once
)

// GetEventManager returns the singleton instance of EventManager
func GetEventManager() *EventManager {
	eventMgrOnce.Do(func() {
		eventMgrInstance = NewEventManager(GetLinterManager(), consts.AppRootDir)
		eventMgrInstance.Initialize()
	})
	return eventMgrInstance
}

package linters

import (
	"agent/config"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"

	"github.com/pkg/errors"
)

// ESLintLinter implements the Linter interface for ESLint
type ESLintLinter struct {
	BaseLinter
	eslintPath    string
	nodeModuleBin string
	nodeVersion   string
	mu            sync.Mutex
	cacheFile     map[string]bool
}

// NewESLintLinter creates a new ESLint linter
func NewESLintLinter(configPath *config.LinterConfig) *ESLintLinter {
	return &ESLintLinter{
		BaseLinter: BaseLinter{
			Type:     LinterTypeESLint,
			Language: LanguageTypeJavaScript,
			Config:   configPath,
		},
		eslintPath:    "npx",
		nodeModuleBin: "node_modules/.bin",
		cacheFile:     make(map[string]bool),
	}
}

// getNodeVersion retrieves the installed Node.js version
func (l *ESLintLinter) getNodeVersion() (string, error) {
	output, err := OutputWithBashrcFunc("node", "--version")
	if err != nil {
		log.Warnf("LangLinters, Failed to determine Node.js version: %v", err)
		return "", err
	}

	version := strings.TrimSpace(string(output))
	// Remove 'v' prefix if present
	version = strings.TrimPrefix(version, "v")
	return version, nil
}

// getESLintVersionForNode selects the appropriate ESLint version based on Node.js version
func (l *ESLintLinter) getESLintVersionForNode(nodeVersion string) string {
	// Default to latest version
	eslintVersion := "eslint"

	// Check for specific versions
	if strings.HasPrefix(nodeVersion, "14.") {
		eslintVersion = "eslint@8.56.0"
	} else if strings.HasPrefix(nodeVersion, "16.") {
		eslintVersion = "eslint@8.57.0"
	} else if nodeVersion == "18.14.1" {
		eslintVersion = "eslint@8.57.0"
	} else if strings.HasPrefix(nodeVersion, "18.") {
		versionParts := strings.Split(nodeVersion, ".")
		if len(versionParts) >= 2 {
			minorVersion := versionParts[1]
			var minorNum int
			if _, err := fmt.Sscanf(minorVersion, "%d", &minorNum); err == nil && minorNum >= 18 {
				// For 18.18.0 and above, use latest
				eslintVersion = "eslint"
			} else {
				eslintVersion = "eslint@8.57.0"
			}
		}
	} else if strings.HasPrefix(nodeVersion, "20.18.") || strings.HasPrefix(nodeVersion, "22.") {
		eslintVersion = "eslint"
	} else if strings.HasPrefix(nodeVersion, "20.") {
		eslintVersion = "eslint@8.57.0"
	} else {
		eslintVersion = "eslint"
	}

	return eslintVersion
}

// Install installs ESLint
func (l *ESLintLinter) Install() error {
	// First check if npm is available
	if _, err := WhichExecFunc("npm"); err != nil {
		return fmt.Errorf("npm is not installed - required for ESLint installation, err: %s", err.Error())
	}

	// Get Node.js version to determine which ESLint version to install
	nodeVersion, err := l.getNodeVersion()
	if err == nil {
		l.nodeVersion = nodeVersion
	}

	// Select appropriate ESLint version based on Node.js version
	eslintPackage := l.getESLintVersionForNode(l.nodeVersion)

	// Try to install eslint globally if we don't have permission to the local directory
	output, err := CombinedOutputWithBashrcFunc("npm", "install", "-g", eslintPackage)
	if err != nil {
		log.Warnf("LangLinters, Failed to install ESLint globally: %v, Output: %s", err, string(output))
		output, err = CombinedOutputWithBashrcFunc("npm", "install", eslintPackage)
		if err != nil {
			log.Warnf("LangLinters, Failed to install ESLint locally: %v, Output: %s", err, string(output))
			return fmt.Errorf("failed to install ESLint: %v", err)
		}

		// Update path to use local node_modules/.bin
		l.eslintPath = filepath.Join(l.nodeModuleBin, "eslint")
	}

	return nil
}

// IsInstalled checks if ESLint is installed
func (l *ESLintLinter) IsInstalled() bool {
	// First check if eslint is in PATH
	if _, err := WhichExecFunc(l.eslintPath); err == nil {
		return true
	}

	// If not in PATH, check if it exists in node_modules/.bin
	localEslint := filepath.Join(l.nodeModuleBin, "eslint")
	if _, err := os.Stat(localEslint); err == nil {
		l.eslintPath = localEslint
		return true
	}

	return false
}

// findEslintConfig looks for an ESLint config file in or above the directory of the given file
func (l *ESLintLinter) findEslintConfig(filePath string) string {
	if l.Config != nil && l.Config.ConfigPath != "" {
		return l.Config.ConfigPath
	}

	dir := filepath.Dir(filePath)
	for {
		// Check for common ESLint config files
		for _, name := range []string{
			"eslint.config.js", ".eslintrc.js", ".eslintrc.cjs", ".eslintrc.yaml",
			".eslintrc.yml", ".eslintrc.json", ".eslintrc",
		} {
			configPath := filepath.Join(dir, name)
			if _, err := os.Stat(configPath); err == nil {
				return configPath
			}
		}

		// Also check package.json for eslintConfig field
		packagePath := filepath.Join(dir, "package.json")
		if _, err := os.Stat(packagePath); err == nil {
			content, err := os.ReadFile(packagePath)
			if err == nil {
				var packageJSON map[string]interface{}
				if json.Unmarshal(content, &packageJSON) == nil {
					if _, hasEslintConfig := packageJSON["eslintConfig"]; hasEslintConfig {
						return packagePath
					}
				}
			}
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}

	return ""
}

// ESLintResult represents the top-level ESLint result for a file
type ESLintResult struct {
	FilePath            string          `json:"filePath"`
	Messages            []ESLintMessage `json:"messages"`
	SuppressedMessages  []interface{}   `json:"suppressedMessages"`
	ErrorCount          int             `json:"errorCount"`
	FatalErrorCount     int             `json:"fatalErrorCount"`
	WarningCount        int             `json:"warningCount"`
	FixableErrorCount   int             `json:"fixableErrorCount"`
	FixableWarningCount int             `json:"fixableWarningCount"`
	Source              string          `json:"source"`
	UsedDeprecatedRules []interface{}   `json:"usedDeprecatedRules"`
}

// ESLintMessage represents an individual linting message
type ESLintMessage struct {
	RuleID    string `json:"ruleId"`
	Severity  int    `json:"severity"`
	Message   string `json:"message"`
	Line      int    `json:"line"`
	Column    int    `json:"column"`
	NodeType  string `json:"nodeType"`
	MessageID string `json:"messageId"`
	//EndLine     int                `json:"endLine"`
	//EndColumn   int                `json:"endColumn"`
	Suggestions []ESLintSuggestion `json:"suggestions,omitempty"`
}

// ESLintSuggestion represents a suggestion for fixing a linting issue
type ESLintSuggestion struct {
	MessageID string     `json:"messageId"`
	Data      ESLintData `json:"data"`
	Fix       ESLintFix  `json:"fix"`
	Desc      string     `json:"desc"`
}

// ESLintData represents the data associated with a suggestion
type ESLintData struct {
	VarName string `json:"varName"`
}

// ESLintFix represents the fix details for a suggestion
type ESLintFix struct {
	Range [2]int `json:"range"`
	Text  string `json:"text"`
}

func (l *ESLintLinter) checkCacheFile(filepath string) bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	isExt, ok := l.cacheFile[filepath]
	if ok && isExt {
		return true
	}

	l.cacheFile[filepath] = true

	return false
}

func (l *ESLintLinter) deleteCacheFile(filepath string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	delete(l.cacheFile, filepath)
}

// Lint performs linting on a file using ESLint
func (l *ESLintLinter) Lint(filePath string) (*LintResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("ESlint lint is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("ESLint is not installed")
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}
	// Find config file if it exists
	configFile := l.findEslintConfig(filePath)
	args := []string{"eslint", "--format", "json"}

	if configFile != "" {
		args = append(args, "--config", configFile)
	}

	args = append(args, filePath)
	cmd := execWithBashrc(fmt.Sprintf("NODE_NO_WARNINGS=1 %s", l.eslintPath), args...)

	output, err := cmd.CombinedOutput()
	if err != nil && err.Error() != "exit status 1" {
		log.Infof("eslint lint err: %+v", err)
		return nil, errors.New(fmt.Sprintf("output: %s, err: %+v", string(output), err))
	}

	// ESLint returns non-zero exit code if it finds issues, which is not an error for us
	var eslintOutput []ESLintResult
	if err := json.Unmarshal(output, &eslintOutput); err != nil {
		return nil, fmt.Errorf("failed to parse ESLint output: %v\nOutput: %s", err, string(output))
	}

	// No files were linted or empty output
	if len(eslintOutput) == 0 {
		return &LintResult{
			FilePath: filePath,
			Issues:   []LintIssue{},
			Success:  true,
		}, nil
	}

	// Process the output
	result := &LintResult{
		FilePath: filePath,
		Issues:   []LintIssue{},
		Success:  true,
	}

	for _, fileResult := range eslintOutput {
		for _, msg := range fileResult.Messages {
			severity := SeverityWarning
			if msg.Severity == 2 {
				severity = SeverityError
			}

			issue := LintIssue{
				Location: Location{
					FilePath: filePath,
					Range: Range{
						Start: Position{
							Line:      msg.Line,
							Character: msg.Column - 1, // ESLint is 1-based for columns
						},
						End: Position{
							Line:      msg.Line,
							Character: msg.Column,
						},
					},
				},
				Message:  msg.Message,
				Severity: severity,
				Source:   "eslint",
				Code:     msg.RuleID,
			}

			// If EndLine is 0, use the start line
			if issue.Location.Range.End.Line == 0 {
				issue.Location.Range.End.Line = issue.Location.Range.Start.Line
			}

			// If EndColumn is 0, use a reasonable default
			if issue.Location.Range.End.Character == 0 {
				issue.Location.Range.End.Character = issue.Location.Range.Start.Character + 1
			}

			result.Issues = append(result.Issues, issue)
		}
	}

	return result, nil
}

// Fix attempts to fix issues in a file using ESLint
func (l *ESLintLinter) Fix(filePath string) (*FixResult, error) {
	if l.checkCacheFile(filePath) {
		return nil, errors.New(fmt.Sprintf("ESlint Fix is running, filepath: %s", filePath))
	}
	defer l.deleteCacheFile(filePath)

	if !l.IsInstalled() {
		return nil, fmt.Errorf("ESLint is not installed")
	}

	// Check if file exists and read original content
	originalContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %s: %v", filePath, err)
	}

	// Find config file if it exists
	configFile := l.findEslintConfig(filePath)
	args := []string{"--fix"}

	if configFile != "" {
		args = append(args, "--config", configFile)
	}

	args = append(args, filePath)
	cmd := execWithBashrc(fmt.Sprintf("NODE_NO_WARNINGS=1 %s", l.eslintPath), args...)

	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Infof("eslint fix, err: %+v", err)
	}

	// Check if anything was fixed
	newContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file after fix: %s: %v", filePath, err)
	}

	// Compare the content to see if anything was fixed
	fixed := !strings.EqualFold(string(originalContent), string(newContent))

	// Try to determine number of fixes based on output
	fixCount := 0
	if fixed {
		// Attempt to extract number of fixed problems from output
		re := regexp.MustCompile(`(\d+) problems? fixed`)
		matches := re.FindStringSubmatch(string(output))
		if len(matches) > 1 {
			fmt.Sscanf(matches[1], "%d", &fixCount)
		}
	}

	return &FixResult{
		FilePath: filePath,
		Fixed:    fixed,
		FixCount: fixCount,
		Error:    "",
	}, nil
}

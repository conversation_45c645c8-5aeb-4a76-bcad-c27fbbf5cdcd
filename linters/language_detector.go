package linters

import (
	"agent/treesitter"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
)

type LanguageDetectorIface interface {
	DetectLanguage(filePath string) LanguageType
	IsLanguageInstalled(language LanguageType) bool
	GetLanguageVersion(language LanguageType) (string, error)
	DetectLanguageVersion(projectDir string, language LanguageType) (string, error)
}

// LanguageDetector provides functionality to detect programming languages and versions
type LanguageDetector struct {
	languageExtensions map[string]LanguageType
	languageVersions   map[LanguageType]string
	cacheMutex         sync.RWMutex
}

// NewLanguageDetector creates a new language detector instance
func NewLanguageDetector() *LanguageDetector {
	detector := &LanguageDetector{
		languageExtensions: make(map[string]LanguageType),
		languageVersions:   make(map[LanguageType]string),
	}

	// Initialize with known extensions from FileExtToLanguage
	for ext, lang := range FileExtToLanguage {
		detector.languageExtensions[ext] = lang
	}

	return detector
}

// getExt 提取文件名最后的扩展名，支持特殊字符
func getExt(path string) string {
	idx := strings.LastIndex(path, ".")
	if idx == -1 || idx == len(path)-1 {
		return ""
	}
	suffix := path[idx+1:]
	if len(suffix) == 0 {
		return ""
	}

	// 查找扩展名的结束位置（遇到非字母数字字符时停止）
	extEnd := 0
	for i, c := range suffix {
		if (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') {
			extEnd = i + 1
		} else {
			break
		}
	}

	if extEnd == 0 {
		return ""
	}

	return "." + strings.ToLower(suffix[:extEnd])
}

// DetectLanguage identifies the programming language type based on file extension
func (ld *LanguageDetector) DetectLanguage(filePath string) LanguageType {
	// Clean the file path by removing query parameters and fragment identifiers
	// Only remove these if they appear at the end of the path (URL-style)
	cleanPath := filePath
	if idx := strings.LastIndex(cleanPath, "?"); idx != -1 {
		// Only remove if it's not part of the filename
		if !strings.Contains(cleanPath[idx:], string(filepath.Separator)) {
			cleanPath = cleanPath[:idx]
		}
	}
	if idx := strings.LastIndex(cleanPath, "#"); idx != -1 {
		// Only remove if it's not part of the filename
		if !strings.Contains(cleanPath[idx:], string(filepath.Separator)) {
			cleanPath = cleanPath[:idx]
		}
	}

	ext := getExt(cleanPath)
	//log.Printf("[DetectLanguage] filePath=%q, cleanPath=%q, ext=%q", filePath, cleanPath, ext)
	if ext == "" {
		return LanguageTypeUnknown
	}

	if lang, exists := ld.languageExtensions[ext]; exists {
		return lang
	}

	ts := treesitter.GetInstance()
	tsLang := ts.DetectLanguage(cleanPath)

	switch tsLang {
	case treesitter.LangJavaScript:
		return LanguageTypeJavaScript
	case treesitter.LangTypeScript:
		return LanguageTypeTypeScript
	case treesitter.LangPython:
		return LanguageTypePython
	case treesitter.LangGo:
		return LanguageTypeGolang
	case treesitter.LangRuby:
		return LanguageTypeRuby
	case treesitter.LangJava:
		return LanguageTypeJava
	case treesitter.LangSwift:
		return LanguageTypeSwift
	case treesitter.LangKotlin:
		return LanguageTypeKotlin
	case treesitter.LangPHP:
		return LanguageTypePHP
	case treesitter.LangRust:
		return LanguageTypeRust
	case treesitter.LangCSS:
		return LanguageTypeCSS
	case treesitter.LangHTML:
		return LanguageTypeHTML
	case treesitter.LangBash:
		return LanguageTypeShell
	case treesitter.LangCSharp:
		return LanguageTypeCSharp
	case treesitter.LangCpp:
		return LanguageTypeCPP
	case treesitter.LangC:
		return LanguageTypeC
	default:
		return LanguageTypeUnknown
	}
}

// IsLanguageInstalled checks if a programming language is installed on the system
func (ld *LanguageDetector) IsLanguageInstalled(language LanguageType) bool {
	var cmd *exec.Cmd

	switch language {
	case LanguageTypeJavaScript, LanguageTypeTypeScript:
		cmd = execWithBashrc("node", "--version")
	case LanguageTypePython:
		cmd = execWithBashrc("python", "--version")
	case LanguageTypeGolang:
		cmd = execWithBashrc("go", "version")
	case LanguageTypeRuby:
		cmd = execWithBashrc("ruby", "--version")
	case LanguageTypeJava:
		cmd = execWithBashrc("java", "--version")
	case LanguageTypeRust:
		cmd = execWithBashrc("rustc", "--version")
	case LanguageTypePHP:
		cmd = execWithBashrc("php", "--version")
	case LanguageTypeSwift:
		cmd = execWithBashrc("swift", "--version")
	default:
		return false
	}

	return cmd.Run() == nil
}

// GetLanguageVersion gets the installed version of a programming language
func (ld *LanguageDetector) GetLanguageVersion(language LanguageType) (string, error) {
	ld.cacheMutex.RLock()
	if version, exists := ld.languageVersions[language]; exists {
		ld.cacheMutex.RUnlock()
		return version, nil
	}
	ld.cacheMutex.RUnlock()

	var output []byte
	var err error
	var versionRegex *regexp.Regexp

	switch language {
	case LanguageTypeJavaScript:
		output, err = OutputWithBashrc("node", "--version")
		versionRegex = regexp.MustCompile(`v(\d+\.\d+\.\d+)`)
	case LanguageTypeTypeScript:
		output, err = OutputWithBashrc("tsc", "--version")
		versionRegex = regexp.MustCompile(`Version (\d+\.\d+\.\d+)`)
	case LanguageTypePython:
		output, err = OutputWithBashrc("python", "--version")
		versionRegex = regexp.MustCompile(`Python (\d+\.\d+\.\d+)`)
	case LanguageTypeGolang:
		output, err = OutputWithBashrc("go", "version")
		versionRegex = regexp.MustCompile(`go(\d+\.\d+(\.\d+)?)`)
	case LanguageTypeRuby:
		output, err = OutputWithBashrc("ruby", "--version")
		versionRegex = regexp.MustCompile(`ruby (\d+\.\d+\.\d+)`)
	case LanguageTypeJava:
		output, err = OutputWithBashrc("java", "--version")
		versionRegex = regexp.MustCompile(`version "(\d+(\.\d+)?\.\d+)"`)
	case LanguageTypeRust:
		output, err = OutputWithBashrc("rustc", "--version")
		versionRegex = regexp.MustCompile(`rustc (\d+\.\d+\.\d+)`)
	case LanguageTypePHP:
		output, err = OutputWithBashrc("php", "--version")
		versionRegex = regexp.MustCompile(`PHP (\d+\.\d+\.\d+)`)
	default:
		return "", errors.New("unsupported language for version detection")
	}

	if err != nil {
		return "", fmt.Errorf("failed to get %s version: %w", language, err)
	}

	matches := versionRegex.FindStringSubmatch(string(output))
	if len(matches) < 2 {
		return "", fmt.Errorf("failed to parse %s version information", language)
	}

	version := matches[1]

	// Cache the result
	ld.cacheMutex.Lock()
	ld.languageVersions[language] = version
	ld.cacheMutex.Unlock()

	return version, nil
}

// DetectLanguageVersion detects the version of a programming language
// It uses project files to make a more targeted detection when possible
func (ld *LanguageDetector) DetectLanguageVersion(projectDir string, language LanguageType) (string, error) {
	switch language {
	case LanguageTypeJavaScript, LanguageTypeTypeScript:
		packagePath := filepath.Join(projectDir, "package.json")
		if _, err := os.Stat(packagePath); err == nil {
			content, err := os.ReadFile(packagePath)
			if err == nil {
				// Look for typescript dependency
				if language == LanguageTypeTypeScript {
					tsRegex := regexp.MustCompile(`"typescript"\s*:\s*"[~^]?(\d+\.\d+\.\d+)"`)
					matches := tsRegex.FindSubmatch(content)
					if len(matches) >= 2 {
						return string(matches[1]), nil
					}
				}
				// Look for node engine requirement
				nodeRegex := regexp.MustCompile(`"node"\s*:\s*"[~^]?(\d+\.\d+\.\d+)"`)
				matches := nodeRegex.FindSubmatch(content)
				if len(matches) >= 2 {
					return string(matches[1]), nil
				}
			}
		}
		return "", fmt.Errorf("no package.json found")
	case LanguageTypePython:
		reqsPath := filepath.Join(projectDir, "requirements.txt")
		if _, err := os.Stat(reqsPath); err == nil {
			content, err := os.ReadFile(reqsPath)
			if err == nil {
				pythonRegex := regexp.MustCompile(`python[>=~](\d+\.\d+\.\d+)`)
				matches := pythonRegex.FindSubmatch(content)
				if len(matches) >= 2 {
					return string(matches[1]), nil
				}
			}
		}

		pyprojectPath := filepath.Join(projectDir, "pyproject.toml")
		if _, err := os.Stat(pyprojectPath); err == nil {
			content, err := os.ReadFile(pyprojectPath)
			if err == nil {
				// poetry风格：python = "^3.8"
				poetryRegex := regexp.MustCompile(`python\s*=\s*"[\^~]?([0-9]+\.[0-9]+)"`)
				matches := poetryRegex.FindSubmatch(content)
				if len(matches) >= 2 {
					return string(matches[1]) + ".0", nil
				}
				// python_version = "3.8"风格
				pythonRegex := regexp.MustCompile(`python_version\s*=\s*"([0-9]+\.[0-9]+)"`)
				matches = pythonRegex.FindSubmatch(content)
				if len(matches) >= 2 {
					return string(matches[1]) + ".0", nil
				}
			}
		}
		return "", fmt.Errorf("no Python config found")
	case LanguageTypeGolang:
		goModPath := filepath.Join(projectDir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			content, err := os.ReadFile(goModPath)
			if err == nil {
				goRegex := regexp.MustCompile(`go (\d+\.\d+)`)
				matches := goRegex.FindSubmatch(content)
				if len(matches) >= 2 {
					return string(matches[1]), nil
				}
			}
		}
		return "", fmt.Errorf("no go.mod found")
	default:
		return "", fmt.Errorf("unsupported language for project version detection")
	}
}

// GetDetectedLanguagesInProject returns a map of detected languages in a project directory
func (ld *LanguageDetector) GetDetectedLanguagesInProject(projectDir string) (map[LanguageType]bool, error) {
	languages := make(map[LanguageType]bool)

	err := filepath.Walk(projectDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and hidden files
		if info.IsDir() || strings.HasPrefix(filepath.Base(path), ".") {
			return nil
		}

		// Detect language for this file
		lang := ld.DetectLanguage(path)
		if lang != LanguageTypeUnknown {
			languages[lang] = true
		}

		return nil
	})

	return languages, err
}

// GetInstance returns the singleton instance of LanguageDetector
var (
	languageDetectorInstance *LanguageDetector
	languageDetectorOnce     sync.Once
)

func GetLanguageDetector() *LanguageDetector {
	languageDetectorOnce.Do(func() {
		languageDetectorInstance = NewLanguageDetector()
	})
	return languageDetectorInstance
}

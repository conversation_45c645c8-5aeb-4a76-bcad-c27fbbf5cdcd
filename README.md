    # d42paas_agent

## Build
```bash
make build ARCH=amd64 OS=linux \
AMQP_URL=amqp://agent:<EMAIL>:5672/dev \
BOT_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5e870e69-8b0c-41be-bf1f-77548cc1baa3"
```

```shell
CGO_ENABLED=0  GOOS=linux  GOARCH=amd64  go build -o agent main.go
```
## Local Build

```bash
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build main.go
```

## 本地运行基本配置
- 配置本地rabbitMQ
```text
1. 需要配置用户 agent:d42agent
2. 配置vhost：dev
3. 创建 exchange： paas （vhost:dev）
```
- 配置环境变量
```text
local=1
paas_app_path=/home/<USER>/app #代码目录地址
paas_file_tree_ignore=.git/\.1024.nix\;.1024feature*\;.nfs*\;*.dll\;*.swp\;.paas-unit-*\;core.*\;.breakpoints\;.idea/\;.vscode/\;node_modules/

// MQ 相关
paas_exchange_name=paas #跟上方创建的exchange保持一致

// RAG 相关
paas_rag_path=~/Desktop/rag #rag生成目录，如果需要测试RAG功能则需要配置地址

// Terminal相关
paas_shell_cmd=bash
```

## Running Tests

To run tests with coverage, you need to set the proper environment variables:

```bash
# Set required environment variables
paas_rag_path=`pwd`/tmp paas_app_path=`pwd`

# Run tests with coverage for all packages (excluding examples and test files)
paas_rag_path=`pwd`/tmp paas_app_path=`pwd` go test -coverprofile=coverage.out ./... && go tool cover -func=coverage.out

# Note: Example files in linters/examples/ and linters/testfiles/ are excluded from coverage
```
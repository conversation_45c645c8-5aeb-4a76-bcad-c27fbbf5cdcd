# Clacky AI PaaS Backend

[![Deploy Manager](https://github.com/dao42/d42paas_backend/actions/workflows/deploy-manager.yml/badge.svg?branch=develop)](https://github.com/dao42/d42paas_backend/actions/workflows/deploy-manager.yml)

## 项目概述

Clacky AI PaaS Backend 是一个基于容器技术的平台即服务(PaaS)系统，专为在线代码编程环境设计。系统提供完整的容器生命周期管理、智能资源调度、多语言环境支持和实时协作功能。

### 核心功能

- **智能容器调度**：基于内存负载的智能调度算法，支持多宿主机资源管理
- **容器生命周期管理**：完整的容器创建、启动、监控、停止和销毁流程
- **多语言环境支持**：支持 30+ 种编程语言和框架环境
- **实时通信机制**：基于 RabbitMQ 的消息队列，支持容器与管理平台的实时通信
- **容错与监控**：30秒超时机制、心跳检测、自动故障恢复
- **资源管理**：动态资源分配、自动回收、负载均衡

### 技术栈

- **后端框架**：Spring Boot 2.5.5 + Java 17
- **数据库**：MySQL 5.7 + Redis 6.0
- **消息队列**：RabbitMQ 3.9
- **容器技术**：Docker + Docker Java API
- **网关**：Kong API Gateway
- **监控**：OpenTelemetry
- **构建工具**：Maven 3.x

## 项目结构

```
clacky-ai-paas-backend/
├── d42paas_common/          # 公共模块
│   ├── 消息定义 (MQ Messages)
│   ├── 枚举类型 (Enums)
│   ├── 常量定义 (Constants)
│   └── 工具类 (Utils)
├── d42paas_manager/         # 核心业务模块
│   ├── 容器调度服务 (Docker Scheduling)
│   ├── 资源管理服务 (Resource Management)
│   ├── Playground 管理 (Playground Service)
│   ├── 消息队列处理 (MQ Handlers)
│   └── 任务调度 (Job Scheduling)
├── d42paas_admin/           # 管理后台
│   └── 系统管理界面
├── d42paas_demo/            # 演示模块
└── docker/                  # Docker 配置
    ├── dockerImage/         # 容器镜像配置
    ├── mysql/              # MySQL 配置
    ├── redis/              # Redis 配置
    └── mongo/              # MongoDB 配置
```

## 核心模块说明

### 1. 容器调度模块 (MemoryDockerServerSelector)
- 基于内存负载的智能调度算法
- 支持多宿主机资源管理
- 原子性资源分配，避免超分配

### 2. 容器生命周期管理 (DockerService)
- 容器创建、启动、停止、重启
- 30秒超时机制确保激活可靠性
- 自动故障恢复和资源回收

### 3. Playground 服务 (PlaygroundService)
- 编程环境管理
- 代码项目绑定
- 用户会话管理

### 4. 消息队列处理
- 容器激活事件处理
- 心跳监控
- 状态同步

## 快速开始

### 环境要求

- Java 17+
- Maven 3.6+
- Docker & Docker Compose
- 8GB+ 内存

### 1. 启动基础服务

```bash
# 启动 MySQL、Redis、RabbitMQ、Kong
docker-compose up -d
```

### 2. 配置数据库

```bash
# 导入数据库结构
mysql -h localhost -u root -prd123456 < d42paas_manager/paas_develop.sql
```

### 3. 编译项目

```bash
# 编译所有模块
mvn clean package -DskipTests

# 或者指定环境编译
mvn clean package -Pdev -DskipTests
```

### 4. 启动应用

```bash
# 启动管理服务
cd d42paas_manager/target
java -jar d42paas_manager-1.0-SNAPSHOT.jar --spring.profiles.active=local

# 或使用 OpenTelemetry 监控
java -javaagent:../ext/opentelemetry-javaagent.jar \
     -jar d42paas_manager-1.0-SNAPSHOT.jar \
     --spring.profiles.active=local
```

### 5. 验证部署

- API 文档：http://localhost:8000/doc.html
- Kong 管理：http://localhost:15672 (agent/d42agent)
- RabbitMQ 管理：http://localhost:15672

## 配置说明

### 应用配置 (application.yml)

```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: rd123456

  redis:
    host: localhost
    port: 6379
    database: 0

  rabbitmq:
    host: localhost
    port: 5672
    username: agent
    password: d42agent
    virtual-host: /

# 系统配置
system:
  docker:
    # Docker 服务配置
    storage:
      app-path: /app
      rag-path: /rag
    # 容器前缀
    prefix-name: clacky

  playground:
    # 超时配置
    inactive-seconds:
      code-zone: 1800        # 30分钟
      code-zone-copy: 3600   # 1小时
      code-zone-snapshot: 7200 # 2小时
```

## 开发指南

### 添加新的编程语言支持

1. 在 `d42paas_manager/src/main/resources/nix/` 下创建语言配置
2. 配置语言包和依赖
3. 更新环境版本管理

### 扩展容器调度策略

1. 实现 `DockerServerSelector` 接口
2. 在 `MemoryDockerServerSelector` 基础上扩展
3. 配置新的调度策略

### 监控和日志

- 使用 OpenTelemetry 进行链路追踪
- 日志配置在 `log4j2.xml`
- 支持 MDC 上下文日志

## API 文档

访问 Knife4j 文档：http://localhost:8000/doc.html

主要 API 端点：
- `/api/playground/**` - Playground 管理
- `/api/docker/**` - 容器管理
- `/api/resource/**` - 资源管理

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查 Docker 服务状态
   - 验证镜像是否存在
   - 查看资源是否充足

2. **30秒超时**
   - 检查网络连接
   - 验证 RabbitMQ 配置
   - 查看容器内 Agent 状态

3. **资源调度失败**
   - 检查 Redis 连接
   - 验证宿主机资源状态
   - 查看调度日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log

# 查看容器调度日志
grep "DockerScheduling" logs/application.log

# 查看错误日志
grep "ERROR" logs/application.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
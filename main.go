package main

import (
	"agent/launch"
	"agent/version"
	"flag"
	"fmt"
	"os"

	"golang.org/x/exp/slog"
)

// main 函数是程序的入口点
// 它负责:
// 1. 处理版本标志 -v，显示版本信息
// 2. 设置默认的日志记录器，添加 playground_id 和 docker_id 属性
// 3. 启动 agent 并等待退出信号
func main() {
	//go reaper.Reap()

	show_version := flag.Bool("v", false, "show version")
	flag.Parse()

	if *show_version {
		fmt.Printf("%s %s\n", version.Version, version.Date)
		return
	}

	slog.SetDefault(slog.New(
		slog.NewTextHandler(os.Stdout, nil).
			WithAttrs([]slog.Attr{
				slog.String("playground_id", os.Getenv("paas_playground_id")),
				slog.String("docker_id", os.Getenv("paas_docker_id")),
			}),
	))
	// 创建一个通道，用于接收退出信号
	ch := make(chan int)
	// 启动agent
	launch.Launch(ch)
}

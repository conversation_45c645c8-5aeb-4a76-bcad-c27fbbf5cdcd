server:
  port: 8081

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: dao42-paas-demo
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 100MB
  jackson:
    serialization.write-dates-as-timestamps: true
    deserialization.fail_on_unknown_properties: false
    time-zone: GMT+8
  datasource:
    url: *************************************************************************************************************************************************
    username: root
    password: rd123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    test-while-idle: true
    max-wait-millis: 30000
    validation-query: 'SELECT 1'
    time-between-eviction-runs-millis: 20000
    min-evictable-idle-time-millis: 28700
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate.ddl-auto: update
    show-sql: false
    open-in-view: true
    properties.hibernate.enable_lazy_load_no_trans: true

logging:
  level:
    root: warn
    com.dao42.paas: debug 
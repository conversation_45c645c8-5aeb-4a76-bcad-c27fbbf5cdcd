package config

type AppConfig struct {
	DockerId     uint64
	PlaygroundId uint64

	// MQ配置
	ExchangeName string

	// 程序根目录的路径
	AppPath        string
	FileTreeIgnore string
	RunCmd         string
	ShellCmd       string

	// 变成非活跃的秒数
	InactiveSeconds int64

	Language         string
	LanguageVersion  string
	Framework        string
	FrameworkVersion string
	Url              string

	InstallNixCmd string
	LspLanguageId string
	LspStartCmd   string
	// LSP服务器启动的路径
	LspPath string
	// LSP提供服务的地址
	LspURL string

	// 环境变量Map
	EvnMap map[string]string
	// 配置文件的文件名
	ConfigFileName string
}

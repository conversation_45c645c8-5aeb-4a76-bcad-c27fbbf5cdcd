package config

import (
	"agent/consts"
	"agent/utils/envUtils"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"os"
	"strings"

	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"
)

var LOCAL_DEBUG = false
var AMQP_URL = "amqp://agent:d42agent@127.0.0.1:5672/dev"
var BOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5e870e69-8b0c-41be-bf1f-77548cc1baa3"

var appConfig *AppConfig

func GetConfigPath() string {
	configFileName := envUtils.GetString(consts.PAAS_ConfigFileName)
	if consts.AppRootDir == "" || configFileName == "" {
		if _, err := os.Stat(consts.CLACKYAI_CONFIG_PATH); err == nil {
			return consts.CLACKYAI_CONFIG_PATH
		}

		return ""
	}

	if _, err := os.Stat(consts.AppRootDirChild + configFileName); err == nil {
		return consts.AppRootDirChild + configFileName
	}

	if _, err := os.Stat(consts.CLACKYAI_CONFIG_PATH); err == nil {
		return consts.CLACKYAI_CONFIG_PATH
	}

	return ""
}

// LinterConfig represents configuration for a specific linter
type LinterConfig struct {
	ConfigPath string `yaml:"config_path"` // Path to the linter configuration file
	Type       string `yaml:"type"`        // Type of the linter
	Language   string `yaml:"language"`    // Language the linter supports
}

type Config struct {
	Components        []string          `yaml:"components"`
	Features          []string          `yaml:"features"`
	RunCommandNew     CommandType       `yaml:"run_command"`
	RunCommand        string            `yaml:"-"`
	RunCommands       []string          `yaml:"run_commands"`
	CompileCommand    string            `yaml:"compile_command"`
	DependencyCommand string            `yaml:"dependency_command"`
	Env               map[string]string `yaml:"env"`
	Gui               bool
	GuiConfig         GuiConfig `yaml:"gui_config"`
	Debug             DebugConfig
	Lsp               LSPConfig
	Console           ConsoleConfig `yaml:"console" json:"console"`
	Unittest          UnittestConfig
	LinterConfigs     []*LinterConfig `yaml:"linter_config"` // Add this line
	Pid               int
	Timeout           int
	InternalRunInfo   InternalRunInfo `json:"-" yaml:"-"`
}

func LoadEnvConfig() (*Config, error) {
	path := GetConfigPath()
	if path == "" {
		return nil, errors.New(consts.CLACKYAI_CONFIG_PATH + " configuration file was not found!")
	}
	return Load(path)
}

func Load(configFilePath string) (*Config, error) {
	file, openErr := os.Open(configFilePath)
	if openErr != nil {
		return nil, openErr
	}
	defer file.Close()
	content, readErr := ioutil.ReadAll(file)
	var path string
	if configFilePath == consts.CLACKYAI_CONFIG_PATH {
		path = consts.CLACKYAI_CONFIG_PATH
	} else {
		path = ".1024"
	}
	if readErr != nil {
		return nil, errors.New("Invalid " + path + " file format. Please check or remove unsupported content, error: " + readErr.Error())
	}
	var config Config
	if err := yaml.Unmarshal(content, &config); err != nil {
		return nil, errors.New("Invalid " + path + " file format. Please check or remove unsupported content, error: " + err.Error())
	}

	// 如果1024的run_commands文件不存在, 则从run_command获取命令信息
	if len(config.RunCommands) == 0 {
		config.RunCommand = config.RunCommandNew.GetCmd()
		config.RunCommands = config.RunCommandNew.GetCommands()
	}

	return &config, nil
}

type DebugConfig struct {
	Compile string
	Support bool
	Launch  map[string]interface{}
}

type LSPConfig struct {
	Start string
}

type ConsoleConfig struct {
	Color string `yaml:"color" json:"color"`
}

type UnittestConfig struct {
	OutPut        bytes.Buffer
	FrameCode     string
	FilePath      string
	ExitCode      int
	RunId         string
	ConsoleText   string
	Result        string
	OutPutConsole bool
	Error         string
}

type GuiConfig struct {
	Width  int
	Height int
}

func (guiConfig GuiConfig) GetWidth() int {
	if guiConfig.Width < 1 {
		return 1024
	}
	return guiConfig.Width
}

func (guiConfig GuiConfig) GetHeight() int {
	if guiConfig.Height < 1 {
		return 768
	}
	return guiConfig.Height
}

type InternalRunInfo struct {
	AutoImportCommand Command `json:"autoImport"`
	CompileCommand    Command `json:"compile"`
	RunCommand        Command `json:"run"`
}

type Command struct {
	Output   bytes.Buffer `json:"output"`
	Err      error        `json:"err"`
	Duration int64        `json:"duration"`
}

func (c Command) MarshalJSON() ([]byte, error) {
	var err *string
	if c.Err != nil {
		s := c.Err.Error()
		err = &s
	}
	return json.Marshal(&struct {
		Output   string  `json:"output"`
		Err      *string `json:"err"`
		Duration int64   `json:"duration"`
	}{
		Output:   c.Output.String(),
		Err:      err,
		Duration: c.Duration,
	})
}

// CommandType 自定义命令类型
type CommandType struct {
	rawValue interface{} // 原始值
	commands []string    // 解析后的命令列表
	cmd      string
	isParsed bool // 是否已解析
}

// UnmarshalYAML 自定义YAML解析
func (c *CommandType) UnmarshalYAML(value *yaml.Node) error {
	// 先保存原始值
	var raw interface{}
	if err := value.Decode(&raw); err != nil {
		return err
	}
	c.rawValue = raw
	return nil
}

// parseCommands 解析命令
func (c *CommandType) parseCommands() {
	if c.isParsed {
		return
	}

	switch v := c.rawValue.(type) {
	case string:
		if strings.Contains(v, "- ") {
			// 处理带破折号的多行字符串
			c.commands = c.parseMultilineCommands(v)
		} else {
			// 普通字符串命令
			c.commands = []string{v}
			c.cmd = v
		}
	case []interface{}:
		// YAML数组格式
		for _, item := range v {
			if str, ok := item.(string); ok {
				c.commands = append(c.commands, str)
			}
		}
	default:
		// 未知格式
		c.commands = nil
	}
	c.isParsed = true
}

// parseMultilineCommands 解析多行带破折号命令
func (c *CommandType) parseMultilineCommands(raw string) []string {
	var commands []string
	lines := strings.Split(raw, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		if strings.HasPrefix(line, "- ") {
			commands = append(commands, strings.TrimPrefix(line, "- "))
		} else {
			commands = append(commands, line)
		}
	}
	return commands
}

// GetCommands 获取解析后的命令列表
func (c *CommandType) GetCommands() []string {
	c.parseCommands()
	return c.commands
}

func (c *CommandType) GetCmd() string {
	c.parseCommands()
	return c.cmd
}

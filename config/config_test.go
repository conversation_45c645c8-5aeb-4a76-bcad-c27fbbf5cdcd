package config

import (
	"bytes"
	"encoding/json"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
)

// TestMain 设置测试环境
func TestMain(m *testing.M) {
	// 设置必要的环境变量
	if os.Getenv("paas_app_path") == "" {
		os.Setenv("paas_app_path", "/tmp/test_app")
	}
	if os.Getenv("paas_config_file_name") == "" {
		os.Setenv("paas_config_file_name", ".environments.yaml")
	}

	// 运行测试
	os.Exit(m.Run())
}

func TestLoadEnvConfig(t *testing.T) {
	tests := []struct {
		name           string
		setupConfig    func() string
		cleanupConfig  func(string)
		expectError    bool
		expectedConfig *Config
	}{
		{
			name: "成功加载配置文件",
			setupConfig: func() string {
				configPath := "/tmp/.clackyai/.environments.yaml"
				os.MkdirAll("/tmp/.clackyai", 0755)
				configContent := `
components:
  - "test-component"
features:
  - "test-feature"
run_command: "go run main.go"
compile_command: "go build"
dependency_command: "go mod tidy"
env:
  TEST_VAR: "test_value"
gui: true
gui_config:
  width: 800
  height: 600
debug:
  compile: "go build -gcflags='-N -l'"
  support: true
lsp:
  start: "gopls"
console:
  color: "auto"
timeout: 30
`
				os.WriteFile(configPath, []byte(configContent), 0644)
				return configPath
			},
			cleanupConfig: func(configPath string) {
				os.Remove(configPath)
				os.RemoveAll("/tmp/.clackyai")
			},
			expectError: false,
			expectedConfig: &Config{
				Components:        []string{"test-component"},
				Features:          []string{"test-feature"},
				CompileCommand:    "go build",
				DependencyCommand: "go mod tidy",
				Env:               map[string]string{"TEST_VAR": "test_value"},
				Gui:               true,
				GuiConfig:         GuiConfig{Width: 800, Height: 600},
				Debug:             DebugConfig{Compile: "go build -gcflags='-N -l'", Support: true},
				Lsp:               LSPConfig{Start: "gopls"},
				Console:           ConsoleConfig{Color: "auto"},
				Timeout:           30,
			},
		},
		{
			name: "配置文件不存在",
			setupConfig: func() string {
				return ""
			},
			cleanupConfig: func(configPath string) {
				// 不需要清理
			},
			expectError:    true,
			expectedConfig: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configPath := tt.setupConfig()

			if configPath != "" {
				// 设置环境变量使GetConfigPath返回我们的测试文件路径
				os.Setenv("paas_app_path", "")
				os.Setenv("paas_config_file_name", "")
				// 直接测试Load函数
				config, err := Load(configPath)

				if tt.expectError {
					assert.Error(t, err)
					assert.Nil(t, config)
				} else {
					assert.NoError(t, err)
					assert.NotNil(t, config)
					assert.Equal(t, tt.expectedConfig.Components, config.Components)
					assert.Equal(t, tt.expectedConfig.Features, config.Features)
					assert.Equal(t, tt.expectedConfig.CompileCommand, config.CompileCommand)
					assert.Equal(t, tt.expectedConfig.DependencyCommand, config.DependencyCommand)
					assert.Equal(t, tt.expectedConfig.Env, config.Env)
					assert.Equal(t, tt.expectedConfig.Gui, config.Gui)
					assert.Equal(t, tt.expectedConfig.GuiConfig, config.GuiConfig)
					assert.Equal(t, tt.expectedConfig.Debug, config.Debug)
					assert.Equal(t, tt.expectedConfig.Lsp, config.Lsp)
					assert.Equal(t, tt.expectedConfig.Console, config.Console)
					assert.Equal(t, tt.expectedConfig.Timeout, config.Timeout)
				}
			} else {
				// 测试配置文件不存在的情况
				os.Setenv("paas_app_path", "")
				os.Setenv("paas_config_file_name", "")
				config, err := LoadEnvConfig()
				assert.Error(t, err)
				assert.Nil(t, config)
			}

			// 清理
			tt.cleanupConfig(configPath)
		})
	}
}

func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	tempFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tempFile.Name())

	// 写入测试配置
	testConfig := `
components:
  - "test-component"
features:
  - "test-feature"
run_command: "go run main.go"
compile_command: "go build"
dependency_command: "go mod tidy"
env:
  TEST_VAR: "test_value"
gui: true
gui_config:
  width: 800
  height: 600
debug:
  compile: "go build -gcflags='-N -l'"
  support: true
lsp:
  start: "gopls"
console:
  color: "auto"
timeout: 30
linter_config:
  - config_path: "/path/to/config"
    type: "eslint"
    language: "javascript"
`

	_, err = tempFile.WriteString(testConfig)
	if err != nil {
		t.Fatal(err)
	}
	tempFile.Close()

	// 测试加载配置
	config, err := Load(tempFile.Name())
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证配置内容
	assert.Equal(t, []string{"test-component"}, config.Components)
	assert.Equal(t, []string{"test-feature"}, config.Features)
	assert.Equal(t, "go run main.go", config.RunCommand)
	assert.Equal(t, "go build", config.CompileCommand)
	assert.Equal(t, "go mod tidy", config.DependencyCommand)
	assert.Equal(t, "test_value", config.Env["TEST_VAR"])
	assert.True(t, config.Gui)
	assert.Equal(t, 800, config.GuiConfig.Width)
	assert.Equal(t, 600, config.GuiConfig.Height)
	assert.Equal(t, "go build -gcflags='-N -l'", config.Debug.Compile)
	assert.True(t, config.Debug.Support)
	assert.Equal(t, "gopls", config.Lsp.Start)
	assert.Equal(t, "auto", config.Console.Color)
	assert.Equal(t, 30, config.Timeout)
	assert.Len(t, config.LinterConfigs, 1)
	assert.Equal(t, "/path/to/config", config.LinterConfigs[0].ConfigPath)
	assert.Equal(t, "eslint", config.LinterConfigs[0].Type)
	assert.Equal(t, "javascript", config.LinterConfigs[0].Language)
}

func TestLoadConfigFileNotFound(t *testing.T) {
	// 测试文件不存在的情况
	_, err := Load("nonexistent_file.yaml")
	assert.Error(t, err)
}

func TestLoadConfigInvalidYAML(t *testing.T) {
	// 创建临时配置文件
	tempFile, err := os.CreateTemp("", "test_invalid_config_*.yaml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tempFile.Name())

	// 写入无效的YAML
	invalidYAML := `
components:
  - "test-component"
features:
  - "test-feature"
run_command: "go run main.go"
  invalid_indent: "this should cause an error"
`

	_, err = tempFile.WriteString(invalidYAML)
	if err != nil {
		t.Fatal(err)
	}
	tempFile.Close()

	// 测试加载无效配置
	_, err = Load(tempFile.Name())
	assert.Error(t, err)
}

func TestLoadConfigReadError(t *testing.T) {
	// 创建一个无法读取的文件（目录）
	tempDir, err := os.MkdirTemp("", "test_dir")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tempDir)

	// 尝试读取目录作为文件
	_, err = Load(tempDir)
	assert.Error(t, err)
}

func TestLoadConfigWithRunCommands(t *testing.T) {
	// 创建临时配置文件
	tempFile, err := os.CreateTemp("", "test_config_run_commands_*.yaml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tempFile.Name())

	// 写入包含run_commands的配置
	testConfig := `
components:
  - "test-component"
run_commands:
  - "go run main.go"
  - "go test ./..."
run_command: "go run main.go"
compile_command: "go build"
`

	_, err = tempFile.WriteString(testConfig)
	if err != nil {
		t.Fatal(err)
	}
	tempFile.Close()

	// 测试加载配置
	config, err := Load(tempFile.Name())
	assert.NoError(t, err)
	assert.NotNil(t, config)

	// 验证run_commands被正确设置
	assert.Equal(t, []string{"go run main.go", "go test ./..."}, config.RunCommands)
	// 当RunCommands存在时，RunCommand应该为空，因为代码逻辑是先检查RunCommands
	assert.Equal(t, "", config.RunCommand)
}

func TestGuiConfigGetWidth(t *testing.T) {
	tests := []struct {
		name     string
		config   GuiConfig
		expected int
	}{
		{
			name:     "正常宽度",
			config:   GuiConfig{Width: 1024},
			expected: 1024,
		},
		{
			name:     "零宽度",
			config:   GuiConfig{Width: 0},
			expected: 1024,
		},
		{
			name:     "负宽度",
			config:   GuiConfig{Width: -100},
			expected: 1024,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.GetWidth()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGuiConfigGetHeight(t *testing.T) {
	tests := []struct {
		name     string
		config   GuiConfig
		expected int
	}{
		{
			name:     "正常高度",
			config:   GuiConfig{Height: 768},
			expected: 768,
		},
		{
			name:     "零高度",
			config:   GuiConfig{Height: 0},
			expected: 768,
		},
		{
			name:     "负高度",
			config:   GuiConfig{Height: -100},
			expected: 768,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.GetHeight()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCommandTypeUnmarshalYAML(t *testing.T) {
	tests := []struct {
		name     string
		yamlData string
		expected []string
	}{
		{
			name:     "字符串命令",
			yamlData: "go run main.go",
			expected: []string{"go run main.go"},
		},
		{
			name: "数组命令",
			yamlData: `
- go run main.go
- go test ./...
`,
			expected: []string{"go run main.go", "go test ./..."},
		},
		{
			name: "多行字符串命令",
			yamlData: `
- go run main.go
- go test ./...
- go build
`,
			expected: []string{"go run main.go", "go test ./...", "go build"},
		},
		{
			name: "带破折号的多行字符串",
			yamlData: `- go run main.go
- go test ./...
- go build`,
			expected: []string{"go run main.go", "go test ./...", "go build"},
		},
		{
			name: "混合格式",
			yamlData: `- go run main.go
- go test ./...
- go build`,
			expected: []string{"go run main.go", "go test ./...", "go build"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var cmdType CommandType
			err := yaml.Unmarshal([]byte(tt.yamlData), &cmdType)
			assert.NoError(t, err)

			commands := cmdType.GetCommands()
			assert.Equal(t, tt.expected, commands)
		})
	}
}

func TestCommandTypeGetCommands(t *testing.T) {
	cmdType := &CommandType{
		rawValue: []interface{}{"cmd1", "cmd2", "cmd3"},
		isParsed: false,
	}

	commands := cmdType.GetCommands()
	assert.Equal(t, []string{"cmd1", "cmd2", "cmd3"}, commands)

	// 测试缓存机制
	commands2 := cmdType.GetCommands()
	assert.Equal(t, []string{"cmd1", "cmd2", "cmd3"}, commands2)
}

func TestCommandTypeGetCmd(t *testing.T) {
	cmdType := &CommandType{
		rawValue: "single command",
		isParsed: false,
	}

	cmd := cmdType.GetCmd()
	assert.Equal(t, "single command", cmd)

	// 测试缓存机制
	cmd2 := cmdType.GetCmd()
	assert.Equal(t, "single command", cmd2)
}

func TestCommandTypeParseCommandsString(t *testing.T) {
	cmdType := &CommandType{
		rawValue: "go run main.go",
		isParsed: false,
	}

	cmdType.parseCommands()
	assert.Equal(t, []string{"go run main.go"}, cmdType.commands)
	assert.Equal(t, "go run main.go", cmdType.cmd)
	assert.True(t, cmdType.isParsed)
}

func TestCommandTypeParseCommandsArray(t *testing.T) {
	cmdType := &CommandType{
		rawValue: []interface{}{"cmd1", "cmd2"},
		isParsed: false,
	}

	cmdType.parseCommands()
	assert.Equal(t, []string{"cmd1", "cmd2"}, cmdType.commands)
	assert.True(t, cmdType.isParsed)
}

func TestCommandTypeParseCommandsMultiline(t *testing.T) {
	cmdType := &CommandType{
		rawValue: "- cmd1\n- cmd2\ncmd3",
		isParsed: false,
	}

	cmdType.parseCommands()
	assert.Equal(t, []string{"cmd1", "cmd2", "cmd3"}, cmdType.commands)
	assert.True(t, cmdType.isParsed)
}

func TestCommandTypeParseCommandsUnknown(t *testing.T) {
	cmdType := &CommandType{
		rawValue: 123, // 未知类型
		isParsed: false,
	}

	cmdType.parseCommands()
	assert.Nil(t, cmdType.commands)
	assert.True(t, cmdType.isParsed)
}

func TestCommandTypeParseMultilineCommands(t *testing.T) {
	cmdType := &CommandType{}

	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "标准多行格式",
			input:    "- cmd1\n- cmd2\n- cmd3",
			expected: []string{"cmd1", "cmd2", "cmd3"},
		},
		{
			name:     "混合格式",
			input:    "- cmd1\ncmd2\n- cmd3",
			expected: []string{"cmd1", "cmd2", "cmd3"},
		},
		{
			name:     "空行",
			input:    "- cmd1\n\n- cmd2",
			expected: []string{"cmd1", "cmd2"},
		},
		{
			name:     "带空格",
			input:    "  - cmd1  \n  - cmd2  ",
			expected: []string{"cmd1", "cmd2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cmdType.parseMultilineCommands(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCommandMarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		command  Command
		expected map[string]interface{}
	}{
		{
			name: "无错误命令",
			command: Command{
				Output:   *bytes.NewBufferString("test output"),
				Err:      nil,
				Duration: 1000,
			},
			expected: map[string]interface{}{
				"output":   "test output",
				"err":      nil,
				"duration": float64(1000),
			},
		},
		{
			name: "有错误命令",
			command: Command{
				Output:   *bytes.NewBufferString("test output"),
				Err:      errors.New("test error"),
				Duration: 2000,
			},
			expected: map[string]interface{}{
				"output":   "test output",
				"err":      "test error",
				"duration": float64(2000),
			},
		},
		{
			name: "空输出",
			command: Command{
				Output:   *bytes.NewBufferString(""),
				Err:      nil,
				Duration: 0,
			},
			expected: map[string]interface{}{
				"output":   "",
				"err":      nil,
				"duration": float64(0),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := tt.command.MarshalJSON()
			assert.NoError(t, err)
			assert.NotNil(t, data)

			// 解析JSON验证内容
			var result map[string]interface{}
			err = json.Unmarshal(data, &result)
			assert.NoError(t, err)

			assert.Equal(t, tt.expected["output"], result["output"])
			assert.Equal(t, tt.expected["duration"], result["duration"])

			if tt.expected["err"] == nil {
				assert.Nil(t, result["err"])
			} else {
				assert.Equal(t, tt.expected["err"], result["err"])
			}
		})
	}
}

func TestLinterConfig(t *testing.T) {
	linterConfig := &LinterConfig{
		ConfigPath: "/path/to/config",
		Type:       "eslint",
		Language:   "javascript",
	}

	assert.Equal(t, "/path/to/config", linterConfig.ConfigPath)
	assert.Equal(t, "eslint", linterConfig.Type)
	assert.Equal(t, "javascript", linterConfig.Language)
}

func TestDebugConfig(t *testing.T) {
	debugConfig := DebugConfig{
		Compile: "go build -gcflags='-N -l'",
		Support: true,
		Launch:  map[string]interface{}{"port": 8080},
	}

	assert.Equal(t, "go build -gcflags='-N -l'", debugConfig.Compile)
	assert.True(t, debugConfig.Support)
	assert.Equal(t, map[string]interface{}{"port": 8080}, debugConfig.Launch)
}

func TestLSPConfig(t *testing.T) {
	lspConfig := LSPConfig{
		Start: "gopls",
	}

	assert.Equal(t, "gopls", lspConfig.Start)
}

func TestConsoleConfig(t *testing.T) {
	consoleConfig := ConsoleConfig{
		Color: "auto",
	}

	assert.Equal(t, "auto", consoleConfig.Color)
}

func TestUnittestConfig(t *testing.T) {
	unittestConfig := UnittestConfig{
		OutPut:        *bytes.NewBufferString("test output"),
		FrameCode:     "test frame",
		FilePath:      "/path/to/file",
		ExitCode:      0,
		RunId:         "test-run-id",
		ConsoleText:   "console text",
		Result:        "success",
		OutPutConsole: true,
		Error:         "",
	}

	assert.Equal(t, "test output", unittestConfig.OutPut.String())
	assert.Equal(t, "test frame", unittestConfig.FrameCode)
	assert.Equal(t, "/path/to/file", unittestConfig.FilePath)
	assert.Equal(t, 0, unittestConfig.ExitCode)
	assert.Equal(t, "test-run-id", unittestConfig.RunId)
	assert.Equal(t, "console text", unittestConfig.ConsoleText)
	assert.Equal(t, "success", unittestConfig.Result)
	assert.True(t, unittestConfig.OutPutConsole)
	assert.Equal(t, "", unittestConfig.Error)
}

func TestInternalRunInfo(t *testing.T) {
	internalRunInfo := InternalRunInfo{
		AutoImportCommand: Command{
			Output:   *bytes.NewBufferString("auto import output"),
			Err:      nil,
			Duration: 100,
		},
		CompileCommand: Command{
			Output:   *bytes.NewBufferString("compile output"),
			Err:      errors.New("compile error"),
			Duration: 200,
		},
		RunCommand: Command{
			Output:   *bytes.NewBufferString("run output"),
			Err:      nil,
			Duration: 300,
		},
	}

	assert.Equal(t, "auto import output", internalRunInfo.AutoImportCommand.Output.String())
	assert.Nil(t, internalRunInfo.AutoImportCommand.Err)
	assert.Equal(t, int64(100), internalRunInfo.AutoImportCommand.Duration)

	assert.Equal(t, "compile output", internalRunInfo.CompileCommand.Output.String())
	assert.Error(t, internalRunInfo.CompileCommand.Err)
	assert.Equal(t, int64(200), internalRunInfo.CompileCommand.Duration)

	assert.Equal(t, "run output", internalRunInfo.RunCommand.Output.String())
	assert.Nil(t, internalRunInfo.RunCommand.Err)
	assert.Equal(t, int64(300), internalRunInfo.RunCommand.Duration)
}

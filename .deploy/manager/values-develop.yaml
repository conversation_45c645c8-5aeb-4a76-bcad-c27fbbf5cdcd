env:
- name: O<PERSON><PERSON>_TRACES_EXPORTER
  value: otlp
- name: OTEL_EXPORTER_OTLP_ENDPOINT
  value: "http://datakit.datakit:4317"
- name: OTEL_METRICS_EXPORTER
  value: otlp
- name: OTEL_LOGS_EXPORTER
  value: none
- name: OTEL_SERVICE_NAME
  value: Manager
- name: OTEL_RESOURCE_ATTRIBUTES
  value: service.namespace=PaaS,deployment.environment=develop,env=develop
- name: OTEL_EXPORTER_OTLP_PROTOCOL
  value: grpc

ingress:
  enabled: true
  className: traefik
  hosts:
    - host: develop.clackypaas.com
      path: /api/v1/
    - host: manager.develop.clackypaas.com
      path: /

resources: 
  limits:
    memory: 2048Mi
    cpu: 4000m
  requests:
    cpu: 200m
    memory: 256Mi

pv:
  fs:
    capacity: 500Gi
    accessModes:
      - ReadWriteMany
    nfs:
      server: ***********
      path: /data/@data
  certs:
    capacity: 5Gi
    accessModes:
      - ReadWriteMany
    efs:
      fileSystemId: fs-0d0041f363427b462


autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
# Clacky AI PaaS 部署运维指南

## 部署架构选择

### 1. 开发环境部署

适用于本地开发和测试，使用 Docker Compose 快速搭建。

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  # 基础服务
  mysql:
    image: mysql:5.7.31
    command: --character-set-server=utf8 --collation-server=utf8_general_ci
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rd123456
      MYSQL_ROOT_HOST: '%'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./d42paas_manager/paas_develop.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:6.0.6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3.9.9-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: agent
      RABBITMQ_DEFAULT_PASS: d42agent
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Kong 网关
  kong-database:
    image: postgres:9.6
    environment:
      POSTGRES_USER: root
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: 123456
    volumes:
      - kong_data:/var/lib/postgresql/data

  kong:
    image: kong:latest
    depends_on:
      - kong-database
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
    ports:
      - "8000:8000"
      - "8001:8001"

  # 应用服务
  paas-manager:
    build:
      context: .
      dockerfile: d42paas_manager/Dockerfile
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - kong
    environment:
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATASOURCE_URL: ************************************
      SPRING_REDIS_HOST: redis
      SPRING_RABBITMQ_HOST: rabbitmq
    ports:
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./logs:/app/logs

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  kong_data:
```

### 2. 生产环境部署

#### Kubernetes 部署配置

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: clacky-paas

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: paas-config
  namespace: clacky-paas
data:
  application.yml: |
    spring:
      profiles:
        active: k8s
      datasource:
        url: ********************************************
        username: root
        password: ${MYSQL_PASSWORD}
      redis:
        host: redis-service
        port: 6379
      rabbitmq:
        host: rabbitmq-service
        port: 5672
        username: agent
        password: ${RABBITMQ_PASSWORD}

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: paas-secrets
  namespace: clacky-paas
type: Opaque
data:
  mysql-password: cmQxMjM0NTY=  # base64 encoded
  rabbitmq-password: ZDQyYWdlbnQ=  # base64 encoded

---
# mysql-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: clacky-paas
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:5.7.31
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: paas-secrets
              key: mysql-password
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc

---
# paas-manager-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paas-manager
  namespace: clacky-paas
spec:
  replicas: 3
  selector:
    matchLabels:
      app: paas-manager
  template:
    metadata:
      labels:
        app: paas-manager
    spec:
      containers:
      - name: paas-manager
        image: clacky/paas-manager:latest
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: paas-secrets
              key: mysql-password
        - name: RABBITMQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: paas-secrets
              key: rabbitmq-password
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: docker-socket
          mountPath: /var/run/docker.sock
      volumes:
      - name: config-volume
        configMap:
          name: paas-config
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: paas-manager-service
  namespace: clacky-paas
spec:
  selector:
    app: paas-manager
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 环境准备

### 1. 系统要求

#### 硬件要求
- **CPU**: 最低 4 核，推荐 8 核以上
- **内存**: 最低 8GB，推荐 16GB 以上
- **存储**: 最低 100GB SSD，推荐 500GB 以上
- **网络**: 千兆网卡

#### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **Kubernetes**: 1.20+ (生产环境)
- **Java**: OpenJDK 17+
- **Maven**: 3.6+

### 2. 依赖服务安装

#### Docker 安装
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 启动 Docker 服务
sudo systemctl enable docker
sudo systemctl start docker

# 验证安装
docker --version
docker-compose --version
```

#### Kubernetes 安装 (生产环境)
```bash
# 安装 kubeadm, kubelet, kubectl
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" | sudo tee /etc/apt/sources.list.d/kubernetes.list
sudo apt-get update
sudo apt-get install -y kubelet kubeadm kubectl

# 初始化集群
sudo kubeadm init --pod-network-cidr=10.244.0.0/16

# 配置 kubectl
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# 安装网络插件 (Flannel)
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
```

## 部署步骤

### 1. 开发环境快速部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/clacky-ai-paas-backend.git
cd clacky-ai-paas-backend

# 2. 启动基础服务
docker-compose -f docker-compose.dev.yml up -d mysql redis rabbitmq kong

# 3. 等待服务启动
sleep 30

# 4. 编译项目
mvn clean package -DskipTests

# 5. 启动应用
docker-compose -f docker-compose.dev.yml up -d paas-manager

# 6. 验证部署
curl http://localhost:8080/actuator/health
```

### 2. 生产环境部署

```bash
# 1. 创建命名空间和配置
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# 2. 部署存储
kubectl apply -f k8s/storage/

# 3. 部署基础服务
kubectl apply -f k8s/mysql/
kubectl apply -f k8s/redis/
kubectl apply -f k8s/rabbitmq/

# 4. 等待基础服务就绪
kubectl wait --for=condition=ready pod -l app=mysql -n clacky-paas --timeout=300s

# 5. 部署应用服务
kubectl apply -f k8s/paas-manager/

# 6. 验证部署
kubectl get pods -n clacky-paas
kubectl get services -n clacky-paas
```

## 配置管理

### 1. 环境配置

#### 开发环境配置 (application-dev.yml)
```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: rd123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

  rabbitmq:
    host: localhost
    port: 5672
    username: agent
    password: d42agent
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true

# 系统配置
system:
  docker:
    storage:
      app-path: /app
      rag-path: /rag
      volume-nix: /nix
    prefix-name: clacky
    paas-gateway-url: http://localhost:8000
  
  playground:
    inactive-seconds:
      code-zone: 1800        # 30分钟
      code-zone-copy: 3600   # 1小时
      code-zone-snapshot: 7200 # 2小时
      batch-create: 172800   # 48小时

# 日志配置
logging:
  level:
    com.dao42.paas: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30
```

#### 生产环境配置 (application-prod.yml)
```yaml
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql-service}:${DB_PORT:3306}/${DB_NAME:paas_develop}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: ${REDIS_HOST:redis-service}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 10

  rabbitmq:
    host: ${RABBITMQ_HOST:rabbitmq-service}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:agent}
    password: ${RABBITMQ_PASSWORD}
    virtual-host: ${RABBITMQ_VHOST:/}

# JVM 配置
server:
  port: 8080
  tomcat:
    threads:
      max: 200
      min-spare: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.dao42.paas: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId},%X{spanId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId},%X{spanId}] %logger{36} - %msg%n"
```

### 2. 安全配置

#### SSL/TLS 配置
```yaml
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: clacky-paas
  port: 8443
```

#### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 8080/tcp    # 应用端口
sudo ufw allow 8443/tcp    # HTTPS 端口
sudo ufw allow 3306/tcp    # MySQL (仅内网)
sudo ufw allow 6379/tcp    # Redis (仅内网)
sudo ufw allow 5672/tcp    # RabbitMQ (仅内网)
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=8443/tcp
sudo firewall-cmd --reload
```

## 监控和运维

### 1. 健康检查

```bash
# 应用健康检查
curl http://localhost:8080/actuator/health

# 数据库连接检查
mysql -h localhost -u root -p -e "SELECT 1"

# Redis 连接检查
redis-cli ping

# RabbitMQ 状态检查
rabbitmqctl status
```

### 2. 日志管理

#### 日志收集配置 (Filebeat)
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: paas-manager
    environment: production
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "paas-logs-%{+yyyy.MM.dd}"

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
```

### 3. 性能监控

#### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'paas-manager'
    static_configs:
      - targets: ['paas-manager:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
```

## 故障排查

### 1. 常见问题

#### 容器启动失败
```bash
# 检查 Docker 服务状态
sudo systemctl status docker

# 查看容器日志
docker logs <container_id>

# 检查资源使用情况
docker stats

# 检查网络连接
docker network ls
docker network inspect bridge
```

#### 数据库连接问题
```bash
# 检查 MySQL 服务状态
sudo systemctl status mysql

# 检查连接数
mysql -e "SHOW PROCESSLIST;"

# 检查慢查询
mysql -e "SHOW VARIABLES LIKE 'slow_query_log';"
tail -f /var/log/mysql/slow.log
```

#### 消息队列问题
```bash
# 检查 RabbitMQ 状态
rabbitmqctl status

# 查看队列状态
rabbitmqctl list_queues

# 查看连接状态
rabbitmqctl list_connections

# 查看消费者状态
rabbitmqctl list_consumers
```

### 2. 性能调优

#### JVM 调优
```bash
# 生产环境 JVM 参数
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/app/logs/heapdump.hprof \
     -XX:+PrintGCDetails \
     -XX:+PrintGCTimeStamps \
     -Xloggc:/app/logs/gc.log \
     -jar paas-manager.jar
```

#### 数据库调优
```sql
-- MySQL 性能优化
SET GLOBAL innodb_buffer_pool_size = 4294967296;  -- 4GB
SET GLOBAL max_connections = 1000;
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL query_cache_size = 268435456;          -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，包括配置管理、监控和故障排查等关键运维内容。

name: Deploy Manager

on:
  push:
    branches:
      - develop
      - staging
    paths:
      - .github/workflows/build-java.yml
      - .github/workflows/deploy-manager.yml
      - d42paas_common/**
      - d42paas_manager/**
      - pom.xml
      - .deploy/manager/**
  pull_request:
    branches:
      - develop
      - staging
    paths:
      - .github/workflows/build-java.yml
      - .github/workflows/deploy-manager.yml
      - d42paas_common/**
      - d42paas_manager/**
      - pom.xml
      - .deploy/manager/**

jobs:
  build-java:
    uses: ./.github/workflows/build-java.yml

  build-docker:
    needs:
      - build-java
    runs-on: ubuntu-latest
    steps:
      - name: Webhook starting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Manager [开始构建]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
      - name: Download Jar and Dockerfile
        uses: actions/download-artifact@v4
        with:
          name: Jar-and-Dockerfile

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Docker meta Manager
        id: meta-manager
        uses: docker/metadata-action@v3
        with:
          images: ghcr.io/clacky-ai/paas/manager
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
          flavor: |
            latest=auto

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_ACTOR }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Build and push Manager
        uses: docker/build-push-action@v2
        timeout-minutes: 5
        with:
          context: d42paas_manager
          file: d42paas_manager/Dockerfile
          push: true
          tags: ${{ steps.meta-manager.outputs.tags }}
          labels: ${{ steps.meta-manager.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max


  deploy-develop:
    needs:
      - build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    steps:
      # - name: Deploy Develop 
      #   run: |
      #     webhooks=(${{ secrets.WEBHOOK_DEVELOP_BACKEND_MANAGER }})
      #     for url in ${webhooks[@]}; do
      #       curl -X POST $url --insecure;
      #     done
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ vars.AWS_REGION }}

      - name: Update kubeconfig
        run: |
            aws eks update-kubeconfig --name ${{ vars.EKS_DEVELOP_NAME }} --region ${{ vars.AWS_REGION }}

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.15.0
      - name: Install helm-s3 plugin
        run: helm plugin install https://github.com/hypnoglow/helm-s3.git
      - name: Add Helm repository
        run: helm repo add dao42 s3://dao42-charts 
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Update Helm repositories
        run: helm repo update

      - name: Deploy to EKS
        run: |
          TAG="sha-$(echo ${{ github.sha }}|head -c 7)"
          helm upgrade --install paas-manager-dev dao42/paas-manager  --version=0.1.5 -n paas --set image.tag=$TAG -f .deploy/manager/values-develop.yaml


      - name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Manager **develop** [部署即将完成]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  deploy-staging:
    needs: build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ vars.AWS_REGION }}

      - name: Update kubeconfig
        run: |
            aws eks update-kubeconfig --name ${{ vars.EKS_STAGING_NAME }} --region ${{ vars.AWS_REGION }}

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.15.0
      - name: Install helm-s3 plugin
        run: helm plugin install https://github.com/hypnoglow/helm-s3.git
      - name: Add Helm repository
        run: helm repo add dao42 s3://dao42-charts 
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Update Helm repositories
        run: helm repo update

      - name: Deploy to EKS
        run: |
          TAG="sha-$(echo ${{ github.sha }}|head -c 7)"
          helm upgrade --install paas-manager-staging dao42/paas-manager  --version=0.1.5 -n paas --set image.tag=$TAG -f .deploy/manager/values-staging.yaml

      - name: Webhook supporting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Manager **Staging** [部署即将完成]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  notify_failure:
    if: failure()  
    runs-on: ubuntu-latest
    needs: 
      - deploy-develop
      - deploy-staging
      - build-docker
    steps:
      - name: Send Webhook on Failure
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.FAIL_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Manager [部署失败]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
name: _Run cov Tests

on:
  workflow_call:
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true


jobs:
  pr-coverage:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Run coverage for changed files
        id: changed-files
        run: |
          # 获取PR的基础分支
          BASE_BRANCH="${{ github.base_ref }}"
          # 获取修改的文件列表
          CHANGED_FILES=$(git diff --name-only origin/$BASE_BRANCH...HEAD | grep '\.go$' | tr '\n' ' ')
          echo "changed_files=$CHANGED_FILES" >> $GITHUB_ENV
          echo "Changed Go files: $CHANGED_FILES"
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.23'
#      -
#        name: Get tantivy
#        env:
#          rag_path: rag/tantivy_files_for_rag_testing
#          github_token: ${{ secrets.GH_TOKEN }}
#        run: |
#          curl -L -H "Accept: application/octet-stream" \
#            -H "Authorization: Bearer $github_token" \
#            -H "X-GitHub-Api-Version: 2022-11-28" \
#            -o tantivy \
#            https://api.github.com/repos/clacky-ai/tantivy-cli/releases/assets/204766293
#          chmod +x tantivy
#          mv tantivy ${GITHUB_WORKSPACE}/${rag_path}
      -
        name: Install ripgrep for grep tests
        # ripgrep is used by file/grep/grep.go for text searching and is required for TestGrep
        run: |
          sudo apt-get update && sudo apt-get install -y ripgrep
      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-mod-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-mod-
      - name: Run coverage for changed files
        run: |
          echo "mode: atomic" > coverage.out
          for file in ${{ env.changed_files }}; do
            if [[ $file == *.go ]]; then
              # Skip example and test files directories
              if [[ $file == *example* ]] || [[ $file == *testfile* ]]; then
                echo "Skipping example/test file: $file"
                continue
              fi
              echo "Running coverage for $file"
              go test -coverprofile=profile.out -covermode=atomic ./$(dirname $file)
              if [ -f profile.out ]; then
                tail -n +2 profile.out >> coverage.out
                rm profile.out
              fi
            fi
          done
          go tool cover -func=coverage.out > coverage.txt
          COVERAGE=$(grep total coverage.txt | awk '{print $3}' | sed 's/%//')
          echo "COVERAGE=$COVERAGE" >> $GITHUB_ENV

      - name: Comment PR
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const coverage = fs.readFileSync('coverage.txt', 'utf8');
            const changedFiles = process.env.changed_files || 'No Go files changed';
            
            let commentBody = '## Test Coverage Report for Changed Files\n\n';
            if (coverage.includes('No Go files changed')) {
              commentBody += 'No Go files were changed in this PR.\n';
            } else {
              // commentBody += `### Changed Files Coverage\n\`\`\`\n${coverage}\n\`\`\`\n\n`;
              commentBody += `PR Coverage: ${process.env.COVERAGE}%\n\n`;
              commentBody += `### Changed Files\n\`\`\`\n${changedFiles}\n\`\`\``;
            }
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: commentBody
            });


#  全量
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}


      - name: Run Tests
        run: |
          export paas_file_tree_ignore='.git/\.1024.nix\;.1024feature*\;.nfs*\;*.dll\;*.swp\;.paas-unit-*\;core.*\;.breakpoints\;.idea/\;.vscode/\;node_modules/'
          go test -coverprofile=coverage.out ./... && go tool cover -func=coverage.out > result.log

      - name: Upload coverage to S3
        if:  github.event_name == 'push'
        run: aws s3 cp result.log s3://llm-smoke-report/coverage/${{ github.event.repository.name }}/${GITHUB_REF#refs/heads/}.log

      - name: Download previous coverage from S3
        if:  github.event_name == 'pull_request'
        run: |
          if aws s3 cp s3://llm-smoke-report/coverage/${{ github.event.repository.name }}/${{ github.event.pull_request.base.ref }}.log previous_coverage.log ; then
            previous_coverage=$(grep -e 'total' -e 'statements' previous_coverage.log | awk '{print $3}')
          else
            previous_coverage="0%"
          fi
          current_coverage=$(grep -e 'total' -e 'statements' result.log | awk '{print $3}')
          echo "previous_coverage=$previous_coverage" >> $GITHUB_ENV
          echo "current_coverage=$current_coverage" >> $GITHUB_ENV

      - name: Comment on PR
        uses: actions/github-script@v6
        if:  github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `Previous Coverage is: **${{ env.previous_coverage }}**\nCurrent Coverage is: **${{ env.current_coverage }}**`
            })

      - name: Check coverage decrease
        if: github.event_name == 'pull_request'
        run: |
          previous_value=${previous_coverage/\%/}
          current_value=${current_coverage/\%/}
          if (( $(echo "$current_value < $previous_value" | bc -l) )); then
            echo "::error::Test coverage decreased from $previous_coverage to $current_coverage, please check and increase the coverage!"
            exit 1
          fi

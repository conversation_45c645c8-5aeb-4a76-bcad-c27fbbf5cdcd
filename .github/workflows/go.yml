name: ci

on:
  push:
    branches:
      - develop
      - staging
      - main
  pull_request:
    branches:
      - develop
      - staging
      - main

jobs:
  cov-test:
    permissions:
      actions: read # Only required for private GitHub Repo
      contents: read
      deployments: write
      pull-requests: write
      issues: write
    uses: ./.github/workflows/cov-test.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  build:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
        env:
          GO111MODULE: 'on'
      -
        name: Test Build
        run: |
          make

  deploy-develop:
    needs:
      - build
#      - cov-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
      -
        name: Build develop
        run: make build  AMQP_URL=${{ secrets.DEVELOP_AMQP_URL }}
      -
        name: Deploy develop
        uses: appleboy/scp-action@master
        with:
          host: ${{ vars.DEVELOP_DOCKER_HOST }}
          username: paas
          key: ${{ secrets.DEVELOP_SSH_KEY }}
          source: "agent"
          target: "agent"
      -
        name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"paas GoAgent **develop** [已部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"


  deploy-staging:
    needs:
      - build
#      - cov-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
      -
        name: Build staging
        run: make build AMQP_URL=${{ secrets.STAGING_AMQP_URL }} BOT_URL=${{ secrets.STAGING_BOT_URL }}

      -
        name: Deploy Staging
        uses: appleboy/scp-action@master
        with:
          host: ${{ vars.STAGING_DOCKER_HOST }}
          username: paas
          key: ${{ secrets.STAGING_SSH_KEY }}
          source: "agent"
          target: "agent"
      -
        name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"paas GoAgent **staging** [已部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  deploy-prod:
    needs:
      - build
#      - cov-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
      -
        name: Build Prod
        run: make build AMQP_URL=${{ secrets.PROD_AMQP_URL }} BOT_URL=${{ secrets.PROD_BOT_URL }}
      -
        name: Deploy Prod
        uses: appleboy/scp-action@master
        with:
          host: ${{ vars.PROD_DOCKER_HOST }}
          username: paas
          key: ${{ secrets.PROD_SSH_KEY }}
          source: "agent"
          target: "agent"
      -
        name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"paas GoAgent **prod** [已部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  notify_failure:
    if: failure()
    runs-on: ubuntu-latest
    needs:
      - deploy-develop
      - deploy-staging
      - deploy-prod
      - build
#      - cov-test
    steps:
      - name: Send Webhook on Failure
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.FAIL_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"paas GoAgent [部署失败]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

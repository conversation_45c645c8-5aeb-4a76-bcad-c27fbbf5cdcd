# 错误处理指南

本项目使用了一个自定义的错误处理包，位于 [pkg/errors/](mdc:pkg/errors/)。

## 核心概念

1.  **基于 `github.com/pkg/errors`**:
    核心错误处理功能（如 `Wrap`, `Cause`, `New`）直接从 `github.com/pkg/errors` 继承。请优先使用这些函数来添加堆栈信息和上下文。
    
    ```go
    import "github.com/Clacky-AI/clacky-ai-paas-agent/pkg/errors"

    // ...

    if err != nil {
        return errors.Wrap(err, "a new error occurred")
    }
    ```

2.  **带错误码的错误 (`CodeError`)**:
    对于需要区分错误类型的场景（如 API 返回），项目定义了 `CodeError` 接口和 `SvrError` 实现。这允许你附加一个整型 `code`。
    - 使用 `errors.NewWithCode(code, err)` 或 `errors.NewWithInfo(code, info)` 创建带错误码的错误。
    - 关键文件: [pkg/errors/error.go](mdc:pkg/errors/error.go)

3.  **Panic-based 错误处理**:
    在某些情况下，项目使用 `panic` 来处理错误，这是一种非标准的 Go 实践，但在此项目中存在。
    - `errors.Check(err)`: 如果 `err` 不为 `nil`，则会 `panic`。
    - `errors.Throw(err)`: 强制 `panic` 一个错误。
    - 请仅在明确需要中断执行流程时使用。
    - 关键文件: [pkg/errors/check.go](mdc:pkg/errors/check.go)

## 最佳实践

- 优先使用 `errors.Wrap` 而不是 `fmt.Errorf` 来保留堆栈跟踪。
- 当需要向上传递特定类型的错误时（例如，HTTP 状态码），使用 `NewWithCode`。
- 谨慎使用 `Check` 和 `Throw`，理解它们会引起 `panic`。

# 日志系统使用指南

本项目使用自定义的日志包，位于 [utils/log/](mdc:utils/log/)，基于 `golang.org/x/exp/slog` 实现。

## 日志级别

项目提供以下日志级别函数：

```go
import "agent/utils/log"

// 调试信息（仅在debug模式下输出）
log.Debugf("调试信息: %s", debugInfo)

// 一般信息
log.Infof("服务启动在端口: %d", port)

// 警告信息
log.Warnf("配置项 %s 使用默认值", configKey)

// 错误信息
log.Errorf("处理请求失败: %v", err)

// 致命错误（会调用 os.Exit(1)）
log.Fatalf("无法连接数据库: %v", err)
```

## 日志使用规范

### 1. 选择合适的日志级别

- **Debug**: 详细的调试信息，仅在开发和调试时使用
- **Info**: 一般的程序执行信息，如服务启动、重要操作完成
- **Warn**: 警告信息，程序可以继续运行但需要注意
- **Error**: 错误信息，程序遇到错误但可以恢复
- **Fatal**: 致命错误，程序无法继续运行

### 2. 日志格式化

使用格式化函数（`Debugf`, `Infof` 等）而不是直接传递字符串：

```go
// ✅ 推荐
log.Infof("用户 %s 登录成功，IP: %s", username, clientIP)

// ❌ 不推荐
log.Infof("用户登录成功")
```

### 3. 错误日志记录

记录错误时，包含足够的上下文信息：

```go
if err != nil {
    log.Errorf("处理文件 %s 时发生错误: %v", filename, err)
    return err
}
```

### 4. 避免敏感信息

不要在日志中记录密码、令牌等敏感信息：

```go
// ❌ 危险
log.Infof("用户认证: username=%s, password=%s", user, pass)

// ✅ 安全
log.Infof("用户 %s 认证成功", user)
```

## Panic 处理

项目提供了专门的 panic 处理函数：

```go
import "agent/utils/log"

// 打印 panic 信息和堆栈跟踪，然后退出程序
log.PrintPanicInfo("发生严重错误: %v", err)
```

**注意**: `PrintPanicInfo` 会调用 `os.Exit(-1)` 终止程序，仅在不可恢复的错误时使用。

## 传统日志函数

项目还保留了标准库风格的日志函数：

```go
// 使用标准库 log 包的格式
log.Printf("格式化输出: %s", message)
log.Println("简单输出", value)
```

## 最佳实践

1. **统一导入**: 始终使用 `agent/utils/log` 包
2. **结构化信息**: 在日志中包含足够的上下文信息
3. **避免过度日志**: 不要在循环中记录大量日志
4. **性能考虑**: Debug 日志在生产环境中可能被禁用
5. **错误传播**: 记录错误后，通常还需要返回错误给调用者

## 配置

日志级别可以通过环境变量控制：
- `PAAS_Log_Level=debug` - 启用调试日志

## 相关文件

- 日志实现：[utils/log/logging.go](mdc:utils/log/logging.go)
- Panic 处理：[utils/log/panic.go](mdc:utils/log/panic.go)
- 日志测试：[utils/log/logging_test.go](mdc:utils/log/logging_test.go)

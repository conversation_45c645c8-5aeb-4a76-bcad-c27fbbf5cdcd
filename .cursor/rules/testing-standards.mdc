# 测试规范指南

本项目使用 Go 标准测试框架以及 `testify` 断言库进行单元测试。

## 测试文件命名规范

- 测试文件必须以 `_test.go` 结尾
- 测试文件名应与被测试的源文件对应（例如：`utils.go` → `utils_test.go`）
- 测试文件应与被测试代码位于同一包中

## 测试函数命名规范

- 测试函数必须以 `Test` 开头，后跟被测试的函数名
- 使用驼峰命名法：`TestFunctionName`
- 对于复杂场景，可以添加描述性后缀：`TestFunctionName_SpecificScenario`

```go
func TestNewProcess(t *testing.T) { /* ... */ }
func TestProcess_SetRestartTimeout(t *testing.T) { /* ... */ }
func TestProcessManager_BasicDependency(t *testing.T) { /* ... */ }
```

## 断言库使用

项目使用 `github.com/stretchr/testify/assert` 作为主要断言库：

```go
import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestExample(t *testing.T) {
    result := someFunction()
    assert.Equal(t, expected, result)
    assert.NoError(t, err)
    assert.True(t, condition)
}
```

## 测试结构模式

### 表格驱动测试
对于多个测试用例，优先使用表格驱动测试：

```go
func TestFunction(t *testing.T) {
    tests := []struct {
        name     string
        input    string
        expected string
    }{
        {"正常情况", "input", "expected"},
        {"边界情况", "edge", "result"},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := function(tt.input)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

### 子测试
使用 `t.Run()` 创建子测试，提供更好的测试结构：

```go
func TestComplexFunction(t *testing.T) {
    t.Run("成功情况", func(t *testing.T) {
        // 测试成功路径
    })
    
    t.Run("错误情况", func(t *testing.T) {
        // 测试错误处理
    })
}
```

## 测试套件（可选）

对于复杂的集成测试，可以使用 `testify/suite`：

```go
import "github.com/stretchr/testify/suite"

type MyTestSuite struct {
    suite.Suite
}

func (suite *MyTestSuite) SetupTest() {
    // 每个测试前的设置
}

func (suite *MyTestSuite) TestSomething() {
    suite.Equal(expected, actual)
}
```

## 最佳实践

1. **测试独立性**：每个测试应该独立运行，不依赖其他测试的状态
2. **清理资源**：使用 `defer` 或 `t.Cleanup()` 确保测试后清理资源
3. **错误处理**：测试错误情况和边界条件
4. **命名清晰**：测试名称应清楚描述测试场景
5. **并发安全**：对于并发代码，编写并发安全测试

## 相关文件

- 测试相关工具：[unittest/](mdc:unittest/) - 多语言单元测试框架支持
- 示例测试：[pkg/errors/error_test.go](mdc:pkg/errors/error_test.go)
- 复杂测试套件：[proxyserver/suite_test.go](mdc:proxyserver/suite_test.go)

# 项目结构指南

- 主入口为 [main.go](mdc:main.go)
- 配置相关代码位于 [config/](mdc:config/) 目录
- 控制台相关代码位于 [console/](mdc:console/) 目录
- 常量定义在 [consts/](mdc:consts/) 目录
- HTTP 代理相关代码在 [httpProxy/](mdc:httpProxy/) 目录
- HTTP 服务端相关代码在 [httpserver/](mdc:httpserver/) 目录
- LSP 相关代码在 [ls/](mdc:ls/) 目录
- 代码检查器和 Linter 相关代码在 [linters/](mdc:linters/) 目录
- 单元测试相关代码在 [unittest/](mdc:unittest/) 目录
- 实用工具函数在 [utils/](mdc:utils/) 目录
- 其他子系统如多终端、RAG、信号监控等有独立目录

详细结构请参考各子目录下的 README 或代码注释。

package fileUtils

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFileExist(t *testing.T) {
	// 测试存在的文件
	assert.True(t, FileExist("fileUtils.go"))

	// 测试不存在的文件
	assert.False(t, FileExist("nonexistent_file_12345.go"))
}

func TestJoinPath(t *testing.T) {
	tests := []struct {
		name     string
		dir      string
		fileName string
		expected string
	}{
		{
			name:     "正常路径拼接",
			dir:      "/path/to",
			fileName: "file.txt",
			expected: "/path/to/file.txt",
		},
		{
			name:     "目录以斜杠结尾",
			dir:      "/path/to/",
			fileName: "file.txt",
			expected: "/path/to/file.txt",
		},
		{
			name:     "空目录",
			dir:      "",
			fileName: "file.txt",
			expected: "/file.txt",
		},
		{
			name:     "空文件名",
			dir:      "/path/to",
			fileName: "",
			expected: "/path/to/",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := JoinPath(tt.dir, tt.fileName)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRead(t *testing.T) {
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "test_read_*.txt")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tempFile.Name())

	// 写入测试内容
	testContent := "Hello, World!"
	_, err = tempFile.WriteString(testContent)
	if err != nil {
		t.Fatal(err)
	}
	tempFile.Close()

	// 测试读取文件
	content, err := Read(tempFile.Name())
	assert.NoError(t, err)
	assert.Equal(t, testContent, content)

	// 测试读取不存在的文件
	_, err = Read("nonexistent_file_12345.txt")
	assert.Error(t, err)
}

func TestMoveFileByCreateAndRemove(t *testing.T) {
	// 创建源文件
	sourceFile, err := os.CreateTemp("", "test_source_*.txt")
	if err != nil {
		t.Fatal(err)
	}
	sourcePath := sourceFile.Name()

	// 写入测试内容
	testContent := "Test content for move"
	_, err = sourceFile.WriteString(testContent)
	if err != nil {
		t.Fatal(err)
	}
	sourceFile.Close()

	// 创建目标路径
	destPath := filepath.Join(os.TempDir(), "test_dest_12345.txt")
	defer os.Remove(destPath)

	// 测试移动文件
	err = MoveFileByCreateAndRemove(sourcePath, destPath)
	assert.NoError(t, err)

	// 验证源文件已被删除
	assert.False(t, FileExist(sourcePath))

	// 验证目标文件存在且内容正确
	assert.True(t, FileExist(destPath))
	content, err := Read(destPath)
	assert.NoError(t, err)
	assert.Equal(t, testContent, content)
}

func TestIsTextFile(t *testing.T) {
	// 创建文本文件
	textFile, err := os.CreateTemp("", "test_text_*.txt")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(textFile.Name())

	_, err = textFile.WriteString("This is a text file")
	if err != nil {
		t.Fatal(err)
	}
	textFile.Close()

	// 测试文本文件
	assert.True(t, IsTextFile(textFile.Name()))

	// 测试不存在的文件
	assert.False(t, IsTextFile("nonexistent_file_12345.txt"))
}

func TestGetFileCrc32(t *testing.T) {
	// 创建测试文件
	testFile, err := os.CreateTemp("", "test_crc32_*.txt")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(testFile.Name())

	_, err = testFile.WriteString("Test content for CRC32")
	if err != nil {
		t.Fatal(err)
	}
	testFile.Close()

	// 测试CRC32计算
	crc32Value, err := GetFileCrc32(testFile.Name())
	assert.NoError(t, err)
	assert.NotEmpty(t, crc32Value)

	// 测试不存在的文件
	_, err = GetFileCrc32("nonexistent_file_12345.txt")
	assert.Error(t, err)
}

func TestGetDirAndFileIgnore(t *testing.T) {
	tests := []struct {
		name           string
		fileTreeIgnore string
		expectedDirs   []string
		expectedFiles  []string
	}{
		{
			name:           "空字符串",
			fileTreeIgnore: "",
			expectedDirs:   nil,
			expectedFiles:  nil,
		},
		{
			name:           "目录忽略",
			fileTreeIgnore: "node_modules/;dist/",
			expectedDirs:   []string{"node_modules", "dist"},
			expectedFiles:  nil,
		},
		{
			name:           "文件忽略",
			fileTreeIgnore: "*.log;*.tmp",
			expectedDirs:   nil,
			expectedFiles:  []string{"*.log", "*.tmp"},
		},
		{
			name:           "混合忽略",
			fileTreeIgnore: "node_modules/;*.log;dist/;*.tmp",
			expectedDirs:   []string{"node_modules", "dist"},
			expectedFiles:  []string{"*.log", "*.tmp"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirs, files := GetDirAndFileIgnore(tt.fileTreeIgnore)
			assert.Equal(t, tt.expectedDirs, dirs)
			assert.Equal(t, tt.expectedFiles, files)
		})
	}
}

func TestIsIgnoreDir(t *testing.T) {
	tests := []struct {
		name     string
		dirName  string
		ignores  []string
		expected bool
	}{
		{
			name:     "正常目录名",
			dirName:  "src",
			ignores:  []string{"node_modules", "dist"},
			expected: false,
		},
		{
			name:     "被忽略的目录",
			dirName:  "node_modules",
			ignores:  []string{"node_modules", "dist"},
			expected: true,
		},
		{
			name:     "隐藏目录",
			dirName:  ".git",
			ignores:  []string{},
			expected: true,
		},
		{
			name:     "带路径的目录名",
			dirName:  "/path/to/node_modules",
			ignores:  []string{"node_modules", "dist"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIgnoreDir(tt.dirName, tt.ignores)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsIgnoreFile(t *testing.T) {
	tests := []struct {
		name     string
		fileName string
		ignores  []string
		expected bool
	}{
		{
			name:     "正常文件名",
			fileName: "main.go",
			ignores:  []string{"*.log", "*.tmp"},
			expected: false,
		},
		{
			name:     "被忽略的文件",
			fileName: "app.log",
			ignores:  []string{"*.log", "*.tmp"},
			expected: true,
		},
		{
			name:     "隐藏文件",
			fileName: ".env",
			ignores:  []string{},
			expected: true,
		},
		{
			name:     "精确匹配",
			fileName: "config.json",
			ignores:  []string{"config.json", "*.log"},
			expected: true,
		},
		{
			name:     "nil ignores",
			fileName: "test.txt",
			ignores:  nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIgnoreFile(tt.fileName, tt.ignores)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFileTreeNodeList(t *testing.T) {
	// 创建测试节点
	dir1 := &FileTreeNode{Name: "dir1", NodeType: FILE_NODE_DIR}
	dir2 := &FileTreeNode{Name: "dir2", NodeType: FILE_NODE_DIR}
	file1 := &FileTreeNode{Name: "file1.txt", NodeType: FILE_NODE_FILE}
	file2 := &FileTreeNode{Name: "file2.txt", NodeType: FILE_NODE_FILE}

	// 测试排序
	list := FileTreeNodeList{file1, dir1, file2, dir2}

	// 验证长度
	assert.Equal(t, 4, list.Len())

	// 验证交换
	list.Swap(0, 1)
	assert.Equal(t, dir1, list[0])
	assert.Equal(t, file1, list[1])

	// 验证比较（目录应该排在文件前面）
	assert.True(t, list.Less(0, 2))  // dir1 < file2
	assert.False(t, list.Less(2, 0)) // file2 > dir1
}

package fileUtils

import (
	"agent/config"
	"agent/utils/log"
	"io/ioutil"
	"os"
	"os/user"
	"strconv"
)

func ChownRecursion(rootPath string, userName string, fileTreeIgnore string) {
	Chown(rootPath, userName)

	dirIgnores, fileIgnores := GetDirAndFileIgnore(fileTreeIgnore)
	chownChildrenDir(rootPath, userName, dirIgnores, fileIgnores)
	chownChildrenFile(rootPath, userName, fileIgnores)
}

func chownChildrenDir(path string, userName string, dirIgnores []string, fileIgnores []string) {
	fs, err := ioutil.ReadDir(path)
	if err != nil {
		return
	}

	for _, f := range fs {
		if f.IsDir() && !IsIgnoreDir(f.Name(), dirIgnores) {
			Chown(path+"/"+f.Name(), userName)

			chownChildrenDir(path+"/"+f.Name(), userName, dirIgnores, fileIgnores)
			chownChildrenFile(path+"/"+f.Name(), userName, fileIgnores)
		}
	}
}

func chownChildrenFile(path string, userName string, ignores []string) {
	fs, err := ioutil.ReadDir(path)
	if err != nil {
		return
	}
	for _, f := range fs {
		if !f.IsDir() && !IsIgnoreFile(f.Name(), ignores) {
			Chown(path+"/"+f.Name(), userName)
		}
	}
}

func Chown(targetPath string, userName string) bool {

	if config.LOCAL_DEBUG {
		return true
	}

	user, e := user.Lookup(userName)
	if e != nil {
		log.Printf("Look up user fail:%s,%s", userName, e)
		return false
	}
	uid, _ := strconv.Atoi(user.Uid)
	gid, _ := strconv.Atoi(user.Gid)

	os.Chown(targetPath, uid, gid)
	return true
}

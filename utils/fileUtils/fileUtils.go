package fileUtils

import (
	"agent/utils/log"
	"agent/utils/stringUtils"
	"fmt"
	"golang.org/x/tools/godoc/util"
	"hash/crc32"
	"io"
	"io/ioutil"
	"os"
	"regexp"
	"sort"
	"strings"
)

func GetFileTree(rootPath string, fileTreeIgnore string) *FileTreeNode {
	fileTree := FileTreeNode{}
	fileTree.Name = "/"
	fileTree.Uri = "file://" + rootPath
	fileTree.NodeType = FILE_NODE_DIR
	fileTree.Children = make([]*FileTreeNode, 0)

	dirIgnores, fileIgnores := GetDirAndFileIgnore(fileTreeIgnore)
	addChildrenDir(&fileTree, rootPath, dirIgnores, fileIgnores)
	addChildrenFile(&fileTree, rootPath, fileIgnores)
	return &fileTree
}

func GetDirAndFileIgnore(fileTreeIgnore string) ([]string, []string) {
	if fileTreeIgnore == "" {
		return nil, nil
	}

	ignroes := strings.Split(fileTreeIgnore, ";")
	var dirIgnores, fileIgnores []string
	for _, ignore := range ignroes {
		if stringUtils.HasSuffix(ignore, "/") {
			dirIgnores = append(dirIgnores, strings.TrimSuffix(ignore, "/"))
		} else {
			fileIgnores = append(fileIgnores, ignore)
		}
	}

	log.Printf("IgnoreDIR:%s", strings.Join(dirIgnores, ","))
	log.Printf("IgnoreFile:%s", strings.Join(fileIgnores, ","))
	return dirIgnores, fileIgnores
}

func addChildrenDir(fileTreeNode *FileTreeNode, path string, dirIgnores []string, fileIgnores []string) {
	fs, err := ioutil.ReadDir(path)
	if err != nil {
		return
	}

	for _, f := range fs {
		if f.IsDir() && !IsIgnoreDir(f.Name(), dirIgnores) {
			node := FileTreeNode{}
			node.NodeType = FILE_NODE_DIR
			node.Name = f.Name()
			node.Uri = JoinPath("file://"+path, f.Name())
			node.Children = make([]*FileTreeNode, 0)
			fileTreeNode.Children = append(fileTreeNode.Children, &node)

			addChildrenDir(&node, JoinPath("file://"+path, f.Name()), dirIgnores, fileIgnores)
			addChildrenFile(&node, JoinPath("file://"+path, f.Name()), fileIgnores)
		}
	}
}

func addChildrenFile(fileTree *FileTreeNode, path string, ignores []string) {
	fs, err := ioutil.ReadDir(path)
	if err != nil {
		return
	}
	for _, f := range fs {
		if !f.IsDir() && !IsIgnoreFile(f.Name(), ignores) {
			node := FileTreeNode{}
			node.NodeType = FILE_NODE_FILE
			node.Name = f.Name()
			node.Uri = JoinPath("file://"+path, f.Name())
			node.Children = make([]*FileTreeNode, 0)
			fileTree.Children = append(fileTree.Children, &node)
		}
	}
	sort.Sort(FileTreeNodeList(fileTree.Children))
}

func IsIgnoreDir(dirName string, ignores []string) bool {
	index := strings.LastIndex(dirName, "/")
	if index != -1 {
		dirName = dirName[index+1:]
	}
	if stringUtils.HasPrefix(dirName, ".") {
		return true
	}
	return stringUtils.IsContain(ignores, dirName)
}

func IsIgnoreFile(fileName string, ignores []string) bool {
	if stringUtils.HasPrefix(fileName, ".") {
		return true
	}

	if ignores == nil {
		return false
	}

	for _, ignore := range ignores {
		if strings.Contains(ignore, "*") {
			strReg := strings.Replace(ignore, ".", "\\.", -1)
			strReg = strings.Replace(strReg, "*", ".*", -1)
			isMatch, err := regexp.MatchString(strReg, fileName)
			if err != nil {
				log.Printf("MatchString:%s", err)
				return false
			}
			if isMatch {
				return true
			}
		} else {
			if ignore == fileName {
				return true
			}
		}
	}

	return false
}

func FileExist(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	return false
}

func JoinPath(dir string, name string) string {
	if strings.HasSuffix(dir, "/") {
		return dir + name
	} else {
		return dir + "/" + name
	}
}
func Read(path string) (string, error) {
	fileContent, err := os.ReadFile(path)
	return string(fileContent), err
}

func MoveFileByCreateAndRemove(sourcePath string, destPath string) error {
	inputFile, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("couldn't open source file: %s", err)
	}
	outputFile, err := os.Create(destPath)
	if err != nil {
		inputFile.Close()
		return fmt.Errorf("couldn't create output file: %s", err)
	}
	defer outputFile.Close()
	_, err = io.Copy(outputFile, inputFile)
	inputFile.Close()
	if err != nil {
		return fmt.Errorf("writing to output file failed: %s", err)
	}
	// The copy was successful, so now delete the original file
	err = os.Remove(sourcePath)
	if err != nil {
		return fmt.Errorf("failed removing original file: %s", err)
	}
	return nil
}

func IsTextFile(path string) bool {
	// 读取文件内容
	f, err := os.Open(path)
	if err != nil {
		return false
	}
	defer func(f *os.File) {
		_ = f.Close()
	}(f)

	buffer := make([]byte, 20)
	readCount, err := f.Read(buffer)
	if err != nil {
		return false
	}

	// 使用 util.IsTextFile 判断是否是文本文件
	isTextFile := util.IsText(buffer[:readCount])
	return isTextFile
}

func GetFileCrc32(filePath string) (string, error) {
	//打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	//创建一个crc32对象
	table := crc32.MakeTable(crc32.IEEE)
	hash := crc32.New(table)

	//将文件内容写入哈希对象中
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	// 计算并返回校验和
	return fmt.Sprintf("%d", hash.Sum32()), nil
}

package fileUtils

import "strings"

const (
	FILE_NODE_DIR  = "DIRECTORY"
	FILE_NODE_FILE = "FILE"
)

type FileTreeNode struct {
	Children []*FileTreeNode `json:"children"`
	NodeType string          `json:"type"`
	Name     string          `json:"name"`
	Uri      string          `json:"uri"`
}

type FileTreeNodeList []*FileTreeNode

func (fs FileTreeNodeList) Len() int {
	return len(fs)
}

func (fs FileTreeNodeList) Less(i, j int) bool {
	if fs[i].NodeType != fs[j].NodeType {
		return fs[i].NodeType == FILE_NODE_DIR
	} else {
		return strings.Compare(fs[i].Name, fs[j].Name) < 0
	}
}

func (fs FileTreeNodeList) Swap(i, j int) {
	fs[i], fs[j] = fs[j], fs[i]
}

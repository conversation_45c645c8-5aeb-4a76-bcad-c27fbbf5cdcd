package encryptUtils

import (
	"encoding/hex"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tjfoc/gmsm/sm4"
)

func TestEncryptAndDecryptWithSm4Ecb(t *testing.T) {
	// mock环境变量，使用正确的键名
	os.Setenv("paas_common_id", "claky_encryptKey")
	origin := "********************************************************"
	enc, err := EncryptWithSm4Ecb(origin)
	assert.NoError(t, err)
	assert.NotEmpty(t, enc)

	dec, err := DecryptWithSm4Ecb(enc)
	assert.NoError(t, err)
	assert.Equal(t, origin, dec)
}

func TestDecryptWithSm4Ecb(t *testing.T) {
	os.Setenv("paas_common_id", "claky_encryptKey")
	var tests = []struct {
		name          string
		key           string
		encryptedText string
		expectedText  string
		setupMocks    func()
		expectedErr   error
	}{
		{
			name:          "successDecryptWithSm4Ecb",
			key:           "claky_encryptKey",
			encryptedText: "d71f87726a64453b3aa854a89b1bceac7c1061567e8a5bdc28594247128de789765d5e7c1967b1690fef7ad6ff6a90a05506139a2f4a62a881124e55ed1afa3c",
			expectedText:  "********************************************************",
			setupMocks:    func() {},
			expectedErr:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("paas_common_id", tt.key)
			// 解码
			key := []byte(tt.key)
			dataBinary, _ := hex.DecodeString(tt.encryptedText)
			ecbDec, err := sm4.Sm4Ecb(key, dataBinary, false) //sm4Ecb模式pksc7填充解密
			decryptText := string(ecbDec)
			if err != nil {
				t.Errorf("got %v, want %v", err, tt.expectedErr)
			}
			t.Log(decryptText)
			if decryptText != tt.expectedText {
				t.Errorf("got %v, want %v", decryptText, tt.expectedText)
			}
			// 新增：直接测试封装函数
			dec, err := DecryptWithSm4Ecb(tt.encryptedText)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedText, dec)
		})
	}
}

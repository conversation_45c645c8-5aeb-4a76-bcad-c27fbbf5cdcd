package encryptUtils

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"encoding/hex"
	"github.com/tjfoc/gmsm/sm4"
)

func EncryptWithSm4Ecb(originText string) (string, error) {
	keyEnv := envUtils.GetString(consts.Sm4Key)
	key := []byte(keyEnv)
	dataBinary := []byte(originText)
	ecbMsg, err := sm4.Sm4Ecb(key, dataBinary, true) //sm4Ecb模式pksc7填充加密
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(ecbMsg), nil
}

func DecryptWithSm4Ecb(encryptText string) (string, error) {
	keyEnv := envUtils.GetString(consts.Sm4Key)
	key := []byte(keyEnv)
	dataBinary, _ := hex.DecodeString(encryptText)
	ecbDec, err := sm4.Sm4Ecb(key, dataBinary, false) //sm4Ecb模式pksc7填充解密
	if err != nil {
		log.Errorf("encryptUtils:DecryptWithSm4Ecb:%s:%s:%s", keyEnv, encryptText, err.Error())
		return "", err
	}
	return string(ecbDec), nil
}

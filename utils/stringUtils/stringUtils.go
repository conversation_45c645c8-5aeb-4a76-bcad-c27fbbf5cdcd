package stringUtils

import (
	"regexp"
	"strings"
)

func HasPrefix(s, prefix string) bool {
	return len(s) >= len(prefix) && s[0:len(prefix)] == prefix
}

func HasSuffix(s, suffix string) bool {
	return len(s) >= len(suffix) && s[len(s)-len(suffix):] == suffix
}

func IsContain(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func SplitBySpace(str string) []string {
	str = strings.TrimSpace(str)
	if str == "" {
		return []string{}
	}
	spaceRegex, _ := regexp.Compile(`\s+`)
	args := spaceRegex.Split(str, -1)
	return args
}

package stringUtils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHasPrefix(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		prefix   string
		expected bool
	}{
		{
			name:     "正常前缀",
			s:        "hello world",
			prefix:   "hello",
			expected: true,
		},
		{
			name:     "不匹配前缀",
			s:        "hello world",
			prefix:   "world",
			expected: false,
		},
		{
			name:     "空字符串",
			s:        "",
			prefix:   "hello",
			expected: false,
		},
		{
			name:     "空前缀",
			s:        "hello world",
			prefix:   "",
			expected: true,
		},
		{
			name:     "完全匹配",
			s:        "hello",
			prefix:   "hello",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := HasPrefix(tt.s, tt.prefix)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasSuffix(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		suffix   string
		expected bool
	}{
		{
			name:     "正常后缀",
			s:        "hello world",
			suffix:   "world",
			expected: true,
		},
		{
			name:     "不匹配后缀",
			s:        "hello world",
			suffix:   "hello",
			expected: false,
		},
		{
			name:     "空字符串",
			s:        "",
			suffix:   "world",
			expected: false,
		},
		{
			name:     "空后缀",
			s:        "hello world",
			suffix:   "",
			expected: true,
		},
		{
			name:     "完全匹配",
			s:        "hello",
			suffix:   "hello",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := HasSuffix(tt.s, tt.suffix)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsContain(t *testing.T) {
	tests := []struct {
		name     string
		items    []string
		item     string
		expected bool
	}{
		{
			name:     "包含元素",
			items:    []string{"apple", "banana", "orange"},
			item:     "banana",
			expected: true,
		},
		{
			name:     "不包含元素",
			items:    []string{"apple", "banana", "orange"},
			item:     "grape",
			expected: false,
		},
		{
			name:     "空切片",
			items:    []string{},
			item:     "apple",
			expected: false,
		},
		{
			name:     "空字符串元素",
			items:    []string{"apple", "", "orange"},
			item:     "",
			expected: true,
		},
		{
			name:     "查找空字符串",
			items:    []string{"apple", "banana", "orange"},
			item:     "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsContain(tt.items, tt.item)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSplitBySpace(t *testing.T) {
	tests := []struct {
		name     string
		str      string
		expected []string
	}{
		{
			name:     "正常分割",
			str:      "hello world test",
			expected: []string{"hello", "world", "test"},
		},
		{
			name:     "多个空格",
			str:      "hello   world    test",
			expected: []string{"hello", "world", "test"},
		},
		{
			name:     "前后空格",
			str:      "  hello world test  ",
			expected: []string{"hello", "world", "test"},
		},
		{
			name:     "制表符",
			str:      "hello\tworld\ttest",
			expected: []string{"hello", "world", "test"},
		},
		{
			name:     "混合空白字符",
			str:      "hello \t world \n test",
			expected: []string{"hello", "world", "test"},
		},
		{
			name:     "空字符串",
			str:      "",
			expected: []string{},
		},
		{
			name:     "只有空格",
			str:      "   \t  \n  ",
			expected: []string{},
		},
		{
			name:     "单个单词",
			str:      "hello",
			expected: []string{"hello"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SplitBySpace(tt.str)
			assert.Equal(t, tt.expected, result)
		})
	}
}

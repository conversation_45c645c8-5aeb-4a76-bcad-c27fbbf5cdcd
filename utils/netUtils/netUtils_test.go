package netUtils

import (
	"net"
	"testing"
	"time"
)

func TestIsPortInUse(t *testing.T) {
	// 测试一个未使用的端口
	unusedPort := 9999
	if IsPortInUse(unusedPort) {
		t.<PERSON><PERSON><PERSON>("端口 %d 应该未被使用", unusedPort)
	}

	// 测试一个正在使用的端口（启动一个临时服务器）
	usedPort := 8888
	listener, err := net.Listen("tcp", ":8888")
	if err != nil {
		t.Fatalf("无法启动测试服务器: %v", err)
	}
	defer listener.Close()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	if !IsPortInUse(usedPort) {
		t.<PERSON><PERSON><PERSON>("端口 %d 应该正在使用", usedPort)
	}
}

func TestConnect(t *testing.T) {
	// 测试连接到不存在的地址
	_, err := Connect("tcp", "localhost:99999", time.Millisecond*100, 1)
	if err == nil {
		t.<PERSON>rror("连接到无效地址应该返回错误")
	}

	// 测试连接到本地回环地址（应该成功）
	conn, err := Connect("tcp", "localhost:8888", time.Millisecond*100, 1)
	if err == nil {
		conn.Close()
	} else {
		// 如果8888端口不可用，尝试其他端口
		conn, err = Connect("tcp", "localhost:8889", time.Millisecond*100, 1)
		if err == nil {
			conn.Close()
		} else {
			t.Logf("无法连接到本地端口进行测试: %v", err)
		}
	}
}

func TestConnectWithRetry(t *testing.T) {
	// 测试重试机制
	start := time.Now()
	_, err := Connect("tcp", "localhost:99999", time.Millisecond*50, 3)
	duration := time.Since(start)

	if err == nil {
		t.Error("连接到无效地址应该返回错误")
	}

	// 验证重试时间（应该至少是 50ms * 2 = 100ms）
	if duration < time.Millisecond*100 {
		t.Errorf("重试时间太短，期望至少100ms，实际: %v", duration)
	}
}

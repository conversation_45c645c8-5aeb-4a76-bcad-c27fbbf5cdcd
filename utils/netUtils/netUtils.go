package netUtils

import (
	"net"
	"strconv"
	"time"
)

func IsPortInUse(port int) bool {
	hostPort := net.JoinHostPort("127.0.0.1", strconv.Itoa(port))
	conn, err := net.DialTimeout("tcp", hostPort, time.Millisecond*500)
	if conn != nil {
		defer conn.Close()
	}
	return err == nil
}

func Connect(network, address string, interval time.Duration, frequency int) (net.Conn, error) {
	var conn net.Conn
	var err error
	for i := 0; i < frequency; i++ {
		conn, err = net.Dial(network, address)
		if err == nil {
			break
		}
		time.Sleep(interval)
	}
	return conn, err
}

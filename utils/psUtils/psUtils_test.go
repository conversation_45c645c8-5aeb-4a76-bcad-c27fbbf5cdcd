package psUtils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetCpu(t *testing.T) {
	cpuInfo := GetCpu()
	assert.NotNil(t, cpuInfo)
	assert.GreaterOrEqual(t, cpuInfo.CpuLogicCores, 0)
	assert.GreaterOrEqual(t, cpuInfo.CupPercent, 0.0)
}

func TestGetMem(t *testing.T) {
	memInfo := GetMem()
	assert.NotNil(t, memInfo)
	assert.Greater(t, memInfo.MemTotal, 0.0)
	assert.GreaterOrEqual(t, memInfo.MemUsed, 0.0)
	assert.GreaterOrEqual(t, memInfo.MemUsedPercent, 0.0)
}

func TestPrintCpuAndMem(t *testing.T) {
	PrintCpuAndMem() // 只验证不会panic
}

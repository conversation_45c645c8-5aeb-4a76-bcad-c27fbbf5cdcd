package psUtils

import (
	"agent/utils/log"
	"fmt"
	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/mem"
	"time"
)

func AlwaysPrintCpuAndMem() {
	PrintCpuAndMem()
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	timeout := time.After(180 * time.Second)

	for {
		select {
		case <-ticker.C:
			PrintCpuAndMem()
		case <-timeout:
			return
		}
	}

}

func PrintCpuAndMem() {
	cpuInfo := GetCpu()
	memInfo := GetMem()
	stat := fmt.Sprintf("Cpu Cores: %d, Cpu Load: %.f%%, Mem Total: %.fMB, MemUsed: %.fMB, MemUsedPercent: %.f%%",
		cpuInfo.CpuLogicCores, cpuInfo.CupPercent, memInfo.MemTotal, memInfo.MemUsed, memInfo.MemUsedPercent)
	log.Println(stat)
}

func GetMem() *MemInfo {
	v, _ := mem.VirtualMemory()
	memInfo := &MemInfo{
		MemTotal:       float64(v.Total) / 1024 / 1024,
		MemUsed:        float64(v.Used) / 1024 / 1024,
		MemUsedPercent: v.UsedPercent,
	}
	return memInfo
}

func GetCpu() *CpuInfo {
	cpuInfo := &CpuInfo{}
	counts, err := cpu.Counts(true)
	if err != nil {
		counts = 0
	}
	cpuInfo.CpuLogicCores = counts

	percent, err := cpu.Percent(1*time.Second, false)
	if err != nil {
		return cpuInfo
	}

	if len(percent) > 0 {
		mergePercent := percent[0]
		cpuInfo.CupPercent = mergePercent
	}

	return cpuInfo
}

type CpuInfo struct {
	CpuLogicCores int
	CupPercent    float64
}

type MemInfo struct {
	MemTotal       float64
	MemUsed        float64
	MemUsedPercent float64
}

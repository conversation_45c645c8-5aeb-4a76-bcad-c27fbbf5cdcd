package envUtils

import (
	"os"
	"runtime"
	"testing"
)

func TestGetInt(t *testing.T) {
	tests := []struct {
		name     string
		envKey   string
		envValue string
		expected int
	}{
		{
			name:     "Valid integer",
			envKey:   "TEST_INT",
			envValue: "123",
			expected: 123,
		},
		{
			name:     "Empty environment variable",
			envKey:   "TEST_EMPTY",
			envValue: "",
			expected: 0,
		},
		{
			name:     "Invalid integer",
			envKey:   "TEST_INVALID",
			envValue: "abc",
			expected: 0,
		},
		{
			name:     "Zero value",
			envKey:   "TEST_ZERO",
			envValue: "0",
			expected: 0,
		},
		{
			name:     "Negative integer",
			envKey:   "TEST_NEGATIVE",
			envValue: "-456",
			expected: -456,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			if tt.envValue != "" {
				os.Setenv(tt.envKey, tt.envValue)
				defer os.Unsetenv(tt.envKey)
			} else {
				os.Unsetenv(tt.envKey)
			}

			result := GetInt(tt.envKey)
			if result != tt.expected {
				t.Errorf("GetInt() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetString(t *testing.T) {
	tests := []struct {
		name     string
		envKey   string
		envValue string
		expected string
	}{
		{
			name:     "Valid string",
			envKey:   "TEST_STRING",
			envValue: "hello world",
			expected: "hello world",
		},
		{
			name:     "Empty environment variable",
			envKey:   "TEST_EMPTY_STRING",
			envValue: "",
			expected: "",
		},
		{
			name:     "Special characters",
			envKey:   "TEST_SPECIAL",
			envValue: "!@#$%^&*()",
			expected: "!@#$%^&*()",
		},
		{
			name:     "Unicode characters",
			envKey:   "TEST_UNICODE",
			envValue: "你好世界",
			expected: "你好世界",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			if tt.envValue != "" {
				os.Setenv(tt.envKey, tt.envValue)
				defer os.Unsetenv(tt.envKey)
			} else {
				os.Unsetenv(tt.envKey)
			}

			result := GetString(tt.envKey)
			if result != tt.expected {
				t.Errorf("GetString() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestIsLocal(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		expected bool
	}{
		{
			name:     "Local environment set",
			envValue: "true",
			expected: true,
		},
		{
			name:     "Local environment not set",
			envValue: "",
			expected: false,
		},
		{
			name:     "Local environment set to any value",
			envValue: "anything",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			if tt.envValue != "" {
				os.Setenv("local", tt.envValue)
				defer os.Unsetenv("local")
			} else {
				os.Unsetenv("local")
			}

			result := IsLocal()
			if result != tt.expected {
				t.Errorf("IsLocal() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetStringOrDefault(t *testing.T) {
	tests := []struct {
		name         string
		envKey       string
		envValue     string
		defaultValue string
		expected     string
	}{
		{
			name:         "Environment variable set",
			envKey:       "TEST_DEFAULT",
			envValue:     "env_value",
			defaultValue: "default_value",
			expected:     "env_value",
		},
		{
			name:         "Environment variable not set",
			envKey:       "TEST_DEFAULT_EMPTY",
			envValue:     "",
			defaultValue: "default_value",
			expected:     "default_value",
		},
		{
			name:         "Empty default value",
			envKey:       "TEST_DEFAULT_EMPTY_DEF",
			envValue:     "",
			defaultValue: "",
			expected:     "",
		},
		{
			name:         "Both values set",
			envKey:       "TEST_DEFAULT_BOTH",
			envValue:     "env_value",
			defaultValue: "default_value",
			expected:     "env_value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			if tt.envValue != "" {
				os.Setenv(tt.envKey, tt.envValue)
				defer os.Unsetenv(tt.envKey)
			} else {
				os.Unsetenv(tt.envKey)
			}

			result := GetStringOrDefault(tt.envKey, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("GetStringOrDefault() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetNixShellCmd(t *testing.T) {
	result := GetNixShellCmd()

	if runtime.GOOS == "darwin" {
		expected := "/nix/var/nix/profiles/default/bin/nix-shell"
		if result != expected {
			t.Errorf("GetNixShellCmd() on darwin = %v, want %v", result, expected)
		}
	} else {
		expected := "nix-shell"
		if result != expected {
			t.Errorf("GetNixShellCmd() on %s = %v, want %v", runtime.GOOS, result, expected)
		}
	}
}

package envUtils

import (
	"os"
	"runtime"
	"strconv"
)

func GetInt(key string) int {
	value := os.Getenv(key)
	if len(value) == 0 {
		return 0
	} else {
		i, err := strconv.Atoi(value)
		if err != nil {
			return 0
		} else {
			return i
		}
	}
}

func GetString(key string) string {
	return os.Getenv(key)
}

func IsLocal() bool {
	return GetString("local") != ""
}

func GetStringOrDefault(key string, def string) string {
	env := os.Getenv(key)
	if env != "" {
		return env
	}
	return def
}

func GetNixShellCmd() string {
	if runtime.GOOS == "darwin" {
		return "/nix/var/nix/profiles/default/bin/nix-shell"
	} else {
		return "nix-shell"
	}
}

package nixUtils

import (
	"agent/consts"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetNixFile(t *testing.T) {
	oldRoot := consts.AppRootDirChild
	tempDir := t.TempDir()
	consts.AppRootDirChild = tempDir + string(os.PathSeparator)
	defer func() { consts.AppRootDirChild = oldRoot }()

	// 没有任何nix文件时，返回默认
	expectedDefault := filepath.Join(tempDir, ".1024nix")
	assert.Equal(t, expectedDefault, GetNixFile())

	// 创建shell.nix
	shellNix := filepath.Join(tempDir, "shell.nix")
	os.WriteFile(shellNix, []byte("test"), 0644)
	assert.Equal(t, shellNix, GetNixFile())
	os.Remove(shellNix)

	// 创建.1024.nix
	dot1024Nix := filepath.Join(tempDir, ".1024.nix")
	os.WriteFile(dot1024Nix, []byte("test"), 0644)
	assert.Equal(t, dot1024Nix, GetNixFile())
	os.Remove(dot1024Nix)

	// 创建.1024nix
	dot1024nix := filepath.Join(tempDir, ".1024nix")
	os.WriteFile(dot1024nix, []byte("test"), 0644)
	assert.Equal(t, dot1024nix, GetNixFile())
	os.Remove(dot1024nix)
}

func TestCheckDependencies(t *testing.T) {
	oldRoot := consts.AppRootDirChild
	tempDir := t.TempDir()
	consts.AppRootDirChild = tempDir + string(os.PathSeparator)
	defer func() { consts.AppRootDirChild = oldRoot }()

	nixFile := filepath.Join(tempDir, ".1024nix")
	os.WriteFile(nixFile, []byte("dep1 dep2 dep3"), 0644)

	assert.True(t, CheckDependencies("dep1"))
	assert.True(t, CheckDependencies("dep2", "dep3"))
	assert.False(t, CheckDependencies("dep4"))
}

package nixUtils

import (
	"agent/consts"
	"agent/utils/fileUtils"
	"fmt"
	"os"
	"strings"
)

var filePossible = []string{
	"shell.nix",
	".1024.nix",
	".1024nix",
}

func GetNixFile() string {
	for _, file := range filePossible {
		if fileUtils.FileExist(consts.AppRootDirChild + file) {
			return consts.AppRootDirChild + file
		}
	}

	// 返回默认值，不能返回目录，返回目录nix-shell会发生错误：Segmentation fault (core dumped)
	return fmt.Sprintf("%s.1024nix", consts.AppRootDirChild)
}

func CheckDependencies(dependencies ...string) bool {
	fileContext, err := os.ReadFile(GetNixFile())
	if err != nil {
		return false
	}
	for _, dependence := range dependencies {
		if !strings.Contains(string(fileContext), dependence) {
			return false
		}
	}
	return true
}

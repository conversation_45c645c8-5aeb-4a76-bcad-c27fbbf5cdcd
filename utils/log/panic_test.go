package log

import (
	"os"
	"os/exec"
	"testing"
)

// 只验证函数存在
func TestPrintPanicInfoFunctionExists(t *testing.T) {
	t.Log("PrintPanicInfo函数存在且可调用")
}

// 用子进程测试os.Exit
func TestPrintPanicInfo_Exit(t *testing.T) {
	if os.Getenv("TEST_PANIC_EXIT") == "1" {
		PrintPanicInfo("test exit: %s", "should exit")
		return
	}
	cmd := exec.Command(os.Args[0], "-test.run=TestPrintPanicInfo_Exit")
	cmd.Env = append(os.Environ(), "TEST_PANIC_EXIT=1")
	err := cmd.Run()
	if e, ok := err.(*exec.ExitError); ok && !e.Success() {
		t.Logf("PrintPanicInfo调用os.Exit，子进程退出码: %v", e.ExitCode())
	} else if err == nil {
		t.Error("PrintPanicInfo未能正确退出子进程")
	} else {
		t.<PERSON><PERSON><PERSON>("PrintPanicInfo测试子进程异常: %v", err)
	}
}

// 只验证不会panic
func TestPrintPanicInfoWithNilError(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("PrintPanicInfo不应该panic: %v", r)
		}
	}()
	// 不直接调用PrintPanicInfo，避免os.Exit
	t.Log("PrintPanicInfoWithNilError跳过os.Exit测试")
}

func TestPrintPanicInfoWithComplexError(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("PrintPanicInfo不应该panic: %v", r)
		}
	}()
	// 不直接调用PrintPanicInfo，避免os.Exit
	t.Log("PrintPanicInfoWithComplexError跳过os.Exit测试")
}

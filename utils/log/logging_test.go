package log

import (
	"testing"
)

func TestDebugf(t *testing.T) {
	Debugf("debug: %s %d", "test", 123)
}

func TestInfof(t *testing.T) {
	Infof("info: %s %d", "test", 456)
}

func TestWarnf(t *testing.T) {
	Warnf("warn: %s %d", "test", 789)
}

func TestErrorf(t *testing.T) {
	Errorf("error: %s %d", "test", 101)
}

func TestPrintf(t *testing.T) {
	Printf("printf: %s %d", "test", 202)
}

func TestPrintln(t *testing.T) {
	Println("println", "test", 303)
}

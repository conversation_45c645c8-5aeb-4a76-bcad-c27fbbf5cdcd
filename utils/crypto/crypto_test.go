package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBase64EncodeDecode(t *testing.T) {
	original := "hello, 世界"
	encoded := Base64Encode([]byte(original))
	decoded, err := Base64Decode(encoded)
	assert.NoError(t, err)
	assert.Equal(t, original, string(decoded))
}

func TestBase64DecodeError(t *testing.T) {
	_, err := Base64Decode("!!!not_base64!!!")
	assert.Error(t, err)
}

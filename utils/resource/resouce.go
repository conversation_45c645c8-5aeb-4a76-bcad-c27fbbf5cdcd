package resouce

import (
	"fmt"
	"github.com/pkg/errors"
	"os"
	"strconv"
	"strings"
)

var cpuMonitor platformCPUMonitor = nil

func readMemory(file string) (int, error) {
	text, err := os.ReadFile(file)
	if err != nil {
		return 0, err
	}
	split := strings.Split(string(text), "\n")
	val, err := strconv.Atoi(split[0])
	if err != nil {
		return 0, err
	}
	return val / 1024 / 1024, nil
}

func MemoryCurrent() (int, error) {
	return readMemory("/sys/fs/cgroup/memory.current")
}

func MemoryMax() (int, error) {
	return readMemory("/sys/fs/cgroup/memory.max")
}

func CpuPercent() (float64, error) {
	if cpuMonitor != nil {
		idle, err := cpuMonitor.getCPUIdle()
		if err != nil {
			return 0, err
		}
		i := fmt.Sprintf("%.2f", (float64(cpuMonitor.numCPU())-idle)*100)
		return strconv.ParseFloat(i, 64)
	}
	return 0, errors.New("no cpu monitor")
}

func init() {
	cpuMonitor, _ = newPlatformCPUMonitor()
}

package cmdUtils

import (
	"io"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"testing"
	"time"
)

func TestNewCmdUtil(t *testing.T) {
	util := NewCmdUtil()
	if util == nil {
		t.Error("NewCmdUtil() returned nil")
	}
}

func TestBuildNixShellCmd(t *testing.T) {
	tests := []struct {
		name     string
		cmd      string
		isLocal  bool
		expected []string
	}{
		{
			name:     "Local environment with simple command",
			cmd:      "echo hello",
			isLocal:  true,
			expected: []string{"sh", "-c", "echo hello"},
		},
		{
			name:     "Non-local environment with simple command",
			cmd:      "echo hello",
			isLocal:  false,
			expected: []string{"nix-shell", "shell.nix", "--no-substitute", "--run", "echo hello"},
		},
		{
			name:     "Local environment with complex command",
			cmd:      "ls -la && echo 'test'",
			isLocal:  true,
			expected: []string{"sh", "-c", "ls -la && echo 'test'"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock the IsLocal function by setting environment variable
			if tt.isLocal {
				os.Setenv("local", "true")
				defer os.Unsetenv("local")
			} else {
				os.Unsetenv("local")
			}

			result := BuildNixShellCmd(tt.cmd)

			// For non-local environment, we need to check the structure but not exact values
			// since GetNixShellCmd() and GetNixFile() depend on runtime environment
			if tt.isLocal {
				if len(result) != 3 {
					t.Errorf("BuildNixShellCmd() returned %d elements, want 3", len(result))
				}
				if result[0] != "sh" || result[1] != "-c" || result[2] != tt.cmd {
					t.Errorf("BuildNixShellCmd() = %v, want %v", result, tt.expected)
				}
			} else {
				// Check structure for non-local environment
				if len(result) != 5 {
					t.Errorf("BuildNixShellCmd() returned %d elements, want 5", len(result))
				}
				if result[2] != "--no-substitute" || result[3] != "--run" || result[4] != tt.cmd {
					t.Errorf("BuildNixShellCmd() structure incorrect, got %v", result)
				}
			}
		})
	}
}

func TestNewStdIOCommand(t *testing.T) {
	// Test with a simple command that should exist on most systems
	cmd, closer, err := NewStdIOCommand("echo", "hello")
	if err != nil {
		t.Skipf("NewStdIOCommand() error = %v, skipping test", err)
	}
	defer closer.Close()

	if cmd == nil {
		t.Error("NewStdIOCommand() returned nil command")
	}
	if closer == nil {
		t.Error("NewStdIOCommand() returned nil closer")
	}
}

func TestStdIoCommand(t *testing.T) {
	cmd := exec.Command("echo", "hello")
	resultCmd, closer, err := StdIoCommand(cmd)
	if err != nil {
		t.Skipf("StdIoCommand() error = %v, skipping test", err)
	}
	defer closer.Close()

	if resultCmd == nil {
		t.Error("StdIoCommand() returned nil command")
	}
	if closer == nil {
		t.Error("StdIoCommand() returned nil closer")
	}
}

func TestSetpgid(t *testing.T) {
	cmd := exec.Command("echo", "hello")
	Setpgid(cmd)

	if cmd.SysProcAttr == nil {
		t.Error("Setpgid() did not set SysProcAttr")
	}
	if !cmd.SysProcAttr.Setpgid {
		t.Error("Setpgid() did not set Setpgid to true")
	}
}

func TestKillByPid(t *testing.T) {
	// Test with invalid PID
	KillByPid(-1)
	// This should not panic or cause issues

	// Test with a PID that doesn't exist (should not cause issues)
	KillByPid(99999)
}

func TestKill(t *testing.T) {
	// Test with nil command
	Kill(nil)

	// Test with command that has no process
	cmd := &exec.Cmd{}
	Kill(cmd)

	// Test with a real command that we can start and kill
	if runtime.GOOS == "windows" {
		t.Skip("Skipping on Windows due to different process handling")
	}

	cmd = exec.Command("sleep", "10")
	err := cmd.Start()
	if err != nil {
		t.Skipf("Could not start test command: %v", err)
	}

	// Give it a moment to start
	time.Sleep(100 * time.Millisecond)

	Kill(cmd)

	// Wait for the process to be killed
	cmd.Wait()
}

func TestTryKill(t *testing.T) {
	// Test with nil command
	TryKill(nil)

	// Test with command that has no process
	cmd := &exec.Cmd{}
	TryKill(cmd)

	// Test with a real command that we can start and try to kill
	if runtime.GOOS == "windows" {
		t.Skip("Skipping on Windows due to different process handling")
	}

	cmd = exec.Command("sleep", "10")
	err := cmd.Start()
	if err != nil {
		t.Skipf("Could not start test command: %v", err)
	}

	// Give it a moment to start
	time.Sleep(100 * time.Millisecond)

	TryKill(cmd)

	// Wait for the process to be killed
	cmd.Wait()
}

func TestLookPath(t *testing.T) {
	tests := []struct {
		name     string
		file     string
		expected bool
	}{
		{
			name:     "Existing command",
			file:     "echo",
			expected: true,
		},
		{
			name:     "Non-existing command",
			file:     "nonexistentcommand12345",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := LookPath(tt.file)
			if result != tt.expected {
				t.Errorf("LookPath(%s) = %v, want %v", tt.file, result, tt.expected)
			}
		})
	}
}

func TestLookPaths(t *testing.T) {
	tests := []struct {
		name     string
		files    []string
		expected []string
	}{
		{
			name:     "All existing commands",
			files:    []string{"echo", "ls"},
			expected: []string{},
		},
		{
			name:     "Mixed existing and non-existing commands",
			files:    []string{"echo", "nonexistentcommand12345", "ls"},
			expected: []string{"nonexistentcommand12345"},
		},
		{
			name:     "All non-existing commands",
			files:    []string{"nonexistentcommand12345", "anothernonexistent"},
			expected: []string{"nonexistentcommand12345", "anothernonexistent"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := LookPaths(tt.files...)

			// Sort both slices for comparison
			if len(result) != len(tt.expected) {
				t.Errorf("LookPaths() returned %d items, want %d", len(result), len(tt.expected))
			}

			// Check if all expected items are in result
			for _, expected := range tt.expected {
				found := false
				for _, actual := range result {
					if actual == expected {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("LookPaths() missing expected item: %s", expected)
				}
			}
		})
	}
}

func TestLookPathsError(t *testing.T) {
	tests := []struct {
		name        string
		files       []string
		expectError bool
	}{
		{
			name:        "All existing commands",
			files:       []string{"echo", "ls"},
			expectError: false,
		},
		{
			name:        "Some non-existing commands",
			files:       []string{"echo", "nonexistentcommand12345"},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := LookPathsError(tt.files...)

			if tt.expectError && err == nil {
				t.Error("LookPathsError() expected error but got nil")
			}

			if !tt.expectError && err != nil {
				t.Errorf("LookPathsError() unexpected error: %v", err)
			}

			if tt.expectError && err != nil {
				// Check error message format
				errorMsg := err.Error()
				if !strings.Contains(errorMsg, "缺少依赖") {
					t.Errorf("LookPathsError() error message does not contain expected text: %s", errorMsg)
				}
			}
		})
	}
}

func TestCmdRWCloser(t *testing.T) {
	// Test cmdRWCloser struct creation and methods
	cmd := exec.Command("echo", "hello")
	stdin, _ := cmd.StdinPipe()
	stdout, _ := cmd.StdoutPipe()

	closer := &cmdRWCloser{
		Cmd:    cmd,
		Reader: stdout,
		Writer: stdin,
	}

	// Test that it implements the interface
	var _ interface {
		io.Reader
		io.Writer
		io.Closer
	} = closer

	// Test Close method
	err := closer.Close()
	// This might fail if the command is not started, but should not panic
	if err != nil && !strings.Contains(err.Error(), "unable to wait on cmd") {
		t.Errorf("Unexpected error from Close(): %v", err)
	}
}

package cmdUtils

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/nixUtils"
	"github.com/pkg/errors"
	"io"
	"os"
	"os/exec"
	"strings"
	"syscall"
	"time"
)

type CmdUtilIface interface {
	StartStdIOCommand(name string, arg ...string) (io.ReadWriteCloser, error)
	StartStdIOCommandExec(cmd *exec.Cmd) (io.ReadWriteCloser, error)
}

type cmdUtil struct{}

// 构造展示
func NewCmdUtil() CmdUtilIface {
	return &cmdUtil{}
}

func (c *cmdUtil) StartStdIOCommand(name string, arg ...string) (io.ReadWriteCloser, error) {
	cmd, closer, err := NewStdIOCommand(name, arg...)
	if err != nil {
		return nil, err
	}
	if err := cmd.Start(); err != nil {
		return nil, errors.Wrap(err, "failed to start cmd")
	}
	return closer, nil
}

func (c *cmdUtil) StartStdIOCommandExec(cmd *exec.Cmd) (io.ReadWriteCloser, error) {
	cmd, closer, err := StdIoCommand(cmd)
	if err != nil {
		return nil, err
	}
	if err := cmd.Start(); err != nil {
		return nil, errors.Wrap(err, "failed to start cmd")
	}

	return closer, nil
}

func BuildNixShellCmd(cmd string) []string {
	// sh
	if envUtils.IsLocal() {
		return []string{"sh", "-c", cmd}
	}

	return []string{envUtils.GetNixShellCmd(), nixUtils.GetNixFile(), "--no-substitute", "--run", cmd}
}

func NewStdIOCommand(c string, arg ...string) (*exec.Cmd, io.ReadWriteCloser, error) {
	cmd := exec.Command(c, arg...)

	return StdIoCommand(cmd)
}

func StdIoCommand(cmd *exec.Cmd) (*exec.Cmd, io.ReadWriteCloser, error) {
	Setpgid(cmd)
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create stdin pipe")
	}

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create stdout pipe")
	}

	cmd.Stderr = os.Stderr
	cmd.Dir = consts.AppRootDir

	return cmd, &cmdRWCloser{
		Cmd:    cmd,
		Reader: stdout,
		Writer: stdin,
	}, nil
}

func StartStdIOCommand(name string, arg ...string) (io.ReadWriteCloser, error) {
	cmd, closer, err := NewStdIOCommand(name, arg...)
	if err != nil {
		return nil, err
	}
	if err := cmd.Start(); err != nil {
		return nil, errors.Wrap(err, "failed to start cmd")
	}
	return closer, nil
}

func StartStdIOCommandExec(cmd *exec.Cmd) (io.ReadWriteCloser, error) {
	cmd, closer, err := StdIoCommand(cmd)
	if err != nil {
		return nil, err
	}
	if err := cmd.Start(); err != nil {
		return nil, errors.Wrap(err, "failed to start cmd")
	}
	return closer, nil
}

type cmdRWCloser struct {
	*exec.Cmd

	// Reader and Writer do not need to be Closers since they are StdoutPipe
	// and StdinPipe respectively. Both of those will be closed by cmd.Wait.
	io.Reader
	io.Writer
}

func (c *cmdRWCloser) Close() error {
	Kill(c.Cmd)
	if err := c.Cmd.Wait(); err != nil {
		return errors.Wrap(err, "unable to wait on cmd to finish during cmdRWCloser.Close()")
	}
	return nil
}

// pty不要使用
func Setpgid(cmd *exec.Cmd) {
	cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
}

func KillByPid(pid int) {
	if pid == -1 {
		return
	}
	syscall.Kill(-pid, syscall.SIGKILL)
	syscall.Kill(pid, syscall.SIGKILL)
}

func Kill(cmd *exec.Cmd) {
	if cmd == nil || cmd.Process == nil {
		return
	}
	syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
	syscall.Kill(cmd.Process.Pid, syscall.SIGKILL)
}

func TryKill(cmd *exec.Cmd) {
	if cmd == nil || cmd.Process == nil {
		return
	}
	syscall.Kill(-cmd.Process.Pid, syscall.SIGTERM)
	syscall.Kill(cmd.Process.Pid, syscall.SIGTERM)
	for i := 0; i < 10; i++ {
		if cmd.ProcessState != nil {
			return
		}
		time.Sleep(time.Millisecond * 100)
	}
	syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
	syscall.Kill(cmd.Process.Pid, syscall.SIGKILL)

}

func LookPathsError(files ...string) error {
	paths := LookPaths(files...)
	var err error
	if len(paths) != 0 {
		return errors.New("缺少依赖 : " + strings.Join(paths, "、"))
	}
	return err
}

func LookPaths(files ...string) []string {
	var arr []string
	for _, file := range files {
		if !LookPath(file) {
			arr = append(arr, file)
		}
	}
	return arr
}

func LookPath(file string) bool {
	_, err := exec.LookPath(file)
	return err == nil
}

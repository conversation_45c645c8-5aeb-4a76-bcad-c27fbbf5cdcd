package consts

const (
	// 环境变量Key
	PAAS_DockerId     = "paas_docker_id"
	PAAS_PlaygroundId = "paas_playground_id"

	// MQ配置
	PAAS_ExchangeName = "paas_exchange_name"

	// 程序根目录的路径
	PAAS_AppPath        = "paas_app_path"
	PAAS_RagPath        = "paas_rag_path"
	PAAS_FileTreeIgnore = "paas_file_tree_ignore"
	PAAS_FileRagIgnore  = "paas_file_rag_ignore"
	PAAS_RunCmd         = "paas_run_cmd"

	// 变成非活跃的秒数
	PAAS_InactiveSeconds = "paas_inactive_seconds"

	PAAS_Language         = "paas_language"
	PAAS_LanguageVersion  = "paas_language_version"
	PAAS_Framework        = "paas_framework"
	PAAS_FrameworkVersion = "paas_framework_version"
	PAAS_Url              = "paas_url"

	PAAS_InstallNixCmd  = "paas_install_nix_cmd"
	PAAS_LspLanguageId  = "paas_lsp_language_id"
	PAAS_LspLanguageIds = "paas_lsp_language_ids"
	PAAS_LspStartCmd    = "paas_lsp_start_cmd"
	PAAS_LspStartCmds   = "paas_lsp_start_cmds"
	// LSP提供服务的地址
	PAAS_LspURL      = "paas_lsp_url"
	PAAS_Lsp_Port    = "paas_lsp_port"
	PAAS_ServicePort = "paas_service_port"

	// 配置文件的文件名
	PAAS_ConfigFileName = "paas_config_file_name"

	// consoleStartCmd
	PAAS_ConsoleStartCmd = "paas_console_start_cmd"
	PAAS_Shell_Cmd       = "paas_shell_cmd"
	PASS_Shell_Cmd_Type  = "paas_shell_cmd_type"
	INIT_Shell_Cmd       = "paas_init_cmd"

	PAAS_Log_Level = "paas_log_level"

	DebugStartCmd = "paas_debug_start_cmd"

	DebugServerPort = "debug_server_port" // 内部 debug服务端port

	PaasConfigDir = "paas_config_dir"

	PaasResourceMonitoring = "paas_resource_monitoring"

	// LlmEncryptKey LLM密钥
	LlmEncryptKey = "paas_resource_id"
	// Sm4Key 加密密钥SM4
	Sm4Key = "paas_common_id"

	EncryptPaasAuthToken = "paas_auth_token"
	EncryptPaasBaseURL   = "paas_base_url"

	MYSQL_HOST = "MYSQL_HOST"
	MYSQL_PORT = "MYSQL_PORT"

	REDIS_HOST = "REDIS_HOST"
	REDIS_PORT = "REDIS_PORT"

	POSTGRE_SQL_HOST = "POSTGRE_SQL_HOST"
	POSTGRE_SQL_PORT = "POSTGRE_SQL_PORT"

	MONGO_HOST = "MONGO_HOST"
	MONGO_PORT = "MONGO_PORT"

	PaasGatewayUrl     = "paas_gateway_url"
	PaasProjectWebPort = "paas_project_web_port"

	PaasJavaLspIsSupport = "paas_java_lsp_is_support"

	CLACKYAI_CONFIG_PATH = "/home/<USER>/.clackyai/.environments.yaml"
)

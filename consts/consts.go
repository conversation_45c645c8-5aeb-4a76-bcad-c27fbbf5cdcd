package consts

import "github.com/sashabaranov/go-openai"

const (
	MQ_CONOSOLE_PREFIX_STATUS    = "status:"
	MQ_CONOSOLE_PREFIX_CONTENT   = "content:"
	MQ_CONOSOLE_PREFIX_PORT_OPEN = "portOpen:"

	CONSOLE_STATUS_RUN  = "run"
	CONSOLE_STATUS_STOP = "stop"
	CONSOLE_RUN_SUCCESS = "0"
	CONSOLE_RUN_FAIL    = "1"

	NIX_SHELL = "nix-shell"

	MQ_XTERM_PREFIX_SIZE             = "size:"
	MQ_TERMINAL_PREFIX_STATUS        = "status:"
	MQ_TERMINAL_PREFIX_CONTENT       = "content:"
	MQ_TERMINAL_PREFIX_CMD           = "cmd:"
	MQ_TERMINAL_PREFIX_CMD_REPLY     = "cmdReply:"
	MQ_TERMINAL_PREFIX_CMD_TERMINALS = "cmdTerminalList:"
	MQ_TERMINAL_PREFIX_KILL          = "kill:"
	MQ_TERMINAL_PREFIX_PROCESSNAME   = "processName:"

	CONSOLE_STATUS_DEBUG_RUN    = "debugRun"
	MQ_CONSOLE_PREFIX_DEBUG     = "debugDAP:"
	MQ_CONSOLE_PREFIX_EXECUTION = "execution:"
	MQ_LSP_START                = "lspStart"
	ENVLIST                     = "envList:"
	NEW_LINE_SIGN               = "PaasNewLineSign"
	OUT_PUT_LIMIT               = 50000
	CONSOLE_STATUS_UNITTEST_RUN = "unittest"
	UNITTEST_FILE               = ".paas-unit-"

	HTTP_PROXY_HOOK_SIGN   = "HttpProxy:hook"
	HTTP_PROXY_PREFIX      = "httpProxy:"
	AVAILABLE_PORTS_PREFIX = "availablePorts:"
	PORTS_CHANGED_PREFIX   = "portsChanged:"
)

const (
	LanguageBash           = "bash"
	LanguageC              = "c"
	LanguageCUE            = "cue"
	LanguageCSS            = "css"
	LanguageCPP            = "c++"
	LanguageCSharp         = "c#"
	LanguageDart           = "dart"
	LanguageDockerFile     = "dockerfile"
	LanguageErlang         = "erlang"
	LanguageElixir         = "elixir"
	LanguageElm            = "elm"
	LanguageGo             = "go"
	LanguageGroovy         = "groovy"
	LanguageHcl            = "hcl"
	LanguageHtml           = "html"
	LanguageJava           = "java"
	LanguageJavaScript     = "javascript"
	LanguageKotlin         = "kotlin"
	LanguageLua            = "lua"
	LanguageMarkDown       = "markdown"
	LanguageMarkDownInline = "markdown-inline"
	LanguageOcaml          = "ocaml"
	LanguageNode           = "node"
	LanguagePHP            = "php"
	LanguageProtobuf       = "protobuf"
	LanguagePython         = "python"
	LanguageRuby           = "ruby"
	LanguageRust           = "rust"
	LanguageScala          = "scala"
	LanguageSql            = "sql"
	LanguageSvelte         = "svelte"
	LanguageSwift          = "swift"
	LanguageToml           = "toml"
	LanguageTS             = "typescript"
	LanguageYaml           = "yaml"
)

const (
	FileChangeChmod  = -2
	FileChangeUpdate = 0
	FileChangeCreate = 1
	FileChangeRemove = 2
	FileType         = 0
	DirType          = 1
)

const (
	ChunkPath          = "/chunks"
	ChunkSuffix        = ".chunk"
	GatherFile         = "/gather.json"
	TantivyCommand     = "/tantivy"
	TantivyIndexName   = "rag-index"
	TantivyResultLimit = 20
)

const (
	GptEmbeddingModel = openai.SmallEmbedding3
	GptMaxTokenLen    = 8192
)

const (
	// SplitFileHighWaterMark 单次切片最多切的字符数
	SplitFileHighWaterMark int = 2000

	SplitFileLspAvgCharInLine uint32 = 60
	SplitFileLspMaxChars      uint32 = SplitFileLspAvgCharInLine * 20
	SplitFileLspCoalesce             = 80
)

const (
	RagVectorSearchWeight    = 2
	RagResultPercentileFloor = 0.3
)

const (
	CaddyAdminPort = 12019

	CaddySeverPort  = "10087"
	CaddyServerName = "proxy"

	LSPServerPort       = "45678"
	LSPServerPathPrefix = "/lsp"

	AgentServerPort       = "10088"
	AgentServerPathPrefix = "/agent"

	VNCServerPort       = "26080"
	VNCServerPathPrefix = "/vncproxy"

	TigerVNCServerPort = "25901"
	ChromeDebugPort    = "29229"
)

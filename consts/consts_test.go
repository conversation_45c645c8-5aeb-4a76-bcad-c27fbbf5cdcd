package consts

import (
	"testing"
)

func TestMQConstants(t *testing.T) {
	// Test MQ console prefixes
	if MQ_CONOSOLE_PREFIX_STATUS != "status:" {
		t.<PERSON>rrorf("MQ_CONOSOLE_PREFIX_STATUS = %v, want status:", MQ_CONOSOLE_PREFIX_STATUS)
	}
	if MQ_CONOSOLE_PREFIX_CONTENT != "content:" {
		t.<PERSON><PERSON>rf("MQ_CONOSOLE_PREFIX_CONTENT = %v, want content:", MQ_CONOSOLE_PREFIX_CONTENT)
	}
	if MQ_CONOSOLE_PREFIX_PORT_OPEN != "portOpen:" {
		t.Errorf("MQ_CONOSOLE_PREFIX_PORT_OPEN = %v, want portOpen:", MQ_CONOSOLE_PREFIX_PORT_OPEN)
	}
}

func TestConsoleStatusConstants(t *testing.T) {
	// Test console status constants
	if CONSOLE_STATUS_RUN != "run" {
		t.<PERSON><PERSON><PERSON>("CONSOLE_STATUS_RUN = %v, want run", CONSOLE_STATUS_RUN)
	}
	if CONSOLE_STATUS_STOP != "stop" {
		t.<PERSON>rrorf("CONSOLE_STATUS_STOP = %v, want stop", CONSOLE_STATUS_STOP)
	}
	if CONSOLE_RUN_SUCCESS != "0" {
		t.Errorf("CONSOLE_RUN_SUCCESS = %v, want 0", CONSOLE_RUN_SUCCESS)
	}
	if CONSOLE_RUN_FAIL != "1" {
		t.Errorf("CONSOLE_RUN_FAIL = %v, want 1", CONSOLE_RUN_FAIL)
	}
}

func TestNixShellConstant(t *testing.T) {
	if NIX_SHELL != "nix-shell" {
		t.Errorf("NIX_SHELL = %v, want nix-shell", NIX_SHELL)
	}
}

func TestMQTerminalConstants(t *testing.T) {
	// Test MQ terminal prefixes
	if MQ_XTERM_PREFIX_SIZE != "size:" {
		t.Errorf("MQ_XTERM_PREFIX_SIZE = %v, want size:", MQ_XTERM_PREFIX_SIZE)
	}
	if MQ_TERMINAL_PREFIX_STATUS != "status:" {
		t.Errorf("MQ_TERMINAL_PREFIX_STATUS = %v, want status:", MQ_TERMINAL_PREFIX_STATUS)
	}
	if MQ_TERMINAL_PREFIX_CONTENT != "content:" {
		t.Errorf("MQ_TERMINAL_PREFIX_CONTENT = %v, want content:", MQ_TERMINAL_PREFIX_CONTENT)
	}
	if MQ_TERMINAL_PREFIX_CMD != "cmd:" {
		t.Errorf("MQ_TERMINAL_PREFIX_CMD = %v, want cmd:", MQ_TERMINAL_PREFIX_CMD)
	}
}

func TestLanguageConstants(t *testing.T) {
	// Test some key language constants
	if LanguageGo != "go" {
		t.Errorf("LanguageGo = %v, want go", LanguageGo)
	}
	if LanguagePython != "python" {
		t.Errorf("LanguagePython = %v, want python", LanguagePython)
	}
	if LanguageJavaScript != "javascript" {
		t.Errorf("LanguageJavaScript = %v, want javascript", LanguageJavaScript)
	}
	if LanguageJava != "java" {
		t.Errorf("LanguageJava = %v, want java", LanguageJava)
	}
	if LanguageBash != "bash" {
		t.Errorf("LanguageBash = %v, want bash", LanguageBash)
	}
}

func TestFileChangeConstants(t *testing.T) {
	// Test file change constants
	if FileChangeChmod != -2 {
		t.Errorf("FileChangeChmod = %v, want -2", FileChangeChmod)
	}
	if FileChangeUpdate != 0 {
		t.Errorf("FileChangeUpdate = %v, want 0", FileChangeUpdate)
	}
	if FileChangeCreate != 1 {
		t.Errorf("FileChangeCreate = %v, want 1", FileChangeCreate)
	}
	if FileChangeRemove != 2 {
		t.Errorf("FileChangeRemove = %v, want 2", FileChangeRemove)
	}
}

func TestTypeConstants(t *testing.T) {
	// Test type constants
	if FileType != 0 {
		t.Errorf("FileType = %v, want 0", FileType)
	}
	if DirType != 1 {
		t.Errorf("DirType = %v, want 1", DirType)
	}
}

func TestChunkConstants(t *testing.T) {
	// Test chunk related constants
	if ChunkPath != "/chunks" {
		t.Errorf("ChunkPath = %v, want /chunks", ChunkPath)
	}
	if ChunkSuffix != ".chunk" {
		t.Errorf("ChunkSuffix = %v, want .chunk", ChunkSuffix)
	}
	if GatherFile != "/gather.json" {
		t.Errorf("GatherFile = %v, want /gather.json", GatherFile)
	}
	if TantivyCommand != "/tantivy" {
		t.Errorf("TantivyCommand = %v, want /tantivy", TantivyCommand)
	}
	if TantivyIndexName != "rag-index" {
		t.Errorf("TantivyIndexName = %v, want rag-index", TantivyIndexName)
	}
	if TantivyResultLimit != 20 {
		t.Errorf("TantivyResultLimit = %v, want 20", TantivyResultLimit)
	}
}

func TestGPTConstants(t *testing.T) {
	// Test GPT related constants
	if GptMaxTokenLen != 8192 {
		t.Errorf("GptMaxTokenLen = %v, want 8192", GptMaxTokenLen)
	}
}

func TestSplitFileConstants(t *testing.T) {
	// Test split file constants
	if SplitFileHighWaterMark != 2000 {
		t.Errorf("SplitFileHighWaterMark = %v, want 2000", SplitFileHighWaterMark)
	}
	if SplitFileLspAvgCharInLine != 60 {
		t.Errorf("SplitFileLspAvgCharInLine = %v, want 60", SplitFileLspAvgCharInLine)
	}
	if SplitFileLspMaxChars != 1200 {
		t.Errorf("SplitFileLspMaxChars = %v, want 1200", SplitFileLspMaxChars)
	}
	if SplitFileLspCoalesce != 80 {
		t.Errorf("SplitFileLspCoalesce = %v, want 80", SplitFileLspCoalesce)
	}
}

func TestRagConstants(t *testing.T) {
	// Test RAG related constants
	if RagVectorSearchWeight != 2 {
		t.Errorf("RagVectorSearchWeight = %v, want 2", RagVectorSearchWeight)
	}
	if RagResultPercentileFloor != 0.3 {
		t.Errorf("RagResultPercentileFloor = %v, want 0.3", RagResultPercentileFloor)
	}
}

func TestPortConstants(t *testing.T) {
	// Test port constants
	if CaddyAdminPort != 12019 {
		t.Errorf("CaddyAdminPort = %v, want 12019", CaddyAdminPort)
	}
	if CaddySeverPort != "10087" {
		t.Errorf("CaddySeverPort = %v, want 10087", CaddySeverPort)
	}
	if CaddyServerName != "proxy" {
		t.Errorf("CaddyServerName = %v, want proxy", CaddyServerName)
	}
	if LSPServerPort != "45678" {
		t.Errorf("LSPServerPort = %v, want 45678", LSPServerPort)
	}
	if LSPServerPathPrefix != "/lsp" {
		t.Errorf("LSPServerPathPrefix = %v, want /lsp", LSPServerPathPrefix)
	}
	if AgentServerPort != "10088" {
		t.Errorf("AgentServerPort = %v, want 10088", AgentServerPort)
	}
	if AgentServerPathPrefix != "/agent" {
		t.Errorf("AgentServerPathPrefix = %v, want /agent", AgentServerPathPrefix)
	}
	if VNCServerPort != "26080" {
		t.Errorf("VNCServerPort = %v, want 26080", VNCServerPort)
	}
	if VNCServerPathPrefix != "/vncproxy" {
		t.Errorf("VNCServerPathPrefix = %v, want /vncproxy", VNCServerPathPrefix)
	}
	if TigerVNCServerPort != "25901" {
		t.Errorf("TigerVNCServerPort = %v, want 25901", TigerVNCServerPort)
	}
	if ChromeDebugPort != "29229" {
		t.Errorf("ChromeDebugPort = %v, want 29229", ChromeDebugPort)
	}
}

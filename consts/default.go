package consts

import (
	"agent/utils/envUtils"
	"os"
	"time"
)

var (
	configRoot = envUtils.GetStringOrDefault(PaasConfigDir, func() string {
		if dir, err := os.UserHomeDir(); err == nil {
			return dir
		}
		return ""
	}())
	DebugJavaJar = configRoot + "/java/com.microsoft.java.debug.plugin.jar"
	NoVncDir     = configRoot + "/noVNC"
	DebugPort    = 34567
)

var (
	AppRootDir      = envUtils.GetString(PAAS_AppPath)
	AppRootDirChild = AppRootDir + "/"
	AppRagDir       = envUtils.GetString(PAAS_RagPath)
)

// lsp相关
var (
	JavaLibDir           = AppRootDirChild + "lib"
	JavaLibWatchInterval = time.Second * 10
)

package console

import (
	"agent/utils/log"
	"github.com/dao42/go-dap"
)

func (wrapper *Wrapper) debugInteractHandler(data string) {
	message, err := dap.DecodeProtocolMessage([]byte(data))
	if err != nil {
		log.Errorf("dap message : %s error : %v", data, err)
		return
	}
	ssize := len(wrapper.debugInfo.sessions)
	if ssize == 0 {
		return
	}
	// 默认取最后一个
	conn := wrapper.debugInfo.sessions[ssize-1].conn
	switch message := message.(type) {
	case *dap.SetBreakpointsListEvent:
		setBreakpointsListEventHandler(wrapper, message)
	case *dap.StepOutRequest:
		stepOutRequestHandler(conn, message)
	case dap.RequestMessage:
		conn.Send(message)
	}
}

func stepOutRequestHandler(conn *dap.Conn, message *dap.StepOutRequest) {
	var resp dap.StepOutResponse
	err := conn.SendRequest(message, &resp)
	if err != nil || !resp.Success {
		conn.Send(&dap.ContinueRequest{
			Request: dap.NewRequest("continue"),
			Arguments: dap.ContinueArguments{
				ThreadId:     message.Arguments.ThreadId,
				SingleThread: message.Arguments.SingleThread,
			},
		})
	}
}

func setBreakpointsListEventHandler(wrapper *Wrapper, message *dap.SetBreakpointsListEvent) {
	for _, session := range wrapper.debugInfo.sessions {
		conn := session.conn
		for _, argument := range message.Arguments {
			conn.Send(&dap.SetBreakpointsRequest{
				Request:   dap.NewRequest("setBreakpoints"),
				Arguments: argument,
			})
		}
		if !session.configurationDone {
			conn.Send(&dap.ConfigurationDoneRequest{
				Request:   dap.NewRequest("configurationDone"),
				Arguments: dap.ConfigurationDoneArguments{},
			})
			session.configurationDone = true
		}
	}

}

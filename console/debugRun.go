package console

import (
	"agent/consts"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/netUtils"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"path/filepath"
	"strings"
	"time"
)

var debugParameterHandlerFactory = map[string]func(wrapper *Wrapper, cmd *string, port *int, runTerminal *bool) error{
	consts.LanguageJava: javaDebugParameterHandler,
	consts.LanguageGo:   goDebugParameterHandler,
	consts.LanguageRuby: rubyDebugParameterHandler,
}

func rubyDebugParameterHandler(wrapper *Wrapper, cmd *string, port *int, terminal *bool) error {
	*terminal = false
	script, ok := wrapper.Config.Debug.Launch["script"]
	if !ok {
		return errors.New("script不能为空")
	}
	run := fmt.Sprintf("%v", script)
	if run == "" {
		return errors.New("script不能为空")
	}
	*cmd += " " + run
	return nil
}

func goDebugParameterHandler(wrapper *Wrapper, cmd *string, port *int, terminal *bool) error {
	*terminal = false
	return nil
}

func javaDebugParameterHandler(wrapper *Wrapper, cmd *string, port *int, terminal *bool) error {
	*port = envUtils.GetInt(consts.DebugServerPort)
	*cmd = ""
	if *port < 1 {
		return errors.New("Java Debug服务目前未启动，请尝试重启LSP(代码提示)服务")
	}
	return nil
}

func (wrapper *Wrapper) doDebugRun() error {
	if !wrapper.Config.Debug.Support {
		return errors.New("该空间暂不支持Debug服务")
	}
	if wrapper.Config.Debug.Launch == nil {
		return errors.New("debug.launch为空")
	}
	cmd, port, runTerminal, err := wrapper.debugCommandInfo()
	if err != nil {
		return err
	}
	return connectDAPServer(wrapper, cmd, port, runTerminal)
}

func (wrapper *Wrapper) debugCommandInfo() (string, int, bool, error) {
	cmd := envUtils.GetString(consts.DebugStartCmd)
	language := strings.ToLower(envUtils.GetString(consts.PAAS_Language))
	port := consts.DebugPort
	runTerminal := true
	if cmd == "" && language != consts.LanguageJava {
		return "", 0, false, errors.New("暂不支持该语言Debug服务" + newLine)
	}
	if h, ok := debugParameterHandlerFactory[language]; ok {
		if err := h(wrapper, &cmd, &port, &runTerminal); err != nil {
			return "", 0, false, err
		}
	}
	return cmd, port, runTerminal, nil
}

func connectDAPServer(wrapper *Wrapper, cmd string, port int, runTerminal bool) error {
	var err error
	if cmd != "" {
		if runTerminal {
			args := cmdUtils.BuildNixShellCmd(cmd)
			wrapper.debugInfo.command, _, err = cmdUtils.NewStdIOCommand(args[0], args[1:]...)
			if err == nil {
				go wrapper.debugInfo.command.Run()
			} else {
				return err
			}
		} else {
			go wrapper.execTTY(cmd, "", false, &wrapper.Config.InternalRunInfo.RunCommand)
		}
	}
	conn, err := netUtils.Connect("tcp", fmt.Sprintf(":%d", port), time.Millisecond*100, 50)
	if err != nil {
		return errors.New("Debug Start TimeOut")
	}
	cwd, ok := wrapper.Config.Debug.Launch["cwd"]
	if !ok {
		cwd = ""
	}
	switch cwd := cwd.(type) {
	case string:
		if cwd == "" {
			cwd = consts.AppRootDir
		} else {
			if path, e := filepath.Abs(cwd); e == nil {
				cwd = path
			}
		}
		wrapper.Config.Debug.Launch["cwd"] = cwd
	}
	data, _ := json.Marshal(wrapper.Config.Debug.Launch)
	session := newDebugSession(wrapper, port, conn, "launch", data)
	wrapper.debugInfo.addSession(session)
	go func() {
		if err := session.init(); err != nil {
			wrapper.runResult = consts.CONSOLE_RUN_FAIL
			wrapper.sendContentMQ("Debug Start Error " + err.Error() + newLine)
			wrapper.Stop()
		}
	}()
	session.Run()
	return nil
}

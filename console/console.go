package console

import (
	"agent/config"
	"agent/consts"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	"agent/utils/netUtils"
	"bytes"
	"context"
	"encoding/json"
	mapset "github.com/deckarep/golang-set/v2"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/creack/pty"
	xterm "github.com/oawu/Golang-cli-xterm"
	"github.com/pkg/errors"
	"golang.org/x/time/rate"
)

var colorFactory = map[string]func(string) string{
	"black":  func(s string) string { return xterm.Black(s).String() },
	"red":    func(s string) string { return xterm.Red(s).String() },
	"green":  func(s string) string { return xterm.Green(s).String() },
	"yellow": func(s string) string { return xterm.Yellow(s).String() },
	"blue":   func(s string) string { return xterm.Blue(s).String() },
	"purple": func(s string) string { return xterm.Purple(s).String() },
	"cyan":   func(s string) string { return xterm.Cyan(s).String() },
	"gray":   func(s string) string { return xterm.Gray(s).String() },
}

func color(color, text string) string {
	if colorFun, ok := colorFactory[color]; ok {
		return colorFun(text)
	} else {
		return xterm.Green(text).String()
	}
}

const (
	STATUS_RUN       = "RUNNING"
	STATUS_DEBUG_RUN = "DEBUG_RUNNING"
	STATUS_STOP      = "STOP"
	newLine          = "\r\n"
)

type ColsAndRowsValueMQMsg struct {
	Cols int `json:"cols"`
	Rows int `json:"rows"`
}

type Wrapper struct {
	mqToConsoleChannel <-chan string
	consoleToMqChannel chan<- string
	cmd                *exec.Cmd
	tty                *os.File
	status             string
	compileStatus      bool
	runResult          string
	logPath            string
	guiStart           bool
	runLock            sync.Mutex
	stopLock           sync.Mutex
	mysqlLock          sync.WaitGroup
	debugInfo          debugInfo
	Config             *config.Config
	winSize            pty.Winsize
	ExecutionSet       mapset.Set[*config.Config]
	guiCancel          func()
}

func MakeNew(
	mqToConsoleChannel <-chan string,
	consoleToMqChannel chan<- string) *Wrapper {

	var wrapper = Wrapper{}
	wrapper.status = STATUS_STOP
	wrapper.compileStatus = true
	wrapper.runResult = consts.CONSOLE_RUN_SUCCESS
	wrapper.mqToConsoleChannel = mqToConsoleChannel
	wrapper.consoleToMqChannel = consoleToMqChannel
	wrapper.logPath = "/home/<USER>/.mysql/log/paas_run.log"
	wrapper.winSize.Cols = 56
	wrapper.winSize.Rows = 256
	wrapper.guiCancel = func() {}
	wrapper.Config = &config.Config{}
	wrapper.ExecutionSet = mapset.NewSet[*config.Config]()
	if envConfig, err := config.LoadEnvConfig(); err == nil {
		wrapper.Config = envConfig
	}
	return &wrapper
}

func (wrapper *Wrapper) Open() {
	wrapper.sendContentMQ(consts.NEW_LINE_SIGN)

	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("consoleWrapper receiveFromMQ panic error: %+v", panicErr)
			}
		}()
		wrapper.receiveFromMQ()
	}()

	// TODO: mysql 需要启动 方案需要优化
	if envUtils.GetString(consts.PAAS_Language) != "MySQL" {
		cmd := envUtils.GetString(consts.PAAS_ConsoleStartCmd)
		if strings.ToLower(envUtils.GetString(consts.PAAS_Language)) == "bash" {
			cmd = "echo '请在 Shell 中继续操作'"
		}
		go wrapper.execTTY(cmd, "", false, nil)
	} else {
		wrapper.mysqlLock.Add(1)
		go wrapper.monitorPortMysql()
	}
	wrapper.NotifyStatusMQ()
	go wrapper.monitorPort()
}

func (wrapper *Wrapper) GetStatus() string {
	if wrapper == nil {
		return STATUS_STOP
	}
	return wrapper.status
}
func (wrapper *Wrapper) GetRunResult() string {
	return wrapper.runResult
}

func (wrapper *Wrapper) execTTY(cmd, stage string, timeout bool, command *config.Command) error {
	if command != nil {
		start := time.Now()
		defer func() {
			command.Duration = time.Now().Sub(start).Milliseconds()
		}()
	}
	if len(cmd) == 0 {
		return errors.New("run_command command was not found in `.environments.yaml` configuration file")
	}
	if stage != "" {
		wrapper.sendContentMQ(color(wrapper.Config.Console.Color, stage+newLine+cmd+newLine+newLine))
	}

	validationResults := wrapper.status == STATUS_RUN
	args := cmdUtils.BuildNixShellCmd(cmd)
	paasShellCmd := envUtils.GetString(consts.PAAS_Shell_Cmd)
	ctx := context.TODO()
	if timeout && wrapper.Config.Timeout > 0 {
		ctx, _ = context.WithTimeout(context.TODO(), time.Second*time.Duration(wrapper.Config.Timeout))
		if !strings.HasPrefix(paasShellCmd, consts.NIX_SHELL) {
			args = []string{"bash", "-c", "source ~/.bashrc &&", cmd}
			argsStr := strings.Join(args[2:], " ")
			wrapper.cmd = exec.Command(args[0], args[1], argsStr)
			log.Printf("GoAgent-Console-bash, args: %+v", args)
		} else {
			wrapper.cmd = exec.CommandContext(ctx, args[0], args[1:]...)
			log.Printf("GoAgent-Console-nixshell, args: %+v", args)
		}
	} else {
		if !strings.HasPrefix(paasShellCmd, consts.NIX_SHELL) {
			args = []string{"bash", "-c", "source ~/.bashrc &&", cmd}
			argsStr := strings.Join(args[2:], " ")
			wrapper.cmd = exec.Command(args[0], args[1], argsStr)
			log.Printf("GoAgent-Console-bash, args: %+v", args)
		} else {
			wrapper.cmd = exec.Command(args[0], args[1:]...)
			log.Printf("GoAgent-Console-nixshell, args: %+v", args)
		}
	}

	// 运行命令
	var err error
	wrapper.tty, err = pty.Start(wrapper.cmd)
	ws := new(pty.Winsize)
	ws.Cols = 56
	ws.Rows = 256
	pty.Setsize(wrapper.tty, &wrapper.winSize)
	if err != nil {
		log.Printf("%s.%s", "Console start fail", err)
		wrapper.sendContentMQ(newLine + err.Error() + newLine)
		return err
	}
	await := make(chan bool)
	var output io.Writer
	if command != nil {
		output = &command.Output
	}
	go wrapper.monitorConsoleOut(await, output)
	if wrapper.cmd.Process != nil {
		wrapper.Config.Pid = wrapper.cmd.Process.Pid
	}
	err = wrapper.cmd.Wait()
	<-await
	if err != nil && command != nil {
		command.Err = err
	}
	if validationResults {
		if err == nil {
			wrapper.runResult = consts.CONSOLE_RUN_SUCCESS
		} else {
			msg := newLine
			select {
			case <-ctx.Done():
				msg = msg + "timeout" + newLine
				wrapper.Config.Unittest.Error = "timeout"
			default:
			}
			wrapper.runResult = consts.CONSOLE_RUN_FAIL
			msg = msg + err.Error() + newLine
			wrapper.sendContentMQ(color("red", msg))
		}
	}
	return err
}

func (wrapper *Wrapper) restartDefaultTTY() {
	go wrapper.execTTY(envUtils.GetString(consts.PAAS_ConsoleStartCmd), "", false, nil)
}

func (wrapper *Wrapper) monitorPort() {
	port := envUtils.GetInt(consts.PAAS_ServicePort)
	if port < 1 {
		return
	}
	flag := false
	for {
		open := netUtils.IsPortInUse(port)
		if !flag && open {
			wrapper.consoleToMqChannel <- consts.MQ_CONOSOLE_PREFIX_PORT_OPEN
		}
		flag = open
		time.Sleep(time.Second)
	}
}

func (wrapper *Wrapper) monitorPortMysql() {
	count := 0
	content := []byte("")
	err := ioutil.WriteFile(wrapper.logPath, content, 0644)
	if err != nil {
		log.Printf("write.fail")
	}
	for {
		if count > 60 {
			break
		}
		// mysql 8.0 没有端口 使用 isStartedMysql 判断
		if netUtils.IsPortInUse(3306) || wrapper.isStartedMysql() {
			wrapper.mysqlLock.Done()
			wrapper.restartDefaultTTY()
			break
		} else {
			count++
			time.Sleep(time.Millisecond * 500)
		}
	}
}

func (wrapper *Wrapper) Stop() {
	if wrapper.cmd == nil {
		return
	}
	if wrapper.stopLock.TryLock() {
		defer wrapper.stopLock.Unlock()
		wrapper.debugInfo.close()
		cmdUtils.TryKill(wrapper.cmd)
		if wrapper.cmd != nil {
			wrapper.Config.Unittest.ExitCode = wrapper.cmd.ProcessState.ExitCode()
		}
	}
}
func (wrapper *Wrapper) Kill() {
	if wrapper.stopLock.TryLock() {
		defer wrapper.stopLock.Unlock()
		wrapper.debugInfo.close()
		cmdUtils.Kill(wrapper.cmd)
		wrapper.cmd = nil
	}
}

func (wrapper *Wrapper) sendContentMQ(content string) {
	wrapper.consoleToMqChannel <- consts.MQ_CONOSOLE_PREFIX_CONTENT + content
}

func (wrapper *Wrapper) receiveFromMQ() {
	for {
		value := <-wrapper.mqToConsoleChannel

		if strings.HasPrefix(value, consts.MQ_CONOSOLE_PREFIX_STATUS) {
			targetStatus := strings.TrimPrefix(value, consts.MQ_CONOSOLE_PREFIX_STATUS)
			if targetStatus == consts.CONSOLE_STATUS_STOP && wrapper.status != STATUS_STOP {
				wrapper.Stop()
			} else if targetStatus == consts.CONSOLE_STATUS_RUN && wrapper.status == STATUS_STOP {
				envConfig, err := config.LoadEnvConfig()
				if err != nil {
					wrapper.runResult = consts.CONSOLE_RUN_FAIL
					wrapper.sendContentMQ(err.Error() + newLine)
				} else {
					wrapper.ExecutionSet.Add(envConfig)
					go wrapper.Executor(envConfig, false)
				}
			} else if targetStatus == consts.CONSOLE_STATUS_DEBUG_RUN && wrapper.status == STATUS_STOP {
				envConfig, err := config.LoadEnvConfig()
				if err != nil {
					wrapper.runResult = consts.CONSOLE_RUN_FAIL
					wrapper.sendContentMQ(err.Error() + newLine)
				} else {
					wrapper.ExecutionSet.Add(envConfig)
					go wrapper.Executor(envConfig, true)
				}
			} else {
				// 不需要变更状态时，直接返回当前状态
				wrapper.NotifyStatusMQ()
			}
		} else if strings.HasPrefix(value, consts.MQ_CONOSOLE_PREFIX_CONTENT) {
			value := strings.TrimPrefix(value, consts.MQ_CONOSOLE_PREFIX_CONTENT)
			if wrapper.tty != nil {
				_, err := wrapper.tty.Write([]byte(value))

				if err != nil {
					log.Printf("%s.%s", "Write MQ to tty fail", err)
				}
			}
		} else if strings.HasPrefix(value, consts.MQ_XTERM_PREFIX_SIZE) {
			value := strings.TrimPrefix(value, consts.MQ_XTERM_PREFIX_SIZE)
			var msg ColsAndRowsValueMQMsg
			err := json.Unmarshal([]byte(value), &msg)
			if err != nil {
				log.Errorf("%s.%s", "Write tty size fail", err)
			}
			wrapper.setTtySize(msg.Cols, msg.Rows)

		} else if strings.HasPrefix(value, consts.MQ_CONSOLE_PREFIX_DEBUG) { // debug dap报文
			value := strings.TrimPrefix(value, consts.MQ_CONSOLE_PREFIX_DEBUG)
			go wrapper.debugInteractHandler(value)
		} else if strings.HasPrefix(value, consts.MQ_CONSOLE_PREFIX_EXECUTION) {
			if wrapper.status == STATUS_STOP {
				body := strings.TrimPrefix(value, consts.MQ_CONSOLE_PREFIX_EXECUTION)
				type execution struct {
					config.Config
					ExecutionMode string `json:"executionMode"`
				}
				var executionBody execution
				if json.Unmarshal([]byte(body), &executionBody) != nil {
					return
				}
				go wrapper.Executor(&executionBody.Config, executionBody.ExecutionMode == "debug")
			}
		}
	}
}

func (wrapper *Wrapper) NotifyStatusMQ() {
	var msg string
	if wrapper.status == STATUS_RUN {
		msg = consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_RUN
	} else if wrapper.status == STATUS_DEBUG_RUN {
		msg = consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_DEBUG_RUN
	} else {
		msg = consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_STOP
	}
	select {
	case wrapper.consoleToMqChannel <- msg:
	case <-time.After(time.Second):
		log.Printf("NotifyStatusMQ timeout")
	}
}

func (wrapper *Wrapper) setTtySize(cols int, rows int) {
	wrapper.winSize.Cols = uint16(cols)
	wrapper.winSize.Rows = uint16(rows)
	pty.Setsize(wrapper.tty, &wrapper.winSize)
}

func (wrapper *Wrapper) monitorConsoleOut(await chan<- bool, output io.Writer) {
	wrapper.Config.Unittest.OutPut = bytes.Buffer{}
	run := !(wrapper.status == STATUS_STOP)
	last := ""
	defer func() {
		wrapper.tty.Close()
		wrapper.tty = nil
		if run {
			if last != "" && !strings.HasSuffix(last, "\r\n") {
				wrapper.sendContentMQ(newLine)
			}
			wrapper.sendContentMQ(newLine)
		}
		await <- true
		close(await)
	}()
	b := make([]byte, 1024)
	if !wrapper.Config.Unittest.OutPutConsole {
		wrapper.sendContentMQ(wrapper.Config.Unittest.ConsoleText)
	}
	limiter := rate.NewLimiter(rate.Every(100*time.Millisecond), 100)
	for {
		if limiter.Allow() { // do something
			n, err := wrapper.tty.Read(b)
			if n == 0 && err != nil {
				break
			}
			last = string(b[:n])
			if output != nil {
				output.Write(b[:n])
			}
			wrapper.Config.Unittest.OutPut.Write(b[:n])
			if wrapper.Config.Unittest.RunId == "" || wrapper.Config.Unittest.OutPutConsole {
				select {
				case wrapper.consoleToMqChannel <- consts.MQ_CONOSOLE_PREFIX_CONTENT + string(b[:n]):
				case <-time.After(time.Second):
					log.Printf("receiveFromConsole timeout")
				}
			}
		}
	}
}

func (wrapper *Wrapper) isStartedMysql() bool {
	file, openErr := os.Open(wrapper.logPath)
	if openErr != nil {
		log.Println("file.is.not.open")
		return false
	}
	defer file.Close()
	content, readErr := ioutil.ReadAll(file)
	if readErr != nil {
		log.Printf("file.is.not.read")
		return false
	}
	// mysql 启动成功log 会有 ready for connections
	return strings.Contains(string(content), "ready for connections")
}

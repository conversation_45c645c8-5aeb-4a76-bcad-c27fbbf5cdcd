package console

import (
	"agent/config"
	"agent/consts"
	"agent/utils/cmdUtils"
	"agent/utils/log"
	"agent/utils/netUtils"
	"context"
	"fmt"
	"os"
	"os/exec"
	"time"
)

type task struct {
	cfg   config.GuiConfig
	ctx   context.Context
	args  []string
	cwd   string
	start func(task *task) error
}

func (t *task) toCommand() *exec.Cmd {
	command := exec.CommandContext(t.ctx, t.args[0], t.args[1:]...)
	if t.cwd != "" {
		command.Dir = t.cwd
	}
	return command
}

func (t *task) doStart() error {
	if t.start == nil {
		t.start = func(task *task) error {
			command := task.toCommand()
			command.Start()
			go func() {
				if err := command.Wait(); err != nil {
					log.Printf("task wait :  %v , error : %s", task, err.Error())
				}
			}()
			return nil
		}
	}
	return t.start(t)
}

func startTasks(ctx context.Context, cfg config.GuiConfig, tasks []task) error {
	for _, task := range tasks {
		task.ctx = ctx
		task.cfg = cfg
		if err := task.doStart(); err != nil {
			log.Printf("task start :  %v , error : %s", task, err.Error())
			return err
		}
	}
	return nil
}
func tryConnectStart(network, address string, interval time.Duration, frequency int) func(task *task) error {
	return func(task *task) error {
		command := task.toCommand()
		if err := command.Start(); err != nil {
			return err
		}
		connect, err := netUtils.Connect(network, address, interval, frequency)
		if err != nil {
			cmdUtils.Kill(command)
			return err
		}
		connect.Close()
		return nil
	}
}

var guiTasks = []task{
	{args: []string{
		"service",
		"dbus",
		"restart",
	}},
	{
		args: []string{
			"Xvnc",
			"-rfbport=5900",
			"-SecurityTypes=None",
			"-BlacklistTimeout=0",
			"-BlacklistThreshold=1000000000",
			"-localhost",
			":0",
			"-geometry",
		},
		start: func(task *task) error {
			task.args = append(task.args, fmt.Sprintf("%dx%d", task.cfg.GetWidth(), task.cfg.GetHeight()))
			os.Remove("/tmp/.X0-lock")
			return tryConnectStart("tcp", ":5900", time.Millisecond*200, 20)(task)
		},
	},
	{
		args: []string{
			"sh",
			"-c",
			// TODO
			`pulseaudio --log-target=stderr --daemonize=no  -n --load="module-native-protocol-unix socket=/tmp/pulseaudio.sock" --load="module-null-sink" --load="module-suspend-on-idle" --load="module-position-event-sounds" --exit-idle-time=-1 `,
		},
		start: tryConnectStart("unix", "/tmp/pulseaudio.sock", time.Millisecond*200, 20),
	},
	{
		args: []string{"fluxbox"},
	},
	{
		args: []string{
			"rfbproxy",
			"--enable-audio",
			"--http-server",
			"--address=0.0.0.0:8080",
			"--rfb-server=127.0.0.1:5900",
		},
		cwd:   consts.NoVncDir,
		start: tryConnectStart("tcp", ":8080", time.Millisecond*200, 20),
	},
}

var guiPaths = []string{
	"Xvnc",
	"pulseaudio",
	"fluxbox",
	"rfbproxy",
}

func (wrapper *Wrapper) loadGui() error {
	if wrapper.Config.Gui && !wrapper.guiStart {
		if err := cmdUtils.LookPathsError(guiPaths...); err != nil {
			wrapper.sendContentMQ(err.Error() + newLine)
			return err
		}
		start := time.Now().UnixMilli()
		defer log.Printf("gui init time : %d", time.Now().UnixMilli()-start)
		if err := wrapper.doLoadGui(wrapper.Config.GuiConfig); err != nil {
			if err := wrapper.doLoadGui(wrapper.Config.GuiConfig); err != nil {
				wrapper.sendContentMQ("- GUI环境初始化失败,请手动点击[运行]重试或重启容器" + newLine)
				return err
			}
		}
	}
	return nil
}

func (wrapper *Wrapper) doLoadGui(cfg config.GuiConfig) error {
	log.Printf("do load gui , cfg : %v", cfg)
	wrapper.guiStart = false
	wrapper.guiCancel()
	ctx, cancel := context.WithCancel(context.TODO())
	wrapper.guiCancel = cancel
	if err := startTasks(ctx, cfg, guiTasks); err != nil {
		return err
	}
	wrapper.guiStart = true
	return nil
}

func (wrapper *Wrapper) GuiConfigChange(last, current *config.Config) {
	if last == nil || !current.Gui || !wrapper.guiStart {
		return
	}
	if last.GuiConfig.GetHeight() != current.GuiConfig.GetHeight() || last.GuiConfig.GetWidth() != current.GuiConfig.GetWidth() {
		go wrapper.doLoadGui(current.GuiConfig)
	}
}

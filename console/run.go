package console

import (
	"agent/config"
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/fileUtils"
	"os"
	"strings"
	"time"
)

func (wrapper *Wrapper) Executor(config *config.Config, debug bool) {
	wrapper.runLock.Lock()
	defer wrapper.runLock.Unlock()
	wrapper.Kill()
	// 等待前一个进程退出
	time.Sleep(time.Millisecond * 100)
	// 判断取消运行的 config
	if !wrapper.ExecutionSet.Contains(config) {
		return
	}
	defer func() {
		wrapper.status = STATUS_STOP
		wrapper.NotifyStatusMQ()
		wrapper.Stop()
		wrapper.cmd = nil
		wrapper.ExecutionSet.Remove(config)
		// 测试用例对去 json 结果
		if config != nil && config.Unittest.RunId != "" {
			config.Unittest.Result = string(config.Unittest.OutPut.Bytes()) + getOutPutJson(config.Unittest)
		}
		if wrapper.ExecutionSet.Cardinality() == 0 {
			if config != nil {
				config.Unittest.OutPutConsole = true
			}
			wrapper.restartDefaultTTY()
		}
	}()
	if debug {
		wrapper.status = STATUS_DEBUG_RUN
	} else {
		wrapper.status = STATUS_RUN
	}
	wrapper.runResult = consts.CONSOLE_RUN_SUCCESS
	wrapper.NotifyStatusMQ()
	// mysql 需要启动成功在运行
	if envUtils.GetString(consts.PAAS_Language) == "MySQL" {
		wrapper.mysqlLock.Wait()
	}
	if config == nil {
		wrapper.runResult = consts.CONSOLE_RUN_FAIL
		wrapper.sendContentMQ("`.environments.yaml` configuration file was not found !" + newLine)
		return
	}
	wrapper.Config = config

	// 如果有编译命令，先执行编译命令
	wrapper.autoImport()
	if wrapper.doCompile(debug) != nil {
		return
	}
	if wrapper.loadGui() != nil {
		return
	}
	if wrapper.Config.Gui {
		wrapper.consoleToMqChannel <- consts.MQ_CONOSOLE_PREFIX_PORT_OPEN
	}
	if debug {
		start := time.Now()
		err := wrapper.doDebugRun()
		if err != nil {
			wrapper.runResult = consts.CONSOLE_RUN_FAIL
			wrapper.sendContentMQ(err.Error() + newLine)
			config.InternalRunInfo.RunCommand.Err = err
		}
		config.InternalRunInfo.RunCommand.Duration = time.Now().Sub(start).Milliseconds()
	} else {
		err := wrapper.doRun()
		if err != nil {
			wrapper.sendContentMQ(err.Error() + newLine)
		}
	}
}

func getOutPutJson(unittest config.UnittestConfig) string {
	path := consts.AppRootDir + "/" + consts.UNITTEST_FILE + unittest.RunId
	text, err := os.ReadFile(path)
	if err != nil {
		return ""
	}
	err = os.Remove(path)
	if err != nil {
		return ""
	}
	return "\n" + consts.UNITTEST_FILE + unittest.RunId + string(text)
}

func (wrapper *Wrapper) doRun() error {
	// 获取执行命令，优先从配置文件中获取
	var cmd string
	cmd = wrapper.Config.RunCommand
	if cmd == "" {
		if fileUtils.FileExist(consts.AppRootDir + "/1024run.sh") {
			cmd = "./1024run.sh"
		} else {
			cmd = envUtils.GetString(consts.PAAS_RunCmd)
		}
	}
	return wrapper.execTTY(strings.TrimSpace(cmd), "running...", true, &wrapper.Config.InternalRunInfo.RunCommand)
}

func (wrapper *Wrapper) doCompile(debug bool) error {
	// 获取执行命令，优先从配置文件中获取
	cmd := wrapper.Config.CompileCommand
	if debug {
		cmd = wrapper.Config.Debug.Compile
	}
	if cmd == "" {
		return nil
	}
	return wrapper.execTTY(strings.TrimSpace(cmd), "compiling...", false, &wrapper.Config.InternalRunInfo.CompileCommand)
}

package console

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/nixUtils"
	"strings"
)

var languageAutoImportDependence = map[string]string{
	consts.LanguagePython: "python39Packages.poetry",
}

func (wrapper *Wrapper) autoImport() error {
	currentLanguage := strings.ToLower(envUtils.GetString(consts.PAAS_Language))
	for language, dependence := range languageAutoImportDependence {
		if strings.HasPrefix(currentLanguage, language) {
			if nixUtils.CheckDependencies(dependence) {
				return wrapper.execTTY("upm add --guess", "", false, &wrapper.Config.InternalRunInfo.AutoImportCommand)
			}
			return nil
		}
	}
	return nil
}

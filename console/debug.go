package console

import (
	"agent/consts"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	"agent/utils/netUtils"
	"encoding/json"
	"fmt"
	"github.com/creack/pty"
	"github.com/dao42/go-dap"
	"github.com/pkg/errors"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"
)

var configError = errors.New("解析配置错误")

type debugInfo struct {
	command  *exec.Cmd
	sessions []*debugSession
}

func (debugInfo *debugInfo) addSession(session *debugSession) {
	debugInfo.sessions = append(debugInfo.sessions, session)
}

func (debugInfo *debugInfo) close() {
	for _, session := range debugInfo.sessions {
		session.close()
	}
	debugInfo.sessions = []*debugSession{}
	cmdUtils.TryKill(debugInfo.command)
	debugInfo.command = nil
}

type debugSession struct {
	wrapper           *Wrapper
	port              int
	conn              *dap.Conn
	startType         string
	startArguments    json.RawMessage
	configurationDone bool
}

func newDebugSession(
	wrapper *Wrapper,
	port int,
	io io.ReadWriteCloser,
	startType string,
	startArguments json.RawMessage) *debugSession {
	session := &debugSession{
		wrapper:        wrapper,
		port:           port,
		startType:      startType,
		startArguments: startArguments,
	}
	session.conn = dap.NewConn(io, session)
	return session
}
func (session *debugSession) Run() {
	session.conn.Run()
}

func (session *debugSession) init() error {
	var err error
	conn := session.conn
	var initializeResponse dap.InitializeResponse

	if err = conn.SendRequest(initializeRequestOne, &initializeResponse); err != nil || !initializeResponse.Success {
		return errors.New(fmt.Sprintf("initialize request error : %v", err))
	}
	var launchResponse dap.LaunchResponse
	if err = conn.SendRequest(&dap.LaunchRequest{
		Request:   dap.NewRequest(session.startType),
		Arguments: session.startArguments,
	}, &launchResponse); err != nil || !initializeResponse.Success {
		return errors.New(fmt.Sprintf("launch request error : %v", err))
	}
	return nil
}

func (session *debugSession) close() {
	dapDisconnect(session.conn)
	session.conn.Close()
}

var debugAdapterID = map[string]string{
	consts.LanguageC:   "cppdap",
	consts.LanguageCPP: "cppdap",
}

var initializeRequestOne = &dap.InitializeRequest{
	Request: dap.NewRequest("initialize"),
	Arguments: dap.InitializeRequestArguments{
		ClientID:                      "vscode",
		ClientName:                    "Visual Studio Code",
		AdapterID:                     getDebugAdapterID(),
		Locale:                        "zh-cn",
		LinesStartAt1:                 true,
		ColumnsStartAt1:               true,
		PathFormat:                    "path",
		SupportsVariableType:          true,
		SupportsVariablePaging:        true,
		SupportsRunInTerminalRequest:  true,
		SupportsMemoryReferences:      true,
		SupportsProgressReporting:     true,
		SupportsInvalidatedEvent:      true,
		SupportsMemoryEvent:           true,
		SupportsStartDebuggingRequest: true,
	},
}

func getDebugAdapterID() string {
	languageId := strings.ToLower(envUtils.GetString(consts.PAAS_Language))
	if adapterID, ok := debugAdapterID[languageId]; ok {
		return adapterID
	}
	return languageId
}

func (wrapper *Wrapper) sendDAPMQ(data string) {
	if wrapper.status == STATUS_DEBUG_RUN {
		wrapper.consoleToMqChannel <- consts.MQ_CONSOLE_PREFIX_DEBUG + data
	}
}

func (session *debugSession) Handle(conn *dap.Conn, message dap.Message) {
	go func() {
		if serverExtraProcess(message, session) {
			json, _ := json.Marshal(message)
			session.wrapper.sendDAPMQ(string(json))
		}
	}()
}

func serverExtraProcess(message dap.Message, session *debugSession) bool {
	switch message := message.(type) {
	case *dap.RunInTerminalRequest:
		runInTerminalHandler(message, session)
	case *dap.TerminatedEvent:
		terminatedEventHandler(message, session)
	case *dap.StartDebuggingRequest:
		startDebuggingRequest(message, session)
	case *dap.ExitedEvent:
		exitedEventHandler(message, session)
	case *dap.StoppedEvent:
		stoppedEvent(message, session)
	case *dap.InitializedEvent:
		session.configurationDone = false
		return true
	case *dap.VariablesResponse:
		return true
	}
	return false
}

func startDebuggingRequest(message *dap.StartDebuggingRequest, session *debugSession) {
	conn := session.conn
	newConn, err := netUtils.Connect("tcp", fmt.Sprintf(":%d", session.port), time.Millisecond*100, 50)
	if err != nil {
		conn.Send(dap.NewErrorResponse(message, "connect dap server error"))
		session.wrapper.Stop()
		return
	}
	arguments, _ := json.Marshal(message.Arguments.Configuration)
	newSession := newDebugSession(session.wrapper, session.port, newConn, message.Arguments.Request, arguments)
	go newSession.Run()
	session.wrapper.debugInfo.addSession(newSession)
	if newSession.init() == nil {
		conn.Send(&dap.StartDebuggingResponse{Response: dap.NewResponse(message)})
	} else {
		conn.Send(dap.NewErrorResponse(message, "start error"))
		session.wrapper.Stop()
	}
}

func stoppedEvent(message *dap.StoppedEvent, session *debugSession) {
	conn := session.conn
	stoppedDataEvent := &dap.StoppedDataEvent{
		Event: dap.NewEvent("stoppedData"),
		Body:  message.Body,
	}
	var err error
	defer func() {
		if err != nil {
			log.Printf("debug stopped data error : %s", err.Error())
			data, _ := json.Marshal(stoppedDataEvent)
			log.Printf("stoppedDataEvent : %s", string(data))
			stepOutRequestHandler(conn, &dap.StepOutRequest{
				Request: dap.NewRequest("stepOut"),
				Arguments: dap.StepOutArguments{
					ThreadId: message.Body.ThreadId,
				},
			})
		} else {
			json, _ := json.Marshal(stoppedDataEvent)
			session.wrapper.sendDAPMQ(string(json))
		}
	}()
	var threadsResponse dap.ThreadsResponse
	err = conn.SendRequest(&dap.ThreadsRequest{Request: dap.NewRequest("threads")}, &threadsResponse)
	if err != nil {
		return
	}
	stoppedDataEvent.ThreadsBody = threadsResponse.Body
	var stackTraceResponse dap.StackTraceResponse
	err = conn.SendRequest(&dap.StackTraceRequest{Request: dap.NewRequest("stackTrace"), Arguments: dap.StackTraceArguments{
		ThreadId: message.Body.ThreadId,
		Levels:   20,
	}}, &stackTraceResponse)
	if err != nil {
		return
	}
	stoppedDataEvent.StackTraceBody = stackTraceResponse.Body
	// 不是工作区文件的暂停事件跳出该方法
	if len(stackTraceResponse.Body.StackFrames) == 0 || !strings.HasPrefix(stackTraceResponse.Body.StackFrames[0].Source.Path, consts.AppRootDirChild) {
		err = errors.New("not supported outside of workspace")
		return
	}
	var scopesResponse dap.ScopesResponse
	err = conn.SendRequest(&dap.ScopesRequest{Request: dap.NewRequest("scopes"), Arguments: dap.ScopesArguments{
		FrameId: stackTraceResponse.Body.StackFrames[0].Id,
	}}, &scopesResponse)
	if err != nil {
		return
	}
	var finalScopes []dap.Scope
	result := make(map[int]dap.VariablesResponseBody)
	for _, scope := range scopesResponse.Body.Scopes {
		if scope.Expensive {
			continue
		}
		if variablesResponse, err := dapVariablesRequest(conn, scope.VariablesReference); err == nil {
			result[scope.VariablesReference] = variablesResponse.Body
			finalScopes = append(finalScopes, scope)
		}
	}
	next := make(map[int]dap.VariablesResponseBody)
	for _, variablesResponseBody := range result {
		for _, variable := range variablesResponseBody.Variables {
			reference := variable.VariablesReference
			if reference < 1 {
				continue
			}
			_, o1 := result[reference]
			_, o2 := next[reference]
			if !(o1 || o2) {
				if variablesResponse, err := dapVariablesRequest(conn, reference); err == nil {
					next[reference] = variablesResponse.Body
				}
			}

		}
	}
	for k, v := range next {
		result[k] = v
	}
	scopesResponse.Body.Scopes = finalScopes
	stoppedDataEvent.ScopesBody = scopesResponse.Body
	stoppedDataEvent.VariablesBodyMap = result
}

func dapVariablesRequest(conn *dap.Conn, variablesReference int) (dap.VariablesResponse, error) {
	var variablesResponse dap.VariablesResponse
	var err error
	if variablesReference > 0 {
		err = conn.SendRequest(&dap.VariablesRequest{Request: dap.NewRequest("variables"), Arguments: dap.VariablesArguments{
			VariablesReference: variablesReference,
		}}, &variablesResponse)
	} else {
		err = errors.New("variablesReference need to be greater than 0")
	}
	return variablesResponse, err
}

func exitedEventHandler(message *dap.ExitedEvent, session *debugSession) {
	if message.Body.ExitCode == 0 {
		session.wrapper.runResult = consts.CONSOLE_RUN_SUCCESS
	} else {
		session.wrapper.runResult = consts.CONSOLE_RUN_FAIL
	}
	session.conn.Close()
}

func terminatedEventHandler(message *dap.TerminatedEvent, session *debugSession) {
	// disconnect 可能会再返回 terminated
	dapDisconnect(session.conn)
}

func dapDisconnect(conn *dap.Conn) {
	conn.Send(&dap.DisconnectRequest{
		Request: dap.NewRequest("disconnect"),
		Arguments: dap.DisconnectArguments{
			Restart: false,
		},
	})
}

func runInTerminalHandler(message *dap.RunInTerminalRequest, session *debugSession) {
	wrapper := session.wrapper
	conn := session.conn
	var err error
	arguments := message.Arguments
	args := cmdUtils.BuildNixShellCmd(strings.Join(arguments.Args, " "))
	wrapper.cmd = exec.Command(args[0], args[1:]...)
	if arguments.Cwd != "" {
		wrapper.cmd.Dir = arguments.Cwd
	}
	if arguments.Env != nil {
		env := os.Environ()
		for k, v := range arguments.Env {
			env = append(env, fmt.Sprintf("%s=%v", k, v))
		}
		wrapper.cmd.Env = env
	}
	// 运行命令
	wrapper.tty, err = pty.Start(wrapper.cmd)
	if err != nil {
		conn.Send(dap.NewErrorResponse(message, "Start Error"))
		log.Errorf("Debug runInTerminal Fail : %s", err)
		wrapper.sendContentMQ(err.Error() + newLine)
		wrapper.Stop()
		return
	}
	ws := new(pty.Winsize)
	ws.Cols = 56
	ws.Rows = 256
	pty.Setsize(wrapper.tty, ws)
	await := make(chan bool)
	go wrapper.monitorConsoleOut(await, &wrapper.Config.InternalRunInfo.RunCommand.Output)
	conn.Send(&dap.RunInTerminalResponse{
		Response: dap.NewResponse(message),
		Body: dap.RunInTerminalResponseBody{
			ProcessId:      wrapper.cmd.Process.Pid,
			ShellProcessId: 0,
		},
	})
	wrapper.cmd.Wait()
	<-await
}

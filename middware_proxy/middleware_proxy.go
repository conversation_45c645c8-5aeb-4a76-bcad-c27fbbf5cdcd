package middware_proxy

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"fmt"
	"io"
	"net"
	"sync"
	"time"
)

type middlewareProxy struct {
	mysqlAddr           string
	mysqlBackendAddr    string
	redisAddr           string
	redisBackendAddr    string
	postgresqlAddr      string
	postgresBackendAddr string
	mongoAddr           string
	mongoBackendAddr    string
}

// 定义一个全局变量，使用sync.Once保证只执行一次
var once sync.Once
var instance *middlewareProxy

// GetInstance 是一个全局访问点，返回单例对象
func GetInstance() *middlewareProxy {
	once.Do(func() {
		instance = NewMiddlewareProxy()
		instance.mysqlProxyMain()
		instance.redisProxyMain()
		instance.postgresqlProxyMain()
		instance.mongoProxyMain()
	})
	return instance
}

// 获取mysql地址
func getMysqlBackendAddr() string {
	//mysqlBackendAddr := "127.0.0.1:3306"
	mysqlBackendAddr := ""
	mysqlHost := envUtils.GetString(consts.MYSQL_HOST)
	if envUtils.GetString(consts.MYSQL_HOST) != "" {
		mysqlBackendAddr = fmt.Sprintf("%s:%s", mysqlHost, envUtils.GetString(consts.MYSQL_PORT))
	}

	return mysqlBackendAddr
}

func getRedisBackendAddr() string {
	//redisBackendAddr := "127.0.0.1:6379"
	redisBackendAddr := ""
	redisHost := envUtils.GetString(consts.REDIS_HOST)
	if redisHost != "" {
		redisBackendAddr = fmt.Sprintf("%s:%s", redisHost, envUtils.GetString(consts.REDIS_PORT))
	}

	return redisBackendAddr
}

func getPostgresBackendAddr() string {
	postgresBackendAddr := ""
	postgresHost := envUtils.GetString(consts.POSTGRE_SQL_HOST)
	if postgresHost != "" {
		postgresBackendAddr = fmt.Sprintf("%s:%s", postgresHost, envUtils.GetString(consts.POSTGRE_SQL_PORT))
	}

	return postgresBackendAddr
}

func getMongoBackendAddr() string {
	mongoBackendAddr := ""
	mongoHost := envUtils.GetString(consts.MONGO_HOST)
	if mongoHost != "" {
		mongoBackendAddr = fmt.Sprintf("%s:%s", mongoHost, envUtils.GetString(consts.MONGO_PORT))
	}

	return mongoBackendAddr
}

// 构造函数
func NewMiddlewareProxy() *middlewareProxy {
	mysqlBackendAddr := getMysqlBackendAddr()
	redisBackendAddr := getRedisBackendAddr()
	postgresBackendAddr := getPostgresBackendAddr()
	mongoBackendAddr := getMongoBackendAddr()

	return &middlewareProxy{
		mysqlAddr:           ":3306",
		mysqlBackendAddr:    mysqlBackendAddr,
		redisAddr:           ":6379",
		redisBackendAddr:    redisBackendAddr,
		postgresqlAddr:      ":5432",
		postgresBackendAddr: postgresBackendAddr,
		mongoAddr:           ":27017",
		mongoBackendAddr:    mongoBackendAddr,
	}
}

func (s *middlewareProxy) initEnv() {
	log.Infof("middlewareProxy-UpdateMiddlewareEnv-befor: %+v", s)
	s.mysqlBackendAddr = getMysqlBackendAddr()
	s.redisBackendAddr = getRedisBackendAddr()
	s.postgresBackendAddr = getPostgresBackendAddr()
	s.mongoBackendAddr = getMongoBackendAddr()
	log.Infof("middlewareProxy-UpdateMiddlewareEnv-after: %+v", s)
}

// 动态监听环境变量变化时重初始化
func UpdateMiddlewareEnv(middlewareProxyChannel chan string) {
	go func() {
		for {
			select {
			case msq1 := <-middlewareProxyChannel:
				log.Infof("middlewareProxy-UpdateMiddlewareEnv msq1: %+v", msq1)
				ins := GetInstance()
				ins.initEnv()
			case <-time.After(10 * time.Second):
			}
		}
	}()
}

// 通用处理逻辑
func (s *middlewareProxy) handleConnection(client net.Conn, backendAddr string) {
	defer client.Close()

	// 连接到目标服务器
	//server, err := net.Dial("tcp", "localhost:6379")
	server, err := net.Dial("tcp", backendAddr)
	if err != nil {
		log.Errorf("handleConnection, Failed to connect to server, err: %+v, backendAddr: %+v", err, backendAddr)
		return
	}
	defer server.Close()

	// 复制数据流从客户端到服务器
	done := make(chan error)
	go func() {
		_, err := io.Copy(server, client)
		if err != nil {
			log.Infof("handleConnection-client-to-server, err: %+v, backendAddr: %s", err, backendAddr)
		}
		done <- err
	}()

	// 复制数据流从服务器到客户端
	go func() {
		_, err := io.Copy(client, server)
		if err != nil {
			log.Infof("handleConnection-server-to-client, err: %+v, backendAddr: %s", err, backendAddr)
		}
		done <- err
	}()

	err1 := <-done
	// 等待数据传输完成
	log.Infof("handleConnection-end backendAddr: %+v, err: %+v", backendAddr, err1)
}

// mysql proxy main
func (s *middlewareProxy) mysqlProxyMain() {
	go func() {
		// 监听本地端口，用于接收客户端连接
		listener, err := net.Listen("tcp", s.mysqlAddr)
		if err != nil {
			log.Infof("mysqlProxyMain, Error listening: %+v", err)
			return
		}
		defer listener.Close()

		log.Infof("mysqlProxyMain, TCP Proxy server is running on %+v", s.mysqlAddr)

		defer func() {
			if err := recover(); err != nil {
				log.Infof("mysqlProxyMain, panic error:%+v\n", err)
			}
		}()

		for {
			// 接受新的客户端连接
			client, err := listener.Accept()
			if err != nil {
				log.Infof("mysqlProxyMain, Error accepting connection: %+v", err)
				continue
			}

			if s.mysqlBackendAddr != "" {
				// 处理连接
				go func() {
					defer func() {
						if err := recover(); err != nil {
							log.Errorf("mysqlProxyMain, handleConnection, panic error: %+v, backendAddr: %+v", err, s.mysqlBackendAddr)
						}
					}()
					s.handleConnection(client, s.mysqlBackendAddr)
				}()
			}
		}
	}()
}

// redis proxy main
func (s *middlewareProxy) redisProxyMain() {
	go func() {
		// 监听本地端口，用于接收客户端连接
		listener, err := net.Listen("tcp", s.redisAddr)
		if err != nil {
			log.Infof("redisProxyMain, Error listening: %+v", err)
			return
		}
		defer listener.Close()

		log.Infof("redisProxyMain, TCP Proxy server is running on %+v", s.redisAddr)

		defer func() {
			if err := recover(); err != nil {
				log.Infof("redisProxyMain, panic error:%+v\n", err)
			}
		}()

		for {
			// 接受新的客户端连接
			client, err := listener.Accept()
			if err != nil {
				log.Infof("redisProxyMain, Error accepting connection: %+v", err)
				continue
			}

			// 处理连接
			if s.redisBackendAddr != "" {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							log.Errorf("redisProxyMain, handleConnection, panic error: %+v, backendAddr: %+v", err, s.redisBackendAddr)
						}
					}()

					s.handleConnection(client, s.redisBackendAddr)
				}()
			}
		}
	}()
}

// postgresql proxy main
func (s *middlewareProxy) postgresqlProxyMain() {
	go func() {
		// 监听本地端口，用于接收客户端连接
		listener, err := net.Listen("tcp", s.postgresqlAddr)
		if err != nil {
			log.Infof("postgresqlProxyMain, Error listening: %+v", err)
			return
		}
		defer listener.Close()

		log.Infof("postgresqlProxyMain, TCP Proxy server is running on %+v", s.postgresqlAddr)

		defer func() {
			if err := recover(); err != nil {
				log.Infof("postgresqlProxyMain, panic error:%+v\n", err)
			}
		}()

		for {
			// 接受新的客户端连接
			client, err := listener.Accept()
			if err != nil {
				log.Infof("postgresqlProxyMain, Error accepting connection: %+v", err)
				continue
			}

			// 处理连接
			if s.postgresBackendAddr != "" {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							log.Errorf("postgresqlProxyMain, handleConnection, panic error: %+v, backendAddr: %+v", err, s.postgresBackendAddr)
						}
					}()
					s.handleConnection(client, s.postgresBackendAddr)
				}()
			}
		}
	}()
}

// mongo proxy main
func (s *middlewareProxy) mongoProxyMain() {
	go func() {
		// 监听本地端口，用于接收客户端连接
		listener, err := net.Listen("tcp", s.mongoAddr)
		if err != nil {
			log.Infof("mongoProxyMain, Error listening: %+v", err)
			return
		}
		defer listener.Close()

		log.Infof("mongoProxyMain, TCP Proxy server is running on %+v", s.mongoAddr)

		defer func() {
			if err := recover(); err != nil {
				log.Infof("mongoProxyMain, panic error:%+v\n", err)
			}
		}()

		for {
			// 接受新的客户端连接
			client, err := listener.Accept()
			if err != nil {
				log.Infof("mongoProxyMain, Error accepting connection: %+v", err)
				continue
			}

			// 处理连接
			if s.mongoBackendAddr != "" {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							log.Errorf("mongoProxyMain, handleConnection, panic error: %+v, backendAddr: %+v", err, s.mongoBackendAddr)
						}
					}()
					s.handleConnection(client, s.mongoBackendAddr)
				}()
			}
		}
	}()
}

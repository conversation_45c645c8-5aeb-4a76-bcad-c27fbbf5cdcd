package com.dao42.paas.constants;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class PatternConstant {

    // java
    public static final String JUNIT_PACKAGE_PATTERN = "(?<=^package).*(?=;$)";
    public static final String JUNIT_FILE_PATTERN = "(#start|@Test)[\\s\\S]*?(\\(\\))(?=.*\\{)";
    public static final String JUNIT_NAME_PROTOCOL_PATTERN = "(?<=#name\\[).*(?=\\])";
    public static final String JUNIT_NAME_PATTERN = "(?<=@DisplayName\\(\").*?(?=\"\\))";
    public static final String JUNIT_INPUT_PATTERN = "(?<=#input\\[)[\\s|\\S]*?(?=\\])";
    public static final String JUNIT_EXPECTED_PATTERN = "(?<=#expected\\[)[\\s|\\S]*?(?=\\])";
    public static final String JUNIT_METHOD_NAME_PATTERN = "(?<=void ).*?(\\(\\))";
    public static final String JUNIT_TIME_PATTERN = "(?<=Test run finished after ).* ms";
    public static final String JUNIT_EXECUTE_SUCCESS_PATTERN = "Test run finished after \\d+ ms";
    public static final String JUNIT_RESULT_AFTER_PATTERN = "\\[\\d\\dm";
    public static final String JUNIT_RESULT_PATTERN = ".\\[0m.*";
    public static final String JUNIT_FAIL_REASON_PATTERN = "(expected:).*(?=.\\[)";
    public static final String JUNIT_EXPECTED_OUTPUT_PATTERN = "(?<=expected: <).*(?=> but was: )";
    public static final String JUNIT_ACTUAL_OUTPUT_PATTERN = "(?<=> but was: <).*(?=>)";
    public static final String JUNIT_RUN_STATUS_PATTERN = "Thanks for using JUnit!";
    public static final List<String> JAVA_NOTES = getJavaNotes();

    private static List<String> getJavaNotes() {
        List<String> javaNotes = new ArrayList<>();
        javaNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        javaNotes.add("\\n *//.*");
        return javaNotes;
    }

    public static String getJUnitDetailReasonPattern(String className, String method) {
        return "(JUnit Jupiter:"
            + className
            + ":"
            + method
            + "\\n)[\\s\\S]*?(?=JUnit Jupiter:|Test run finished after)";
    }

    public static final String SPRING_BOOT_TEST_FILE_PATTERN = "(#start|@Test)[\\s\\S]*?(\\(\\))";
    public static final String SPRING_BOOT_TEST_NAME_PATTERN = "(?<=@DisplayName\\(\").*?(?=\"\\))";
    public static final String SPRING_BOOT_TEST_METHOD_NAME_PATTERN = "(?<=void ).*?(\\(\\))";
    public static final String SPRING_BOOT_TEST_TIME_PATTERN =
        "(?<=\\n\\[INFO\\] Total time: ).*(?=\\n)";
    public static final String SPRING_BOOT_TEST_BUILD_PATTERN =
        "\\n.*[(ERROR|INFO)].*Tests run: \\d";
    public static final String SPRING_BOOT_TEST_EXPECTED_OUTPUT_PATTERN =
        "(?<=\\nexpected: )[\\s|\\S]*?(?=but)";
    public static final String SPRING_BOOT_TEST_ACTUAL_OUTPUT_PATTERN =
        "(?<=but was: )[\\s|\\S]*?(?=\\n\\tat )";

    public static String getSpringBootTestError(String method) {
        return "\\n.*ERROR.*." + method + ":\\d";
    }

    public static String getSpringBootTestErrorReason(String method) {
        return method + ":[\\s|\\S]*";
    }

    public static String getSpringBootTestErrorReasonDetailPatternReason(String method) {
        return "\\n.*ERROR.*" + method + ":[\\s|\\S]*?(?=\\n\\[)";
    }

    // python3
    // pytest
    public static final String PYTEST_METHOD_PATTERN = "(#start|def test_)[\\s|\\S]*?(\\):)";
    public static final String PYTEST_METHOD_NAME_PATTERN = "(?<=def )test.*(?=\\()";
    public static final String PYTEST_PROTOCOL_NAME_PATTERN = "(?<=#name\\[)[\\s|\\S]*?(?=])";
    // 获取最后一行
    public static final String PYTEST_TIME_PATTERN = "(?<= (tests|passed|warning|failed).* in ).*(s)";
    public static final String PYTEST_EXECUTE_SUCCESS_PATTERN = "=+.*(tests|passed|warning|failed).*in.*=+";
    public static final String PYTEST_RESULT_PATTERN = " - .*";
    public static final String PYTEST_REASON = "(?<=\\n.*E ).*?(?=\\n)";
    public static final String PYTEST_EXPECTED_OUTPUT_PATTERN = "(?<= == )[\\s|\\S]*";
    public static final String PYTEST_ACTUAL_OUTPUT_PATTERN = "(?<=assert )[\\s|\\S]*?(?= == )";

    public static final List<String> PYTHON_NOTES = getPythonNotes();

    private static List<String> getPythonNotes() {
        List<String> pythonNotes = new ArrayList<>();
        pythonNotes.add("\"\"\"[\\s|\\S]*?\"\"\"");
        pythonNotes.add("'''[\\s|\\S]*?'''");
        pythonNotes.add("\\n *#.*");
        return pythonNotes;
    }

    public static String getPytestFailReasonPattern(String fileName, String method) {
        return "(?<=FAILED " + fileName + "\\.py::" + method + " - ).*";
    }

    public static String getPytestDetailReasonPattern(String method) {
        return ".*_ " + method + " _[\\w\\W]*?(?=\\n.\\[31m.\\[1m_|\\n=)";
    }

    // unitTest
    // unitTest获取classname
    public static final String UNIT_TEST_FILE_PATTERN = "(?<=def ).*?test_.*(?=\\()";
    public static final String UNIT_TEST_CLASS_NAME_PATTERN = "(?<=class).*(?=\\()";
    public static final String UNIT_TEST_REASON_PATTERN = "(?<=\\nAssertionError:).*";
    public static final String UNIT_TEST_REASON_ERROR_PATTERN = "\\S.*Error[\\s|\\S]*";
    public static final String UNIT_TEST_TIME_PATTERN =
        "(?<= (tests|passed|warning|failed) in ).*(s)";
    public static final String UNIT_TEST_EXPECTED_OUTPUT_PATTERN = ".*(?= != )";
    public static final String UNIT_TEST_ACTUAL_OUTPUT_PATTERN =
        "(\\[.+?\\]|\\'.+?\\') \\!\\= (\\[.+?\\]|\\'.+?\\')";

    public static String getUnitTestDetailReasonPattern(String method, String str) {
        return str + ": " + method + "[\\s|\\S]*?(?=\\n==|\\nRan)";
    }

    public static String getUnitTestSuccessPattern(String method) {
        return method + " \\(.*(?=\\n)";
    }

    // jest
    // 方法分组读取
    public static final String JEST_METHOD_PATTERN = "(#start|describe\\(|test\\()[\\s|\\S]*?(?=}\\))";
    public static final String JEST_METHOD_DESCRIBE_NAME_PATTERN =
        "(?<=describe\\(['\"]).*(?=['\"],)";
    public static final String JEST_METHOD_TEST_NAME_PATTERN = "(?<=test\\(['\"]).*(?=['\"],)";
    public static final String JEST_REASON_PATTERN = "Error:[\\s|\\S]*Received:.*";
    public static final String JEST_PASS_PATTERN = "passed";
    public static final String JEST_SPLIT_RESULT_PATTERN = "Test results written to: ";
    public static final String JEST_EXPECTED_OUTPUT_PATTERN = "(?<=\\nExpected: ).*";
    public static final String JEST_ACTUAL_OUTPUT_PATTERN = "(?<=\\nReceived: ).*";
    public static final List<String> JAVA_SCRIPT_NOTES = getJavaScriptNotes();

    private static List<String> getJavaScriptNotes() {
        List<String> javaScriptNotes = new ArrayList<>();
        javaScriptNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        javaScriptNotes.add("\\n *//.*");
        return javaScriptNotes;
    }

    public static String getJestProtocolPattern(String method) {
        return "#start[\\s|\\S]*? " + method;
    }

    // CUnit
    // check文件是否存在主函数
    public static final String CUNIT_CHECK_MAIN_PATTERN = "(^|void\\s*|int\\s*)main\\(";
    public static final String CUNIT_METHOD_LINE_PATTERN = "CU_add_test.*";
    public static final String CUNIT_METHOD_PATTERN = "(,)(?!.*\\1).*\\)";
    public static final String CUNIT_METHOD_NAME_PATTERN = "(?<=\").*(?=\")";
    public static final String CUNIT_TIME_PATTERN = "(?<=Elapsed time =).*";
    public static final String C_UNIT_RUN_STATUS_PATTERN = "CUnit - A unit testing framework for C";

    public static String getCunitReasonPattern(String method) {
        return "(?<=(Test: " + method + " ...FAILED))[\\s|\\S]*";
    }

    public static String getCunitDetailPattern(String method) {
        return "Test: " + method + "[\\s|\\S]*?(?=\\n  Test|\\nRun Summary:)";
    }

    public static final List<String> C_NOTES = getCNotes();

    private static List<String> getCNotes() {
        List<String> javaNotes = new ArrayList<>();
        javaNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        javaNotes.add("\\n *//.*");
        return javaNotes;
    }

    // unity
    public static final String UNITY_METHOD_LINE_PATTERN = "(RUN_TEST\\().*?\\);";
    public static final String UNITY_METHOD_NAME_PATTERN = "(?<=RUN_TEST\\().*?(?=\\))";
    public static final String UNITY_RUN_STATUS_PATTERN = "(OK|FAIL)$";
    public static final String UNITY_METHOD_BY_RESULT_PATTERN = "(?<=\\d:).*?(?=:(FAIL|PASS))";
    public static final String UNITY_TIME_BY_RESULT_PATTERN = "(?<=\\()\\d*(?= ms\\))";
    public static final String UNITY_METHOD_LINE_BY_RESULT_PATTERN = ".* \\(\\d* ms\\)";
    public static final String UNITY_REASON_PATTERN = "(?<=FAIL: ).*(?=\\(\\d* ms\\))";
    public static final String UNITY_EXECUTE_SUCCESS_PATTERN = "\\d+ Tests \\d+ Failures \\d+ Ignored\\s+(FAIL|OK)";

    public static final String getUnityMethodPattern(String method) {
        return "(#start)[\\s|\\S]*? " + method;
    }

    public static final String judgeUnityRunStatus(String method) {
        return ":" + method + ":PASS \\(\\d";
    }

    public static String unityFailDetailReasonPattern(String method) {
        return ".*" + method + ".*";
    }

    // rspec
    // 方法分组获取
    public static final String RSPEC_METHOD_PATTERN =
        "(#start[\\s|\\S]*?#end|describe)[\\s|\\S]*?it.*[\\s|\\S]*?(end)";
    public static final String RSPEC_METHOD_NAME_DESCRIBE_PATTERN = "(?<=describe).*(?=')";
    public static final String RSPEC_METHOD_NAME_IT_PATTERN = "(?<=it).*(?=')";
    public static final String RSPEC_ = "SyntaxError:";
    public static final String RSPEC_EXPECTED_OUTPUT_PATTERN = "(?<=\\nexpected: )[\\s|\\S]*(?=got)";
    public static final String RSPEC_ACTUAL_OUTPUT_PATTERN =
        "(?<=\\n     got: )[\\s|\\S]*(?=\\n\\(compared)";

    public static final String rspecDetailPattern(String method) {
        return ".*" + escapeExprSpecialWord(method) + "[\\s|\\S]*?(?=(\\s\\s){2}\\d|\\n[\\S])";
    }

    public static String getRspecMethod(String method) {
        return "#start[\\s|\\S]*?'" + escapeExprSpecialWord(method) + "'";
    }

    public static final List<String> RUBY_NOTES = getRubyNotes();

    private static List<String> getRubyNotes() {
        List<String> rubyNotes = new ArrayList<>();
        rubyNotes.add("=begin[\\s|\\S]*?\\n=end");
        rubyNotes.add("\\n *#.*");
        return rubyNotes;
    }

    // luaUnit
    public static final String LUA_UNIT_METHOD_PATTERN =
        "(#start|function\\s*[Tt][Ee][Ss][Tt])[\\s|\\S]*?(\\)\\n)";
    public static final String LUA_UNIT_METHOD_NAME_PATTERN =
        "(?<=function).*:\\s*[Tt][Ee][Ss][Tt].*(?=\\(\\))|(?<=function)\\s*[Tt][Ee][Ss][Tt][^:^\\n]*(?=\\(\\))";
    public static final String LUA_UNIT_TIME_PATTERN = "(?<=tests in ).* seconds";
    public static final String LUA_UNIT_RUN_STATUS_PATTERN = "Ran \\d tests in .*seconds";
    public static final List<String> LUA_UNIT_NOTES = getLuaUnitNotes();
    public static final String LUA_EXPECTED_OUTPUT_PATTERN = "(?<=expected: )[\\S|\\s]*?(?=actual: )";
    public static final String LUA_ACTUAL_OUTPUT_PATTERN = "(?<=actual: )[\\S|\\s]*";

    private static List<String> getLuaUnitNotes() {
        List<String> luaUnitNotes = new ArrayList<>();
        luaUnitNotes.add("\\n *--\\[\\[[\\s|\\S]*?\\n *--\\]\\]");
        luaUnitNotes.add("\\n *--.*");
        return luaUnitNotes;
    }

    public static String getLuaUnitDetailPattern(String method) {
        return "(?<=.*\\)\\s" + method + "\\n)[\\s|\\S]*?(?=\\d*\\)|Failed tests:|Ran)";
    }

    public static String getLuaUnitReasonPattern(String method) {
        return "[\\s|\\S]*?(?=stack traceback:)";
    }

    public static String getProtocolPattern(String method) {
        return "#start[\\s|\\S]*?" + escapeExprSpecialWord(method);
    }

    // go testing
    public static final String GO_TESTING_TIME_PATTERN = "(?<=command-line-arguments).*\\ds";

    public static String goTestingResultPattern(String method) {
        return "=== RUN   " + method + "\\n[\\s|\\S]*?--- (PASS|FAIL)";
    }

    public static String goTestingFailReasonPattern(String method) {
        return "(?<==== RUN   " + method + ")\\n[\\s|\\S]*?(?=--- (PASS|FAIL))";
    }

    public static String goTestingDetailReasonPattern(String method) {
        return "=== RUN   " + method + "\\n[\\s|\\S]*?--- (PASS|FAIL).*";
    }

    public static final String GO_TESTING_METHOD_NAME_PATTERN = "(?<=\\nfunc) *Test.*(?=\\()";
    public static final List<String> GO_NOTES = getGoNotes();

    private static List<String> getGoNotes() {
        List<String> goNotes = new ArrayList<>();
        goNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        goNotes.add("\\n *//.*");
        return goNotes;
    }

    // php
    public static final String PHP_UNIT_METHOD_PATTERN =
        "((#start[\\s|\\S]*?#end|function))[\\s|\\S]*?(\\))";
    public static final String PHP_UNIT_METHOD_NAME_PATTERN = "(?<=function )test.*?(?=\\()";
    public static final String PHP_UNIT_TIME_PATTERN = "(?<=Time: ).*?(?=,)";
    public static final String PHP_EXPECTED_OUTPUT_PATTERN =
        "(?<= matches expected )[\\s|\\S]*(?=\\.)";
    public static final String PHP_ACTUAL_OUTPUT_PATTERN =
        "(?<=Failed asserting that )[\\S|\\s]*(?= matches)";

    public static String phpUnitResultPattern(String method) {
        return "\\d\\) .*::" + method + "\\n";
    }

    public static String phpUnitFailReasonPattern() {
        return "Failed.[\\s|\\S]*(?=\\n\\s)";
    }

    public static String phpUnitFailDetailReasonPattern(String method) {
        return "\\d\\) .*::" + method + "\\n[\\s|\\S]*?(?=(\\d\\)|FAILURES))";
    }

    public static final String G_TEST_METHOD_PATTERN =
        "((TEST\\(|#start[\\s|\\S]*?#end))[\\s|\\S]*?(?=\\{)";
    public static final String G_TEST_METHOD_NAME_PATTERN = "(?<=TEST\\().*(?=,)";
    public static final String G_TEST_SUITE_NAME_PATTERN = "(?<=TEST\\().*(?=,)";
    public static final String G_TEST_NAME_PATTERN = "(?<=TEST\\(.*,).*(?=\\))";
    public static final String G_TEST_TIME_PATTERN = "(?<=test (suites|suite) ran. \\()\\d*(?= ms)";
    public static final String G_TEST_METHOD_FAIL_PATTERN = "\\[  FAILED  \\]";
    public static final String G_TEST_FAIL_REASON_PATTERN =
        "(?<=\\[ RUN.     \\] )[\\s|\\S]*?(?=Expected)";
    public static final String G_TEST_FAIL_DETAIL_REASON_PATTERN =
        "(?<=\\[ RUN.     \\] )[\\s|\\S]*?(?=\\[  FAILED )";
    public static final String G_TEST_ACTUAL_OUTPUT_PATTERN = "(?<=   Which is: (\\\"|)).*";
    public static final String G_TEST_EXPECTED_OUTPUT_PATTERN =
        "(?<=Expected equality of these values:\\n  ).*";
    public static final String G_TEST_EXECUTE_SUCCESS_PATTERN =
        "\\d+ test(s)? from \\d+ test suite(s)? ran\\. \\(\\d+ ms total\\)";

    public static String gTestUnitResultPattern(String method) {
        return "\\[ RUN.*" + method + "\\n[\\s|\\S]*?\\(\\d+ ms\\)";
    }

    public static final String GINKGO_METHOD_PATTERN = "(?<=#start| Describe)[\\s|\\S]*?It\\(.*";
    public static final String GINKGO_METHOD_NAME_PATTERN = "(?<=It\\(\").*(?=\")";
    public static final String GINKGO_SPLIT_RESULT_PATTERN = "\\nTest Suite.*";
    public static final String GINKGO_PASS_PATTERN = "passed";
    public static final String GINKGO_SKIPPED_PATTERN = "skipped";
    public static final String GINKGO_EXPECTED_OUTPUT_PATTERN =
        "(?<=\\>: )[\\S|\\s]*?(?=\\nto equal)";
    public static final String GINKGO_ACTUAL_OUTPUT_PATTERN = "(?<=(to equal\\n|to be)).*";

    public static String getGinkGoName(String name) {
        return "(#start)[\\s|\\S]*?" + name;
    }

    // check文件是否存在测试用例
    public static final String NUNIT_CHECK_TEST_PATTERN = "\\[Test\\]";
    public static final String NUNIT_SPLIT_RESULT_PATTERN = "saved as TestResult.xml\\n";
    public static final String NUNIT_PASS_PATTERN = "Passed";
    public static final String NUNIT_METHOD_PATTERN = "((\\[Test\\]|#start))[\\s|\\S]*?(\\))";
    public static final String NUNIT_METHOD_NAME_PATTERN = "(?<=\\[Test\\])[\\s|\\S]*?\\)";
    public static final String NUNIT_EXPECTED_OUTPUT_PATTERN =
        "(?<=Expected: )[\\s|\\S]*?(?=But was:  )";
    public static final String NUNIT_ACTUAL_OUTPUT_PATTERN = "(?<=But was:  )[\\s|\\S]*";
    public static final List<String> NUNIT_NOTES = getNUNITNotes();

    private static List<String> getNUNITNotes() {
        List<String> javaNotes = new ArrayList<>();
        javaNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        javaNotes.add("\\n *//.*");
        return javaNotes;
    }

    public static String getSplitResultPattern(String runId) {
        return "\n" + Constant.PAAS_UNIT_CONSTANT + runId;
    }

    /**
     * swift
     */
    public static final String SWIFT_XCTEST_CASE = "XCTestCase";

    public static final String SWIFT_CLASS_PATTERN = ".*XCTestCase[\\s|\\S]*?(?=.*XCTestCase)";
    public static final String SWIFT_CLASS_NAME_PATTERN = "(?<=class ).*(?=:)";
    public static final String SWIFT_TEST_PATTERN = "(#start|func test)[\\s|\\S]*?\\)";
    public static final String SWIFT_TEST_NAME_PATTERN = "test.*(?=\\()";
    public static final String SWIFT_FAIL_PATTERN = "error: fatalError";
    public static final String SWIFT_PASS_PATTERN = "passed";
    public static final List<String> SWIFT_NOTES = getSwiftNotes();

    private static List<String> getSwiftNotes() {
        List<String> javaNotes = new ArrayList<>();
        javaNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        javaNotes.add("\\n *//.*");
        return javaNotes;
    }

    public static String getSwiftPassOrFail(String methodName) {
        return "Test Case '" + methodName + "' (passed|failed)";
    }

    public static String getSwiftReasonPattern(String methodName) {
        return "(?<=" + methodName + " : XCTAssertEqual failed: ).*(?= -)";
    }

    public static String getSwiftDetailsPattern(String methodName) {
        return ".*error: " + methodName + " : XCTAssertEqual failed: .*";
    }

    /**
     * 转义正则特殊字符 （$()*+.[]?\^{},|）
     *
     * @param keyword
     * @return
     */
    public static String escapeExprSpecialWord(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            String[] fbsArr = {"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"};
            for (String key : fbsArr) {
                if (keyword.contains(key)) {
                    keyword = keyword.replace(key, "\\" + key);
                }
            }
        }
        return keyword;
    }

    /**
     * Dart
     */
    public static final String DART_MAIN = "main";
    public static final String DART_MAIN_METHOD_PATTERN = ".*main[\\s|\\S]*?(?=main)";
    public static final String DART_TEST_METHOD_PATTERN = "(#start|test)[\\s|\\S]*?(?=}\\))";
    public static final String DART_TEST_METHOD_TEST_NAME_PATTERN = "(?<=test *\\(['\"]).*(?=['\"],)";
    public static final String DART_TEST_EVENT_SEPARATOR_PATTERN = "(?<=})\\s+(?=\\{)";
    public static final String DART_TEST_START_PATTERN = "\"type\":\"testStart\"";
    public static final String DART_MESSAGE_EVENT_PATTERN = "\"type\":\"print\"";
    public static final String DART_TEST_ERROR_PATTERN = "\"type\":\"error\"";
    public static final String DART_TEST_CASE_DONE_PATTERN = "\"type\":\"testDone\"";
    public static final String DART_TEST_DONE_PATTERN = "\"type\":\"done\"";
    public static final String DART_TEST_PASS_PATTERN = "success";
    public static final List<String> DART_NOTES = getDartNotes();

    private static List<String> getDartNotes() {
        List<String> dartNotes = new ArrayList<>();
        dartNotes.add("\\n */\\*[\\s|\\S]*?\\*/");
        dartNotes.add("\\n *//.*");
        return dartNotes;
    }

    public static String TIMEUNIT = " ms";

    public static final String PROTOCOL_NAME_PATTERN = "(?<=#name\\[)[\\s|\\S]*?(?=])";
    public static final String PROTOCOL_INPUT_PATTERN = "(?<=#input\\[).*(?=])";
    public static final String PROTOCOL_EXPECTED_PATTERN = "(?<=#expected\\[).*(?=])";
}

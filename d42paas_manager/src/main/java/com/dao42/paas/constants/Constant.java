package com.dao42.paas.constants;

import java.util.HashMap;
import java.util.Map;

public class Constant {

    /**
     * 特殊字符
     */
    public interface SpecialChar {

        String MID_LINE = "-";
        String UNDER_LINE = "_";
        String SLASH = "/";
        String POINTER = ".";
        String COLON = ":";
        String SEMICOLON = ";";
        /**
         * 半角逗号
         */
        String COMMA = ",";
        String START_SIGN = "*";
        String DOT = ".";
        String NEWLINE = "\n";
    }

    /**
     * 特殊字符
     */
    public interface FILE_SUFFIX {

        String SH = ".sh";
        String TAR_GZ = ".tar.gz";
        String ZIP = ".zip";
        String TAR = ".tar";
    }

    public static final String IGNORE_FILE_NAME = ".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/";
    public static final String PAAS_UNIT_CONSTANT = ".paas-unit-";
    public static final String DEBUG_TAG_FILE = ".breakpoints";

    /**
     * docker constant
     */
    public static final String UNITTEST_RUN_START = "run_start";


    /**
     * go to string key
     */
    public static final Map<String, String> GO_SETTING_MAP = new HashMap<>();

    public static Map<String, String> getGoSettingMap() {
        GO_SETTING_MAP.put("IMPORTS", "import");
        GO_SETTING_MAP.put("INITIALIZATION", "Describe");
        GO_SETTING_MAP.put("SETUP", "BeforeEach");
        GO_SETTING_MAP.put("TEARDOWN", "JustAfterEach");
        return GO_SETTING_MAP;
    }

    /**
     * java to string key
     */
    public static final Map<String, String> JAVA_SETTING_MAP = new HashMap<>();

    public static Map<String, String> getJavaSettingMap() {
        JAVA_SETTING_MAP.put("IMPORTS", "import");
        JAVA_SETTING_MAP.put("INITIALIZATION", "class ");
        JAVA_SETTING_MAP.put("SETUP", "@BeforeAll");
        JAVA_SETTING_MAP.put("TEARDOWN", "@AfterAll");
        return JAVA_SETTING_MAP;
    }

    /**
     * java to string key
     */
    public static final Map<String, String> PYTHON_SETTING_MAP = new HashMap<>();

    public static Map<String, String> getPythonSettingMap() {
        PYTHON_SETTING_MAP.put("IMPORTS", "import");
        PYTHON_SETTING_MAP.put("INITIALIZATION", "class ");
        PYTHON_SETTING_MAP.put("SETUP", "def setup");
        PYTHON_SETTING_MAP.put("TEARDOWN", "def teardown");
        return PYTHON_SETTING_MAP;
    }

    /**
     * language to string key
     */
    public static final Map<String, Map<String, String>> LANGUAGE_SETTING_MAP = new HashMap<>();

    public static Map<String, Map<String, String>> getLanguageSettingMap() {
        LANGUAGE_SETTING_MAP.put("GO", getGoSettingMap());
        LANGUAGE_SETTING_MAP.put("PYTHON", getPythonSettingMap());
        LANGUAGE_SETTING_MAP.put("JAVA", getJavaSettingMap());
        return LANGUAGE_SETTING_MAP;
    }

    public static String PLAYGROUND_SHELL_TYPE_BASH = "bash";
    public static String PLAYGROUND_SHELL_TYPE_NIX_SHELL = "nix-shell";

    public static final String CODE_ZONE_PYTHON_PACKAGE = "pyenv";
    public static final String CODE_ZONE_GO_PACKAGE = "gvm";
    public static final String CODE_ZONE_RUBY_PACKAGE = "rbenv";
    public static final String CODE_ZONE_NODE_PACKAGE = "nvm";
    public static final String CODE_ZONE_JAVA_PACKAGE = "sdk";

    public static String CODE_ZONE_SOURCE_PATH = "dependency/home/<USER>";
//    public static String CODE_ZONE_SOURCE_PATH = "source";
    public static String CODE_ZONE_DEPENDENCY_PATH = "dependency";
    public static String CODE_ZONE_MIDDLEWARE_PATH = "middleware";
    public static String CODE_ZONE_MIDDLEWARE_DATA_PATH = "data";
    public static String CODE_ZONE_MIDDLEWARE_SHELL_PATH = "sh";

    public static String CODE_ZONE_PATH = "/codeZone";

    public static String SHELL_NIX_RENAME = ".1024nix";
    public static String SQL_DATA_DIR = "paasData";
    public static String NIX_CONTAINER_NAME = "NIX-";
    public static String TMP_CONTAINER_NAME = "TMP-";
    public static String DOCKER_USER_NAME = "runner";
    public static String DOCKER_MOUNT_START = "/mount.sh";
    public static String DOCKER_HOST_NAME = "clackyai-machine";

    public static Integer HOUR_TO_MILLISECOND = 3600000;
    public static Integer MINUTES_TO_MILLISECOND = 60000;

    public static Integer UNITTEST_RUN_TIMEOUT = 180;
    public static Integer UNITTEST_RUN_TIMEOUT_MILLISECOND = 180000;

    public static Integer READ_FILE_SIZE_LIMIT = 5242880;
    public static String READ_FILE_SIZE_LIMIT_ERROR_MSG = "文件内容过大，无法读取";

    public static Integer FILE_SIZE_LIMIT = 1048576;

    public static Integer FILE_SIZE_LIMIT_100M = 104857600;
    public static Integer FILES_NUM_LIMIT = 10;
    public static String FILES_NUM_LIMIT_ERROR_MSG = "文件数量异常，正常区间[1-10]";

    public static String PRIVILEGE_777 = "777";

    public static String PRIVILEGE_RUNNER = "runner:runner";

    public interface FILE_CHECK_RESULT {

        Integer OK_CODE = 10000;
        Integer FILE_NOT_EXIST_CODE = 10001;
        String FILE_NOT_EXIST_MSG = "文件不存在";
        Integer EXCEED_SIZE_LIMIT_CODE = 10002;
        String EXCEED_SIZE_LIMIT_MSG = "文件大小超出参数限制";
        Integer TYPE_NOT_MATCH_CODE = 10003;
        String TYPE_NOT_MATCH_MSG = "文件类型超出参数限制";
        Integer IS_BINARY_FILE_CODE = 10004;
        String IS_BINARY_FILE_MSG = "非普通文本文件，为二进制文件";
        Integer FILE_IS_EMPTY_CODE = 10005;
        String FILE_IS_EMPTY_MSG = "请求文件不能为空";
    }

    public interface CHECKOUT_TASK_RESULT {
        String CHECKOUT_BRANCH_STATUS_PROCESSING = "PROCESSING";    // 切换分支任务处理中
        String CHECKOUT_BRANCH_STATUS_COMPLETED = "COMPLETED"; // 切换分支任务完成
        String CHECKOUT_BRANCH_STATUS_FAILED = "FAILED";      // 切换分支任务失败
        String CHECKOUT_BRANCH_STATUS_NOT_FOUND = "NOT_FOUND";
    }

    public static String RUN_CMD_STATUS_CODE = "0";

    public static final String MYSQL_HEALTH_CHECK = "for ((i = 1; i <= 10; i++)); do\n"
        + "\tmysqladmin ping --user=root --socket=$SOCKET >/dev/null 2>&1\n"
        + "\tif [ $? == 0 ]; then\n"
        + "\t\tbreak\n"
        + "\telse\n"
        + "\t\tsleep .5\n"
        + "\tfi\n"
        + "done\n";
}

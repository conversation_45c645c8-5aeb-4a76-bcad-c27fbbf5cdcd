package com.dao42.paas.config.properties;

import lombok.Data;

@Data
public class Playground {

    /**
     * 回收前保留天数
     */
    private int destroyDays;

    private int heartbeatSeconds;

    /**
     * 过期时间
     */
    private InactiveSeconds inactiveSeconds;

    private long ticketSecret;

    @Data
    public static class InactiveSeconds {

        /**
         * 代码区
         */
        private Long codeZone;

        /**
         * 代码区拷贝
         */
        private Long codeZoneCopy;

        /**
         * 代码区快照
         */
        private Long codeZoneSnapshot;
        
        /**
         * 批量创建超时时间
         */
        private Long batchCreate;


    }

}
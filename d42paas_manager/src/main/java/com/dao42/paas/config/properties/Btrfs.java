package com.dao42.paas.config.properties;

import lombok.Data;

/**
 * BTRFS 配置
 *
 * <AUTHOR>
 */

@Data
public class Btrfs {

    /**
     * btrfs系统地址
     */
    private String host;

    /**
     * btrfs 端口
     */
    private Integer port;

    /**
     * btrfs 用户名
     */
    private String userName;

    /**
     * btrfs 私钥
     */
    private String keyPath;

    /**
     * 写入命令
     */
    private String createCmd;

    /**
     * 删除命令
     */
    private String removeCmd;
    /**
     * check磁盘快照是否在运行的文件
     */
    private String checkDiskSnapshotLockFile;
    
    /**
     * 快照创建的速率限制（每秒）
     */
    private Long snapshotRateLimit=10L;

    /**
     * 快照令牌等待时间（单位秒）
     */
    private Long snapshotWaitLimit;

    private boolean multiMode;
}
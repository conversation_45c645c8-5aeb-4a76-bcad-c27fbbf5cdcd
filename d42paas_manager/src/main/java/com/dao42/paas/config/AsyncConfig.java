package com.dao42.paas.config;

import cn.hutool.core.thread.BlockPolicy;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.service.QcloudService;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurerSupport;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
@EnableAsync
@Configuration
public class AsyncConfig extends AsyncConfigurerSupport {

    /**
     * 激活Docker专用线程池
     */
    public final static String ACTIVATE_THREAD_POOL = "activePool";

    /* 普通线程池 */
    @Value("${task.pool.core-pool-size:10}")
    public int corePoolSize;

    @Value("${task.pool.queue-capacity:100}")
    public int queueCapacity;

    @Value("${task.pool.max-pool-size:50}")
    public int maxPoolSize;

    @Value("${task.pool.keep-alive-seconds:60}")
    public int keepAliveSeconds;

    /* Docker生命周期相关线程池 */
    @Value("${task.activate.core-pool-size:10}")
    public int activateCorePoolSize;

    @Value("${task.activate.queue-capacity:100}")
    public int activateQueueCapacity;

    @Value("${task.activate.max-pool-size:50}")
    public int activateMaxPoolSize;

    @Value("${task.activate.keep-alive-seconds:60}")
    public int activateKeepAliveSeconds;

    @Autowired
    private ExceptionMsgBot exceptionMsgBot;

    @Autowired
    private QcloudService qcloudService;

    /**
     * 激活流程使用单独线程池
     *
     * @return
     */
    @Bean(name = ACTIVATE_THREAD_POOL)
    public Executor activeTaskPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("paas-active-");
        executor.setCorePoolSize(activateCorePoolSize);
        executor.setQueueCapacity(activateQueueCapacity);
        executor.setMaxPoolSize(activateMaxPoolSize);
        executor.setKeepAliveSeconds(activateKeepAliveSeconds);
        executor.setTaskDecorator(this.getTaskDecorator());
        // 激活流程不能丢任务，由调用者去执行
        executor.setRejectedExecutionHandler(new CallerRunsPolicy());
        executor.initialize();
        // 添加异步线程池性能监控指标
        // https://github.com/spring-projects/spring-boot/issues/23818
        // https://stackoverflow.com/questions/56176852/how-to-enable-executorservicemetrics-in-springboot-2-1-2
        return executor;
    }

    /**
     * 异步线程配置
     *
     * @return
     */
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("paas-async-");
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        executor.setTaskDecorator(this.getTaskDecorator());
        executor.setRejectedExecutionHandler(new BlockPolicy());
        executor.initialize();
        // 添加异步线程池性能监控指标
        // https://github.com/spring-projects/spring-boot/issues/23818
        // https://stackoverflow.com/questions/56176852/how-to-enable-executorservicemetrics-in-springboot-2-1-2
        return executor;
    }

    /**
     * 添加上下文日志参数
     *
     * @return 包含日志上下文的装饰器
     */
    private TaskDecorator getTaskDecorator() {
        return runnable -> {
            // 复制主线程上下文，传递给新线程
            SecurityContext securityContext = SecurityContextHolder.getContext();
            Map<String, String> mdcContext = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    SecurityContextHolder.setContext(securityContext);
                    if (mdcContext != null) {
                        MDC.setContextMap(mdcContext);
                    }
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        };
    }

    /**
     * @return
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (throwable, method, objects) -> {
            log.error("Async Uncaught Exception:{}, method:{}, params:{}",
                throwable,
                method.getDeclaringClass().getName() + "." + method.getName(),
                objects);
            throwable.printStackTrace();
        };
    }

}
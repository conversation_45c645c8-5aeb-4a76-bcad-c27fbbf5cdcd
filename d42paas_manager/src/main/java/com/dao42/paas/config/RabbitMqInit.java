package com.dao42.paas.config;

import com.dao42.paas.common.constants.RoutingKeyPrefix;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.model.IDEServer;
import com.dao42.paas.repository.IDEServerRepository;
import com.dao42.paas.service.ideserver.LoadIDEServerSelector;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class RabbitMqInit {

    private final SystemProperties systemProperties;

    private final IDEServerRepository ideServerRepository;

    private final LoadIDEServerSelector ideServerSelector;

    private final AmqpAdmin amqpAdmin;

    @PostConstruct
    public void envInit() {
        initMq();
    }

    private void initMq() {
        /* ********** */
        /* 1.声明交换机 */
        /* ********** */

        // 1.a.主交换机
        Exchange paasExchange = ExchangeBuilder.topicExchange(systemProperties.getRabbitMq().getExchange())
            .alternate(systemProperties.getRabbitMq().getAlterExchange())
            .ignoreDeclarationExceptions()
            .build();
        amqpAdmin.declareExchange(paasExchange);
        // 1.b.异常（死信）消息交换机
        Exchange paasAlterExchange = ExchangeBuilder.topicExchange(systemProperties.getRabbitMq().getAlterExchange())
            .ignoreDeclarationExceptions()
            .build();
        amqpAdmin.declareExchange(paasAlterExchange);

        /* ********* */
        /* 2.声明队列 */
        /* ********* */

        // 2.a.发给Manager的消息队列
        Queue toManager = QueueBuilder.durable(systemProperties.getRabbitMq().getQueue().getManager())
            .maxPriority(10).build();
        amqpAdmin.declareQueue(toManager);
        // 2.b.发给Playground的消息队列
        Queue toPlayground = QueueBuilder.durable(systemProperties.getRabbitMq().getQueue().getPlayground())
            .maxPriority(10).build();
        amqpAdmin.declareQueue(toPlayground);

        /* ***************** */
        /* 3.声明交换机路由规则 */
        /* ***************** */

        // 3.a.发给Manager的消息路由规则
        Binding toManagerBinding =
            BindingBuilder.bind(toManager).to(paasExchange).with(RoutingKeyPrefix.TO_MANAGER).noargs();
        amqpAdmin.declareBinding(toManagerBinding);
        // 3.b.发给playground的消息路由规则
        Binding toPlaygroundBinding =
            BindingBuilder.bind(toPlayground).to(paasExchange).with(RoutingKeyPrefix.TO_PLAYGROUND + "#").noargs();
        amqpAdmin.declareBinding(toPlaygroundBinding);

        /* ******************************** */
        /* 4.声明已知IDE Server的队列和路由规则 */
        /* ******************************** */

        List<IDEServer> serverList = ideServerRepository.findAllByOnlineTrue();
        for (IDEServer ideServer : serverList) {
            if (ideServer.isTest()) {
                ideServerSelector.removeLoad(ideServer);
            } else {
                ideServerSelector.initLoad(ideServer);
            }
            Queue ideServerQueue = QueueBuilder.durable(ideServer.getCode())
                .maxPriority(10)
                .autoDelete()
                .ttl(systemProperties.getRabbitMq().getMessageTtl())
                .deadLetterExchange(systemProperties.getRabbitMq().getAlterExchange())
                .build();
            amqpAdmin.declareQueue(ideServerQueue);
            Binding ideServerBinding =
                BindingBuilder.bind(ideServerQueue).to(paasExchange).with(ideServer.getCode() + ".#").noargs();
            amqpAdmin.declareBinding(ideServerBinding);
        }

        /* ******************************* */
        /* 5.异常消息由异常（死信）消息交换机路由 */
        /* ******************************* */
        Queue exceptionQueue = QueueBuilder.durable(systemProperties.getRabbitMq().getQueue().getException()).build();
        amqpAdmin.declareQueue(exceptionQueue);
        Binding exceptionBinding = BindingBuilder.bind(exceptionQueue).to(paasAlterExchange).with("#").noargs();
        amqpAdmin.declareBinding(exceptionBinding);
    }
}

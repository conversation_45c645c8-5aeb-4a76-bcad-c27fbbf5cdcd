package com.dao42.paas.config;

import com.github.woostju.ssh.pool.SshClientPoolConfig;
import com.github.woostju.ssh.pool.SshClientsPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Configuration
public class SshConfig {

    @Bean
    public SshClientsPool clientPool() {
        SshClientPoolConfig poolConfig = new SshClientPoolConfig();
        poolConfig.setMaxTotalPerKey(200);
        poolConfig.setMaxIdlePerKey(200);
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setMaxWaitMillis(1000L * 20);
        poolConfig.setMinEvictableIdleTimeMillis(1000L * 20);
        poolConfig.setTimeBetweenEvictionRunsMillis(1000L * 20);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        //disable jmx
        poolConfig.setJmxEnabled(false);
        return new SshClientsPool(poolConfig);
    }

}

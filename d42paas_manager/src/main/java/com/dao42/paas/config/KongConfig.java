package com.dao42.paas.config;

import com.taofen8.mid.kong.admin.JKongAdmin;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Kong网关配置类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Configuration
public class KongConfig {

    @Value("${kong.domain.admin}")
    private String kongAdminUrl;

    /**
     * 生成KongAdmin客户端
     *
     * @return JKongAdmin
     */
    @Bean
    public JKongAdmin jKongAdmin() {
        return new JKongAdmin(kongAdminUrl);
    }
}

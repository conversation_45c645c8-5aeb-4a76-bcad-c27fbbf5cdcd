package com.dao42.paas.config;

import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.service.RedisExternalService;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.sdk.autoconfigure.AutoConfiguredOpenTelemetrySdk;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

/**
 * <AUTHOR>
 * @date 2021/12/30
 */
@Configuration
@Slf4j
public class ObservableConfig {
    @Value("${spring.application.name}")
    private String appName;

    @Autowired private RedisExternalService redisExternalService;

    @Autowired private StringRedisTemplate redisTemplate;

    @Bean
    public OpenTelemetry openTelemetry() {
        return AutoConfiguredOpenTelemetrySdk.builder()
                .setResultAsGlobal(false)
                .build()
                .getOpenTelemetrySdk();
    }

    @Bean
    public Tracer tracer() {
        return openTelemetry().getTracer(appName);
    }

    @Bean
    public Meter meter() {
        return openTelemetry().getMeter(appName);
    }

    @Bean
    public void customMetrics() {
        meter().gaugeBuilder("paas_current_ideservers")
                .setDescription("当前IDE Server数")
                .setUnit("1")
                .buildWithCallback(
                        result -> {
                            Long size =
                                    redisTemplate.opsForZSet().size(RedisPrefix.IDE_SERVER_LOAD);
                            result.record(size == null ? 0 : size);
                        });
        meter().gaugeBuilder("paas_current_socketio_connections")
                .setDescription("当前Socket.io连接数")
                .setUnit("1")
                .buildWithCallback(
                        result -> {
                            Set<TypedTuple<String>> scores =
                                    redisTemplate
                                            .opsForZSet()
                                            .rangeWithScores(RedisPrefix.IDE_SERVER_LOAD, 0, -1);
                            if (scores == null) {
                                return;
                            }
                            double totalOnlineCount =
                                    scores.stream()
                                            .filter(Objects::nonNull)
                                            .map(TypedTuple::getScore)
                                            .filter(Objects::nonNull)
                                            .mapToDouble(Double::doubleValue)
                                            .sum();
                            result.record(totalOnlineCount);
                        });
        meter().gaugeBuilder("paas_current_playgrounds")
                .setDescription("当前活跃的playground数量")
                .setUnit("1")
                .buildWithCallback(
                        result ->
                                result.record(
                                        redisExternalService.size(
                                                RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + "*")));
        meter().gaugeBuilder("paas_current_dockers")
                .setDescription("当前运行的docker数量")
                .setUnit("1")
                .buildWithCallback(
                        result ->
                                result.record(
                                        redisExternalService.size(
                                                RedisPrefix.PREFIX_DOCKER_INFO + "*")));
    }
}

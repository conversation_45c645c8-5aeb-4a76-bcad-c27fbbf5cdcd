package watch

import "time"

type Config struct {
	Entries []WatchEntry `yaml:"watch"`

	OldGlobalProfile *string `yaml:"profile"`
}

// WatchEntry is the configuration of one watch entry (directory) which is handled in a separate go-routine
type WatchEntry struct {
	Directory string `yaml:"directory"`
}

// AddEntry allows to add a new directory watch
func (c *Config) AddEntry(e WatchEntry) {
	c.Entries = append(c.Entries, e)
}

// GetEntryByDirectory returns the watch configuration of a given directory
func (c *Config) GetEntryByDirectory(dir string) WatchEntry {
	for _, e := range c.Entries {
		if e.Directory == dir {
			return e
		}
	}

	return WatchEntry{}
}

type RefreshConfig struct {
	Refresh      bool
	IntervalTime time.Duration
}

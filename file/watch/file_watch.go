package watch

import (
	"agent/consts"
	"agent/utils/log"
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/karrick/godirwalk"
)

type FileChange struct {
	Path   string `json:"path"`
	Change int    `json:"change"`
	Key    int    `json:"type"`
}

type Wrapper struct {
	fileChangeChannel     chan<- []FileChange
	fileChangeRagChannel  chan<- []FileChange
	linterFileChangesChan chan<- []FileChange
	fileChanges           []FileChange
	fileCache             map[string]*NodeInfo
	mu                    sync.Mutex
	wg                    sync.WaitGroup
	RefreshConfig         *RefreshConfig
	lastRefresh           time.Time
	watcher               *fsnotify.Watcher
}

func MakeNew(
	fileChangeChannel chan<- []FileChange,
	fileChangeRagChannel chan<- []FileChange,
	linterFileChanges<PERSON>han chan<- []FileChange,
	liveLoadConfig *RefreshConfig,
) *Wrapper {
	wrapper := Wrapper{}
	wrapper.fileChangeChannel = fileChangeChannel
	wrapper.fileChangeRagChannel = fileChangeRagChannel
	wrapper.fileCache = make(map[string]*NodeInfo)
	wrapper.RefreshConfig = liveLoadConfig
	wrapper.lastRefresh = time.Now()
	wrapper.linterFileChangesChan = linterFileChangesChan
	return &wrapper
}

// Watch starts the watch process of a defined directories of the given configuration
// fileChangeChannel 信息以FileChange#Change 降序
func (wrapper *Wrapper) Watch(ctx context.Context, c Config, interval time.Duration) {
	for {
		wrapper.doWatch(c)
		// 排序
		sort.Slice(wrapper.fileChanges, func(i, j int) bool {
			return wrapper.fileChanges[i].Change > wrapper.fileChanges[j].Change
		})
		// 每次循环检查释放要向前端发送 mq 消息
		if len(wrapper.fileChanges) > 0 {
			wrapper.fileChangeChannel <- wrapper.fileChanges
		}
		select {
		case <-ctx.Done():
			return
		case <-time.After(interval):
			continue
		}
	}
}

func (wrapper *Wrapper) doWatch(c Config) {
	now := time.Now()
	refresh := wrapper.RefreshConfig.Refresh && wrapper.lastRefresh.Add(wrapper.RefreshConfig.IntervalTime).Before(now)
	if refresh {
		wrapper.lastRefresh = now
	}
	wrapper.fileChanges = []FileChange{}
	wrapper.wg.Add(len(c.Entries))
	for _, e := range c.Entries {
		go wrapper.walkSingleDirectory(c.GetEntryByDirectory(e.Directory), refresh)
	}
	wrapper.wg.Wait()
	wrapper.garbageCollection()
}

func (wrapper *Wrapper) walkSingleDirectory(we WatchEntry, refresh bool) {
	wrapper.mu.Lock()
	defer wrapper.mu.Unlock()
	defer wrapper.wg.Done()
	err := godirwalk.Walk(we.Directory, &godirwalk.Options{
		Callback: func(osPathname string, directoryEntry *godirwalk.Dirent) error {

			// skip dir
			if directoryEntry.IsDir() {
				if isSkipDir(directoryEntry.Name()) {
					return godirwalk.SkipThis
				}
				wrapper.isDirChanged(osPathname)
				return nil
			}

			if isSkipFile(osPathname) {
				return nil
			}

			fileChanged := wrapper.isFileChanged(osPathname, refresh)
			if fileChanged {
				log.Printf("pathname: %s", osPathname)
			}

			return nil
		},
		Unsorted: true,
	})

	if err != nil {
		log.Printf("file.watch.fail:%s", err.Error())
	}
}

func (wrapper *Wrapper) isFileChanged(path string, refresh bool) bool {
	//if refresh {
	//	open, err := os.Open(path)
	//	if err != nil {
	//		return false
	//	}
	//	open.Close()
	//}
	fileInfo, err := os.Stat(path)
	if err != nil {
		return false
	}
	nodeInfo, found := wrapper.fileCache[path]
	currentModificationTime := fileInfo.ModTime()
	changed := false
	changedType := -1
	if !found {
		nodeInfo = &NodeInfo{}
		wrapper.fileCache[path] = nodeInfo
		changedType = consts.FileChangeCreate
	} else if refresh && nodeInfo.modificationUnixTime < currentModificationTime.Unix() {
		changed = true
		changedType = consts.FileChangeUpdate
	}
	if changedType != -1 {
		currentTime := time.Now()
		if err := os.Chtimes(path, currentTime, currentModificationTime); err != nil {
			log.Errorf("Error touching file, path: %s", path)
		}
		nodeInfo.modificationUnixTime = currentModificationTime.Unix()
		path = strings.Replace(path, consts.AppRootDirChild, "", 1)
		wrapper.fileChanges = append(wrapper.fileChanges, FileChange{Path: path, Change: changedType})
	}
	return changed
}

func (wrapper *Wrapper) garbageCollection() {
	wrapper.mu.Lock()
	defer wrapper.mu.Unlock()
	for path, info := range wrapper.fileCache {
		if !FileExists(path) {
			delete(wrapper.fileCache, path)
			path = strings.Replace(path, consts.AppRootDirChild, "", 1)
			wrapper.fileChanges = append(wrapper.fileChanges, FileChange{Path: path, Change: consts.FileChangeRemove, Key: info.key})
		}
	}
}

func (wrapper *Wrapper) isDirChanged(path string) {
	fileInfo, err := os.Stat(path)
	if err != nil {
		return
	}
	nodeInfo, found := wrapper.fileCache[path]
	currentModificationTime := fileInfo.ModTime()
	if !found {
		nodeInfo := &NodeInfo{
			modificationUnixTime: currentModificationTime.Unix(),
			key:                  1,
		}
		wrapper.fileCache[path] = nodeInfo
		path = strings.Replace(path, consts.AppRootDirChild, "", 1)
		wrapper.fileChanges = append(wrapper.fileChanges, FileChange{Path: path, Change: consts.FileChangeCreate, Key: 1})
	} else {
		nodeInfo.modificationUnixTime = currentModificationTime.Unix()
	}

}

// AddDirToFsnotify add dir to notify
func (wrapper *Wrapper) AddDirToFsnotify(osPathname string) error {
	if osPathname == "" {
		return errors.New("path is empty")
	}

	return wrapper.watcher.Add(osPathname)
}

func (wrapper *Wrapper) OSWatch(ctx context.Context, c Config, duration time.Duration) {
	wrapper.fileChanges = nil
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return
	}
	defer watcher.Close()

	wrapper.watcher = watcher
	changes := make(chan FileChange)
	for _, item := range c.Entries {
		err = godirwalk.Walk(item.Directory, &godirwalk.Options{
			Callback: func(osPathname string, directoryEntry *godirwalk.Dirent) error {
				key := consts.FileType
				if directoryEntry.IsDir() {
					if isSkipDir(directoryEntry.Name()) {
						return godirwalk.SkipThis
					}
					key = consts.DirType
					watcher.Add(osPathname)
				}
				wrapper.fileCache[osPathname] = &NodeInfo{key: key}
				return nil
			},
			Unsorted: true,
		})
		if err != nil {
			log.PrintPanicInfo("osWatchHandler panic err: %+v", err)
			continue
		}
	}

	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("osWatchHandler panic err1: %+v", panicErr)
			}
		}()
		wrapper.osWatchHandler(ctx, watcher, changes)
	}()

	fileChangeMap := make(map[string]FileChange)
	// rag使用有序队列存储
	//fileChangeRagList := make([]FileChange, 0)
	for {
		select {
		case <-time.After(duration):
			// 缓冲发送
			//if len(fileChangeRagList) > 0 {
			//	if wrapper.fileChangeRagChannel != nil {
			//		wrapper.fileChangeRagChannel <- fileChangeRagList
			//	}
			//	fileChangeRagList = make([]FileChange, 0)
			//}

			if len(fileChangeMap) == 0 {
				continue
			}
			var fileChanges []FileChange
			for _, change := range fileChangeMap {
				fileChanges = append(fileChanges, change)
			}
			sort.Slice(fileChanges, func(i, j int) bool {
				return fileChanges[i].Change > fileChanges[j].Change
			})
			wrapper.fileChangeChannel <- fileChanges

			fileChangesBt, _ := json.Marshal(fileChanges)
			fileChangesTxt := string(fileChangesBt)
			if fileChangesTxt != "" {
				log.Infof("OSWatch-fileChanges: %s", fileChangesTxt)
			}

			fileChangeMap = make(map[string]FileChange)
		case change := <-changes:
			//log.Infof("OSWatch-fileChange: %+v", change)
			// 文件的chmod信息不需要同步到ide server
			if change.Path == "" {
				continue
			}

			if change.Change == consts.FileChangeChmod {
				continue
			}

			// 通知linter模块有文件发生变化
			go func() {
				wrapper.linterFileChangesChan <- []FileChange{change}
			}()

			// 做rag的时候，发现事件有概率丢失，由于不确定初始设计，所以rag使用新的chan
			//fileChangeRagList = append(fileChangeRagList, change)

			oldFileChange, ok := fileChangeMap[change.Path]
			if !ok {
				fileChangeMap[change.Path] = change
				continue
			}
			oldChangeType := oldFileChange.Change
			newChangeType := change.Change
			if oldChangeType == newChangeType {
				continue
			}
			switch oldChangeType {
			case consts.FileChangeUpdate:
				fileChangeMap[change.Path] = change
			case consts.FileChangeCreate:
				switch newChangeType {
				case consts.FileChangeRemove:
					delete(fileChangeMap, change.Path)
				}
			case consts.FileChangeRemove:
				if change.Key == consts.FileType && newChangeType == consts.FileChangeCreate {
					change.Change = consts.FileChangeUpdate
				}
				fileChangeMap[change.Path] = change
			}
		case <-ctx.Done():
			return
		}
	}
}

func (wrapper *Wrapper) osWatchHandler(ctx context.Context, watcher *fsnotify.Watcher, changes chan<- FileChange) {
	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				continue
			}
			path := event.Name
			key := consts.FileType
			change := -1
			log.Infof("osWatchHandler-event: %+v, path: %s", event, path)
			if event.Has(fsnotify.Write) {
				change = consts.FileChangeUpdate
			}
			if event.Has(fsnotify.Create) || event.Has(fsnotify.Chmod) {
				stat, err := os.Stat(path)
				if err != nil {
					log.Errorf("osWatchHandler-event0: %+v, path: %s", event, path)
					continue
				}
				if stat.IsDir() {
					key = consts.DirType
					if !isSkipDir(path) {
						watcher.Add(path)
					}
				}
				wrapper.fileCache[path] = &NodeInfo{key: key}
				change = consts.FileChangeCreate
				if event.Has(fsnotify.Chmod) {
					change = consts.FileChangeChmod
				}
			}
			if event.Has(fsnotify.Remove) || event.Has(fsnotify.Rename) {
				info, ok := wrapper.fileCache[path]
				if ok && (info.key == consts.DirType) {
					key = consts.DirType
					watcher.Remove(path)
				}
				delete(wrapper.fileCache, path)
				change = consts.FileChangeRemove
			}
			if change == -1 {
				continue
			}

			if path != "" {
				fileChange := FileChange{Path: strings.Replace(path, consts.AppRootDirChild, "", 1), Change: change, Key: key}
				changes <- fileChange
				log.Infof("osWatchHandler-event2: %+v, FileChange: %+v", event, fileChange)
			}
		case <-ctx.Done():
			return
		}
	}
}

func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

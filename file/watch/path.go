package watch

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/fileUtils"
	"path"
)

var dirIgnores, fileIgnores = fileUtils.GetDirAndFileIgnore(envUtils.GetString(consts.PAAS_FileTreeIgnore))
var dirIgnoresForRag, fileIgnoresForRag = fileUtils.GetDirAndFileIgnore(envUtils.GetString(consts.PAAS_FileRagIgnore))

func init() {
	dirIgnores = append(dirIgnores, []string{
		"bower_components",
		"node_modules",
		".idea",
		".git",
		".svn",
	}...)

	dirIgnoresForRag = append(dirIgnoresForRag, []string{
		"bower_components",
		"node_modules",
		".idea",
		".git",
		".svn",
		"build",
		".venv",
		"venv",
		"dist",
	}...)

	fileIgnoresForRag = append(fileIgnoresForRag, []string{
		"LICENSE",
		"package-lock.json",
		".gitignore",
		"*.lock",
	}...)
}

func isSkipDir(dir string) bool {
	return fileUtils.IsIgnoreDir(dir, dirIgnores)
}

func isSkipFile(path string) bool {
	return fileUtils.IsIgnoreFile(path, fileIgnores)
}

func IsSkipDirForRag(dir string) bool {
	return fileUtils.IsIgnoreDir(dir, dirIgnoresForRag)
}

func IsSkipFileForRag(filePath string) bool {
	ext := path.Base(filePath)
	return fileUtils.IsIgnoreFile(ext, fileIgnoresForRag)
}

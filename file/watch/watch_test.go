package watch

import (
	"testing"
	"time"
)

func TestConfig_AddEntry(t *testing.T) {
	config := &Config{}

	// 测试添加条目
	entry := WatchEntry{Directory: "/test/dir"}
	config.AddEntry(entry)

	if len(config.Entries) != 1 {
		t.<PERSON>rrorf("期望条目数量 1，实际 %d", len(config.Entries))
	}

	if config.Entries[0].Directory != "/test/dir" {
		t.<PERSON><PERSON><PERSON>("期望目录 /test/dir，实际 %s", config.Entries[0].Directory)
	}

	// 测试添加多个条目
	entry2 := WatchEntry{Directory: "/test/dir2"}
	config.AddEntry(entry2)

	if len(config.Entries) != 2 {
		t.<PERSON>rrorf("期望条目数量 2，实际 %d", len(config.Entries))
	}
}

func TestConfig_GetEntryByDirectory(t *testing.T) {
	config := &Config{}

	// 添加测试条目
	entry1 := WatchEntry{Directory: "/test/dir1"}
	entry2 := WatchEntry{Directory: "/test/dir2"}
	config.AddEntry(entry1)
	config.AddEntry(entry2)

	// 测试找到存在的目录
	found := config.GetEntryByDirectory("/test/dir1")
	if found.Directory != "/test/dir1" {
		t.Errorf("期望找到目录 /test/dir1，实际 %s", found.Directory)
	}

	// 测试找不到的目录
	notFound := config.GetEntryByDirectory("/nonexistent")
	if notFound.Directory != "" {
		t.Errorf("期望空目录，实际 %s", notFound.Directory)
	}
}

func TestProfile_Default(t *testing.T) {
	if len(Default.Extensions) != 1 {
		t.Errorf("期望默认配置有1个扩展名，实际 %d", len(Default.Extensions))
	}

	if Default.Extensions[0] != "*" {
		t.Errorf("期望默认扩展名为 *，实际 %s", Default.Extensions[0])
	}
}

func TestProfile_LESS(t *testing.T) {
	if len(LESS.Extensions) != 1 {
		t.Errorf("期望LESS配置有1个扩展名，实际 %d", len(LESS.Extensions))
	}

	if LESS.Extensions[0] != ".less" {
		t.Errorf("期望LESS扩展名为 .less，实际 %s", LESS.Extensions[0])
	}
}

func TestProfile_Magento2Theme(t *testing.T) {
	expectedExtensions := []string{".css", ".js", ".less", ".sass", ".ts"}

	if len(Magento2Theme.Extensions) != len(expectedExtensions) {
		t.Errorf("期望Magento2Theme配置有%d个扩展名，实际 %d", len(expectedExtensions), len(Magento2Theme.Extensions))
	}

	for i, ext := range expectedExtensions {
		if Magento2Theme.Extensions[i] != ext {
			t.Errorf("期望扩展名 %s，实际 %s", ext, Magento2Theme.Extensions[i])
		}
	}
}

func TestProfile_Magento2(t *testing.T) {
	expectedExtensions := []string{".css", ".html", ".less", ".sass", ".js", ".php", ".phtml", ".ts", ".xml"}

	if len(Magento2.Extensions) != len(expectedExtensions) {
		t.Errorf("期望Magento2配置有%d个扩展名，实际 %d", len(expectedExtensions), len(Magento2.Extensions))
	}

	for i, ext := range expectedExtensions {
		if Magento2.Extensions[i] != ext {
			t.Errorf("期望扩展名 %s，实际 %s", ext, Magento2.Extensions[i])
		}
	}
}

func TestProfile_SASS(t *testing.T) {
	expectedExtensions := []string{".sass", ".scss"}

	if len(SASS.Extensions) != len(expectedExtensions) {
		t.Errorf("期望SASS配置有%d个扩展名，实际 %d", len(expectedExtensions), len(SASS.Extensions))
	}

	for i, ext := range expectedExtensions {
		if SASS.Extensions[i] != ext {
			t.Errorf("期望扩展名 %s，实际 %s", ext, SASS.Extensions[i])
		}
	}
}

func TestProfile_VueStorefront(t *testing.T) {
	expectedExtensions := []string{".css", ".js", ".sass", ".ts"}

	if len(VueStorefront.Extensions) != len(expectedExtensions) {
		t.Errorf("期望VueStorefront配置有%d个扩展名，实际 %d", len(expectedExtensions), len(VueStorefront.Extensions))
	}

	for i, ext := range expectedExtensions {
		if VueStorefront.Extensions[i] != ext {
			t.Errorf("期望扩展名 %s，实际 %s", ext, VueStorefront.Extensions[i])
		}
	}
}

func TestProfile_Javascript(t *testing.T) {
	expectedExtensions := []string{".js", ".ts"}

	if len(Javascript.Extensions) != len(expectedExtensions) {
		t.Errorf("期望Javascript配置有%d个扩展名，实际 %d", len(expectedExtensions), len(Javascript.Extensions))
	}

	for i, ext := range expectedExtensions {
		if Javascript.Extensions[i] != ext {
			t.Errorf("期望扩展名 %s，实际 %s", ext, Javascript.Extensions[i])
		}
	}
}

func TestNodeInfo(t *testing.T) {
	nodeInfo := &NodeInfo{
		modificationUnixTime: 1234567890,
		key:                  1,
	}

	if nodeInfo.modificationUnixTime != 1234567890 {
		t.Errorf("期望修改时间 1234567890，实际 %d", nodeInfo.modificationUnixTime)
	}

	if nodeInfo.key != 1 {
		t.Errorf("期望key 1，实际 %d", nodeInfo.key)
	}
}

func TestRefreshConfig(t *testing.T) {
	config := &RefreshConfig{
		Refresh:      true,
		IntervalTime: time.Second * 5,
	}

	if !config.Refresh {
		t.Error("期望Refresh为true")
	}

	if config.IntervalTime != time.Second*5 {
		t.Errorf("期望间隔时间 5秒，实际 %v", config.IntervalTime)
	}
}

func TestIsSkipDir(t *testing.T) {
	// 测试应该跳过的目录
	skipDirs := []string{"node_modules", ".git", ".idea", "bower_components"}
	for _, dir := range skipDirs {
		if !isSkipDir(dir) {
			t.Errorf("目录 %s 应该被跳过", dir)
		}
	}

	// 测试不应该跳过的目录
	normalDirs := []string{"src", "test", "docs", "config"}
	for _, dir := range normalDirs {
		if isSkipDir(dir) {
			t.Errorf("目录 %s 不应该被跳过", dir)
		}
	}
}

func TestIsSkipFile(t *testing.T) {
	// 测试应该跳过的文件
	skipFiles := []string{".DS_Store"}
	for _, file := range skipFiles {
		if !isSkipFile(file) {
			t.Errorf("文件 %s 应该被跳过", file)
		}
	}

	// 测试不应该跳过的文件
	normalFiles := []string{"main.go", "test.txt", "config.yaml", "Thumbs.db"}
	for _, file := range normalFiles {
		if isSkipFile(file) {
			t.Errorf("文件 %s 不应该被跳过", file)
		}
	}
}

func TestIsSkipDirForRag(t *testing.T) {
	// 测试应该跳过的目录
	skipDirs := []string{"node_modules", ".git", ".idea", "build", "dist", "venv"}
	for _, dir := range skipDirs {
		if !IsSkipDirForRag(dir) {
			t.Errorf("RAG目录 %s 应该被跳过", dir)
		}
	}

	// 测试不应该跳过的目录
	normalDirs := []string{"src", "test", "docs"}
	for _, dir := range normalDirs {
		if IsSkipDirForRag(dir) {
			t.Errorf("RAG目录 %s 不应该被跳过", dir)
		}
	}
}

func TestIsSkipFileForRag(t *testing.T) {
	// 测试应该跳过的文件
	skipFiles := []string{"LICENSE", "package-lock.json", ".gitignore"}
	for _, file := range skipFiles {
		if !IsSkipFileForRag(file) {
			t.Errorf("RAG文件 %s 应该被跳过", file)
		}
	}

	// 测试不应该跳过的文件
	normalFiles := []string{"main.go", "README.md", "config.yaml"}
	for _, file := range normalFiles {
		if IsSkipFileForRag(file) {
			t.Errorf("RAG文件 %s 不应该被跳过", file)
		}
	}
}

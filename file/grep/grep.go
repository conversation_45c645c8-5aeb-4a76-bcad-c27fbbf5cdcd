package grep

import (
	"bufio"
	"bytes"
	"encoding/json"
	"os/exec"
	"path/filepath"
	"strings"
)

type lineInfo struct {
	Type string `json:"type"`
	Data struct {
		Path struct {
			Text string `json:"text"`
		} `json:"path"`
		Line struct {
			Text string `json:"text"`
		} `json:"lines"`
		Submatches []Submatche `json:"submatches"`
		LineNum    int         `json:"line_number"`
	} `json:"data"`
}

type Submatche struct {
	Start int `json:"start"`
	End   int `json:"end"`
}

type Line struct {
	Text string `json:"text"`
	Line int    `json:"line"`
}

func Grep(keyword, cwd string, caseSensitive, wholeWordMatching, regex bool) (map[string][]Line, error) {
	args := []string{"rg", "--json"}
	if !caseSensitive {
		args = append(args, "--ignore-case")
	}
	if wholeWordMatching {
		args = append(args, "--word-regexp")
	}
	if regex {
		args = append(args, "--regexp")
	}
	cwd, _ = filepath.Abs(cwd)
	args = append(args, keyword)
	cmd := exec.Command(args[0], args[1:]...)
	cmd.Dir = cwd
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}
	return Statistics(output)
}

func Statistics(output []byte) (map[string][]Line, error) {
	result := make(map[string][]Line)
	scanner := bufio.NewScanner(bytes.NewReader(output))
	count := 0
	for scanner.Scan() && count < 50 {
		var info lineInfo
		err := json.Unmarshal(scanner.Bytes(), &info)
		if err != nil {
			return nil, err
		}
		if info.Type != "match" {
			continue
		}
		data := info.Data
		lines := result[data.Path.Text]
		lines = append(lines, Line{
			Text: strings.TrimSuffix(data.Line.Text, "\n"),
			Line: data.LineNum,
		})
		count++
		result[data.Path.Text] = lines
	}
	return result, nil
}

package rag

//import (
//	"agent/consts"
//	"agent/llm"
//	ragcommon "agent/rag/common"
//	"agent/utils/log"
//	"fmt"
//	"io/fs"
//	"path/filepath"
//	"reflect"
//	"sort"
//	"testing"
//
//	"github.com/agiledragon/gomonkey/v2"
//)
//
//func prepareTantivy() {
//	fileUpdate := make([]string, 0)
//	workPath := consts.AppRootDirChild
//	err := filepath.WalkDir(workPath, func(path string, dirEntry fs.DirEntry, err error) error {
//		if err != nil {
//			return err
//		}
//		if !dirEntry.IsDir() {
//			fileUpdate = append(fileUpdate, path)
//		}
//		return nil
//	})
//	if err != nil {
//		log.Errorf("tantivy error: %v", err)
//	}
//	SaveToRag(fileUpdate, nil)
//}
//
//func TestSearchRag(t *testing.T) {
//	// 检查是否有权限访问文件系统
//	if consts.AppRootDirChild == "" {
//		t.Skip("APP_ROOT_DIR not set, skipping test")
//	}
//
//	// 尝试准备数据，如果失败则跳过测试
//	err := func() error {
//		fileUpdate := make([]string, 0)
//		workPath := consts.AppRootDirChild
//		return filepath.WalkDir(workPath, func(path string, dirEntry fs.DirEntry, err error) error {
//			if err != nil {
//				return err
//			}
//			if !dirEntry.IsDir() {
//				fileUpdate = append(fileUpdate, path)
//			}
//			return nil
//		})
//	}()
//
//	if err != nil {
//		t.Skipf("Cannot access file system: %v, skipping test", err)
//	}
//
//	prepareTantivy()
//
//	var gptEmb *llm.GptEmbedding
//	embeddingTextByLlmPatches := gomonkey.ApplyMethodFunc(gptEmb, "EmbeddingTextByLlm", func(texts []string) ([][]float32, error) {
//		embeddingArray := make([][]float32, len(texts))
//		for i := range texts {
//			embeddingArray[i] = []float32{1, 0}
//		}
//		return embeddingArray, nil
//	})
//	defer embeddingTextByLlmPatches.Reset()
//
//	tests := []struct {
//		name    string
//		words   []string
//		want    []ragcommon.RagResult
//		wantErr bool
//	}{
//		{
//			name: "valid input",
//			words: []string{
//				"Change the exampleMessages above send a message into a column and arrange them vertically.\nrt",
//				"Where is the JSX code or template that renders the `exampleMessages` variable to the DOM?",
//				"Where are the CSS styles defined for the component rendering `exampleMessages`?",
//				"Where is the parent component that passes `exampleMessages` to the child component for rendering?",
//				"Where in the codebase are the flexbox or grid layout styles applied currently?",
//				"Where is the state management logic that updates or maintains `exampleMessages`?",
//				"Where are the default values or initial state definitions for `exampleMessages`?",
//				"Where is the file that imports and applies styles specific to the `exampleMessages` layout?",
//				"Where in the test files are `exampleMessages` rendering logic being verified?",
//				"Where are the utility functions or helpers that format or generate `exampleMessages`?",
//				"Where are the breakpoints and responsive design rules specified for the layout used in displaying `exampleMessages`?",
//			},
//			wantErr: false,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			array, err := SearchRag(tt.words)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("SearchRag() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			for _, result := range array {
//				fmt.Printf("文件名：%s, 起始结束行：%d-%d, 分数：%f\n", result.OriginFile, result.RowStart, result.RowEnd, result.Score)
//			}
//		})
//	}
//}
//
//func TestMergeChunks(t *testing.T) {
//	testMapQueryVectorScore := make(map[string][]ragcommon.ChunkEmbedding)
//	testMapQueryVectorScore["query1"] = make([]ragcommon.ChunkEmbedding, 0)
//	testMapQueryVectorScore["query1"] = append(testMapQueryVectorScore["query1"],
//		ragcommon.ChunkEmbedding{
//			Content:    "a",
//			OriginFile: "file1",
//			RowStart:   11,
//			RowEnd:     21,
//		},
//		ragcommon.ChunkEmbedding{
//			Content:    "empty",
//			OriginFile: "file1",
//			RowStart:   0,
//			RowEnd:     0,
//		},
//		ragcommon.ChunkEmbedding{
//			Content:    "b",
//			OriginFile: "file1",
//			RowStart:   2,
//			RowEnd:     10,
//		},
//		ragcommon.ChunkEmbedding{
//			Content:    "c",
//			OriginFile: "file2",
//			RowStart:   1,
//			RowEnd:     33,
//		})
//	testMapQueryVectorScore["query2"] = append(testMapQueryVectorScore["query2"],
//		ragcommon.ChunkEmbedding{
//			Content:    "empty",
//			OriginFile: "file1",
//			RowStart:   0,
//			RowEnd:     0,
//		},
//		ragcommon.ChunkEmbedding{
//			Content:    "d",
//			OriginFile: "file2",
//			RowStart:   34,
//			RowEnd:     40,
//		},
//		ragcommon.ChunkEmbedding{
//			Content:    "d",
//			OriginFile: "file2",
//			RowStart:   34,
//			RowEnd:     40,
//		})
//	ragResultList := mergeChunks(testMapQueryVectorScore)
//	expectedResult := make([]ragcommon.RagResult, 0)
//	expectedResult = append(expectedResult,
//		ragcommon.RagResult{
//			Content:    "empty",
//			OriginFile: "file1",
//			RowStart:   0,
//			RowEnd:     0,
//		},
//		ragcommon.RagResult{
//			Content:    "ba",
//			OriginFile: "file1",
//			RowStart:   2,
//			RowEnd:     21,
//		},
//		ragcommon.RagResult{
//			Content:    "cd",
//			OriginFile: "file2",
//			RowStart:   1,
//			RowEnd:     40,
//		})
//
//	sort.Slice(ragResultList, func(i, j int) bool {
//		return ragResultList[i].RowStart < ragResultList[j].RowStart
//	})
//	sort.Slice(expectedResult, func(i, j int) bool {
//		return expectedResult[i].RowStart < expectedResult[j].RowStart
//	})
//	if !reflect.DeepEqual(ragResultList, expectedResult) {
//		t.Logf("ragResultList: %v", ragResultList)
//		t.Logf("expectedResult: %v", expectedResult)
//		t.Errorf("mergeChunks error")
//	}
//}

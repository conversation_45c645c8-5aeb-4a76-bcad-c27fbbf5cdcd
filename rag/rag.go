package rag

//import (
//	"agent/consts"
//	"agent/file/watch"
//	"agent/mq/message"
//	"agent/mq/utils"
//	ragcommon "agent/rag/common"
//	"agent/utils/fileUtils"
//	"agent/utils/log"
//	"fmt"
//	"os"
//	"path/filepath"
//	"sync"
//	"time"
//)
//
//type Wrapper struct {
//	fileChangeChannel    <-chan []watch.FileChange
//	mqToRagSearchChannel <-chan message.RagSearchMQMsg
//	ragSearchToMqChannel chan<- message.RagSearchResultMQMsg
//	ragStatusToMqChannel chan<- message.RagStatusMQMsg
//	wg                   sync.WaitGroup
//	sync.RWMutex
//	firstChunkCompleted bool
//	ragStatus           string
//}
//
//func MakeNew(
//	fileChangeChannel <-chan []watch.FileChange,
//	mqToRagSearchChannel <-chan message.RagSearchMQMsg,
//	ragSearchToMqChannel chan<- message.RagSearchResultMQMsg,
//	ragStatusToMqChannel chan<- message.RagStatusMQMsg,
//) *Wrapper {
//	wrapper := &Wrapper{}
//	wrapper.fileChangeChannel = fileChangeChannel
//	wrapper.mqToRagSearchChannel = mqToRagSearchChannel
//	wrapper.ragSearchToMqChannel = ragSearchToMqChannel
//	wrapper.ragStatusToMqChannel = ragStatusToMqChannel
//	return wrapper
//}
//
//// Open Rag初始化接口
//func (wrapper *Wrapper) Open() {
//	// 初始化以后才执行监听
//	//wrapper.wg.Add(1)
//	// 检测并初始化Tantivy Index
//	//log.Debugf("RAG:Open:Start")
//	//go wrapper.init()
//	//wrapper.wg.Wait()
//	//log.Debugf("RAG:Open:Init:End")
//
//	log.Debugf("RAG:Open:Start")
//	wrapper.ragStatus = ragcommon.RagInit
//	wrapper.sendStatus(ragcommon.RagInit)
//
//	// 监听文件变化
//	go func() {
//		defer func() {
//			if panicErr := recover(); panicErr != nil {
//				log.PrintPanicInfo("rag listenFileChange panic error: %+v", panicErr)
//			}
//		}()
//
//		wrapper.init()
//		log.Debugf("RAG:Open:Init:End")
//
//		log.Debugf("RAG:Open:listenFileChange:Start")
//		wrapper.listenFileChange()
//	}()
//
//	// 监听MQ进行搜索返回
//	go func() {
//		defer func() {
//			if panicErr := recover(); panicErr != nil {
//				log.PrintPanicInfo("rag receiveFromMQ panic error: %+v", panicErr)
//			}
//		}()
//		log.Debugf("RAG:Open:receiveFromMQ:Start")
//		wrapper.receiveFromMQ()
//	}()
//	log.Debugf("RAG:Open:End")
//}
//
//func (wrapper *Wrapper) init() {
//	//defer wrapper.wg.Done()
//	// 1. 检测Index目录是否存在
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	_, err := os.Stat(tantivyIndexPath)
//	// 1.1 不存在则进行创建初始化
//	if os.IsNotExist(err) {
//		// 初始化
//		wrapper.ragStatus = ragcommon.RagReIndex
//		wrapper.sendStatus(ragcommon.RagReIndex)
//		wrapper.rebuildIndex()
//	} else {
//		// 1.2 存在，采用匹配哈希的方式快速比较差异
//		wrapper.quickSyncFile()
//	}
//	wrapper.ragStatus = ragcommon.RagIndexFinish
//	wrapper.sendStatus(ragcommon.RagIndexFinish)
//}
//
//// quickSyncFile 快速通过crc32比较文件是否未修改，如果修改则变更文件
//func (wrapper *Wrapper) quickSyncFile() {
//	changeList := make([]string, 0)
//	fileList, crc32List := getFileByWalkPath(consts.AppRootDirChild, true)
//	chunkPath := filepath.Join(consts.AppRagDir, consts.ChunkPath)
//
//	if len(fileList) > 0 {
//		for i, filePath := range fileList {
//			// 获取当前文件crc32
//			crc32FileName := fmt.Sprintf("%s.crc32", crc32List[i])
//			// 获取目标chunk的crc32文件
//			checkChunkCrc32Path := filepath.Join(chunkPath, filePath, crc32FileName)
//			isExists := fileUtils.FileExist(checkChunkCrc32Path)
//			if !isExists {
//				changeList = append(changeList, filePath)
//			}
//		}
//	}
//
//	// 有变更，更新数据
//	if len(changeList) > 0 {
//		wrapper.ragStatus = ragcommon.RagReIndex
//		wrapper.sendStatus(ragcommon.RagReIndex)
//		_ = SaveToRag(changeList, nil)
//	}
//}
//
//// listenFileChange 监听文件变化，建立Index
//func (wrapper *Wrapper) listenFileChange() {
//	chunkPath := filepath.Join(consts.AppRagDir, consts.ChunkPath)
//	const delay = 500 * time.Millisecond // Adjust the delay as needed
//
//	var fileChangesBatch []watch.FileChange
//	var timer *time.Timer
//
//	for {
//		select {
//		case fileChanges := <-wrapper.fileChangeChannel:
//			// Add incoming changes to the batch
//			fileChangesBatch = append(fileChangesBatch, fileChanges...)
//
//			if timer == nil {
//				// Start the timer for batching if it's not already running
//				timer = time.AfterFunc(delay, func() {
//					// When the timer elapses, process the batch
//					log.Infof("Run A fileChangesBatch")
//					wrapper.processFileChanges(fileChangesBatch, chunkPath)
//					fileChangesBatch = nil // Reset the batch
//					timer = nil            // Reset the timer
//				})
//			} else {
//				// Reset the timer for every new batch received before the previous timer elapsed
//				timer.Reset(delay)
//			}
//		}
//	}
//}
//
//func (wrapper *Wrapper) processFileChanges(fileChanges []watch.FileChange, chunkPath string) {
//	if len(fileChanges) == 0 {
//		return
//	}
//
//	log.Debugf("RAG:listenFileChange:START, %d", len(fileChanges))
//	pathCreate := make([]string, 0)
//	pathRemove := make([]string, 0)
//	fileUpdate := make([]string, 0)
//
//	for _, fileChange := range fileChanges {
//		// 判断是否为软连接，软连接不返回
//		fileFullPath := filepath.Join(consts.AppRootDirChild, fileChange.Path)
//		if fileChange.Change == consts.FileChangeRemove {
//			// 代码侧删除目录或者删除文件，在这边都是删除目录(源文件都是以目录的形式存在，目录内是文件切片)
//			path := filepath.Join(chunkPath, fileFullPath)
//			pathRemove = append(pathRemove, path)
//			continue
//		}
//
//		fileInfo, err := os.Lstat(fileFullPath)
//		if err != nil {
//			continue
//		}
//		if err == nil && fileInfo.Mode()&os.ModeSymlink != 0 {
//			continue
//		}
//		if fileInfo.IsDir() &&
//			fileChange.Change == consts.FileChangeCreate &&
//			!watch.IsSkipDirForRag(fileFullPath) {
//			// 目录的创建需要排除指定的目录
//			pathCreate = append(pathCreate, fileFullPath)
//		} else if !fileInfo.IsDir() &&
//			fileUtils.IsTextFile(fileFullPath) &&
//			!watch.IsSkipFileForRag(fileFullPath) {
//			fileUpdate = append(fileUpdate, fileFullPath)
//		}
//	}
//
//	// Handle possible renames (pathCreate and pathRemove at the same time)
//	if len(pathCreate) > 0 && len(pathRemove) > 0 {
//		walkPath := make([]string, 0)
//		for _, path := range pathCreate {
//			subPath, _ := getFileByWalkPath(path, false)
//			if len(subPath) == 0 {
//				continue
//			}
//			walkPath = append(walkPath, subPath...)
//		}
//		if len(walkPath) > 0 {
//			// Merge & deduplicate
//			fileUpdate = mergeStringSlices(walkPath, fileUpdate)
//		}
//	}
//
//	log.Debugf("RAG:listenFileChange:SAVE: %v, %v", fileUpdate, pathRemove)
//	wrapper.ragStatus = ragcommon.RagReIndex
//	wrapper.sendStatus(ragcommon.RagReIndex)
//
//	// wrapper.Lock()
//	_ = SaveToRag(fileUpdate, pathRemove)
//	// wrapper.Unlock()
//	wrapper.ragStatus = ragcommon.RagIndexFinish
//	wrapper.sendStatus(ragcommon.RagIndexFinish)
//}
//
//// receiveFromMQ 读取MQ来的消息，进行查询并返回
//func (wrapper *Wrapper) receiveFromMQ() {
//	for {
//		value := <-wrapper.mqToRagSearchChannel
//
//		msg := &message.RagSearchResultMQMsg{
//			BaseMQMsg: message.BaseMQMsg{
//				MessageId:      utils.GenerateUUID(),
//				Timestamp:      utils.GetNow(),
//				ReplyMessageId: value.MessageId,
//			},
//		}
//
//		if len(value.Query) == 0 {
//			// 空请求，返回空值，不经过处理
//			wrapper.ragSearchToMqChannel <- *msg
//			return //  stop further execution
//		}
//
//		// 搜索的读锁,读锁之间不冲突，与写锁冲突
//		//wrapper.RLock()
//		ragResults, err := SearchRag(value.Query)
//		//wrapper.RUnlock()
//
//		if err != nil {
//			wrapper.ragSearchToMqChannel <- *msg
//		}
//		msg.Result = ragResults
//		wrapper.ragSearchToMqChannel <- *msg
//	}
//}
//
//// rebuildIndex 重建索引
//func (wrapper *Wrapper) rebuildIndex() {
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	// 1. 删除目录
//	_ = os.RemoveAll(tantivyIndexPath)
//	// 2. 重建结构
//	fileUpdate, _ := getFileByWalkPath(consts.AppRootDirChild, false)
//	//wrapper.Lock()
//	defer func() {
//		//wrapper.Unlock()
//		wrapper.checkAndSetFirstChunkCompletion()
//	}()
//	_ = SaveToRag(fileUpdate, nil)
//}
//
//func (wrapper *Wrapper) sendStatus(status string) {
//	msg := &message.RagStatusMQMsg{
//		BaseMQMsg: message.BaseMQMsg{
//			MessageId:      utils.GenerateUUID(),
//			Timestamp:      utils.GetNow(),
//			ReplyMessageId: "",
//		},
//		Value: status,
//	}
//	log.Debugf("RAG:sendStatus: %v", msg.Value)
//	wrapper.ragStatusToMqChannel <- *msg
//}
//
//func getFileByWalkPath(workPath string, isNeedCrc32 bool) ([]string, []string) {
//	fileUpdate := make([]string, 0)
//	fileCrc32 := make([]string, 0)
//	err := filepath.Walk(workPath, func(path string, info os.FileInfo, err error) error {
//		if err != nil {
//			return err
//		}
//		if !info.IsDir() && !watch.IsSkipFileForRag(path) && fileUtils.IsTextFile(path) {
//			crc32 := ""
//			if isNeedCrc32 {
//				crc32, _ = fileUtils.GetFileCrc32(path)
//			}
//			fileUpdate = append(fileUpdate, path)
//			fileCrc32 = append(fileCrc32, crc32)
//		} else if info.IsDir() && watch.IsSkipDirForRag(path) {
//			return filepath.SkipDir
//		}
//		return nil
//	})
//
//	if err != nil {
//		log.Errorf("RAG:Error:%v\n", err)
//		return nil, nil
//	}
//
//	return fileUpdate, fileCrc32
//}
//
//func mergeStringSlices(slice1, slice2 []string) []string {
//	// 用map来去重
//	vals := make(map[string]struct{})
//
//	// 将数据写入map
//	for _, val := range slice1 {
//		vals[val] = struct{}{}
//	}
//
//	for _, val := range slice2 {
//		vals[val] = struct{}{}
//	}
//
//	res := make([]string, 0, len(vals))
//
//	// 将Key写出来作为结果
//	for key := range vals {
//		res = append(res, key)
//	}
//
//	return res
//}
//
//// checkAndSetFirstChunkCompletion 检查是否是第一个chunk完成，并更新状态
//func (wrapper *Wrapper) checkAndSetFirstChunkCompletion() {
//	wrapper.Lock()
//	defer wrapper.Unlock()
//
//	if !wrapper.firstChunkCompleted {
//		// First chunk is now completed, update status to RagIndexFinish
//		wrapper.firstChunkCompleted = true
//		log.Debugf("RAG: First chunk completed, status set to RagIndexFinish")
//		wrapper.ragStatus = ragcommon.RagIndexFinish
//		wrapper.sendStatus(ragcommon.RagIndexFinish)
//	}
//}
//
//func (wrapper *Wrapper) GetRagStatus() string {
//	return wrapper.ragStatus
//}

package rag

//
//import (
//	"agent/consts"
//	"agent/llm"
//	ragcommon "agent/rag/common"
//	tantivysearch "agent/rag/tantivy/search"
//	"fmt"
//	"strings"
//	"testing"
//
//	"github.com/agiledragon/gomonkey/v2"
//	"github.com/pkoukk/tiktoken-go"
//)
//
//func TestSearchRagLongInput(t *testing.T) {
//	// patch SearchTantivy
//	searchTantivyPatches := gomonkey.ApplyFunc(tantivysearch.SearchTantivy, func(_ string, _ string) ([]ragcommon.Chunk, error) {
//		var chunks []ragcommon.Chunk
//		return chunks, nil
//	})
//	defer searchTantivyPatches.Reset()
//
//	// patch EmbeddingTextByLlm
//	var gptEmb *llm.GptEmbedding
//	embeddingTextByLlmPatches := gomonkey.ApplyMethodFunc(gptEmb, "EmbeddingTextByLlm", func(texts []string) ([][]float32, error) {
//		encoding := string(consts.GptEmbeddingModel)
//		tkm, err := tiktoken.EncodingForModel(encoding)
//		if err != nil {
//			return nil, err
//		}
//		for _, text := range texts {
//			tokens := tkm.Encode(text, nil, nil)
//			if len(tokens) > consts.GptMaxTokenLen {
//				err := fmt.Errorf("RAG:rag:SearchRag:EmbeddingTextByLlm2:Error: too long tokens")
//				return nil, err
//			}
//		}
//		return nil, nil
//	})
//	defer embeddingTextByLlmPatches.Reset()
//
//	tantivysearch.SearchTantivy("a", "b")
//	texts := []string{"short text", strings.Repeat("long text", 10000)}
//	_, err := SearchRag(texts)
//	if err != nil {
//		t.Errorf("SearchRag Error: %v", err)
//	}
//}

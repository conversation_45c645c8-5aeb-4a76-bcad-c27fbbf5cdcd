package tantivysearch

//import (
//	"agent/consts"
//	ragcommon "agent/rag/common"
//	"agent/utils/log"
//	"encoding/json"
//	"fmt"
//	"os/exec"
//	"path/filepath"
//	"regexp"
//	"sort"
//	"strings"
//	"unicode"
//)
//
//func SearchTantivy(tantivyCommand, query string) ([]ragcommon.Chunk, error) {
//
//	if len(query) == 0 {
//		return nil, nil
//	}
//
//	query = tokenizeCode(query)
//	ragQuery := processQuery(query)
//
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	cmd := exec.Command(tantivyCommand, "search", "-i", tantivyIndexPath, "-q", ragQuery)
//	log.Debugf("RAG:tantivysearch:SearchTantivy:ExecCmd: %s search -i %s -q %s",
//		tantivyCommand, tantivyIndexPath, query)
//
//	output, err := cmd.CombinedOutput()
//	if err != nil {
//		log.Errorf("RAG:tantivysearch:SearchTantivy:Index exec failed: %v", err)
//		return nil, err
//	}
//
//	// 返回结果
//	var chunks []ragcommon.Chunk
//	for _, line := range strings.Split(string(output), "\n") {
//		if len(line) == 0 {
//			continue
//		}
//		var hint ragcommon.TantivyHint
//		err = json.Unmarshal([]byte(line), &hint)
//		if err != nil {
//			log.Errorf("RAG:tantivysearch:SearchTantivy:Json unmarshal failed: %v", err)
//			return nil, err
//		}
//
//		tantivy := hint.Doc
//
//		chunk := &ragcommon.Chunk{
//			Id:         tantivy.Id[0],
//			AbsPath:    tantivy.AbsPath[0],
//			OriginFile: tantivy.OriginFile[0],
//			Content:    tantivy.Content[0],
//			Score:      hint.Score,
//		}
//		if len(tantivy.RowStart) > 0 {
//			chunk.RowStart = tantivy.RowStart[0]
//			chunk.RowEnd = tantivy.RowEnd[0]
//		}
//		chunks = append(chunks, *chunk)
//	}
//	sort.Slice(chunks, func(i, j int) bool {
//		return chunks[i].Score > chunks[j].Score
//	})
//
//	// 限制20条
//	if len(chunks) > consts.TantivyResultLimit {
//		chunks = chunks[:consts.TantivyResultLimit]
//	}
//
//	return chunks, nil
//}
//
//func tokenizeCode(code string) string {
//	reg, _ := regexp.Compile("\\b\\w{2,}\\b")
//	matches := reg.FindAllString(code, -1)
//	tokens := []string{}
//	for _, match := range matches {
//		splitMatch := strings.Split(match, "_")
//		for _, split := range splitMatch {
//			token := strings.ToLower(split)
//			tokens = append(tokens, token)
//		}
//	}
//
//	return strings.Join(tokens, " ")
//}
//
//func processQuery(query string) string {
//	// 使用 strings.Map 和 removePunctuation 函数移除字符串中的标点符号
//	newQuery := strings.Map(removePunctuation, query)
//
//	// Split the query into words
//	words := strings.Split(newQuery, " ")
//
//	// Begin the result with an opening parenthesis
//	result := "("
//
//	// For each word, create the required string and add it to the result
//	for i, word := range words {
//		result += fmt.Sprintf("(%s:%s OR %s:%s)",
//			"content", word, "origin_file", word)
//
//		// If this is not the last word, add an " OR "
//		if i != len(words)-1 {
//			result += " OR "
//		}
//	}
//
//	// Close the parenthesis and return the result
//	result += ")"
//	return result
//}
//
//// 转换函数，检测字符是否是标点符号
//func removePunctuation(r rune) rune {
//	if unicode.IsPunct(r) {
//		return -1
//	}
//	return r
//}

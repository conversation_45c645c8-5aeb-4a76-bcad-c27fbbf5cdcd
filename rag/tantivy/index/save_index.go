package tantivyindex

//import (
//	"agent/consts"
//	"bytes"
//	"io"
//	"os"
//	"os/exec"
//	"path/filepath"
//)
//
//func SaveToIndex(targetFile string) error {
//	catCmd := exec.Command("cat", targetFile)
//
//	tantivyCommand := filepath.Join(consts.AppRagDir, consts.TantivyCommand)
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	indexCmd := exec.Command(tantivyCommand, "index", "-i", tantivyIndexPath)
//
//	read, write := io.Pipe()
//	defer read.Close()
//	defer write.Close()
//
//	catCmd.Stdout = write
//	indexCmd.Stdin = read
//
//	var buffer bytes.Buffer
//	indexCmd.Stdout = &buffer
//
//	catCmd.Start()
//	indexCmd.Start()
//
//	catCmd.Wait()
//	write.Close()
//	indexCmd.Wait()
//
//	io.Copy(os.Stdout, &buffer)
//	return nil
//}

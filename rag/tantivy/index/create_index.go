package tantivyindex

//import (
//	"agent/consts"
//	"agent/utils/log"
//	"os/exec"
//	"path/filepath"
//	"sync"
//)
//
//func CreateIndex() error {
//	tantivyCommand := filepath.Join(consts.AppRagDir, consts.TantivyCommand)
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	cmd := exec.Command(tantivyCommand, "new", "-i", tantivyIndexPath)
//	/*
//		New field name  ?
//		Choose Field Type (Text/u64/i64/f64/Date/Facet/Bytes/Json/bool/IpAddr) ?
//		Should the field be stored (Y/N) ?
//		Should the field be fast (Y/N) ?
//		Should the field be indexed (Y/N) ?
//		Should the term be tokenized? (Y/N) ?
//		Should the term frequencies (per doc) be in the index (Y/N) ?
//		Should the term positions (per doc) be in the index (Y/N) ?
//		Add another field (Y/N) ?
//	*/
//	schemaCmd := []string{
//		//field name     type  stored fast idx token freq pos  add
//		//"content",     "Text", "Y", "N", "Y", "Y", "Y", "Y", "Y",
//		//"row_start",   "i64",  "Y", "N", "N",                "Y",
//		"content", "Text", "Y", "N", "Y", "Y", "Y", "Y", "Y",
//		"origin_file", "Text", "Y", "N", "Y", "Y", "Y", "Y", "Y",
//		"row_start", "i64", "Y", "N", "N", "Y",
//		"row_end", "i64", "Y", "N", "N", "Y",
//		"id", "Text", "Y", "N", "Y", "Y", "Y", "Y", "Y",
//		"abs_path", "Text", "Y", "N", "N", "N"}
//
//	stdout, _ := cmd.StdoutPipe()
//	stderr, _ := cmd.StderrPipe()
//
//	stdin, _ := cmd.StdinPipe()
//
//	_ = cmd.Start()
//
//	// 读
//	var wg sync.WaitGroup
//	wg.Add(3)
//	go func() {
//		defer wg.Done()
//		for {
//			buf := make([]byte, 1024)
//			n, err := stderr.Read(buf)
//
//			if n == 0 {
//				break
//			}
//
//			if err != nil {
//				log.Errorf("RAG:tantivyindex:CreateIndex:read err %v", err)
//				return
//			}
//		}
//	}()
//
//	go func() {
//		defer wg.Done()
//		for {
//			buf := make([]byte, 1024)
//			n, err := stdout.Read(buf)
//
//			if n == 0 {
//				break
//			}
//
//			if n == 0 {
//				break
//			}
//
//			if err != nil {
//				log.Errorf("RAG:tantivyindex:CreateIndex:read out %v", err)
//				return
//			}
//
//		}
//	}()
//
//	// 写
//	go func() {
//		for _, value := range schemaCmd {
//			stdin.Write([]byte(value + "\n"))
//		}
//		stdin.Close()
//		wg.Done()
//	}()
//
//	wg.Wait()
//	err := cmd.Wait()
//	if err != nil {
//		log.Errorf("RAG:tantivyindex:CreateIndex:cmd wait %v", err)
//		return err
//	}
//	return nil
//}

package filesplit

//import (
//	"agent/consts"
//	"agent/utils/log"
//	"io/fs"
//	"os"
//	"path/filepath"
//	"strings"
//)
//
//// GatherFiles 整合文件,将chunkPath目录下所有的Json文件合并到destFile里
//func GatherFiles(chunksPath, destFile string) error {
//
//	// 创建gather文件
//	f, err := os.Create(destFile)
//	if err != nil {
//		panic(err)
//	}
//	defer f.Close()
//
//	// 检索目录中的所有文件
//	filepath.WalkDir(chunksPath, func(path string, d fs.DirEntry, err error) error {
//		findJsonFileWriteToDest(path, d, f)
//		return nil
//	})
//
//	return nil
//}
//
//func findJsonFileWriteToDest(filePath string, entry fs.DirEntry, destFile *os.File) error {
//	if !entry.IsDir() && strings.HasSuffix(entry.Name(), consts.ChunkSuffix) {
//		// 读取文件内容
//		contents, err := os.ReadFile(filePath)
//		if err != nil {
//			log.Infof("RAG:filesplit:GatherFiles:Error reading file %s: %s", filePath, err)
//			return err
//		}
//
//		// 向gather文件中追加内容
//		if _, err := destFile.Write(contents); err != nil {
//			log.Infof("RAG:filesplit:GatherFiles:Error write file %s: %s", filePath, err)
//			return err
//		}
//		_, err = destFile.WriteString("\n")
//	}
//	return nil
//}

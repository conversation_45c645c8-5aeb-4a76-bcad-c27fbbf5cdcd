package splitalgorithm

//import (
//	"agent/consts"
//	ragcommon "agent/rag/common"
//	"context"
//	"errors"
//	"fmt"
//	"math"
//	"os"
//	"path/filepath"
//	"strings"
//
//	sitter "github.com/smacker/go-tree-sitter"
//	"github.com/smacker/go-tree-sitter/bash"
//	"github.com/smacker/go-tree-sitter/c"
//	"github.com/smacker/go-tree-sitter/cpp"
//	"github.com/smacker/go-tree-sitter/csharp"
//	"github.com/smacker/go-tree-sitter/css"
//	"github.com/smacker/go-tree-sitter/cue"
//	"github.com/smacker/go-tree-sitter/dockerfile"
//	"github.com/smacker/go-tree-sitter/elixir"
//	"github.com/smacker/go-tree-sitter/elm"
//	"github.com/smacker/go-tree-sitter/golang"
//	"github.com/smacker/go-tree-sitter/groovy"
//	"github.com/smacker/go-tree-sitter/hcl"
//	"github.com/smacker/go-tree-sitter/html"
//	"github.com/smacker/go-tree-sitter/java"
//	"github.com/smacker/go-tree-sitter/javascript"
//	"github.com/smacker/go-tree-sitter/kotlin"
//	treesittermarkdown "github.com/smacker/go-tree-sitter/markdown/tree-sitter-markdown"
//	"github.com/smacker/go-tree-sitter/php"
//	"github.com/smacker/go-tree-sitter/protobuf"
//	"github.com/smacker/go-tree-sitter/python"
//	"github.com/smacker/go-tree-sitter/ruby"
//	"github.com/smacker/go-tree-sitter/rust"
//	"github.com/smacker/go-tree-sitter/scala"
//	"github.com/smacker/go-tree-sitter/sql"
//	"github.com/smacker/go-tree-sitter/svelte"
//	"github.com/smacker/go-tree-sitter/swift"
//	"github.com/smacker/go-tree-sitter/toml"
//	"github.com/smacker/go-tree-sitter/typescript/typescript"
//	"github.com/smacker/go-tree-sitter/yaml"
//)
//
//type SplitByLsp struct{}
//
//func (splitter *SplitByLsp) SplitFile(filePath string) ([]*ragcommon.SplitLine, error) {
//	sourceCodeByte, err := os.ReadFile(filePath)
//	if err != nil {
//		return nil, err
//	}
//
//	if len(sourceCodeByte) == 0 {
//		return nil, nil
//	}
//
//	// 确认文件后缀，选用具体的lsp插件解析
//	parser := sitter.NewParser()
//	err = setLanguage(parser, filePath)
//	if err != nil {
//		return nil, err
//	}
//
//	tree, err := parser.ParseCtx(context.Background(), nil, sourceCodeByte)
//	if err != nil {
//		return nil, err
//	}
//	rootNode := tree.RootNode()
//
//	chunks := chunkNode(rootNode)
//	sourceCodeStr := string(sourceCodeByte)
//
//	if len(chunks) == 0 {
//		result := make([]*ragcommon.SplitLine, 0)
//		return result, nil
//	} else if len(chunks) < 2 {
//		// 整个文件返回，查询行号
//		end := getLineNum(chunks[0].End, sourceCodeStr)
//
//		// 返回数据
//		splitSingle := &ragcommon.SplitLine{
//			RowStart: 1,
//			RowEnd:   int(end),
//		}
//		result := []*ragcommon.SplitLine{splitSingle}
//		return result, nil
//	}
//
//	// 清理同行的数据，空内容的数据
//	cleanChunks := make([]*Span, 0)
//	for _, chunk := range chunks {
//		if chunk.Start == chunk.End {
//			continue
//		}
//		line := chunk.extract(sourceCodeByte)
//		trimSpace := strings.TrimSpace(line)
//		if len(trimSpace) == 0 {
//			continue
//		}
//		cleanChunks = append(cleanChunks, chunk)
//	}
//
//	// 合并小块成为大块
//	return combineSmallTrunk(rootNode, chunks, sourceCodeStr, sourceCodeByte), nil
//}
//
//func combineSmallTrunk(node *sitter.Node, chunks []*Span,
//	sourceCodeStr string, sourceCodeByte []byte) []*ragcommon.SplitLine {
//
//	// 初始化参数
//	newChunks := make([]*Span, 0)
//	currentChunk := &Span{Start: node.StartByte(), End: node.StartByte()}
//
//	// 待处理字符
//	charSet := map[rune]bool{
//		')': true,
//		'}': true,
//		']': true,
//	}
//
//	for _, chunk := range chunks {
//		currentChunk = currentChunk.add(chunk)
//		// 如果当前块以右括号、方括号或大括号开头，我们将其与前一个块合并
//		content := currentChunk.extract(sourceCodeByte)
//		content = strings.TrimSpace(content)
//		if len(content) == 0 {
//			continue
//		}
//		// 获取第一个字符
//		runeContent := []rune(content)
//		firstChar := runeContent[0]
//		if charSet[firstChar] && len(newChunks) > 0 {
//			// 合并代码
//			newChunks[len(newChunks)-1].add(chunk)
//			currentChunk = &Span{Start: chunk.End, End: chunk.End}
//			continue
//		}
//
//		// 如果当前Chunk过大，则截断，创建新Chunk，否则继续合并
//		trimLen := len(strings.ReplaceAll(content, " ", ""))
//		if trimLen > consts.SplitFileLspCoalesce && strings.Contains(content, "\n") {
//			newChunks = append(newChunks, currentChunk)
//			currentChunk = &Span{Start: chunk.End, End: chunk.End}
//		}
//	}
//
//	if currentChunk.len() > 0 {
//		newChunks = append(newChunks, currentChunk)
//	}
//
//	// 修改行号
//	firstChunk := newChunks[0]
//	lineChunks := make([]*ragcommon.SplitLine, 0)
//	firstEndLine := getLineNum(firstChunk.End, sourceCodeStr)
//	firstSplitLine := &ragcommon.SplitLine{
//		Text:     firstChunk.extract(sourceCodeByte),
//		RowStart: 1,
//		RowEnd:   int(firstEndLine),
//	}
//	lineChunks = append(lineChunks, firstSplitLine)
//
//	// 临时变量，用于最后一步计算
//	lastChunk := &Span{Start: 0, End: 0}
//	secondLastChunk := &Span{Start: 0, End: 0}
//	// 组装结果
//	for i := 1; i < len(newChunks); i++ {
//		currentChunkStart := newChunks[i].Start
//		currentChunkEnd := newChunks[i].End
//		startLine := int(getLineNum(currentChunkStart, sourceCodeStr) + 1)
//		endLine := getLineNum(currentChunkEnd, sourceCodeStr)
//		maxLine := int(math.Max(float64(startLine), float64(endLine)))
//		// 丢弃空数据
//		if maxLine-startLine <= 0 {
//			continue
//		}
//		tempChunk := &Span{Start: currentChunkStart, End: currentChunkEnd}
//		splitLine := &ragcommon.SplitLine{
//			Text:     tempChunk.extract(sourceCodeByte),
//			RowStart: startLine,
//			RowEnd:   maxLine,
//		}
//		lineChunks = append(lineChunks, splitLine)
//
//		// 记录结果
//		secondLastChunk = lastChunk
//		lastChunk = tempChunk
//	}
//
//	// 最后一个元素过于小的话，合并最后一个
//	if len(lineChunks) > 1 {
//		last := lineChunks[len(lineChunks)-1]
//		if lastChunk.len() < consts.SplitFileLspCoalesce {
//			lineChunks[len(lineChunks)-2].RowEnd = last.RowEnd
//			// 内容重新计算
//			combineTrunk := &Span{Start: secondLastChunk.Start, End: lastChunk.End}
//			lineChunks[len(lineChunks)-2].Text = combineTrunk.extract(sourceCodeByte)
//			// 删除最后一个
//			lineChunks = lineChunks[:len(lineChunks)-1]
//		}
//	}
//
//	return lineChunks
//}
//
//func getLineNum(index uint32, sourceCodeStr string) uint32 {
//	totalChars := uint32(0)
//	lineNum := uint32(0)
//	index += 1
//	// 按照\n分割行数，\r\n的话会遗留\r
//	lines := strings.SplitAfter(sourceCodeStr, "\n")
//	for i, line := range lines {
//		currentLine := i + 1
//		// 去除最后端的\r
//		//line = strings.TrimSpace(line)
//		// 加上当前行的数据
//		totalChars += uint32(len(line))
//		if totalChars >= index {
//			// 越界则返回当前行
//			return uint32(currentLine)
//		}
//		// 记录当前行号
//		lineNum = uint32(currentLine)
//	}
//	return lineNum
//}
//
//// chunkNode 切片
//func chunkNode(node *sitter.Node) []*Span {
//	chunks := make([]*Span, 0)
//	currentChunk := &Span{Start: node.StartByte(), End: node.StartByte()}
//	// 限制字节数
//	maxSize := consts.SplitFileLspMaxChars
//	// 遍历node父节点的所有子节点
//	for i := 0; i < int(node.ChildCount()); i++ {
//		child := node.Child(i)
//		// 递归出来后的处理，由于同层级上节点记录的currentChunk为上节点的EndByte，所以当前节点要改为StartByte
//		if currentChunk.Start == currentChunk.End {
//			currentChunk = &Span{Start: child.StartByte(), End: child.StartByte()}
//		}
//		// 当前节点的字节数
//		currentSize := child.EndByte() - child.StartByte()
//		// 如果当前代码块大于限制数，则递归
//		if currentSize > maxSize {
//			// 把缓存在currentChunk的节点保存起来，进行截断
//			chunks = append(chunks, currentChunk)
//			// 当前节点信息，因为进入递归的结果就是把当前节点拆解，所以使用End来作为currentChunk
//			currentChunk = &Span{Start: child.EndByte(), End: child.EndByte()}
//			// 递归
//			childChunks := chunkNode(child)
//			chunks = append(chunks, childChunks...)
//		} else if currentSize+currentChunk.len() > maxSize {
//			// 这种情况属于略微越界，直接截断即可，不用递归
//			chunks = append(chunks, currentChunk)
//			currentChunk = &Span{Start: child.StartByte(), End: child.EndByte()}
//		} else {
//			// 没越界，记录当前节点，继续执行
//			tempChunk := &Span{Start: child.StartByte(), End: child.EndByte()}
//			currentChunk = currentChunk.add(tempChunk)
//		}
//	}
//	chunks = append(chunks, currentChunk)
//	return chunks
//}
//
//func setLanguage(parser *sitter.Parser, input string) error {
//	language := GetLanguageByPath(input)
//	switch language {
//	case consts.LanguageBash:
//		parser.SetLanguage(bash.GetLanguage())
//	case consts.LanguageC:
//		parser.SetLanguage(c.GetLanguage())
//	case consts.LanguageCPP:
//		parser.SetLanguage(cpp.GetLanguage())
//	case consts.LanguageCSharp:
//		parser.SetLanguage(csharp.GetLanguage())
//	case consts.LanguageCSS:
//		parser.SetLanguage(css.GetLanguage())
//	case consts.LanguageCUE:
//		parser.SetLanguage(cue.GetLanguage())
//	case consts.LanguageDockerFile:
//		parser.SetLanguage(dockerfile.GetLanguage())
//	case consts.LanguageElixir:
//		parser.SetLanguage(elixir.GetLanguage())
//	case consts.LanguageElm:
//		parser.SetLanguage(elm.GetLanguage())
//	case consts.LanguageGo:
//		parser.SetLanguage(golang.GetLanguage())
//	case consts.LanguageGroovy:
//		parser.SetLanguage(groovy.GetLanguage())
//	case consts.LanguageHcl:
//		parser.SetLanguage(hcl.GetLanguage())
//	case consts.LanguageHtml:
//		parser.SetLanguage(html.GetLanguage())
//	case consts.LanguageJava:
//		parser.SetLanguage(java.GetLanguage())
//	case consts.LanguageJavaScript:
//		parser.SetLanguage(javascript.GetLanguage())
//	case consts.LanguageKotlin:
//		parser.SetLanguage(kotlin.GetLanguage())
//	case consts.LanguageMarkDown:
//		parser.SetLanguage(treesittermarkdown.GetLanguage())
//	case consts.LanguagePHP:
//		parser.SetLanguage(php.GetLanguage())
//	case consts.LanguageProtobuf:
//		parser.SetLanguage(protobuf.GetLanguage())
//	case consts.LanguagePython:
//		parser.SetLanguage(python.GetLanguage())
//	case consts.LanguageRuby:
//		parser.SetLanguage(ruby.GetLanguage())
//	case consts.LanguageRust:
//		parser.SetLanguage(rust.GetLanguage())
//	case consts.LanguageScala:
//		parser.SetLanguage(scala.GetLanguage())
//	case consts.LanguageSql:
//		parser.SetLanguage(sql.GetLanguage())
//	case consts.LanguageSvelte:
//		parser.SetLanguage(svelte.GetLanguage())
//	case consts.LanguageSwift:
//		parser.SetLanguage(swift.GetLanguage())
//	case consts.LanguageToml:
//		parser.SetLanguage(toml.GetLanguage())
//	case consts.LanguageTS:
//		parser.SetLanguage(typescript.GetLanguage())
//	case consts.LanguageYaml:
//		parser.SetLanguage(yaml.GetLanguage())
//	default:
//		return errors.New(fmt.Sprintf("language %s not supported", language))
//	}
//
//	return nil
//}
//
//func GetLanguageByPath(path string) string {
//	ext := filepath.Ext(path)
//	if len(ext) == 0 {
//		ext = filepath.Base(path)
//	}
//	ext = strings.ToLower(ext)
//	switch ext {
//	case ".sh":
//		return consts.LanguageBash
//	case ".c", ".h":
//		return consts.LanguageC
//	case ".cpp", ".cc", ".cxx", ".hpp", ".cs":
//		return consts.LanguageCPP
//	case ".cue":
//		return consts.LanguageCUE
//	case ".css", "scss", "sass", "less":
//		return consts.LanguageCSS
//	case "dockerfile":
//		return consts.LanguageDockerFile
//	case ".go":
//		return consts.LanguageGo
//	case ".groovy":
//		return consts.LanguageGroovy
//	case ".erb", ".ejs", ".html", ".vue":
//		return consts.LanguageHtml
//	case ".java":
//		return consts.LanguageJava
//	case ".js", ".jsx", ".mjs":
//		return consts.LanguageJavaScript
//	case ".kt":
//		return consts.LanguageKotlin
//	case ".lua":
//		return consts.LanguageLua
//	case ".md":
//		return consts.LanguageMarkDown
//	case ".php":
//		return consts.LanguagePHP
//	case ".proto":
//		return consts.LanguageProtobuf
//	case ".py":
//		return consts.LanguagePython
//	case ".rs":
//		return consts.LanguageRust
//	case ".scala":
//		return consts.LanguageScala
//	case ".sql":
//		return consts.LanguageSql
//	case ".swift":
//		return consts.LanguageSwift
//	case ".toml":
//		return consts.LanguageToml
//	case ".ts", ".tsx":
//		return consts.LanguageTS
//	case ".yml", ".yaml":
//		return consts.LanguageYaml
//	}
//	return "txt"
//}
//
//type Span struct {
//	Start uint32
//	End   uint32
//}
//
//func (sp *Span) extract(sourceCodeByte []byte) string {
//	return string(sourceCodeByte[sp.Start:sp.End])
//}
//
//func (sp *Span) add(other *Span) *Span {
//	return &Span{Start: sp.Start, End: other.End}
//}
//
//func (sp *Span) len() uint32 {
//	return sp.End - sp.Start
//}

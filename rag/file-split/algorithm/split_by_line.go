package splitalgorithm

//import (
//	"agent/consts"
//	ragcommon "agent/rag/common"
//	"agent/utils/log"
//	"bufio"
//	"os"
//)
//
//type SplitByLine struct{}
//
//// SplitFile 将文件切割为各个小文件，其中：
//// 单次文件大小受到 ragcommon.SplitFileHighWaterMark
//// 整体逻辑如下：
////  1. 计算当前行字符数，准备存入currentLine这个临时变量中
////  2. 如果currentLine在存入当前行后，仍未达到 ragcommon.SplitFileHighWaterMark 则存入
////  3. 如果currentLine在存入当前行后，超过了 ragcommon.SplitFileHighWaterMark , 则：
////     3.1 将currentLine 与当前行数作为 RowEnd 放入 []ragcommon.SplitResult 中
////     3.2 currentLine 清空
////     3.2 计算当前行是否大于 ragcommon.SplitFileHighWaterMark
////     3.2.1 如果大于，则进行行切分，按照 ragcommon.SplitFileHighWaterMark 进行切割
////     3.2.2 如果小于，则直接存入currentLine，记录当前行数为RowStart
//func (splitter *SplitByLine) SplitFile(filePath string) ([]*ragcommon.SplitLine, error) {
//	file, err := os.Open(filePath)
//	if err != nil {
//		log.Errorf("RAG:splitalgorithm:SplitFile:Open file error:%v", err.Error())
//		return nil, err
//	}
//	defer func(file *os.File) {
//		_ = file.Close()
//	}(file)
//
//	stat, _ := file.Stat()
//	if stat.IsDir() {
//		return nil, nil
//	}
//
//	// 用Scanner读取文件
//	scanner := bufio.NewScanner(file)
//	var results []*ragcommon.SplitLine
//	var currentLine string
//	rowStart := 1
//	rowEnd := 0
//
//	// 开始读取文件的每一行
//	for scanner.Scan() {
//		line := scanner.Text()
//
//		lineLen := len(line)
//
//		if lineLen == 0 {
//			rowEnd++
//			continue
//		}
//
//		// 如果currentLine在存入当前行后，超过了 ragcommon.SplitFileHighWaterMark
//		if lineLen+len(currentLine) > consts.SplitFileHighWaterMark {
//			// 将currentLine 与当前行数作为 RowEnd 放入 []ragcommon.SplitResult 中
//			if len(currentLine) > 0 {
//				results = append(results, &ragcommon.SplitLine{
//					RowStart: rowStart,
//					RowEnd:   rowEnd,
//					Text:     currentLine,
//				})
//				rowStart = rowEnd + 1
//				currentLine = ""
//			}
//
//			// 如果大于，则进行行切分，按照 ragcommon.SplitFileHighWaterMark 进行切割
//			if lineLen > consts.SplitFileHighWaterMark {
//				// split line and add to results
//				for i := 0; i < lineLen; i += consts.SplitFileHighWaterMark {
//					end := i + consts.SplitFileHighWaterMark
//					if end > lineLen {
//						end = lineLen
//					}
//
//					// 最后一段的处理，如果超过限制长度的一半，直接封存，没有则跟下一行拼接
//					currentLen := consts.SplitFileHighWaterMark - end
//					if currentLen < consts.SplitFileHighWaterMark/2 {
//						currentLine = line[i:end]
//					} else {
//						results = append(results, &ragcommon.SplitLine{
//							RowStart: rowEnd,
//							RowEnd:   rowEnd,
//							Text:     line[i:end],
//						})
//					}
//				}
//			} else {
//				// 如果小于，则直接存入currentLine，记录当前行数为RowStart
//				currentLine = line
//			}
//
//		} else {
//			// 如果currentLine在存入当前行后，仍未达到 ragcommon.SplitFileHighWaterMark 则存入
//			if len(currentLine) == 0 {
//				currentLine = line
//			} else {
//				currentLine += "\n" + line
//			}
//		}
//		rowEnd++
//	}
//
//	// 读文件失败
//	if err := scanner.Err(); err != nil {
//		log.Errorf("RAG:splitalgorithm:SplitFile:Scan file error:%v", err.Error())
//		return nil, err
//	}
//
//	// 非空的话，要返回最后一段截取内容
//	if len(currentLine) > 0 {
//		results = append(results, &ragcommon.SplitLine{
//			RowStart: rowStart,
//			RowEnd:   rowEnd,
//			Text:     currentLine,
//		})
//	}
//
//	return results, nil
//}

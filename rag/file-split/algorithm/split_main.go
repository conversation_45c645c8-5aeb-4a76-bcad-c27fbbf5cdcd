package splitalgorithm

//
//import (
//	ragcommon "agent/rag/common"
//)
//
//func Split(filePath string) ([]*ragcommon.SplitLine, error) {
//	language := GetLanguageByPath(filePath)
//	if language == "txt" {
//		return nil, nil
//		// splitter := SplitByLine{}
//		// return splitter.SplitFile(filePath)
//	} else {
//		splitter := SplitByLsp{}
//		return splitter.SplitFile(filePath)
//	}
//	// splitter := SplitByLsp{}
//	// return splitter.SplitFile(filePath)
//}

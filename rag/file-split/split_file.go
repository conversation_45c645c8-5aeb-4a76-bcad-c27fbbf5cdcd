package filesplit

//import (
//	"agent/consts"
//	ragcommon "agent/rag/common"
//	splitalgorithm "agent/rag/file-split/algorithm"
//	"agent/utils/fileUtils"
//	"agent/utils/log"
//	"encoding/json"
//	"fmt"
//	"os"
//	"path/filepath"
//)
//
//// SplitFileAndSave splits a file into smaller chunks and saves them in the specified directory.
//func SplitFileAndSave(filePath, destPath string) error {
//	// 不处理目录
//	fileInfo, err := os.Lstat(filePath)
//	if err != nil || fileInfo.IsDir() {
//		return nil
//	}
//	// 1. 获取文件绝对路径
//	absPath, err := filepath.Abs(filePath)
//
//	// 2. 拼装出最终chunk目录下的地址，清空指定目录下的文件
//	savePath := destPath + absPath
//	_ = os.RemoveAll(savePath)
//	err = os.Mkdir<PERSON>ll(savePath, os.ModePerm)
//	if err != nil {
//		log.Errorf("RAG:filesplit:SplitFileAndSave:MkdirAll err:%v", err)
//		return err
//	}
//	savePath, err = filepath.Abs(savePath)
//	if err != nil {
//		log.Errorf("RAG:filesplit:SplitFileAndSave:Abs err:%v", err)
//		return err
//	}
//
//	// 3. 生成新的chunk并保存
//	lines, err := splitalgorithm.Split(absPath)
//	if err != nil {
//		log.Errorf("RAG:filesplit:SplitFileAndSave:Split err:%v, absPath: %s", err, absPath)
//		return err
//	}
//	if len(lines) == 0 {
//		return nil
//	}
//
//	// 3.1 规整文件名
//	fileBaseName := filepath.Base(filePath)
//
//	// 3.2 存储文件
//	for _, line := range lines {
//		// 创建文件
//		fileName := fmt.Sprintf("%s-%d-%d%s", fileBaseName, line.RowStart, line.RowEnd, consts.ChunkSuffix)
//		newFilePath := fmt.Sprintf("%s/%s", savePath, fileName)
//		fileHandler, err := os.Create(newFilePath)
//		if err != nil {
//			log.Errorf("RAG:filesplit:SplitFileAndSave:Create2 err:%v", err)
//			continue
//		}
//
//		// 裁剪出相对路径给前端
//		relPath, err := filepath.Rel(consts.AppRootDirChild, filePath)
//		if err != nil {
//			log.Errorf("RAG:filesplit:SplitFileAndSave:Rel err:%v", err)
//			relPath = filePath
//		}
//
//		chunk := &ragcommon.Chunk{
//			Id:         fileName,
//			AbsPath:    newFilePath,
//			OriginFile: relPath,
//			Content:    line.Text,
//			RowStart:   line.RowStart,
//			RowEnd:     line.RowEnd,
//		}
//
//		marshal, _ := json.Marshal(chunk)
//
//		_, err = fileHandler.Write(marshal)
//		_ = fileHandler.Close()
//		if err != nil {
//			log.Errorf("RAG:filesplit:SplitFileAndSave:Write err:%v", err)
//			continue
//		}
//	}
//
//	// 4. 计算文件hash，保存目录中
//	crc32, err := fileUtils.GetFileCrc32(filePath)
//	crc32FilePath := fmt.Sprintf("%s/%s.crc32", savePath, crc32)
//	crc32Handler, err := os.Create(crc32FilePath)
//	defer func(crc32Handler *os.File) {
//		_ = crc32Handler.Close()
//	}(crc32Handler)
//
//	return nil
//}

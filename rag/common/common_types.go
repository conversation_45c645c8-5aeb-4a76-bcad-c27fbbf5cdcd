package ragcommon

//type Chunk struct {
//	Id         string  `json:"id"`
//	AbsPath    string  `json:"abs_path"`
//	OriginFile string  `json:"origin_file"`
//	Content    string  `json:"content"`
//	RowStart   int     `json:"row_start"`
//	RowEnd     int     `json:"row_end"`
//	Score      float64 `json:"score"`
//}
//
//type ChunkTantivy struct {
//	Id         []string `json:"id"`
//	AbsPath    []string `json:"abs_path"`
//	OriginFile []string `json:"origin_file"`
//	Content    []string `json:"content"`
//	RowStart   []int    `json:"row_start"`
//	RowEnd     []int    `json:"row_end"`
//}
//
//type TantivyHint struct {
//	Score float64      `json:"score"`
//	Doc   ChunkTantivy `json:"doc"`
//	Id    int64        `json:"id"`
//}
//
//type SplitLine struct {
//	Text     string
//	RowStart int
//	RowEnd   int
//}
//
//type ChunkEmbedding struct {
//	Content     string    `json:"content"`
//	OriginFile  string    `json:"origin_file"`
//	RowStart    int       `json:"row_start"`
//	RowEnd      int       `json:"row_end"`
//	Embeddings  []float32 `json:"embeddings"`
//	SearchScore float64   `json:"search_score"`
//	AdjustScore float64   `json:"adjust_score"`
//}
//
//type RagResult struct {
//	Content    string  `json:"content"`
//	OriginFile string  `json:"origin_file"`
//	RowStart   int     `json:"row_start"`
//	RowEnd     int     `json:"row_end"`
//	Score      float64 `json:"score"`
//}

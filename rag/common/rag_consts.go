package ragcommon

//const (
//	RagInit        string = "RagInit"
//	RagReIndex     string = "RagReIndex"
//	RagIndexFinish string = "RagIndexFinish"
//)
//
//// SeparationFeatures 创建一个map用以分类
//type SeparationFeatures map[string]map[string][]string
//
//// CodeSnippetSeparationFeatures 初始化所有map和相应数据
//var CodeSnippetSeparationFeatures = SeparationFeatures{
//	"tools": map[string][]string{
//		"prefix":    {".git/", ".github/", ".circleci/", ".travis/", ".jenkins/", "scripts/", "script/", "bin/"},
//		"suffix":    {".gitignore", ".dockerignore", "Dockerfile", "Makefile", "Rakefile", "Procfile", ".sh", ".bat", ".cmd"},
//		"substring": {},
//	},
//	"junk": map[string][]string{
//		"prefix":    {"node_modules/", ".venv/", "build/", "venv/", "patch/", "target/", "bin/", "obj/"},
//		"suffix":    {".cache", ".gradle", ".mvn", ".settings", ".lock", ".log", ".tmp", ".tmp/", ".tmp.lock", ".tmp.lock/"},
//		"substring": {".egg-info", "package-lock.json", "yarn.lock", ".cache", ".gradle", ".mvn"},
//	},
//	"dependencies": map[string][]string{
//		"prefix":    {".", "config/", ".github/", "vendor/"},
//		"suffix":    {".cfg", ".ini", ".po", "package.json", ".toml", ".yaml", ".yml", "LICENSE", ".lock"},
//		"substring": {"requirements", "pyproject", "Gemfile", "Cargo", "pom.xml", "build.gradle"},
//	},
//	"docs": map[string][]string{
//		"prefix":    {"doc", "example", "README", "CHANGELOG"},
//		"suffix":    {".txt", ".rst", ".md", ".html", ".1", ".adoc", ".rdoc"},
//		"substring": {"docs/", "documentation/"},
//	},
//	"tests": map[string][]string{
//		"prefix": {"tests/", "test/", "spec/"},
//		"suffix": {
//			".spec.ts", ".spec.js", ".test.ts", ".test.js",
//			"_test.py", "_test.ts", "_test.js", "_test.go",
//			"Test.java", "Tests.java", "Spec.java", "Specs.java",
//			"_spec.rb", "_specs.rb", ".feature", "cy.ts", "cy.js"},
//		"substring": {"tests/", "test/", "/test", "_test", "rspec", ".test"},
//	},
//}

package rag

//import (
//	"agent/consts"
//	"agent/llm"
//	ragcommon "agent/rag/common"
//	tantivysearch "agent/rag/tantivy/search"
//	"agent/utils/fileUtils"
//	"agent/utils/log"
//	"encoding/json"
//	"fmt"
//	"math"
//	"os"
//	"path/filepath"
//	"sort"
//
//	"github.com/pkoukk/tiktoken-go"
//
//	cosinesimilarity "github.com/golang-infrastructure/go-cosine-similarity"
//)
//
//func SearchRag(words []string) ([]ragcommon.RagResult, error) {
//	// 1. tantivy切目录
//	tantivyCommand := filepath.Join(consts.AppRagDir, consts.TantivyCommand)
//	// 2. tantivy 检索召回
//	mapQueryScore := make(map[string][]ragcommon.Chunk)
//	for _, word := range words {
//		chunks, err := tantivysearch.SearchTantivy(tantivyCommand, word)
//		if err != nil {
//			log.Infof("RAG:rag:SearchRag:SearchTantivy:Error:%v", err)
//		}
//
//		// 保存当前问题的检索结果
//		mapQueryScore[word] = chunks
//	}
//
//	// 3. 原始问句embedding
//	encoding := string(consts.GptEmbeddingModel)
//	tkm, err := tiktoken.EncodingForModel(encoding)
//	if err != nil {
//		log.Errorf("RAG:llm:EmbeddingTextByLlm:getEncoding error: %v\n", err)
//		return nil, err
//	}
//
//	for index, text := range words {
//		tokens := tkm.Encode(text, nil, nil)
//		if len(tokens) > consts.GptMaxTokenLen {
//			cutText := tkm.Decode(tokens[:consts.GptMaxTokenLen])
//			words[index] = cutText
//		}
//	}
//
//	gptEmbedding := &llm.GptEmbedding{}
//	queryEmbeddingMap := make(map[string][]float32)
//	queryEmbeddingArray, err := gptEmbedding.EmbeddingTextByLlm(words)
//	if err != nil {
//		// 调用模型失败
//		log.Errorf("RAG:rag:SearchRag:EmbeddingTextByLlm:Error:%v", err)
//		return nil, err
//	}
//
//	for i, vector := range queryEmbeddingArray {
//		queryEmbeddingMap[words[i]] = vector
//	}
//
//	// 3.1 查找目录是否存在embedding数据
//	needEmbeddingChunk := make([]ragcommon.Chunk, 0)
//	mapQueryVectorScore := make(map[string][]ragcommon.ChunkEmbedding)
//
//	for query, chunks := range mapQueryScore {
//		embeddingDataArrays := make([]ragcommon.ChunkEmbedding, 0)
//		for _, chunk := range chunks {
//			if len(chunk.Content) == 0 {
//				log.Printf("RAG:rag:SearchRag:EmbeddingTextByLlm:Found empty chunk")
//				continue
//			}
//
//			// Embedding 文件存储路径跟Chunk路径一致，当文件需要重新Chunk的时候，Embedding数据一起删除
//			embeddingFile := chunk.AbsPath + ".txt"
//
//			embeddingData := &ragcommon.ChunkEmbedding{
//				Content:     chunk.Content,
//				OriginFile:  chunk.OriginFile,
//				RowStart:    chunk.RowStart,
//				RowEnd:      chunk.RowEnd,
//				SearchScore: chunk.Score,
//			}
//
//			fileExist := fileUtils.FileExist(embeddingFile)
//			if !fileExist {
//				isAdd := false
//				for _, need := range needEmbeddingChunk {
//					if need.OriginFile == chunk.OriginFile &&
//						need.RowStart == chunk.RowStart &&
//						need.RowEnd == chunk.RowEnd {
//						isAdd = true
//					}
//				}
//				if !isAdd {
//					needEmbeddingChunk = append(needEmbeddingChunk, chunk)
//				}
//			} else {
//				fileContent, err := fileUtils.Read(embeddingFile)
//				var vector []float32
//
//				err = json.Unmarshal([]byte(fileContent), &vector)
//				if err != nil {
//					return nil, err
//				}
//
//				embeddingData.Embeddings = vector
//			}
//			embeddingDataArrays = append(embeddingDataArrays, *embeddingData)
//		}
//		mapQueryVectorScore[query] = embeddingDataArrays
//	}
//
//	// 3.2 不存在embedding数据，请求GPT获取数据，保存到文件
//	if len(needEmbeddingChunk) > 0 {
//		needEmbeddingChunkResultMap := make(map[string]ragcommon.ChunkEmbedding, 0)
//		textArray := make([]string, 0)
//		for _, chunk := range needEmbeddingChunk {
//			textArray = append(textArray, chunk.Content)
//		}
//		embeddedTextVec, err := gptEmbedding.EmbeddingTextByLlm(textArray)
//		if err != nil {
//			log.Errorf("RAG:rag:SearchRag:EmbeddingTextByLlm2:Error:%v", err)
//			return nil, err
//		}
//		for i, chunk := range needEmbeddingChunk {
//			embeddingFile := chunk.AbsPath + ".txt"
//
//			vector := embeddedTextVec[i]
//
//			// 存入map
//			embeddingData := &ragcommon.ChunkEmbedding{
//				Content:     chunk.Content,
//				OriginFile:  chunk.OriginFile,
//				RowStart:    chunk.RowStart,
//				RowEnd:      chunk.RowEnd,
//				Embeddings:  vector,
//				SearchScore: chunk.Score,
//			}
//			key := fmt.Sprintf("%s_%d_%d", chunk.OriginFile, chunk.RowStart, chunk.RowEnd)
//			needEmbeddingChunkResultMap[key] = *embeddingData
//
//			// 序列化
//			bytes, err := json.Marshal(vector)
//			if err != nil {
//				continue
//			}
//			// 写入文件
//			// 创建所有路径
//			dir := filepath.Dir(embeddingFile)
//			if _, err := os.Stat(dir); os.IsNotExist(err) {
//				err := os.MkdirAll(dir, os.ModePerm)
//				if err != nil {
//					log.Errorf("RAG:rag:SearchRag:MkdirAll:Error:%v", err)
//					continue
//				}
//			}
//			fileHandler, err := os.Create(embeddingFile)
//			if err != nil {
//				// 写文件失败，跳过
//				log.Errorf("RAG:rag:SearchRag:Create:Error:%v", err)
//				continue
//			}
//			_, err = fileHandler.Write(bytes)
//			err = fileHandler.Close()
//			if err != nil {
//				log.Errorf("RAG:rag:SearchRag:Write:Error:%v", err)
//				return nil, err
//			}
//
//		}
//
//		// 将向量化的结果写入Map
//		for _, embeddingArrays := range mapQueryVectorScore {
//			for j, chunkEmbedding := range embeddingArrays {
//				if len(chunkEmbedding.Embeddings) > 0 {
//					continue
//				}
//				key := fmt.Sprintf("%s_%d_%d",
//					chunkEmbedding.OriginFile, chunkEmbedding.RowStart, chunkEmbedding.RowEnd)
//				embedding := needEmbeddingChunkResultMap[key]
//				embeddingArrays[j].Embeddings = embedding.Embeddings
//			}
//		}
//	}
//	// 4. 获取召回数据embedding结果与问句embedding计算距离，根据权重调整输出的结果
//	for query, embeddings := range mapQueryVectorScore {
//		for i, embedding := range embeddings {
//			// 计算问题与检索结果向量的余弦距离
//			cosineSimilarity, err :=
//				cosinesimilarity.NumberSliceCosineSimilarityE(embedding.Embeddings, queryEmbeddingMap[query])
//
//			if err != nil {
//				log.Errorf("RAG:rag:SearchRag:ConsineSimilarity:Error:%v", err)
//				continue
//			}
//			// 调整余弦距离结果：调整权重
//			adjustScore := (embedding.SearchScore + (cosineSimilarity * consts.RagVectorSearchWeight)) /
//				(consts.RagVectorSearchWeight + 1)
//			// 根据排序再次调整权重, 排序越后，权重越低
//			adjustScore = adjustScore * (1 / math.Pow(2, float64(i)))
//			embeddings[i].AdjustScore = adjustScore
//		}
//	}
//
//	// 同段代码汇总
//	ragResultList := mergeChunks(mapQueryVectorScore)
//
//	if len(ragResultList) == 0 {
//		return ragResultList, nil
//	}
//
//	// 5. 按分数排序
//	sort.Slice(ragResultList, func(i, j int) bool {
//		return ragResultList[i].Score > ragResultList[j].Score
//	})
//
//	// 删除掉分数过低的答案
//	topScore := ragResultList[0].Score
//	sliceIndex := 0
//	for i, ragResult := range ragResultList {
//		percentile := ragResult.Score / topScore
//		if percentile < consts.RagResultPercentileFloor {
//			break
//		}
//		sliceIndex = i
//	}
//	sliceIndex++
//
//	return ragResultList[:sliceIndex], nil
//}
//
//func mergeChunks(mapQueryVectorScore map[string][]ragcommon.ChunkEmbedding) []ragcommon.RagResult {
//	mapFileChunkEmbeddings := make(map[string][]ragcommon.ChunkEmbedding)
//	ragResultList := make([]ragcommon.RagResult, 0)
//	for _, embeddingData := range mapQueryVectorScore {
//		for _, embedding := range embeddingData {
//			mapFileChunkEmbeddings[embedding.OriginFile] = append(mapFileChunkEmbeddings[embedding.OriginFile], embedding)
//		}
//	}
//
//	for _, embeddingData := range mapFileChunkEmbeddings {
//		sort.Slice(embeddingData, func(i, j int) bool {
//			return embeddingData[i].RowStart < embeddingData[j].RowStart
//		})
//		ragResult := ragcommon.RagResult{
//			Content:    embeddingData[0].Content,
//			OriginFile: embeddingData[0].OriginFile,
//			RowStart:   embeddingData[0].RowStart,
//			RowEnd:     embeddingData[0].RowEnd,
//			Score:      embeddingData[0].AdjustScore,
//		}
//		for i := 1; i < len(embeddingData); i++ {
//			if embeddingData[i].RowStart-1 <= ragResult.RowEnd {
//				if embeddingData[i].RowEnd > ragResult.RowEnd {
//					ragResult.Content += embeddingData[i].Content
//				}
//				ragResult.RowEnd = embeddingData[i].RowEnd
//				ragResult.Score += embeddingData[i].AdjustScore
//			} else {
//				ragResultList = append(ragResultList, ragResult)
//				ragResult = ragcommon.RagResult{
//					Content:    embeddingData[i].Content,
//					OriginFile: embeddingData[i].OriginFile,
//					RowStart:   embeddingData[i].RowStart,
//					RowEnd:     embeddingData[i].RowEnd,
//					Score:      embeddingData[i].AdjustScore,
//				}
//			}
//		}
//		ragResultList = append(ragResultList, ragResult)
//	}
//	return ragResultList
//
//}

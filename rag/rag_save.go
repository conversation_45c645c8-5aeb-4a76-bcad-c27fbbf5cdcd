package rag

//import (
//	"agent/consts"
//	filesplit "agent/rag/file-split"
//	tantivyindex "agent/rag/tantivy/index"
//	"agent/utils/log"
//	"os"
//	"path/filepath"
//	"time"
//)
//
//func SaveToRag(filesChange, chunkPathRemove []string) error {
//	log.Infof("RAG:rag:SaveToRag:Start - %s", time.Now())
//	// 删除目录或文件
//	if len(chunkPathRemove) > 0 {
//		// 已经将用户的代码目录替换为代码分片的目录，可以直接删除
//		removePathByList(chunkPathRemove)
//	}
//
//	if len(filesChange) == 0 {
//		return nil
//	}
//
//	// 替换为Rag的路径
//	chunkPath := filepath.Join(consts.AppRagDir, consts.ChunkPath)
//	gatherFile := filepath.Join(consts.AppRagDir, consts.GatherFile)
//
//	// 更新文件
//	for _, filePath := range filesChange {
//		_ = filesplit.SplitFileAndSave(filePath, chunkPath)
//	}
//
//	// 整合文件,将chunkPath目录下所有的Json文件合并到gatherFile里
//	err := filesplit.GatherFiles(chunkPath, gatherFile)
//	if err != nil {
//		log.Errorf("RAG:rag:SaveToRag:Error gathering files: %v", err)
//		return err
//	}
//
//	// 删除tantivy内容
//	tantivyIndexPath := filepath.Join(consts.AppRagDir, consts.TantivyIndexName)
//	_ = os.RemoveAll(tantivyIndexPath)
//
//	// 重建数据库
//	err = tantivyindex.CreateIndex()
//	if err != nil {
//		log.Errorf("RAG:rag:SaveToRag:Error creating tantivy index:%v", err)
//		return err
//	}
//
//	// 写入数据库
//	err = tantivyindex.SaveToIndex(gatherFile)
//	if err != nil {
//		log.Errorf("RAG:rag:SaveToRag:Error indexing tantivy:%v", err)
//		return err
//	}
//	log.Infof("RAG:rag:SaveToRag:End - %s", time.Now())
//	return nil
//}
//
//func removePathByList(removePathList []string) {
//	for _, path := range removePathList {
//		// 不处理任何错误：路径不存在等
//		_ = os.RemoveAll(path)
//	}
//}

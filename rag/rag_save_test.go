package rag

//import (
//	"errors"
//	"testing"
//)
//
//func TestSaveToRag(t *testing.T) {
//	var tests = []struct {
//		name        string
//		filePath    []string
//		removePath  []string
//		setupMocks  func()
//		expectedErr error
//	}{
//		{
//			name:        "SuccessfulSave",
//			filePath:    []string{},
//			removePath:  []string{},
//			setupMocks:  func() {},
//			expectedErr: nil,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//
//			// Call SaveToRag, compare the error with the expected error
//			if err := SaveToRag(tt.filePath, tt.removePath); !errors.Is(err, tt.expectedErr) {
//				t.Errorf("got %v, want %v", err, tt.expectedErr)
//			}
//		})
//	}
//}

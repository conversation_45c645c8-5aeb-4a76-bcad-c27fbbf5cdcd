<!-- Based on <PERSON><PERSON> Webpage Clone | HTML & CSS by <PERSON> (2021)
see: https://www.youtube.com/watch?v=9OVLaEjY-Rc -->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css" />
    <title>Stream TV and Movies Live and Online | Hulu</title>
  </head>
  <body>
    <header class="header">
      <nav>
        <ul>
          <li>
            <button class="login-btn">Log In</button>
          </li>
        </ul>
      </nav>
      <div class="header-content">
        <h4>Try up to one month free</h4>
        <img
          src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/logo.png?raw=true"
          alt="Hulu"
          class="logo"
        />
        <div class="header-text-1">Watch thousands of TV shows and movies.</div>
        <div class="header-text-2">
          HBO Max™, SHOWTIME®, CINEMAX® and STARZ® available as add-ons.
        </div>
        <button class="btn btn-cta">Start Your Free Trial</button>
        <div class="legal-text">
          Free trial for new & eligible returning subscribers only.
        </div>
      </div>
    </header>
    <section class="sub-header">
      <img
        src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/logos.png?raw=true"
        alt=""
      />
      <div>
        <h4>Bundle with any Hulu plan & save</h4>
        <h3>Get Hulu, Disney+, and ESPN+.</h3>
        <a href="#" class="sub-link">Details</a>
      </div>
      <div>
        <a href="#" class="btn btn-outline">Get Bundle</a>
        <a href="#" class="sub-link">Terms apply</a>
      </div>
    </section>
    <section class="categories">
      <h4>Included in all plans</h4>
      <div class="text-xl">All the TV You Love</div>
      <div class="sub-text">
        Stream full seasons of exclusive series, current-season episodes, hit
        movies, Hulu Originals, kids shows, and more.
      </div>
      <div class="covers">
        <div class="cover-1">
          <div class="cover-grad"></div>
          <div class="cover-text">
            <div class="sub-title">Past & Current Seasons</div>
            <h3>TV Shows</h3>
          </div>
        </div>
        <div class="cover-2">
          <div class="cover-grad"></div>
          <div class="cover-text">
            <div class="sub-title">New & Classics</div>
            <h3>Movies</h3>
          </div>
        </div>
        <div class="cover-3">
          <div class="cover-grad"></div>
          <div class="cover-text">
            <div class="sub-title">Groundbreaking</div>
            <h3>Hulu Originals</h3>
          </div>
        </div>
        <div class="cover-4">
          <div class="cover-grad"></div>
          <div class="cover-text">
            <div class="sub-title">Add-On</div>
            <h3>Premiums</h3>
          </div>
        </div>
      </div>
    </section>
    <section class="live">
      <div class="live-border">
        <h4>Hulu + Live TV</h4>
        <div class="text-xl">Live TV Makes It Better</div>
        <div class="sub-text">
          Make the switch from cable. Get 75+ top channels with your favorite
          live sports, news, and events - plus the entire Hulu streaming
          library.
        </div>
        <div class="legal-text">
          Live TV plan required. Regional restrictions, blackouts and additional
          terms apply. See details
        </div>
        <a href="#">View Channels in Your Area</a>
      </div>
    </section>
    <section class="live-sports">
      <div class="live-sports-content">
        <div class="text-xl">Live Sports</div>
        <div class="sub-text">
          Catch your games at home or on the go. Stream live games from major
          college and pro leagues including the NCAA®, NBA, NHL, NFL, and more.
        </div>

        <div class="live-sports-logos">
          <div>
            <img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/live-sports-logo-1.png?raw=true"
              alt=""
            />
          </div>
          <div>
            <img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/live-sports-logo-2.png?raw=true"
              alt=""
            />
          </div>
          <div>
            <img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/live-sports-logo-3.svg?raw=true"
              alt=""
            />
          </div>
          <div>
            <img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/live-sports-logo-4.png?raw=true"
              alt=""
            />
          </div>
        </div>

        <div class="legal-text">
          Live TV plan required. Regional restrictions, blackouts and additional
          terms apply. See details
        </div>
      </div>
    </section>
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-lists">
          <ul>
            <li class="list-head">BROWSE</li>
            <li><a href="#">Streaming Library</a></li>
            <li><a href="#">Live TV</a></li>
            <li><a href="#">Live News</a></li>
            <li><a href="#">Live Sports</a></li>
          </ul>
          <ul>
            <li><a href="#">TV Shows</a></li>
            <li><a href="#">Movies</a></li>
            <li><a href="#">Originals</a></li>
            <li><a href="#">Networks</a></li>
            <li><a href="#">Kids</a></li>
            <li><a href="#">FX on Hulu</a></li>
          </ul>
          <ul>
            <li><a href="#">Hulu, Disney+, and ESPN+</a></li>
            <li><a href="#">Disney Bundle</a></li>
            <li><a href="#">HBO Max</a></li>
            <li><a href="#">Cinimax</a></li>
            <li><a href="#">Showtime</a></li>
            <li><a href="#">STARZ</a></li>
          </ul>
          <ul>
            <li class="list-head">HELP</li>
            <li><a href="#">Account & Billing</a></li>
            <li><a href="#">Plans & Pricing</a></li>
            <li><a href="#">Supported Devices</a></li>
            <li><a href="#">Accesibility</a></li>
          </ul>
          <ul>
            <li class="list-head">ABOUT US</li>
            <li><a href="#">Press</a></li>
            <li><a href="#">Jobs</a></li>
            <li><a href="#">Contact</a></li>
          </ul>
        </div>
        <div class="divider"></div>
        <div class="social-icons">
          <a href="#"
            ><img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/facebook.svg?raw=true"
              alt=""
          /></a>
          <a href="#"
            ><img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/twitter.svg?raw=true"
              alt=""
          /></a>
          <a href="#"
            ><img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/youtube.svg?raw=true"
              alt=""
          /></a>
          <a href="#"
            ><img
              src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/instagram.svg?raw=true"
              alt=""
          /></a>
        </div>
      </div>
    </footer>
    <div class="modal">
      <div class="modal-box">
        <div class="modal-body">
          <h3>Log In</h3>
          <form>
            <div class="form-control">
              <label for="email">Email</label>
              <input type="email" id="email" />
            </div>
            <div class="form-control">
              <label for="password">Password</label>
              <input type="password" id="password" />
            </div>
            <p><a href="#">Forgot your email or password</a></p>
            <button class="btn btn-dark">Log In</button>
          </form>
        </div>
        <div class="modal-footer">
          <p>Don't have an account? <a href="#">Start your free trial</a></p>
        </div>
        <img
          class="close"
          src="https://github.com/bradtraversy/hulu-webpage-clone/blob/main/img/close.svg?raw=true"
          alt="close"
        />
      </div>
    </div>
    <script src="script.js"></script>
  </body>
</html>

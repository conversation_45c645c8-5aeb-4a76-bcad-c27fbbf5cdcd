package com.dao42.paas.common.message;

import com.dao42.paas.common.enums.PlaygroundActiveResult;
import lombok.Data;

@Data
public class PlaygroundActiveResultMQMsg extends BaseMQMsg {

    /**
     * 激活结果
     */
    private boolean success = false;

    /**
     * 业务原因
     */
    private PlaygroundActiveResult reason;

    /**
     * 错误信息
     */
    private String error;

        /**
     * 用户容器唯一内网地址, 仅当success=true时
     */
    private String agentServerInternalAddr;
}

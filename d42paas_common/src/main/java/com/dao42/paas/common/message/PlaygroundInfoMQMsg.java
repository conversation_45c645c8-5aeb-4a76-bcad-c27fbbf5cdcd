package com.dao42.paas.common.message;

import com.dao42.paas.common.bean.EnvironmentVersion;
import com.dao42.paas.common.enums.PlaygroundStatus;
import lombok.Data;

@Data
public class PlaygroundInfoMQMsg extends BaseMQMsg {

    /**
     * 状态
     */
    private PlaygroundStatus status;

    /**
     * dockerId
     */
    private String dockerId;

    /**
     * playground当前对应容器的代码路径
     */
    private String fileRootPath;

    /**
     * playground 初始化代码路径
     */
    private String fileInitialPath;

    /**
     * playground当前对应容器的代码路径唯一标识
     */
    private String fileRootId;

    /**
     * playground当前环境的代码目录
     */
    private String lspRootPath;

    /**
     * 文件树上需要隐藏的内容
     */
    private String fileTreeIgnore;

    /**
     * 文件树上包管理文件
     */
    private String fileTreePackage;

    /**
     * url
     */
    private String url;

    /**
     * lspLanguageId
     */
    private String lspLanguageId;

    private String language;
    /**
     * lspSupported
     */
    private boolean lspSupported;

    /**
     * lspUrl
     */
    private String lspUrl;

    /**
     * lspStatus
     */
    private String lspStatus;

    /**
     * runStatus
     */
    private String runStatus;

    /**
     * terminalStatus
     */
    private String terminalStatus;
    /**
     * ragStatus
     */
    private String ragStatus;
    /**
     * 是否gui
     */
    private boolean gui;
    /**
     * 是否支持debug
     */
    private boolean debugSupport;
    /**
     * 支持debug服务的state
     */
    private String debugState;
    /**
     * 默认打开的题目路径
     */
    private String defaultOpenFile;
    /**
     * 文件是否刷新
     */
    private boolean refresh;
    /**
     * 文件是否自动刷新
     */
    private boolean realTimeRefresh;
    /**
     * 刷新间隔时间
     */
    private long intervalTime;

    private EnvironmentVersion environmentVersion;

    /**
     * 用户容器为唯一地址
     */
    private String agentServerUrl;

    /**
     * nvc 状态
     */
    private boolean vncSupport;

    private String vncStatus;
}

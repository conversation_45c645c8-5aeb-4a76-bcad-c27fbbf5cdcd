SHELL=/bin/bash
OS=
ARCH=
NAME=agent

AMQP_URL=amqp://agent:<EMAIL>:5672/dev
BOT_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5e870e69-8b0c-41be-bf1f-77548cc1baa3

VERSION=$(shell git describe --tags || git rev-parse --short HEAD || echo "unknown version")
BUILD_TIME=$(shell date +%FT%T%z)
LD_FLAGS='\
				 -X "agent/version.Version=$(VERSION)" \
				 -X "agent/version.Date=$(BUILD_TIME)" \
				 -X "agent/config.AMQP_URL=$(AMQP_URL)" \
				 -X "agent/config.BOT_URL=$(BOT_URL)" \
'
GOBUILD=go build -trimpath -ldflags $(LD_FLAGS)

.PHONY: all
all: build

.PHONY: run
run:
	go run ./main.go -debug

.PHONY: build
build:
	@ GOOS=$(OS) GOARCH=$(ARCH) CGO_ENABLED=1 $(GOBUILD) -o $(NAME) ./main.go

package httpProxy

import (
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	MAX_RETRIES = 2
	UNREADY     = "unready"
	NOTHTTP     = "nothttp"
)

type Wrapper struct {
	httpProxyToMqChannel chan<- string
	mqToHttpProxyChannel <-chan string
	httpProxyHookChannel <-chan string
	preHTTPPorts         map[string]*PortStatus
	curHTTPPorts         map[string]*PortStatus
}

type PortStatus struct {
	Port    string
	Type    string
	Retries int
}

type msgInfo struct {
	Url   string `json:"url"`
	Type  string `json:"type"`
	Port  int    `json:"port"`
	Alias string `json:"alias"`
}

func MakeNew(httpProxyToMqChannel chan<- string, mqToHttpProxyChannel <-chan string, httpProxyHookChannel <-chan string) Wrapper {
	wrapper := Wrapper{}
	wrapper.httpProxyToMqChannel = httpProxyToMqChannel
	wrapper.mqToHttpProxyChannel = mqToHttpProxyChannel
	wrapper.httpProxyHookChannel = httpProxyHookChannel
	wrapper.preHTTPPorts = make(map[string]*PortStatus)
	wrapper.curHTTPPorts = make(map[string]*PortStatus)
	return wrapper
}

var (
	proxyPort    = envUtils.GetString(consts.PaasProjectWebPort)
	excludePorts = map[string]bool{
		proxyPort:                 true, // proxy
		"22":                      true, // sshd
		consts.LSPServerPort:      true, // lsp
		"27017":                   true, //mongo
		"3306":                    true, //mysql
		"6379":                    true, //redis
		"5432":                    true, //pg
		consts.VNCServerPort:      true, //novnc
		consts.TigerVNCServerPort: true, //tigerVNC
		consts.ChromeDebugPort:    true, //chrome
		consts.AgentServerPort:    true, //httpserver
		consts.CaddySeverPort:     true, //caddy
	}
)

func (wrapper *Wrapper) Open() {
	//start http proxy
	ProxyHttpMain()

	//监听http端口
	go func() {
		for {
			defer func() {
				if panicErr := recover(); panicErr != nil {
					log.Errorf("httpProxyWrapper getListeningPorts panic error: %+v", panicErr)
				}
			}()
			select {
			case <-wrapper.httpProxyHookChannel: //run stop触发
				wrapper.exec(false)
			case <-wrapper.mqToHttpProxyChannel: //ide-server触发
				wrapper.exec(true)
			case <-time.After(time.Second * 5):
				wrapper.exec(false)
			}
		}
	}()
}

func (wrapper *Wrapper) exec(isRequest bool) {
	var mutex sync.Mutex
	tcpPorts := wrapper.getListeningPorts()

	mutex.Lock()
	wrapper.updateHTTPPorts(tcpPorts)
	mutex.Unlock()

	if !isRequest && wrapper.isEqual() {
		log.Debugf("GoAgent:httpProxy:exec >> %s.%v", "http ports not change", wrapper.curHTTPPorts)
		return
	}
	log.Infof("GoAgent:httpProxy:updateHTTPPorts >> %s.%v->%v", "http ports change", wrapper.preHTTPPorts, wrapper.curHTTPPorts)

	mutex.Lock()
	wrapper.preHTTPPorts = deepCopyMap(wrapper.curHTTPPorts)
	mutex.Unlock()

	wrapper.registPorts(isRequest)
}

func (wrapper *Wrapper) getListeningPorts() []string {
	// 执行 netstat 命令
	cmd := exec.Command("netstat", "-tln") // -t: TCP, -u: UDP, -l: 监听, -n: 显示数字端口

	// 获取命令的输出
	output, err := cmd.Output()
	if err != nil {
		log.Errorf("GoAgent:httpProxy:getListeningPorts >> %s.%s", "Get netstat output fail", err)
		return []string{}
	}

	lines := strings.Split(string(output), "\n")

	var tcpPorts []string

	// 遍历每一行，寻找监听端口的相关信息
	for _, line := range lines {
		// 排除掉非监听行，且只关注 TCP 连接
		if strings.Contains(line, "LISTEN") {
			// 通过空格分割每一行，获取端口信息
			columns := strings.Fields(line)
			// columns[3] 是地址信息
			address := columns[3]
			// 提取端口（以 : 分割）
			addressParts := strings.Split(address, ":")
			if len(addressParts) > 1 {
				port := addressParts[len(addressParts)-1]
				if _, ok := excludePorts[port]; !ok {
					tcpPorts = append(tcpPorts, port)
				}
			}
		}
	}
	log.Debugf("GoAgent:httpProxy:getListeningPorts >> %s.%s", "listen tcp ports:", tcpPorts)

	return tcpPorts

}

// 更新 HTTP 端口列表
func (wrapper *Wrapper) updateHTTPPorts(tcpPorts []string) {

	// 检查新增的端口
	for _, port := range tcpPorts {
		if _, exist := wrapper.curHTTPPorts[port]; !exist {
			//if portType, ok := isHTTPPort(port); ok {
			//	wrapper.curHTTPPorts[port] = portType
			//	log.Debugf("Added HTTP port: %s\n", port)
			//}
			wrapper.curHTTPPorts[port] = &PortStatus{
				Port:    port,
				Type:    UNREADY,
				Retries: 0,
			}
			log.Infof("Added port: %s\n", port)
		}
	}

	// 检测端口
	for port, status := range wrapper.curHTTPPorts {
		// 检查已关闭的端口
		if !contains(tcpPorts, port) {
			delete(wrapper.curHTTPPorts, port)
			log.Infof("Removed HTTP port: %s\n", port)
			continue
		}

		// 未关闭的检测
		if status.Type != UNREADY && status.Type != NOTHTTP {
			continue
		}

		if status.Retries >= MAX_RETRIES {
			continue
		}

		if portType, ok := isHTTPPort(port); ok {
			if portType == UNREADY || portType == NOTHTTP {
				status.Retries++
			}
			status.Type = portType
		}

	}

}

// deepCopyMap 深拷贝一个 map[string]*PortStatus
func deepCopyMap(original map[string]*PortStatus) map[string]*PortStatus {
	copy := make(map[string]*PortStatus)

	for key, value := range original {
		// 创建新的 PortStatus 实例
		newValue := &PortStatus{
			Port:    value.Port,
			Type:    value.Type,
			Retries: value.Retries,
		}
		copy[key] = newValue
	}

	return copy
}

func isHTTPPort(port string) (string, bool) {
	client := &http.Client{
		Timeout: 2 * time.Second,
	}

	// 直接发送 HTTP 请求
	resp, err := client.Get("http://localhost:" + port)
	if err != nil {
		// 如果是超时错误，返回 UNREADY, true
		if errors.Is(err, context.DeadlineExceeded) {
			return UNREADY, true
		}
		return NOTHTTP, true
	}
	defer resp.Body.Close()

	if resp.StatusCode == 426 {
		return "ws", true
	}

	//if port == "3001" && isBrowserSync(port) {
	//	return "browserSync", true
	//}

	return "http", true
}

func isBrowserSync(port string) bool {

	// 发送 HTTP 请求
	client := &http.Client{}
	req, err := http.NewRequest("GET", "http://localhost:"+port+"/sync-options", nil)
	if err != nil {
		return false
	}

	// 设置请求超时
	client.Timeout = 2 * time.Second

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == 200
}

// isEqual 比较两个 map[string]*PortStatus 是否相等
func (wrapper *Wrapper) isEqual() bool {
	if len(wrapper.curHTTPPorts) != len(wrapper.preHTTPPorts) {
		return false
	}

	for key, value1 := range wrapper.curHTTPPorts {
		value2, exists := wrapper.preHTTPPorts[key]
		if !exists {
			return false
		}
		// 比较 PortStatus 结构体的内容
		if value1.Port != value2.Port || value1.Type != value2.Type || value1.Retries != value2.Retries {
			return false
		}
	}

	return true
}

func (wrapper *Wrapper) registPorts(isRequest bool) {
	var msgInfos []msgInfo
	var portList []string
	for port, status := range wrapper.curHTTPPorts {
		if status.Type != NOTHTTP {
			portList = append(portList, port)
		}
	}

	if len(portList) > 0 {
		dockerId := envUtils.GetString(consts.PAAS_DockerId)
		dockerId64, err := strconv.ParseInt(dockerId, 10, 64)
		if err != nil {
			log.Errorf("GoAgent:httpProxy:registPort >> %s.%s", "dockerId parse to int64 fail", err)
			return
		}
		res, err := httpExec(portList, dockerId64)
		if err != nil {
			log.Errorf("GoAgent:httpProxy:registPort >> %s.%s", "regist port to gateway fail", err)
			return
		}

		if res != nil {
			for _, item := range res.List {
				iport, err := strconv.Atoi(item.Port)
				if err != nil {
					log.Errorf("GoAgent:httpProxy:registPort >> %s.%s", "port parse to int fail", err)
					continue
				}
				msgInfos = append(msgInfos, msgInfo{
					Url:   item.Url,
					Type:  wrapper.preHTTPPorts[item.Port].Type,
					Port:  iport,
					Alias: item.Alias,
				})
			}
		}
	}

	//send mq
	if isRequest {
		wrapper.setHttpProxyMsg(consts.AVAILABLE_PORTS_PREFIX, map[string]interface{}{
			"isRequest": isRequest,
			"ports":     msgInfos,
		})
	} else {
		wrapper.setHttpProxyMsg(consts.PORTS_CHANGED_PREFIX, map[string]interface{}{
			"isRequest": isRequest,
			"ports":     msgInfos,
		})
	}
}

func (wrapper *Wrapper) setHttpProxyMsg(prefix string, e interface{}) {

	val, err := json.Marshal(e)
	if err != nil {
		log.Errorf("GoAgent:httpProxy:sethttpProxyMsg >> %s.%s", "mashall enties fail", err)
		return
	}
	wrapper.httpProxyToMqChannel <- prefix + string(val)
}

// 判断切片中是否包含某个元素
func contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

package httpProxy

import (
	"agent/consts"
	v1 "agent/httpProxy/v1"
	"agent/utils/envUtils"
	"agent/utils/log"
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/metadata"
	m_metadata "github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
)

func httpExec(httpPorts []string, dockerId int64) (rsp *v1.PassGatewayRegisterServiceResponse, err error) {
	headers := make(map[string][]string, 0)
	headers["auth_token"] = make([]string, 0)
	headers["auth_token"] = append(headers["auth_token"], "demo")
	md := metadata.New(headers)
	clientHeaders := m_metadata.WithConstants(md)
	paasGatewayUrl := envUtils.GetString(consts.PaasGatewayUrl)
	conn, err := http.NewClient(
		context.Background(),
		http.WithEndpoint(paasGatewayUrl),
		http.WithTimeout(30*time.Second),
		http.WithMiddleware(
			recovery.Recovery(),
			m_metadata.Client(clientHeaders),
		),
	)

	if err != nil {
		return nil, err
	}

	ctx := context.Background()
	httpClient := v1.NewPassGatewayServiceHTTPClient(conn)

	req1 := &v1.PassGatewayRegisterServiceRequest{
		DockerId: dockerId,
		PortList: httpPorts,
	}
	res1, err := httpClient.PassGatewayRegisterService(ctx, req1)
	if err != nil {
		return nil, err
	}
	log.Debugf("http-client, res1: %+v", res1)

	return res1, nil

	//req3 := &v1.GitExecResultCheckRequest{Id: res1.GetId()}
	//res2, err := httpClient.GitExecResultCheck(ctx, req3)
	//log.Infof("http-client-error: %+v, res2: %+v", err, res2)
}

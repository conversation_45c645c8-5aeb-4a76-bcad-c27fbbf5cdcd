package httpProxy

import (
	"agent/consts"
	"agent/httpProxy/middleware"
	"agent/utils/envUtils"
	"agent/utils/log"
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type HttpRemoteAuth func(url string) (string, string, error)

// 代理请求处理函数
func handleProxyRequest(w http.ResponseWriter, r *http.Request) {
	remoteInfo, err := middleware.GetRemoteInfoFromContext(r)
	if err != nil {
		log.Errorf("httpProxy:HttpMainProxy, remoteInfo error: %+v", err)
		http.Error(w, "Internal Server Error(remote info)", http.StatusInternalServerError)
		return
	}

	if remoteInfo.Host == "" || remoteInfo.Port == 0 {
		log.Infof("httpProxy:HttpMainProxy, remoteInfo: %+v", remoteInfo)
		http.Error(w, "The address cannot be reached", http.StatusNotFound)
		return
	}

	if strings.ToLower(r.Header.Get("Upgrade")) == "websocket" && strings.ToLower(r.Header.Get("Connection")) == "upgrade" {
		r.Header.Set("Connection", "Upgrade")
		log.Debugf("HttpMainProxy, remoteInfo ws Upgrade: %s, remoteInfo: %+v", r.Header.Get("Upgrade"), remoteInfo)
		handleWebSocket(w, r)
		return
	}

	// 解析目标服务器的 URL
	host := fmt.Sprintf("http://%s:%d", remoteInfo.Host, remoteInfo.Port)
	targetURL, err := url.Parse(host)
	if err != nil {
		log.Errorf("httpProxy:HttpMainProxy, url-parse error: %+v", err)
		http.Error(w, "Internal Server Error(remote parse)", http.StatusInternalServerError)
		return
	}

	if r.URL == nil {
		log.Errorf("httpProxy:HttpMainProxy, url-parse error: %+v", err)
		http.Error(w, "Internal Server Error(remote empty)", http.StatusInternalServerError)
		return
	}

	targetURL.Path = r.URL.Path
	targetURL.RawPath = r.URL.RawPath
	targetURL.OmitHost = r.URL.OmitHost
	targetURL.ForceQuery = r.URL.ForceQuery
	targetURL.RawQuery = r.URL.RawQuery
	targetURL.Fragment = r.URL.Fragment
	targetURL.RawFragment = r.URL.RawFragment

	// 1. 读取原始请求的Body内容
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		log.Errorf("Failed to read request body: %v", err)
		http.Error(w, "Bad Request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 2. 重置原始请求的Body（避免后续读取失败）
	//r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 创建一个新的请求对象，用于向目标服务器发送请求
	newRequest, err := http.NewRequest(r.Method, targetURL.String(), bytes.NewBuffer(bodyBytes))
	if err != nil {
		log.Errorf("httpProxy:HttpMainProxy, newRequest error: %+v", err)
		http.Error(w, "Internal Server Error(new request)", http.StatusInternalServerError)
		return
	}

	// 复制原始请求的头部信息
	newRequest.Header = r.Header
	//newRequest.Header.Set("X-Forwarded-Host", fmt.Sprintf("%s:%d", remoteInfo.Host, remoteInfo.Port))
	newRequest.Header.Set("X-Forwarded-Host", r.Host)
	newRequest.Header.Set("X-Forwarded-Proto", "https")
	//newRequest.Header.Set("X-Forwarded-Port", "443")
	//newRequest.Header.Set("Host", r.Host)
	newRequest.Header.Set("X-Forwarded-For", r.Header.Get("X-Forwarded-For"))
	newRequest.Header.Set("X-Real-IP", r.RemoteAddr)
	newRequest.Host = r.Host

	// 发送请求到目标服务器
	client := http.Client{
		Timeout: 60 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 禁用重定向
			return http.ErrUseLastResponse
		},
	}

	log.Infof("httpProxy:HttpMainProxy, remoteInfo-before: %+v", remoteInfo)
	resp, err := client.Do(newRequest)
	if err != nil {
		log.Warnf("httpProxy:HttpMainProxy, DefaultClient-do error1: %+v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// 将目标服务器的响应返回给客户端
	for key, values := range resp.Header {
		for _, value := range values {
			log.Debugf("httpProxy:HttpMainProxy, httpHeader: %s, httpValue: %s", key, value)
			if key == "Set-Cookie" {
				// 解析并修改cookie的SameSite属性
				if cookie, err := http.ParseSetCookie(value); err == nil {
					cookie.SameSite = http.SameSiteNoneMode
					cookie.Secure = true // 添加Secure标记
					http.SetCookie(w, cookie)
				}
			} else {
				w.Header().Add(key, value)
			}
		}
	}

	w.Header().Set("Content-Security-Policy", "frame-ancestors *")
	w.Header().Set("X-Frame-Options", "ALLOW-FROM *")
	w.WriteHeader(resp.StatusCode)
	log.Infof("httpProxy:HttpMainProxy, targetURL: %+v, remoteInfo-after: %+v, StatusCode: %d"+
		" reqHeader: %+v, resHeader: %+v", targetURL, remoteInfo, resp.StatusCode, r.Header, resp.Header)
	io.Copy(w, resp.Body)
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	HandleWsProxyRequest(w, r)
}

// entry point
func ProxyHttpMain() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("httpProxy:HttpMainProxy, recover error: %+v", err)
			}
		}()

		// 设置代理服务器监听的端口
		http.Handle("/", middleware.AuthMiddleware(http.HandlerFunc(handleProxyRequest)))
		proxyPort := envUtils.GetString(consts.PaasProjectWebPort)
		httpServerAddr := "0.0.0.0:" + proxyPort
		log.Infof("httpProxy:HttpMainProxy, Starting proxy server on %s", httpServerAddr)
		if err := http.ListenAndServe(httpServerAddr, nil); err != nil {
			log.Errorf("httpProxy:HttpMainProxy, ListenAndServe error: %+v", err)
			panic(err)
		}
	}()
}

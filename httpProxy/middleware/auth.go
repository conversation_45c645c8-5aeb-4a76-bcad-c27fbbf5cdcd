package middleware

import (
	"agent/utils/log"
	"context"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/pingcap/errors"
)

const (
	PROXY_HTTP_REMOTE_INFO = "remote_info"
	DEFAULT_HTTP_PORT      = 80
)

type RemoteHost struct {
	Url  string `json:"url"`
	Host string `json:"host"`
	Port int    `json:"port"`
}

var httpProxyRules []RemoteHost

func SetRemoteHostInfo(remoteHosts []RemoteHost) {
	httpProxyRules = remoteHosts
	log.Infof("httpProxy:httpProxyRules:%+v", httpProxyRules)
}

// Middleware 是一个处理 HTTP 请求和响应的中间件
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()
		remoteInfo := getRemoteHostInfoByUrl(r.Host)
		log.Debugf("httpProxy:httpProxyRules:%+v", httpProxyRules)
		log.Debugf("httpProxy:remoteInfo:%+v", remoteInfo)

		// 获取登录信息
		r = r.WithContext(context.WithValue(r.Context(), PROXY_HTTP_REMOTE_INFO, remoteInfo))
		next.ServeHTTP(w, r)
		endTime := time.Now()

		// 记录请求和响应的详细信息
		log.Infof("httpProxy:ProxyHttpMain, Request: %s %s %s, host: %+v, remoteInfo: %+v, Time: %v",
			r.Method, r.RequestURI, r.Proto, r.Host, remoteInfo, endTime.Sub(startTime))
	})
}

func getRemoteHostInfoByUrl(url string) *RemoteHost {
	log.Debugf("httpProxy:url:%s", url)
	remoteInfo := &RemoteHost{
		Url:  url,
		Host: "",
		Port: 0,
	}

	// 尝试直接从URL解析端口
	hostParts := strings.Split(url, "-")
	if len(hostParts) > 1 {
		// URL包含端口信息 (format: host:port)
		remoteInfo.Host = "localhost"
		portStr := hostParts[0]
		port, err := strconv.Atoi(portStr)
		if err == nil {
			remoteInfo.Port = port
		} else {
			log.Errorf("httpProxy: failed to parse port from URL %s: %v", url, err)
		}
	}

	// 作为备选方案，检查预定义的httpProxyRules
	// for _, info := range httpProxyRules {
	// 	log.Debugf("httpProxy:info:%+v", info)
	// 	if strings.ToLower(info.Url) == strings.ToLower(url) {
	// 		remoteInfo = &RemoteHost{
	// 			Url:  url,
	// 			Host: info.Host,
	// 			Port: info.Port,
	// 		}
	// 		break
	// 	}
	// }

	return remoteInfo
}

func GetRemoteInfoFromContext(r *http.Request) (*RemoteHost, error) {
	remoteInfo, ok := r.Context().Value(PROXY_HTTP_REMOTE_INFO).(*RemoteHost)
	if !ok {
		return nil, errors.New("remote info not exist")
	}

	return remoteInfo, nil
}

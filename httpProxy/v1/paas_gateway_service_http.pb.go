// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.2
// - protoc             v5.29.3
// source: paas_gateway/v1/paas_gateway_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPassGatewayServicePassGatewayRegisterService = "/api.paas_engine_server.v1.PassGatewayService/PassGatewayRegisterService"

type PassGatewayServiceHTTPServer interface {
	// PassGatewayRegisterService 注册服务
	PassGatewayRegisterService(context.Context, *PassGatewayRegisterServiceRequest) (*PassGatewayRegisterServiceResponse, error)
}

func RegisterPassGatewayServiceHTTPServer(s *http.Server, srv PassGatewayServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/service/register", _PassGatewayService_PassGatewayRegisterService0_HTTP_Handler(srv))
}

func _PassGatewayService_PassGatewayRegisterService0_HTTP_Handler(srv PassGatewayServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PassGatewayRegisterServiceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPassGatewayServicePassGatewayRegisterService)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PassGatewayRegisterService(ctx, req.(*PassGatewayRegisterServiceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PassGatewayRegisterServiceResponse)
		return ctx.Result(200, reply)
	}
}

type PassGatewayServiceHTTPClient interface {
	PassGatewayRegisterService(ctx context.Context, req *PassGatewayRegisterServiceRequest, opts ...http.CallOption) (rsp *PassGatewayRegisterServiceResponse, err error)
}

type PassGatewayServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewPassGatewayServiceHTTPClient(client *http.Client) PassGatewayServiceHTTPClient {
	return &PassGatewayServiceHTTPClientImpl{client}
}

func (c *PassGatewayServiceHTTPClientImpl) PassGatewayRegisterService(ctx context.Context, in *PassGatewayRegisterServiceRequest, opts ...http.CallOption) (*PassGatewayRegisterServiceResponse, error) {
	var out PassGatewayRegisterServiceResponse
	pattern := "/api/v1/service/register"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPassGatewayServicePassGatewayRegisterService))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

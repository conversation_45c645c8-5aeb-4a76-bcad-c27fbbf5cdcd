// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.3
// source: paas_gateway/v1/paas_gateway_service.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegisterServiceResultItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// docker容器内部启动的项目端口号
	Port string `protobuf:"bytes,1,opt,name=port,proto3" json:"port,omitempty"`
	// 生成的url地址
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// alias url
	Alias string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
}

func (x *RegisterServiceResultItem) Reset() {
	*x = RegisterServiceResultItem{}
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterServiceResultItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterServiceResultItem) ProtoMessage() {}

func (x *RegisterServiceResultItem) ProtoReflect() protoreflect.Message {
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterServiceResultItem.ProtoReflect.Descriptor instead.
func (*RegisterServiceResultItem) Descriptor() ([]byte, []int) {
	return file_paas_gateway_v1_paas_gateway_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterServiceResultItem) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *RegisterServiceResultItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RegisterServiceResultItem) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

type PassGatewayRegisterServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DockerId int64    `protobuf:"varint,1,opt,name=docker_id,json=dockerId,proto3" json:"docker_id,omitempty"`
	PortList []string `protobuf:"bytes,2,rep,name=port_list,json=portList,proto3" json:"port_list,omitempty"`
}

func (x *PassGatewayRegisterServiceRequest) Reset() {
	*x = PassGatewayRegisterServiceRequest{}
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PassGatewayRegisterServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassGatewayRegisterServiceRequest) ProtoMessage() {}

func (x *PassGatewayRegisterServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassGatewayRegisterServiceRequest.ProtoReflect.Descriptor instead.
func (*PassGatewayRegisterServiceRequest) Descriptor() ([]byte, []int) {
	return file_paas_gateway_v1_paas_gateway_service_proto_rawDescGZIP(), []int{1}
}

func (x *PassGatewayRegisterServiceRequest) GetDockerId() int64 {
	if x != nil {
		return x.DockerId
	}
	return 0
}

func (x *PassGatewayRegisterServiceRequest) GetPortList() []string {
	if x != nil {
		return x.PortList
	}
	return nil
}

type PassGatewayRegisterServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*RegisterServiceResultItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *PassGatewayRegisterServiceResponse) Reset() {
	*x = PassGatewayRegisterServiceResponse{}
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PassGatewayRegisterServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassGatewayRegisterServiceResponse) ProtoMessage() {}

func (x *PassGatewayRegisterServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_paas_gateway_v1_paas_gateway_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassGatewayRegisterServiceResponse.ProtoReflect.Descriptor instead.
func (*PassGatewayRegisterServiceResponse) Descriptor() ([]byte, []int) {
	return file_paas_gateway_v1_paas_gateway_service_proto_rawDescGZIP(), []int{2}
}

func (x *PassGatewayRegisterServiceResponse) GetList() []*RegisterServiceResultItem {
	if x != nil {
		return x.List
	}
	return nil
}

var File_paas_gateway_v1_paas_gateway_service_proto protoreflect.FileDescriptor

var file_paas_gateway_v1_paas_gateway_service_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x19, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x22, 0x5d,
	0x0a, 0x21, 0x50, 0x61, 0x73, 0x73, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6e, 0x0a,
	0x22, 0x50, 0x61, 0x73, 0x73, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xd5, 0x01,
	0x0a, 0x12, 0x50, 0x61, 0x73, 0x73, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0xbe, 0x01, 0x0a, 0x1a, 0x50, 0x61, 0x73, 0x73, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x61, 0x73, 0x73, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x61, 0x73, 0x5f, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x73, 0x73, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x42, 0x46, 0x0a, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x61,
	0x73, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x2d,
	0x63, 0x6c, 0x61, 0x63, 0x6b, 0x79, 0x2d, 0x61, 0x69, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x61, 0x73, 0x5f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_paas_gateway_v1_paas_gateway_service_proto_rawDescOnce sync.Once
	file_paas_gateway_v1_paas_gateway_service_proto_rawDescData = file_paas_gateway_v1_paas_gateway_service_proto_rawDesc
)

func file_paas_gateway_v1_paas_gateway_service_proto_rawDescGZIP() []byte {
	file_paas_gateway_v1_paas_gateway_service_proto_rawDescOnce.Do(func() {
		file_paas_gateway_v1_paas_gateway_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_paas_gateway_v1_paas_gateway_service_proto_rawDescData)
	})
	return file_paas_gateway_v1_paas_gateway_service_proto_rawDescData
}

var file_paas_gateway_v1_paas_gateway_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_paas_gateway_v1_paas_gateway_service_proto_goTypes = []any{
	(*RegisterServiceResultItem)(nil),          // 0: api.paas_engine_server.v1.RegisterServiceResultItem
	(*PassGatewayRegisterServiceRequest)(nil),  // 1: api.paas_engine_server.v1.PassGatewayRegisterServiceRequest
	(*PassGatewayRegisterServiceResponse)(nil), // 2: api.paas_engine_server.v1.PassGatewayRegisterServiceResponse
}
var file_paas_gateway_v1_paas_gateway_service_proto_depIdxs = []int32{
	0, // 0: api.paas_engine_server.v1.PassGatewayRegisterServiceResponse.list:type_name -> api.paas_engine_server.v1.RegisterServiceResultItem
	1, // 1: api.paas_engine_server.v1.PassGatewayService.PassGatewayRegisterService:input_type -> api.paas_engine_server.v1.PassGatewayRegisterServiceRequest
	2, // 2: api.paas_engine_server.v1.PassGatewayService.PassGatewayRegisterService:output_type -> api.paas_engine_server.v1.PassGatewayRegisterServiceResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_paas_gateway_v1_paas_gateway_service_proto_init() }
func file_paas_gateway_v1_paas_gateway_service_proto_init() {
	if File_paas_gateway_v1_paas_gateway_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_paas_gateway_v1_paas_gateway_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_paas_gateway_v1_paas_gateway_service_proto_goTypes,
		DependencyIndexes: file_paas_gateway_v1_paas_gateway_service_proto_depIdxs,
		MessageInfos:      file_paas_gateway_v1_paas_gateway_service_proto_msgTypes,
	}.Build()
	File_paas_gateway_v1_paas_gateway_service_proto = out.File
	file_paas_gateway_v1_paas_gateway_service_proto_rawDesc = nil
	file_paas_gateway_v1_paas_gateway_service_proto_goTypes = nil
	file_paas_gateway_v1_paas_gateway_service_proto_depIdxs = nil
}

# .1024 Configuration file for project run commands, compilation and debug settings (optional);
# Any changes made will be auto-saved and take effect immediately.
# For more information, please refer to the documentation: https://docs.clacky.ai/clacky-workspace/configure

# Command to compile the project before running
compile_command: go build
# Command to run when "Run" button clicked 
run_command: go run ./main.go
package browser

import (
	"os"
	"os/exec"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultChromePath(t *testing.T) {
	// 测试默认Chrome路径常量
	assert.Equal(t, "/usr/chrome-linux64/chrome", DefaultChromePath)
	assert.NotEmpty(t, DefaultChromePath)
	assert.True(t, filepath.IsAbs(DefaultChromePath), "默认路径应该是绝对路径")
}

func TestGetChromePath_DefaultPath(t *testing.T) {
	// 测试默认路径存在时的情况
	path := GetChromePath()

	// 路径可能为空（如果没有安装浏览器）或者是有效路径
	if path != "" {
		// 如果路径不为空，应该是绝对路径
		assert.True(t, filepath.IsAbs(path), "返回的路径应该是绝对路径: %s", path)
		assert.True(t, len(path) > 0, "路径长度应该大于0")
	}

	// 验证返回值是字符串类型（可能为空）
	assert.IsType(t, "", path)
}

func TestGetChromePath_SystemCommands(t *testing.T) {
	// 测试系统命令查找逻辑
	// 由于不能直接模拟exec.LookPath，我们通过测试实际的行为来验证

	t.Run("验证GetChromePath的查找顺序", func(t *testing.T) {
		path := GetChromePath()

		// 如果返回了路径，它应该是以下之一：
		// 1. 默认路径
		// 2. google-chrome命令路径
		// 3. chromium-browser命令路径
		// 4. 空字符串

		if path != "" {
			isValidPath := false

			// 检查是否是默认路径（如果存在）
			if path == DefaultChromePath {
				if _, err := os.Lstat(DefaultChromePath); err == nil {
					isValidPath = true
					t.Logf("使用默认路径: %s", path)
				}
			}

			// 检查是否是通过LookPath找到的
			if !isValidPath {
				if chromePath, err := exec.LookPath("google-chrome"); err == nil && path == chromePath {
					isValidPath = true
					t.Logf("通过google-chrome命令找到: %s", path)
				}
			}

			if !isValidPath {
				if chromiumPath, err := exec.LookPath("chromium-browser"); err == nil && path == chromiumPath {
					isValidPath = true
					t.Logf("通过chromium-browser命令找到: %s", path)
				}
			}

			// 验证路径是有效的
			assert.True(t, isValidPath || filepath.IsAbs(path), "返回的路径应该是有效的: %s", path)
		} else {
			t.Log("未找到Chrome路径，这在没有安装Chrome的环境中是正常的")
		}
	})

	t.Run("验证路径查找的一致性", func(t *testing.T) {
		// 多次调用应该返回相同的结果
		path1 := GetChromePath()
		path2 := GetChromePath()
		assert.Equal(t, path1, path2, "多次调用GetChromePath应该返回相同结果")
	})

	t.Run("验证默认路径检查", func(t *testing.T) {
		// 检查默认路径是否存在
		_, err := os.Lstat(DefaultChromePath)
		if err == nil {
			// 如果默认路径存在，GetChromePath应该返回它
			path := GetChromePath()
			assert.Equal(t, DefaultChromePath, path, "默认路径存在时应该优先返回默认路径")
		} else {
			t.Logf("默认路径不存在: %s", DefaultChromePath)
		}
	})
}

func TestGetChromePath_FileSystemChecks(t *testing.T) {
	// 测试文件系统检查逻辑
	path := GetChromePath()

	// 如果返回了路径，验证它确实存在（或者是可执行的）
	if path != "" {
		// 检查文件是否存在或者是否可以通过LookPath找到
		if filepath.IsAbs(path) {
			// 绝对路径的情况，检查文件存在性
			_, err := os.Stat(path)
			if err == nil {
				t.Logf("Chrome路径存在: %s", path)
			} else {
				t.Logf("Chrome路径可能不存在但函数返回了: %s, 错误: %v", path, err)
			}
		} else {
			// 相对路径的情况，应该是通过LookPath找到的
			_, err := exec.LookPath(path)
			assert.NoError(t, err, "通过LookPath找到的路径应该是可执行的")
		}
	}

	// 验证返回值类型
	assert.IsType(t, "", path)
}

func TestChromeArgs_Basic(t *testing.T) {
	args := ChromeArgs()

	// 验证返回的是字符串切片
	assert.IsType(t, []string{}, args)
	assert.NotEmpty(t, args, "Chrome参数列表不应该为空")

	// 验证参数数量合理（应该有很多参数）
	assert.Greater(t, len(args), 50, "Chrome参数数量应该大于50个")

	// 验证所有参数都是非空字符串
	for i, arg := range args {
		assert.NotEmpty(t, arg, "参数 %d 不应该为空", i)
		assert.IsType(t, "", arg, "参数 %d 应该是字符串类型", i)
		assert.True(t, len(arg) > 1, "参数 %d 长度应该大于1: %s", i, arg)
	}
}

func TestChromeArgs_RequiredFlags(t *testing.T) {
	args := ChromeArgs()

	// 测试关键的必需参数（移除GPU相关参数，因为它们在config.go中被注释掉了）
	requiredFlags := []string{
		"--no-sandbox",
		"--start-maximized",
		"--disable-dev-shm-usage",
		"--no-first-run",
		"--enable-automation",
		"--disable-infobars",
		"--disable-web-security",
		"--mute-audio",
		"--noerrdialogs",
	}

	for _, flag := range requiredFlags {
		assert.Contains(t, args, flag, "应该包含必需参数: %s", flag)
	}
}

func TestChromeArgs_SecurityFlags(t *testing.T) {
	args := ChromeArgs()

	// 验证包含重要的安全和自动化相关参数
	securityFlags := []string{
		"--no-sandbox",
		"--disable-blink-features=AutomationControlled",
		"--enable-automation",
		"--disable-infobars",
		"--disable-notifications",
		"--password-store=basic",
		"--use-mock-keychain",
		"--no-service-autorun",
	}

	for _, flag := range securityFlags {
		assert.Contains(t, args, flag, "应该包含安全标志: %s", flag)
	}
}

func TestChromeArgs_PerformanceFlags(t *testing.T) {
	args := ChromeArgs()

	// 验证包含性能相关参数（移除GPU相关参数）
	performanceFlags := []string{
		"--disable-dev-shm-usage",
		"--disable-background-timer-throttling",
		"--disable-backgrounding-occluded-windows",
		"--disable-extensions",
		"--disable-plugins",
		"--disable-sync",
		"--aggressive-cache-discard",
		"--memory-pressure-off",
		"--max_old_space_size=512",
	}

	for _, flag := range performanceFlags {
		assert.Contains(t, args, flag, "应该包含性能标志: %s", flag)
	}
}

func TestChromeArgs_GPUFlags(t *testing.T) {
	// 由于GPU相关参数在config.go中被注释掉了，这个测试应该跳过或者验证参数确实不存在
	t.Skip("GPU相关参数在config.go中被注释掉了，跳过此测试")

	// 如果将来重新启用GPU参数，可以使用以下代码：
	/*
		args := ChromeArgs()
		gpuFlags := []string{
			"--disable-gpu",
			"--disable-gpu-sandbox",
			"--disable-gpu-process-crash-limit",
			"--disable-gpu-rasterization",
			"--disable-gpu-compositing",
			"--disable-accelerated-2d-canvas",
			"--disable-accelerated-jpeg-decoding",
			"--disable-accelerated-mjpeg-decode",
			"--disable-accelerated-video-decode",
			"--disable-accelerated-video-encode",
			"--use-angle=swiftshader-webgl",
			"--disable-webgl",
			"--disable-webgl2",
			"--disable-3d-apis",
			"--disable-vulkan",
			"--disable-d3d11",
			"--disable-metal",
		}

		for _, flag := range gpuFlags {
			assert.Contains(t, args, flag, "应该包含GPU禁用标志: %s", flag)
		}
	*/
}

func TestChromeArgs_AIServiceFlags(t *testing.T) {
	args := ChromeArgs()

	// 验证AI和机器学习服务禁用参数（解决on-device model service错误）
	aiFlags := []string{
		"--disable-machine-learning-service",
		"--disable-on-device-translation",
		"--disable-ml-model-service",
		"--disable-optimization-guide-fetching",
		"--disable-component-cloud-policy",
		"--disable-remote-fonts",
		"--disable-remote-playback-api",
	}

	for _, flag := range aiFlags {
		assert.Contains(t, args, flag, "应该包含AI服务禁用标志: %s", flag)
	}
}

func TestChromeArgs_UserDataDir(t *testing.T) {
	args := ChromeArgs()

	// 查找用户数据目录参数
	userDataDirFound := false
	for _, arg := range args {
		if arg == "--user-data-dir=/tmp/chrome-user-data" {
			userDataDirFound = true
			break
		}
	}

	assert.True(t, userDataDirFound, "应该设置用户数据目录到临时文件夹")
}

func TestChromeArgs_NoHarmfulParams(t *testing.T) {
	args := ChromeArgs()

	// 确保没有包含一些可能有害的参数
	harmfulFlags := []string{
		"--enable-logging",      // 可能产生大量日志
		"--log-level=0",         // 详细日志级别
		"--enable-benchmarking", // 基准测试模式
	}

	for _, flag := range harmfulFlags {
		assert.NotContains(t, args, flag, "不应该包含可能有害的标志: %s", flag)
	}
}

func TestChromeArgs_BlinkSettings(t *testing.T) {
	args := ChromeArgs()

	// 验证Blink引擎设置
	blinkSettingsFound := false
	expectedBlinkSettings := "--blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4"

	for _, arg := range args {
		if arg == expectedBlinkSettings {
			blinkSettingsFound = true
			break
		}
	}

	assert.True(t, blinkSettingsFound, "应该包含Blink引擎的悬停和指针类型配置")
}

func TestChromeArgs_FeatureFlags(t *testing.T) {
	args := ChromeArgs()

	// 验证功能特性禁用参数
	disableFeaturesFound := false
	for _, arg := range args {
		if arg == "--disable-features=ImprovedCookieControls,LazyFrameLoading,GlobalMediaControls,DestroyProfileOnBrowserClose,MediaRouter,DialMediaRouteProvider,AcceptCHFrame,AutoExpandDetailsElement,CertificateTransparencyComponentUpdater,AvoidUnnecessaryBeforeUnloadCheckSync,Translate,HttpsUpgrades,PaintHolding" {
			disableFeaturesFound = true
			break
		}
	}

	assert.True(t, disableFeaturesFound, "应该禁用指定的功能特性")

	// 验证VizDisplayCompositor功能禁用
	vizDisableFound := false
	for _, arg := range args {
		if arg == "--disable-features=VizDisplayCompositor" {
			vizDisableFound = true
			break
		}
	}

	assert.True(t, vizDisableFound, "应该禁用VizDisplayCompositor功能")
}

func TestChromeArgs_Consistency(t *testing.T) {
	// 测试多次调用返回相同结果
	args1 := ChromeArgs()
	args2 := ChromeArgs()

	require.Equal(t, len(args1), len(args2), "多次调用应该返回相同数量的参数")

	for i, arg := range args1 {
		assert.Equal(t, arg, args2[i], "参数 %d 应该相同", i)
	}
}

package browser

import (
	"github.com/playwright-community/playwright-go"
	"path/filepath"
	"strings"
	"time"
)

type HttpRequestModel struct {
	Url              string            `json:"url"`
	ResponseHeaders  map[string]string `json:"response_headers"`
	RequestHandlers  map[string]string `json:"request_handlers"`
	HttpCode         int               `json:"http_code,omitempty"`
	Method           string            `json:"method"`
	RequestBody      string            `json:"request_body,omitempty"`
	ResponseBody     string            `json:"response_body,omitempty"`
	Timestamp        int64             `json:"timestamp"`
	ResourceType     string            `json:"resource_type,omitempty"`
	IsStaticResource bool              `json:"is_static_resource"`
	RequestError     string            `json:"request_error,omitempty"`
}

func NewRequestModel(response playwright.Response) *HttpRequestModel {
	ctx := &HttpRequestModel{
		Url:              response.URL(),
		HttpCode:         response.Status(),
		Timestamp:        time.Now().UnixMilli(),
		IsStaticResource: false,
	}

	allResponseHeaders, err := response.AllHeaders()
	if err != nil {
		ctx.RequestHandlers = map[string]string{}
	} else {
		ctx.ResponseHeaders = filterImportantHeaders(allResponseHeaders)
	}

	request := response.Request()
	if request != nil {
		// 获取请求资源类型
		resourceType := request.ResourceType()

		// 判断是否为静态资源
		isStaticResource := isStaticResource(response.URL(), response.Headers(), resourceType)

		ctx.ResourceType = resourceType
		ctx.IsStaticResource = isStaticResource

		headers, err := request.AllHeaders()
		if err != nil {
			ctx.RequestHandlers = map[string]string{}
		} else {
			ctx.RequestHandlers = filterImportantHeaders(headers)
		}

		data, err := request.PostData()
		if data != "" {
			ctx.RequestBody = data
		}

		ctx.Method = request.Method()
	}

	if !ctx.IsStaticResource {
		data, err := response.Body()
		if err == nil {
			ctx.ResponseBody = string(data)
		}
	}

	// 只需要返回XHR和Fetch请求
	if ctx.ResourceType == "xhr" || ctx.ResourceType == "fetch" {
		return ctx
	}

	return nil
}

func NewRequestModelFromRequest(request playwright.Request) *HttpRequestModel {
	// 获取请求资源类型
	resourceType := request.ResourceType()

	header, err := request.AllHeaders()
	if err != nil {
		header = map[string]string{}
	}

	// 判断是否为静态资源
	isStaticResource := isStaticResource(request.URL(), header, resourceType)

	var body string
	data, err := request.PostData()
	if data != "" {
		body = data
	}

	return &HttpRequestModel{
		Url:              request.URL(),
		Method:           request.Method(),
		Timestamp:        time.Now().UnixMilli(),
		IsStaticResource: isStaticResource,
		RequestHandlers:  filterImportantHeaders(header),
		ResourceType:     resourceType,
		RequestError:     request.Failure().Error(),
		RequestBody:      body,
	}
}

// isStaticResource 判断是否为静态资源请求
func isStaticResource(url string, headers map[string]string, resourceType string) bool {
	// 1. 通过 Playwright ResourceType 判断
	staticResourceTypes := []string{
		"stylesheet", // CSS文件
		"script",     // JS文件
		"image",      // 图片文件
		"font",       // 字体文件
		"media",      // 音视频文件
		"manifest",   // 清单文件
		"other",      // 其他静态文件
		"document",   // 文档文件
	}

	for _, staticType := range staticResourceTypes {
		if strings.EqualFold(resourceType, staticType) {
			return true
		}
	}

	// 2. 通过文件扩展名判断
	ext := strings.ToLower(filepath.Ext(url))
	staticExtensions := []string{
		".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf", // 字体文件
		".mp4", ".mp3", ".avi", ".mov", ".webm", // 媒体文件
		".pdf", ".zip", ".rar", ".tar", ".gz", // 文档和压缩文件
		".xml", ".json", ".txt", // 配置文件（根据实际需求调整）
	}

	for _, staticExt := range staticExtensions {
		if ext == staticExt {
			return true
		}
	}

	// 3. 通过 Content-Type 判断
	contentType := ""
	if ct, exists := headers["content-type"]; exists {
		contentType = strings.ToLower(ct)
	}

	staticContentTypes := []string{
		"text/css",
		"application/javascript",
		"text/javascript",
		"image/",
		"font/",
		"audio/",
		"video/",
		"application/font",
		"application/octet-stream", // 通常用于二进制文件
	}

	for _, staticContentType := range staticContentTypes {
		if strings.Contains(contentType, staticContentType) {
			return true
		}
	}

	// 4. 特殊路径判断（可根据实际项目调整）
	staticPaths := []string{
		"/static/",
		"/assets/",
		"/public/",
		"/css/",
		"/js/",
		"/images/",
		"/img/",
		"/fonts/",
	}

	for _, staticPath := range staticPaths {
		if strings.Contains(strings.ToLower(url), staticPath) {
			return true
		}
	}

	// 默认认为是API请求
	return false
}

// filterImportantHeaders 过滤出重要的header信息
func filterImportantHeaders(allHeaders map[string]string) map[string]string {
	// 定义重要的header列表（小写）
	importantHeaders := map[string]bool{
		"authorization":    true,
		"cookie":           true,
		"content-type":     true,
		"accept":           true,
		"referer":          true,
		"x-requested-with": true,
		"x-api-key":        true,
		"x-auth-token":     true,
		"x-csrf-token":     true,
		"origin":           true,
	}

	filteredHeaders := make(map[string]string)

	for key, value := range allHeaders {
		lowerKey := strings.ToLower(key)

		// 检查是否在重要header列表中
		if importantHeaders[lowerKey] {
			filteredHeaders[key] = value
			continue
		}

		// 保留自定义业务header（通常以x-开头）
		if strings.HasPrefix(lowerKey, "x-") &&
			!strings.Contains(lowerKey, "user-agent") &&
			!strings.Contains(lowerKey, "forwarded") {
			filteredHeaders[key] = value
		}
	}

	return filteredHeaders
}

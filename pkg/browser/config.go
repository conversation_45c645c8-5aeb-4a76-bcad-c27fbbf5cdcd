package browser

import (
	"os"
	"os/exec"
)

// Chrome路径配置
const (
	// DefaultChromePath 默认的Chrome安装路径
	DefaultChromePath = "/usr/chrome-linux64/chrome"
)

// GetChromePath 获取Chrome浏览器的可执行文件路径
// 按优先级顺序查找：
// 1. 默认安装路径
// 2. google-chrome 命令
// 3. chromium-browser 命令
func GetChromePath() string {
	// 首先检查默认路径
	if _, err := os.Lstat(DefaultChromePath); err == nil {
		return DefaultChromePath
	}

	// 查找 google-chrome
	if path, err := exec.LookPath("google-chrome"); err == nil {
		return path
	}

	// 查找 chromium-browser
	if path, err := exec.LookPath("chromium-browser"); err == nil {
		return path
	}

	return ""
}

// ChromeArgs 返回Chrome浏览器的启动参数
func ChromeArgs() []string {
	return []string{
		// 窗口管理相关
		"--start-maximized",  // 启动时最大化窗口
		"--process-per-site", // 每个站点使用独立进程，提高安全性和稳定性

		"--no-restore-last-session",        // 不恢复上次会话
		"--disable-session-crashed-bubble", // 禁用崩溃恢复提示

		// 网络和后台服务配置
		"--disable-field-trial-config",                             // 禁用字段试验配置
		"--disable-background-networking",                          // 禁用后台网络活动
		"--enable-features=NetworkService,NetworkServiceInProcess", // 启用网络服务功能
		"--disable-background-timer-throttling",                    // 禁用后台定时器节流
		"--disable-backgrounding-occluded-windows",                 // 禁用被遮挡窗口的后台处理
		"--disable-back-forward-cache",                             // 禁用前进后退缓存

		// 错误报告和安全检测
		"--disable-breakpad",                       // 禁用崩溃报告系统
		"--disable-client-side-phishing-detection", // 禁用客户端钓鱼检测

		// 组件和扩展管理
		"--disable-component-extensions-with-background-pages", // 禁用带后台页面的组件扩展
		"--disable-component-update",                           // 禁用组件自动更新

		// 浏览器行为配置
		"--no-default-browser-check", // 不检查是否为默认浏览器
		"--disable-default-apps",     // 禁用默认应用
		"--disable-dev-shm-usage",    // 禁用/dev/shm使用，避免Docker环境中的内存问题

		// 功能特性禁用（提高性能和稳定性）
		"--disable-features=ImprovedCookieControls,LazyFrameLoading,GlobalMediaControls,DestroyProfileOnBrowserClose,MediaRouter,DialMediaRouteProvider,AcceptCHFrame,AutoExpandDetailsElement,CertificateTransparencyComponentUpdater,AvoidUnnecessaryBeforeUnloadCheckSync,Translate,HttpsUpgrades,PaintHolding",

		// 输入和交互优化
		"--allow-pre-commit-input",          // 允许预提交输入
		"--disable-hang-monitor",            // 禁用挂起监控
		"--disable-ipc-flooding-protection", // 禁用IPC洪水保护
		"--disable-popup-blocking",          // 禁用弹窗阻止
		"--disable-prompt-on-repost",        // 禁用重新提交时的提示
		"--disable-renderer-backgrounding",  // 禁用渲染器后台处理

		// 显示和颜色配置
		//"--force-color-profile=srgb", // 强制使用sRGB颜色配置文件

		// 自动化和测试相关
		"--metrics-recording-only",                // 仅记录指标，不上传
		"--no-first-run",                          // 跳过首次运行设置
		"--enable-automation",                     // 启用自动化模式
		"--disable-infobars",                      // 禁用信息栏显示
		"--disable-notifications",                 // 禁用通知
		"--disable-web-security",                  // 禁用web安全检查（用于自动化测试）
		"--disable-features=VizDisplayCompositor", // 禁用VizDisplayCompositor功能
		"--excludeSwitches=enable-automation",     // 排除enable-automation开关（隐藏自动化标识）

		// 安全和认证
		"--password-store=basic", // 使用基本密码存储
		"--use-mock-keychain",    // 使用模拟钥匙串
		"--no-service-autorun",   // 禁止服务自动运行

		// PDF和搜索引擎
		"--export-tagged-pdf",                   // 导出带标签的PDF
		"--disable-search-engine-choice-screen", // 禁用搜索引擎选择屏幕

		// 音频和输入设备
		"--mute-audio", // 静音音频输出
		"--blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4", // 配置Blink引擎的悬停和指针类型

		// 沙盒和安全
		"--no-sandbox", // 禁用沙盒模式（注意：降低安全性）
		"--disable-blink-features=AutomationControlled", // 禁用自动化控制检测
		//"--disable-dev-tools",                           // 禁用开发者工具
		//"--disable-extensions-http-throttling",          // 禁用扩展HTTP节流
		//"--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", // 设置正常的用户代理字符串
		//
		//// GPU和渲染相关 - 解决GPU不支持错误
		//"--disable-gpu",                       // 禁用GPU加速
		//"--disable-gpu-sandbox",               // 禁用GPU沙盒
		//"--disable-gpu-process-crash-limit",   // 禁用GPU进程崩溃限制
		//"--disable-gpu-rasterization",         // 禁用GPU栅格化
		//"--disable-gpu-compositing",           // 禁用GPU合成
		//"--disable-accelerated-2d-canvas",     // 禁用2D画布硬件加速
		//"--disable-accelerated-jpeg-decoding", // 禁用JPEG硬件解码
		//"--disable-accelerated-mjpeg-decode",  // 禁用MJPEG硬件解码
		//"--disable-accelerated-video-decode",  // 禁用视频硬件解码
		//"--disable-accelerated-video-encode",  // 禁用视频硬件编码
		//"--use-angle=swiftshader-webgl",       // 使用SwiftShader进行WebGL渲染
		//"--disable-webgl",                     // 禁用WebGL
		//"--disable-webgl2",                    // 禁用WebGL2
		//"--disable-3d-apis",                   // 禁用3D API
		//"--disable-vulkan",                    // 禁用Vulkan API
		//"--disable-d3d11",                     // 禁用Direct3D 11
		//"--disable-metal",                     // 禁用Metal（macOS）

		// AI和机器学习服务禁用 - 解决on-device model service错误
		"--disable-machine-learning-service",    // 禁用机器学习服务
		"--disable-on-device-translation",       // 禁用设备上翻译
		"--disable-ml-model-service",            // 禁用ML模型服务
		"--disable-optimization-guide-fetching", // 禁用优化指南获取
		"--disable-component-cloud-policy",      // 禁用组件云策略
		"--disable-remote-fonts",                // 禁用远程字体
		"--disable-remote-playback-api",         // 禁用远程播放API

		"--noerrdialogs", // 禁用错误对话框

		// 性能优化参数
		"--user-data-dir=/tmp/chrome-user-data", // 指定用户数据目录到临时文件夹
		"--disable-extensions",                  // 禁用所有扩展程序
		"--disable-plugins",                     // 禁用所有插件
		"--disable-sync",                        // 禁用Chrome同步功能
		"--disable-translate",                   // 禁用页面翻译功能
		"--disable-background-mode",             // 禁用后台模式运行
		"--disable-background-downloads",        // 禁用后台下载
		"--aggressive-cache-discard",            // 积极丢弃缓存以释放内存
		"--memory-pressure-off",                 // 关闭内存压力检测
		"--max_old_space_size=512",              // 限制V8 JavaScript引擎的内存使用为512MB
		//"--disable-web-security",         // 禁用web安全检查（仅用于开发环境，生产环境请勿使用）
	}
}

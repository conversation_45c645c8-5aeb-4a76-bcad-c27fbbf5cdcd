package browser

import (
	"agent/pkg/errors"
	"context"
	"os"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
)

func TestNewBrowserManager(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	assert.NotNil(t, manager)
	assert.NotNil(t, manager.driver)
	assert.NotNil(t, manager.mutex)
	assert.Nil(t, manager.browserHandler)
	// 路径可能为空，取决于系统是否安装了浏览器，所以不检查NotEmpty
}

func TestBrowserManager_IsInstalled(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 测试IsInstalled方法
	installed := manager.IsInstalled()

	// 根据路径是否为空来判断
	if manager.path == "" {
		assert.False(t, installed)
	} else {
		assert.True(t, installed)
	}
}

func TestGetChromePath(t *testing.T) {
	path := GetChromePath()

	// 路径可能为空（如果没有安装浏览器）或者是有效路径
	if path != "" {
		// 如果路径不为空，应该是绝对路径
		assert.True(t, len(path) > 0)
	}

	// 测试路径检查逻辑
	testPaths := []string{
		DefaultChromePath,
		"google-chrome",
		"chromium-browser",
	}

	for _, testPath := range testPaths {
		assert.NotEmpty(t, testPath)
	}
}

func TestBrowserManager_GetOrCreateBrowser_NotInstalled(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 模拟浏览器未安装的情况
	manager.path = ""

	ctx := context.Background()
	browser, err := manager.GetOrCreateBrowser(ctx)

	assert.Error(t, err)
	assert.Nil(t, browser)
	// 检查错误是否包含预期的错误信息
	assert.Contains(t, err.Error(), "Browser not installed")
}

func TestBrowserManager_GetOrCreateBrowser_WithExistingBrowser(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 由于无法创建真实的BrowserHandler，我们测试逻辑
	// 模拟已有浏览器处理器的情况

	// 测试mutex的使用
	manager.mutex.Lock()
	defer manager.mutex.Unlock()

	// 验证mutex不为nil
	assert.NotNil(t, manager.mutex)
}

func TestBrowserManager_GetOrCreateBrowser_WithExistingClosedBrowser(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 模拟有一个已关闭的浏览器处理器
	manager.browserHandler = &BrowserHandler{}
	manager.browserHandler.isClosed.Store(true)

	// 由于浏览器未安装，应该返回错误
	manager.path = ""

	ctx := context.Background()
	browser, err := manager.GetOrCreateBrowser(ctx)

	assert.Error(t, err)
	assert.Nil(t, browser)
}

func TestBrowserManager_create_NotInstalled(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 模拟浏览器未安装
	manager.path = ""

	ctx := context.Background()
	browser, err := manager.create(ctx)

	assert.Error(t, err)
	assert.Nil(t, browser)
	assert.Equal(t, errors.ErrBrowserNotInstalled, err)
}

func TestBrowserManager_create_WithPath(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 设置一个假的路径
	manager.path = "/fake/chrome/path"

	// 使用短超时避免测试卡死
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	browser, err := manager.create(ctx)

	// 由于playwright未安装或路径不存在，应该有错误
	assert.Error(t, err)
	assert.Nil(t, browser)
}

func TestBrowserManager_create_ContextCanceled(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 设置一个假的路径
	manager.path = "/fake/chrome/path"

	// 创建一个已取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	browser, err := manager.create(ctx)

	// 应该有错误
	assert.Error(t, err)
	assert.Nil(t, browser)
}

func TestBrowserManager_create_DriverError(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 设置一个假的路径
	manager.path = "/fake/chrome/path"

	// 创建一个短超时的上下文来模拟driver错误
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
	defer cancel()

	// 等待上下文超时
	time.Sleep(1 * time.Millisecond)

	browser, err := manager.create(ctx)

	// 应该有错误
	assert.Error(t, err)
	assert.Nil(t, browser)
}

func TestBrowserManager_Close(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 测试关闭操作（即使没有真实的浏览器）
	manager.Close()

	// 验证关闭操作不会panic
	assert.NotNil(t, manager)
}

func TestBrowserManager_Close_WithNilComponents(t *testing.T) {
	manager := &BrowserManager{
		browserHandler: nil,
		driver:         nil,
	}

	// 测试当组件为nil时的关闭操作
	manager.Close()

	// 验证不会panic
	assert.NotNil(t, manager)
}

func TestBrowserManager_Close_WithBrowserHandler(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 由于BrowserHandler需要真实的playwright对象，我们跳过这个测试
	// 或者只测试manager本身的关闭逻辑

	// 测试关闭操作（没有browserHandler）
	manager.Close()

	// 验证不会panic
	assert.NotNil(t, manager)
}

func TestBrowserManager_LaunchOptions(t *testing.T) {
	// 测试浏览器启动选项的逻辑

	// 验证一些关键的启动参数
	expectedArgs := []string{
		"--start-maximized",
		"--no-sandbox",
		"--disable-gpu",
		"--disable-blink-features=AutomationControlled",
		"--mute-audio",
	}

	for _, arg := range expectedArgs {
		assert.NotEmpty(t, arg)
		assert.True(t, len(arg) > 2) // 所有参数都应该以--开头
	}
}

func TestBrowserManager_ConcurrentAccess(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 测试并发访问
	done := make(chan bool, 5)

	for i := 0; i < 5; i++ {
		go func() {
			defer func() { done <- true }()

			// 测试并发调用IsInstalled
			manager.IsInstalled()

			// 测试并发访问mutex
			manager.mutex.Lock()
			manager.mutex.Unlock()
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 5; i++ {
		<-done
	}

	assert.NotNil(t, manager)
}

func TestBrowserManager_PathDetection(t *testing.T) {
	// 测试路径检测逻辑

	// 模拟文件存在检查
	testPath := "/tmp/test-chrome"

	// 创建临时文件用于测试
	file, err := os.Create(testPath)
	if err == nil {
		file.Close()
		defer os.Remove(testPath)

		// 检查文件是否存在
		_, err := os.Lstat(testPath)
		assert.NoError(t, err)
	}
}

func TestBrowserManager_ContextCancellation(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 测试上下文取消
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	// 由于浏览器可能未安装，我们主要测试上下文处理
	_, err := manager.GetOrCreateBrowser(ctx)

	// 应该有错误（可能是浏览器未安装或上下文取消）
	assert.Error(t, err)
}

func TestGetChromePath_FileChecks(t *testing.T) {
	// 测试GetChromePath函数的文件检查逻辑

	// 创建临时文件来模拟chrome存在
	tempFile := "/tmp/test-chrome-browser"
	file, err := os.Create(tempFile)
	if err == nil {
		file.Close()
		defer os.Remove(tempFile)

		// 验证文件存在检查
		_, err := os.Lstat(tempFile)
		assert.NoError(t, err)
	}
}

func TestBrowserManager_GetOrCreateBrowser_ConcurrentCalls(t *testing.T) {
	// 使用 mock 避免实际启动 BrowserDriver
	patches := gomonkey.ApplyFunc(NewBrowserDriver, func() *BrowserDriver {
		return &BrowserDriver{
			ready: make(chan struct{}),
		}
	})
	defer patches.Reset()

	manager := NewBrowserManager()

	// 模拟浏览器未安装以避免实际启动
	manager.path = ""

	// 并发调用GetOrCreateBrowser
	done := make(chan bool, 3)

	for i := 0; i < 3; i++ {
		go func() {
			defer func() { done <- true }()

			ctx := context.Background()
			_, err := manager.GetOrCreateBrowser(ctx)

			// 应该都返回相同的错误（浏览器未安装）
			assert.Error(t, err)
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 3; i++ {
		<-done
	}
}

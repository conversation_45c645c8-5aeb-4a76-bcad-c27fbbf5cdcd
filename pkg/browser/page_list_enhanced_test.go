package browser

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestPageList_AddPage_RealHandler 测试AddPage方法的真实场景
func TestPageList_AddPage_RealHandler(t *testing.T) {
	pageList := NewPageList()

	// 创建测试用的PageHandler（不设置page字段避免类型问题）
	handler := &PageHandler{
		pageID:       "test-page-1",
		createTime:   time.Now(),
		consoleLogs:  make([]string, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     false,
		pageListener: nil,
	}

	// 测试AddPage方法
	pageList.AddPage(handler)

	// 验证页面已添加
	assert.Equal(t, 1, len(pageList.pages))
	assert.Equal(t, handler, pageList.pages["test-page-1"])
}

// TestPageList_AddPage_MultipleHandlers 测试添加多个页面
func TestPageList_AddPage_MultipleHandlers(t *testing.T) {
	pageList := NewPageList()

	// 添加多个页面
	handlers := make([]*PageHandler, 3)
	for i := 0; i < 3; i++ {
		handlers[i] = &PageHandler{
			pageID:       fmt.Sprintf("test-page-%d", i),
			createTime:   time.Now().Add(time.Duration(i) * time.Millisecond),
			consoleLogs:  make([]string, 0, maxLogs),
			mux:          &sync.Mutex{},
			isClosed:     false,
			pageListener: nil,
		}
		pageList.AddPage(handlers[i])
	}

	// 验证所有页面都已添加
	assert.Equal(t, 3, len(pageList.pages))
	for i := 0; i < 3; i++ {
		pageID := fmt.Sprintf("test-page-%d", i)
		assert.NotNil(t, pageList.pages[pageID])
		assert.Equal(t, pageID, pageList.pages[pageID].GetPageID())
	}
}

// TestPageList_RemovePage_ActivePageSwitch 测试移除活动页面时的切换逻辑
func TestPageList_RemovePage_ActivePageSwitch(t *testing.T) {
	pageList := NewPageList()

	// 创建两个页面，第二个页面创建时间更晚
	time1 := time.Now()
	time2 := time1.Add(time.Second)

	handler1 := &PageHandler{
		pageID:       "page-1",
		createTime:   time1,
		consoleLogs:  make([]string, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     false,
		pageListener: nil,
	}

	handler2 := &PageHandler{
		pageID:       "page-2",
		createTime:   time2,
		consoleLogs:  make([]string, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     false,
		pageListener: nil,
	}

	// 添加页面
	pageList.AddPage(handler1)
	pageList.AddPage(handler2)
	pageList.SetActivePage("page-1")

	// 移除活动页面，应该切换到page-2（更新的页面）
	pageList.RemovePage("page-1")

	assert.Equal(t, 1, len(pageList.pages))
	assert.Equal(t, "page-2", pageList.activePage)
	assert.Nil(t, pageList.pages["page-1"])
	assert.NotNil(t, pageList.pages["page-2"])
}

// TestPageList_getNextActivePageIDWithoutLock_Logic 测试选择下一个活动页面的逻辑
func TestPageList_getNextActivePageIDWithoutLock_Logic(t *testing.T) {
	pageList := NewPageList()

	// 创建三个页面，时间递增
	baseTime := time.Now()
	handlers := []*PageHandler{
		{
			pageID:     "page-1",
			createTime: baseTime,
			mux:        &sync.Mutex{},
		},
		{
			pageID:     "page-2",
			createTime: baseTime.Add(time.Second),
			mux:        &sync.Mutex{},
		},
		{
			pageID:     "page-3",
			createTime: baseTime.Add(2 * time.Second),
			mux:        &sync.Mutex{},
		},
	}

	// 确保createTime字段被正确设置
	for _, handler := range handlers {
		if handler.createTime.IsZero() {
			handler.createTime = time.Now()
		}
	}

	// 添加到内部map
	for _, handler := range handlers {
		pageList.pages[handler.pageID] = handler
	}

	// 测试选择最新的页面
	nextID := pageList.getNextActivePageIDWithoutLock("removed-page")
	// 由于map的遍历顺序不确定，我们只验证返回了一个有效的页面ID
	assert.Contains(t, []string{"page-1", "page-2", "page-3"}, nextID)
	assert.NotEmpty(t, nextID)
}

// TestPageList_CloseAll_RealHandlers 测试CloseAll方法
func TestPageList_CloseAll_RealHandlers(t *testing.T) {
	pageList := NewPageList()

	// 创建真实的PageHandler，但预先设置为已关闭状态避免调用playwright.Page.Close()
	handler1 := &PageHandler{
		pageID:       "page-1",
		createTime:   time.Now(),
		consoleLogs:  make([]string, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     true, // 预先设置为已关闭
		pageListener: nil,
	}

	handler2 := &PageHandler{
		pageID:       "page-2",
		createTime:   time.Now(),
		consoleLogs:  make([]string, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     true, // 预先设置为已关闭
		pageListener: nil,
	}

	// 添加页面
	pageList.AddPage(handler1)
	pageList.AddPage(handler2)

	// 验证页面已添加
	assert.Equal(t, 2, len(pageList.pages))

	// 测试CloseAll
	pageList.CloseAll()

	// 验证map被清空
	assert.Equal(t, 0, len(pageList.pages))

	// 验证页面保持关闭状态
	assert.True(t, handler1.IsClosed())
	assert.True(t, handler2.IsClosed())
}

// TestPageList_SetActivePage_EdgeCases 测试SetActivePage的边界情况
func TestPageList_SetActivePage_EdgeCases(t *testing.T) {
	pageList := NewPageList()

	// 测试设置空字符串
	result := pageList.SetActivePage("")
	assert.False(t, result)
	assert.Equal(t, "", pageList.activePage)

	// 测试设置不存在的页面
	result = pageList.SetActivePage("non-existent")
	assert.False(t, result)
	assert.Equal(t, "", pageList.activePage)

	// 添加一个页面
	handler := &PageHandler{
		pageID:      "test-page",
		createTime:  time.Now(),
		consoleLogs: make([]string, 0, maxLogs),
		mux:         &sync.Mutex{},
		isClosed:    false,
	}
	pageList.AddPage(handler)

	// 测试设置存在的页面
	result = pageList.SetActivePage("test-page")
	assert.True(t, result)
	assert.Equal(t, "test-page", pageList.activePage)

	// 再次设置相同页面
	result = pageList.SetActivePage("test-page")
	assert.True(t, result)
	assert.Equal(t, "test-page", pageList.activePage)
}

// TestPageList_GetPageByID_Coverage 测试GetPageByID方法的覆盖率
func TestPageList_GetPageByID_Coverage(t *testing.T) {
	pageList := NewPageList()

	// 测试获取不存在的页面
	page := pageList.GetPageByID("non-existent")
	assert.Nil(t, page)

	// 测试获取空字符串
	page = pageList.GetPageByID("")
	assert.Nil(t, page)

	// 添加一个页面
	handler := &PageHandler{
		pageID:      "test-page",
		createTime:  time.Now(),
		consoleLogs: make([]string, 0, maxLogs),
		mux:         &sync.Mutex{},
		isClosed:    false,
	}
	pageList.AddPage(handler)

	// 测试获取存在的页面
	page = pageList.GetPageByID("test-page")
	assert.NotNil(t, page)
	assert.Equal(t, "test-page", page.GetPageID())
}

// TestPageList_FindPageHandler_NilPage 测试FindPageHandler方法
func TestPageList_FindPageHandler_NilPage(t *testing.T) {
	pageList := NewPageList()

	// 测试查找nil页面
	found := pageList.FindPageHandler(nil)
	assert.Nil(t, found)

	// 添加一个页面（page字段为nil）
	handler := &PageHandler{
		page:        nil,
		pageID:      "test-page",
		createTime:  time.Now(),
		consoleLogs: make([]string, 0, maxLogs),
		mux:         &sync.Mutex{},
		isClosed:    false,
	}
	pageList.AddPage(handler)

	// 测试查找nil页面（应该找到page字段为nil的handler）
	found = pageList.FindPageHandler(nil)
	assert.NotNil(t, found)
	assert.Equal(t, "test-page", found.GetPageID())
}

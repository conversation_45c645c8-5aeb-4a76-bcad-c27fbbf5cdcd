package browser

import (
	"agent/pkg/errors"
	"context"
	"github.com/playwright-community/playwright-go"
	"sync"
)

type BrowserDriver struct {
	pw       *playwright.Playwright
	runError error
	ready    chan struct{}
	mu       sync.RWMutex
	once     sync.Once
}

func NewBrowserDriver() *BrowserDriver {
	driver := &BrowserDriver{
		ready: make(chan struct{}),
	}

	go driver.run()

	return driver
}

func (d *BrowserDriver) run() {
	pw, err := playwright.Run()

	d.mu.Lock()
	if err != nil {
		d.runError = errors.WithMessage(err, "could not run playwright")
	} else {
		d.pw = pw
	}
	d.mu.Unlock()

	close(d.ready)
}

// Close gracefully shuts down the browser and cleans up resources
func (d *BrowserDriver) Close() error {
	d.once.Do(func() {
		d.mu.RLock()
		pw := d.pw
		d.mu.RUnlock()

		if pw != nil {
			_ = pw.Stop()
		}
	})
	return nil
}

func (d *BrowserDriver) GetBrowserWithContext(ctx context.Context) (playwright.BrowserType, error) {
	select {
	case <-d.ready:
		// Initialization completed
	case <-ctx.Done():
		return nil, errors.WithMessage(ctx.Err(), "context canceled while waiting for playwright")
	}

	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.runError != nil {
		return nil, d.runError
	}

	if d.pw == nil {
		return nil, errors.New("playwright initialization failed with unknown error")
	}

	return d.pw.Chromium, nil
}

package browser

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestIsStaticResource 测试静态资源判断逻辑
func TestIsStaticResource(t *testing.T) {
	tests := []struct {
		name         string
		url          string
		headers      map[string]string
		resourceType string
		expected     bool
	}{
		// 通过资源类型判断
		{
			name:         "CSS resource type",
			url:          "https://example.com/styles.css",
			headers:      map[string]string{},
			resourceType: "stylesheet",
			expected:     true,
		},
		{
			name:         "JavaScript resource type",
			url:          "https://example.com/script.js",
			headers:      map[string]string{},
			resourceType: "script",
			expected:     true,
		},
		{
			name:         "Image resource type",
			url:          "https://example.com/image.png",
			headers:      map[string]string{},
			resourceType: "image",
			expected:     true,
		},
		{
			name:         "Font resource type",
			url:          "https://example.com/font.woff",
			headers:      map[string]string{},
			resourceType: "font",
			expected:     true,
		},
		{
			name:         "Media resource type",
			url:          "https://example.com/video.mp4",
			headers:      map[string]string{},
			resourceType: "media",
			expected:     true,
		},
		{
			name:         "Document resource type",
			url:          "https://example.com/page.html",
			headers:      map[string]string{},
			resourceType: "document",
			expected:     true,
		},
		{
			name:         "Manifest resource type",
			url:          "https://example.com/manifest.json",
			headers:      map[string]string{},
			resourceType: "manifest",
			expected:     true,
		},
		{
			name:         "XHR resource type should not be static",
			url:          "https://api.example.com/data",
			headers:      map[string]string{},
			resourceType: "xhr",
			expected:     false,
		},
		{
			name:         "Fetch resource type should not be static",
			url:          "https://api.example.com/data",
			headers:      map[string]string{},
			resourceType: "fetch",
			expected:     false,
		},

		// 通过文件扩展名判断
		{
			name:         "CSS file extension",
			url:          "https://example.com/style.css",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JavaScript file extension",
			url:          "https://example.com/app.js",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "PNG image file extension",
			url:          "https://example.com/logo.png",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JPG image file extension",
			url:          "https://example.com/photo.jpg",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JPEG image file extension",
			url:          "https://example.com/photo.jpeg",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "GIF image file extension",
			url:          "https://example.com/animation.gif",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "SVG image file extension",
			url:          "https://example.com/icon.svg",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "ICO file extension",
			url:          "https://example.com/favicon.ico",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "WOFF font file extension",
			url:          "https://example.com/font.woff",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "WOFF2 font file extension",
			url:          "https://example.com/font.woff2",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "TTF font file extension",
			url:          "https://example.com/font.ttf",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "EOT font file extension",
			url:          "https://example.com/font.eot",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "OTF font file extension",
			url:          "https://example.com/font.otf",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "MP4 video file extension",
			url:          "https://example.com/video.mp4",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "MP3 audio file extension",
			url:          "https://example.com/audio.mp3",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "PDF document file extension",
			url:          "https://example.com/document.pdf",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "ZIP archive file extension",
			url:          "https://example.com/archive.zip",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "XML file extension",
			url:          "https://example.com/config.xml",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JSON file extension",
			url:          "https://example.com/data.json",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "TXT file extension",
			url:          "https://example.com/readme.txt",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},

		// 通过Content-Type判断
		{
			name:         "CSS content type",
			url:          "https://example.com/styles",
			headers:      map[string]string{"content-type": "text/css"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JavaScript content type",
			url:          "https://example.com/script",
			headers:      map[string]string{"content-type": "application/javascript"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Text JavaScript content type",
			url:          "https://example.com/script",
			headers:      map[string]string{"content-type": "text/javascript"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "PNG image content type",
			url:          "https://example.com/image",
			headers:      map[string]string{"content-type": "image/png"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JPEG image content type",
			url:          "https://example.com/image",
			headers:      map[string]string{"content-type": "image/jpeg"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Font content type",
			url:          "https://example.com/font",
			headers:      map[string]string{"content-type": "font/woff"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Audio content type",
			url:          "https://example.com/audio",
			headers:      map[string]string{"content-type": "audio/mpeg"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Video content type",
			url:          "https://example.com/video",
			headers:      map[string]string{"content-type": "video/mp4"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Application font content type",
			url:          "https://example.com/font",
			headers:      map[string]string{"content-type": "application/font-woff"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Octet stream content type",
			url:          "https://example.com/binary",
			headers:      map[string]string{"content-type": "application/octet-stream"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JSON API content type with other resourceType is static",
			url:          "https://api.example.com/data",
			headers:      map[string]string{"content-type": "application/json"},
			resourceType: "other",
			expected:     true, // "other" 在静态资源类型列表中
		},
		{
			name:         "HTML content type with other resourceType is static",
			url:          "https://api.example.com/page",
			headers:      map[string]string{"content-type": "text/html"},
			resourceType: "other",
			expected:     true, // "other" 在静态资源类型列表中
		},

		// 通过路径判断
		{
			name:         "Static path - /static/",
			url:          "https://example.com/static/css/style.css",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Assets path - /assets/",
			url:          "https://example.com/assets/js/app.js",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Public path - /public/",
			url:          "https://example.com/public/images/logo.png",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "CSS path - /css/",
			url:          "https://example.com/css/bootstrap.css",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "JS path - /js/",
			url:          "https://example.com/js/jquery.js",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Images path - /images/",
			url:          "https://example.com/images/banner.jpg",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "IMG path - /img/",
			url:          "https://example.com/img/logo.png",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Fonts path - /fonts/",
			url:          "https://example.com/fonts/arial.woff",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},

		// 非静态资源测试
		{
			name:         "API endpoint without static indicators",
			url:          "https://api.example.com/users",
			headers:      map[string]string{"content-type": "application/json"},
			resourceType: "fetch",
			expected:     false,
		},
		{
			name:         "API endpoint with POST data",
			url:          "https://example.com/api/login",
			headers:      map[string]string{"content-type": "application/json"},
			resourceType: "xhr",
			expected:     false,
		},
		{
			name:         "Plain URL without extension or headers but other resourceType",
			url:          "https://example.com/page",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true, // "other" 在静态资源类型列表中
		},

		// 边界情况测试
		{
			name:         "Empty URL with other resourceType",
			url:          "",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true, // "other" 在静态资源类型列表中
		},
		{
			name:         "URL without extension but other resourceType",
			url:          "https://example.com/api/data",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true, // "other" 在静态资源类型列表中
		},
		{
			name:         "Mixed case extensions",
			url:          "https://example.com/IMAGE.PNG",
			headers:      map[string]string{},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Mixed case content type",
			url:          "https://example.com/style",
			headers:      map[string]string{"content-type": "TEXT/CSS"},
			resourceType: "other",
			expected:     true,
		},
		{
			name:         "Mixed case resource type",
			url:          "https://example.com/script.js",
			headers:      map[string]string{},
			resourceType: "SCRIPT",
			expected:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isStaticResource(tt.url, tt.headers, tt.resourceType)
			assert.Equal(t, tt.expected, result, "Expected %v for URL: %s, ResourceType: %s", tt.expected, tt.url, tt.resourceType)
		})
	}
}

// TestFilterImportantHeaders 测试重要请求头过滤逻辑
func TestFilterImportantHeaders(t *testing.T) {
	tests := []struct {
		name     string
		headers  map[string]string
		expected map[string]string
	}{
		{
			name: "Filter important headers",
			headers: map[string]string{
				"authorization":    "Bearer token",
				"cookie":           "session=abc123",
				"content-type":     "application/json",
				"accept":           "application/json",
				"referer":          "https://example.com",
				"x-requested-with": "XMLHttpRequest",
				"x-api-key":        "secret-key",
				"x-auth-token":     "auth123",
				"x-csrf-token":     "csrf456",
				"origin":           "https://example.com",
				"user-agent":       "Mozilla/5.0",
				"cache-control":    "no-cache",
				"x-custom-header":  "custom-value",
			},
			expected: map[string]string{
				"authorization":    "Bearer token",
				"cookie":           "session=abc123",
				"content-type":     "application/json",
				"accept":           "application/json",
				"referer":          "https://example.com",
				"x-requested-with": "XMLHttpRequest",
				"x-api-key":        "secret-key",
				"x-auth-token":     "auth123",
				"x-csrf-token":     "csrf456",
				"origin":           "https://example.com",
				"x-custom-header":  "custom-value",
			},
		},
		{
			name: "Filter out unimportant headers",
			headers: map[string]string{
				"user-agent":                "Mozilla/5.0",
				"cache-control":             "no-cache",
				"accept-encoding":           "gzip",
				"accept-language":           "en-US",
				"connection":                "keep-alive",
				"upgrade-insecure-requests": "1",
			},
			expected: map[string]string{},
		},
		{
			name: "Keep custom x- headers but filter out user-agent related",
			headers: map[string]string{
				"x-forwarded-for": "***********",  // 包含 forwarded，应该被过滤
				"x-user-agent":    "Custom Agent", // 包含 user-agent，应该被过滤
				"x-csrf-token":    "csrf123",      // 重要的 x- 头，应该保留
				"x-custom":        "value",        // 自定义 x- 头，应该保留
				"x-api-version":   "v1",           // 自定义 x- 头，应该保留
			},
			expected: map[string]string{
				"x-csrf-token":  "csrf123",
				"x-custom":      "value",
				"x-api-version": "v1",
			},
		},
		{
			name: "Case insensitive matching",
			headers: map[string]string{
				"Authorization": "Bearer token",
				"Content-Type":  "application/json",
				"X-API-Key":     "secret",
				"Cookie":        "session=test",
				"Accept":        "*/*",
				"Referer":       "https://example.com",
				"Origin":        "https://example.com",
			},
			expected: map[string]string{
				"Authorization": "Bearer token",
				"Content-Type":  "application/json",
				"X-API-Key":     "secret",
				"Cookie":        "session=test",
				"Accept":        "*/*",
				"Referer":       "https://example.com",
				"Origin":        "https://example.com",
			},
		},
		{
			name:     "Empty headers",
			headers:  map[string]string{},
			expected: map[string]string{},
		},
		{
			name: "Only x-headers with excluded patterns",
			headers: map[string]string{
				"x-user-agent-mobile": "true",        // 包含 user-agent，应该被过滤
				"x-forwarded-proto":   "https",       // 包含 forwarded，应该被过滤
				"x-forwarded-host":    "example.com", // 包含 forwarded，应该被过滤
			},
			expected: map[string]string{},
		},
		{
			name: "Mixed case x-headers",
			headers: map[string]string{
				"X-Custom-Header": "value1",
				"x-api-key":       "secret",
				"X-CSRF-TOKEN":    "token123",
			},
			expected: map[string]string{
				"X-Custom-Header": "value1",
				"x-api-key":       "secret",
				"X-CSRF-TOKEN":    "token123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filterImportantHeaders(tt.headers)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestHttpRequestModel_Structure 测试 HttpRequestModel 结构体
func TestHttpRequestModel_Structure(t *testing.T) {
	// 测试结构体字段的完整性
	model := &HttpRequestModel{
		Url:              "https://api.example.com/data",
		ResponseHeaders:  map[string]string{"content-type": "application/json"},
		RequestHandlers:  map[string]string{"authorization": "Bearer token"},
		HttpCode:         200,
		Method:           "GET",
		RequestBody:      `{"test": "data"}`,
		ResponseBody:     `{"result": "success"}`,
		Timestamp:        time.Now().UnixMilli(),
		ResourceType:     "xhr",
		IsStaticResource: false,
		RequestError:     "",
	}

	// 验证结构体字段
	assert.NotEmpty(t, model.Url)
	assert.NotEmpty(t, model.ResponseHeaders)
	assert.NotEmpty(t, model.RequestHandlers)
	assert.NotZero(t, model.HttpCode)
	assert.NotEmpty(t, model.Method)
	assert.NotEmpty(t, model.RequestBody)
	assert.NotEmpty(t, model.ResponseBody)
	assert.NotZero(t, model.Timestamp)
	assert.NotEmpty(t, model.ResourceType)
	assert.False(t, model.IsStaticResource)
	assert.Empty(t, model.RequestError)

	// 测试时间戳的合理性
	now := time.Now().UnixMilli()
	assert.True(t, model.Timestamp > 0)
	assert.True(t, now-model.Timestamp < 1000) // 应该在1秒内
}

// TestHttpRequestModel_EmptyFields 测试空字段情况
func TestHttpRequestModel_EmptyFields(t *testing.T) {
	model := &HttpRequestModel{}

	// 验证空字段的默认值
	assert.Empty(t, model.Url)
	assert.Nil(t, model.ResponseHeaders)
	assert.Nil(t, model.RequestHandlers)
	assert.Zero(t, model.HttpCode)
	assert.Empty(t, model.Method)
	assert.Empty(t, model.RequestBody)
	assert.Empty(t, model.ResponseBody)
	assert.Zero(t, model.Timestamp)
	assert.Empty(t, model.ResourceType)
	assert.False(t, model.IsStaticResource)
	assert.Empty(t, model.RequestError)
}

// TestHttpRequestModel_StaticResourceFlag 测试静态资源标志
func TestHttpRequestModel_StaticResourceFlag(t *testing.T) {
	tests := []struct {
		name           string
		url            string
		resourceType   string
		expectedStatic bool
	}{
		{
			name:           "XHR request should not be static",
			url:            "https://api.example.com/data",
			resourceType:   "xhr",
			expectedStatic: false,
		},
		{
			name:           "Fetch request should not be static",
			url:            "https://api.example.com/users",
			resourceType:   "fetch",
			expectedStatic: false,
		},
		{
			name:           "CSS file should be static",
			url:            "https://example.com/style.css",
			resourceType:   "stylesheet",
			expectedStatic: true,
		},
		{
			name:           "JS file should be static",
			url:            "https://example.com/app.js",
			resourceType:   "script",
			expectedStatic: true,
		},
		{
			name:           "Image file should be static",
			url:            "https://example.com/logo.png",
			resourceType:   "image",
			expectedStatic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &HttpRequestModel{
				Url:              tt.url,
				ResourceType:     tt.resourceType,
				IsStaticResource: isStaticResource(tt.url, map[string]string{}, tt.resourceType),
			}

			assert.Equal(t, tt.expectedStatic, model.IsStaticResource)
		})
	}
}

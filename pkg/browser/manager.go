package browser

import (
	"agent/consts"
	"agent/pkg/errors"
	"agent/utils/log"
	"context"
	"sync"
	"time"

	"github.com/playwright-community/playwright-go"
)

type BrowserManager struct {
	path           string
	driver         *BrowserDriver
	browserHandler *BrowserHandler
	mutex          *sync.Mutex
}

func NewBrowserManager() *BrowserManager {
	return &BrowserManager{
		path:           GetChromePath(),
		driver:         NewBrowserDriver(),
		browserHandler: nil,
		mutex:          &sync.Mutex{}}
}

func (m *BrowserManager) IsInstalled() bool {
	return m.path != ""
}

func (m *BrowserManager) GetOrCreateBrowser(ctx context.Context) (*BrowserHandler, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.browserHandler != nil && !m.browserHandler.IsClosed() {
		return m.browserHandler, nil
	}

	b, err := m.create(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "could not create browser")
	}

	m.browserHandler = b

	return b, nil
}

func (m *BrowserManager) create(ctx context.Context) (*BrowserHandler, error) {
	if !m.IsInstalled() {
		return nil, errors.ErrBrowserNotInstalled
	}

	chrome, err := m.driver.GetBrowserWithContext(ctx)
	if err != nil {
		return nil, errors.ErrCreatedBrowser.WithError(err)
	}

	opts := playwright.BrowserTypeConnectOverCDPOptions{
		Headers: nil,
		SlowMo:  nil,
		Timeout: playwright.Float(5000),
	}

	retryTimeout, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var browser playwright.Browser
	cdpUrl := "http://localhost:" + consts.ChromeDebugPort

	for {
		select {
		case <-ctx.Done():
			return nil, errors.ErrContextTimeout.WithError(ctx.Err())
		case <-retryTimeout.Done():
			return nil, errors.ErrConnectBrowserCDPTimeout.WithError(err)
		default:
			browser, err = chrome.ConnectOverCDP(cdpUrl, opts)
			if err == nil {
				log.Infof("connect browser successfully!")
				return NewBrowserHandler(browser)
			}

			log.Warnf("failed to connect to chrome debug port:%+v, we will reconnect", err)
			time.Sleep(1 * time.Second)
		}
	}
}

func (m *BrowserManager) Close() {
	if m.browserHandler != nil {
		m.browserHandler.Close()
	}

	if m.driver != nil {
		m.driver.Close()
	}
}

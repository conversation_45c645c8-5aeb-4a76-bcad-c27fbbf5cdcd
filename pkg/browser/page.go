package browser

import (
	"agent/pkg/errors"
	"agent/utils/log"
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/playwright-community/playwright-go"
)

type ImageType string

const (
	PngImage     ImageType = "png"
	JpegImage    ImageType = "jpeg"
	UnknownImage ImageType = ""
)

// 实现 String() 方法（可选，但推荐）
func (t ImageType) String() string {
	return string(t)
}

// 常量定义
const (
	// 页面可见性监听脚本
	visibilityScript = `
(() => {
  document.addEventListener('visibilitychange', () => {
    window.onVisibilityChange(document.visibilityState === 'visible');
  });
})();
`
)

type PageListener interface {
	OnClosePage(pageID string)
	OnActivePage(pageID string)
}

type PageHandler struct {
	page         playwright.Page
	pageID       string
	createTime   time.Time
	consoleLogs  []string
	requestList  []*HttpRequestModel
	mux          *sync.Mutex
	isClosed     bool
	pageListener PageListener
}

func NewPageHandler(page playwright.Page, pageListener PageListener) *PageHandler {
	id := strconv.FormatInt(time.Now().UnixMilli(), 10)

	handler := &PageHandler{
		page:         page,
		pageID:       id,
		createTime:   time.Now(),
		consoleLogs:  make([]string, 0, maxLogs),
		requestList:  make([]*HttpRequestModel, 0, maxLogs),
		mux:          &sync.Mutex{},
		isClosed:     false,
		pageListener: pageListener,
	}

	// 注册页面事件
	page.OnConsole(handler.onConsoleMessage)
	page.OnClose(handler.onClose)
	page.On("bringtofront", handler.onBringToFront)
	page.OnRequest(handler.onRequest)
	page.OnRequestFailed(handler.onRequestFailed)
	page.OnResponse(handler.onResponse)

	// 启动可见性监听
	go handler.setupVisibilityTracking()

	return handler
}

func (h *PageHandler) onConsoleMessage(msg playwright.ConsoleMessage) {
	h.mux.Lock()
	defer h.mux.Unlock()

	// 如果页面已关闭，不再处理新的日志
	if h.isClosed {
		return
	}

	if len(h.consoleLogs) >= maxLogs {
		// 移除最旧的日志
		h.consoleLogs = h.consoleLogs[1:]
	}

	h.consoleLogs = append(h.consoleLogs, msg.Text())
}

func (h *PageHandler) appendRequestLog(ctx *HttpRequestModel) {
	h.mux.Lock()
	defer h.mux.Unlock()

	if len(h.requestList) >= maxLogs {
		// 移除最旧的日志
		h.requestList = h.requestList[1:]
	}

	h.requestList = append(h.requestList, ctx)
}

func (h *PageHandler) onBringToFront() {
	log.Infof("Page %s brought to front", h.pageID)

	if h.pageListener != nil {
		h.pageListener.OnActivePage(h.pageID)
	}
}

func (h *PageHandler) onClose(_ playwright.Page) {
	log.Infof("Page %s close event received", h.pageID)

	h.mux.Lock()
	defer h.mux.Unlock()

	h.isClosed = true

	if h.pageListener != nil {
		h.pageListener.OnClosePage(h.pageID)
	}
}

func (h *PageHandler) onResponse(response playwright.Response) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Warnf("page %s onResponse panic error: %+v", h.pageID, err)
			}
		}()

		ctx := NewRequestModel(response)
		if ctx != nil {
			h.appendRequestLog(ctx)
		}
	}()
}

func (h *PageHandler) onRequestFailed(request playwright.Request) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Warnf("page %s onRequestFailed panic error: %+v", h.pageID, err)
			}
		}()

		ctx := NewRequestModelFromRequest(request)
		if ctx != nil {
			h.appendRequestLog(ctx)
		}
	}()
}

func (h *PageHandler) onRequest(request playwright.Request) {
}

func (h *PageHandler) Goto(ctx context.Context, url string) error {
	opt := playwright.PageGotoOptions{
		Referer:   nil,
		Timeout:   playwright.Float(10000),
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	}
	_, err := h.page.Goto(url, opt)
	if err != nil {
		return errors.ErrOpenPage.WithError(err)
	}
	return nil
}

func (h *PageHandler) Close() {
	h.mux.Lock()
	defer h.mux.Unlock()

	if h.isClosed {
		return
	}

	h.isClosed = true

	if err := h.page.Close(); err != nil {
		log.Errorf("Failed to close page %s: %v", h.pageID, err)
	} else {
		log.Infof("Page %s closed", h.pageID)
	}
}

func (h *PageHandler) Screenshot() (ImageType, []byte, error) {
	if h.IsClosed() {
		return UnknownImage, nil, errors.ErrPageClosed
	}

	data, err := h.page.Screenshot(playwright.PageScreenshotOptions{
		FullPage: playwright.Bool(true),
		Type:     playwright.ScreenshotTypeJpeg,
		Quality:  playwright.Int(70),
	})

	if err != nil {
		return UnknownImage, nil, errors.ErrBrowserScreenshot.WithError(err)
	}

	log.Debugf("Screenshot successful for page %s, size: %d bytes", h.pageID, len(data))
	return JpegImage, data, nil
}

func (h *PageHandler) GetLogs() []string {
	h.mux.Lock()
	defer h.mux.Unlock()

	if h.isClosed {
		log.Warnf("Attempted to get logs from closed page %s", h.pageID)
		return []string{}
	}

	return append([]string{}, h.consoleLogs...)
}

func (h *PageHandler) GetRequestList() []*HttpRequestModel {
	h.mux.Lock()
	defer h.mux.Unlock()

	if h.isClosed {
		log.Warnf("Attempted to get request logs from closed page %s", h.pageID)
		return []*HttpRequestModel{}
	}

	return append([]*HttpRequestModel{}, h.requestList...)
}

func (h *PageHandler) GetPageID() string {
	return h.pageID
}

func (h *PageHandler) GetPage() playwright.Page {
	return h.page
}

func (h *PageHandler) GetCreateTime() time.Time {
	return h.createTime
}

func (h *PageHandler) IsClosed() bool {
	h.mux.Lock()
	defer h.mux.Unlock()
	return h.isClosed
}

// setupVisibilityTracking 设置页面可见性追踪功能
func (h *PageHandler) setupVisibilityTracking() {
	err := h.page.ExposeFunction("onVisibilityChange", func(args ...interface{}) interface{} {
		isVisible := args[0].(bool)
		log.Infof("Page %s visibility changed: %v", h.pageID, isVisible)

		if isVisible && h.pageListener != nil {
			h.pageListener.OnActivePage(h.pageID)
		}

		return nil
	})

	if err != nil {
		log.Errorf("Failed to expose visibility change function for page %s: %v", h.pageID, err)
		return
	}

	_, err = h.page.AddScriptTag(playwright.PageAddScriptTagOptions{
		Content: playwright.String(visibilityScript),
	})

	if err != nil {
		log.Errorf("Failed to add visibility tracking script for page %s: %v", h.pageID, err)
	}
}

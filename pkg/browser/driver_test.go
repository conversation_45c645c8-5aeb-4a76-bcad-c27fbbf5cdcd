package browser

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewBrowserDriver(t *testing.T) {
	driver := NewBrowserDriver()

	assert.NotNil(t, driver)
	assert.NotNil(t, driver.ready)

	// 等待初始化完成
	select {
	case <-driver.ready:
		// 初始化完成
	case <-time.After(10 * time.Second):
		t.<PERSON>("Driver initialization timeout")
	}

	// 清理资源
	err := driver.Close()
	assert.NoError(t, err)
}

func TestBrowserDriver_GetBrowserWithContext(t *testing.T) {
	driver := NewBrowserDriver()
	defer driver.Close()

	ctx := context.Background()

	// 等待初始化完成
	select {
	case <-driver.ready:
		// 初始化完成
	case <-time.After(10 * time.Second):
		t.<PERSON><PERSON>("Driver initialization timeout")
	}

	browser, err := driver.GetBrowserWithContext(ctx)

	// 由于测试环境可能没有安装playwright，这里主要测试逻辑
	if err != nil {
		// 如果有错误，应该是playwright相关的错误
		assert.Contains(t, err.<PERSON>rror(), "playwright")
	} else {
		assert.NotNil(t, browser)
	}
}

func TestBrowserDriver_GetBrowserWithContext_Timeout(t *testing.T) {
	driver := NewBrowserDriver()
	defer driver.Close()

	// 创建一个已经取消的context
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	browser, err := driver.GetBrowserWithContext(ctx)

	assert.Error(t, err)
	assert.Nil(t, browser)
	assert.Contains(t, err.Error(), "context canceled")
}

func TestBrowserDriver_Close(t *testing.T) {
	driver := NewBrowserDriver()

	// 等待初始化完成
	select {
	case <-driver.ready:
		// 初始化完成
	case <-time.After(10 * time.Second):
		t.Fatal("Driver initialization timeout")
	}

	// 第一次关闭
	err := driver.Close()
	assert.NoError(t, err)

	// 第二次关闭应该也不会出错（幂等性）
	err = driver.Close()
	assert.NoError(t, err)
}

func TestBrowserDriver_GetBrowserWithContext_AfterClose(t *testing.T) {
	driver := NewBrowserDriver()

	// 等待初始化完成
	select {
	case <-driver.ready:
		// 初始化完成
	case <-time.After(10 * time.Second):
		t.Fatal("Driver initialization timeout")
	}

	// 关闭driver
	err := driver.Close()
	require.NoError(t, err)

	ctx := context.Background()
	browser, err := driver.GetBrowserWithContext(ctx)

	// 关闭后仍然可以获取browser（如果playwright已经初始化）
	// 但可能会有错误，这取决于playwright的状态
	if err != nil {
		assert.Contains(t, err.Error(), "playwright")
	}

	// browser可能为nil或非nil，取决于playwright的状态
	_ = browser
}

func TestBrowserDriver_ConcurrentAccess(t *testing.T) {
	driver := NewBrowserDriver()
	defer driver.Close()

	// 等待初始化完成
	select {
	case <-driver.ready:
		// 初始化完成
	case <-time.After(10 * time.Second):
		t.Fatal("Driver initialization timeout")
	}

	// 并发访问测试
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- true }()

			ctx := context.Background()
			_, err := driver.GetBrowserWithContext(ctx)

			// 不检查具体错误，因为可能是playwright相关的环境问题
			_ = err
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		select {
		case <-done:
			// 完成
		case <-time.After(5 * time.Second):
			t.Fatal("Concurrent access test timeout")
		}
	}
}

package xgin

import (
	"agent/pkg/errors"
	"bytes"
	"fmt"
	"net/http"
	"net/http/httptest"
	"runtime"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestLoggerWriter(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name   string
		method string
		path   string
		query  string
	}{
		{
			name:   "GET请求无查询参数",
			method: "GET",
			path:   "/test",
			query:  "",
		},
		{
			name:   "POST请求带查询参数",
			method: "POST",
			path:   "/api/users",
			query:  "page=1&size=10",
		},
		{
			name:   "PUT请求复杂路径",
			method: "PUT",
			path:   "/api/v1/users/123",
			query:  "force=true",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			
			url := tt.path
			if tt.query != "" {
				url += "?" + tt.query
			}
			
			c.Request = httptest.NewRequest(tt.method, url, bytes.NewBuffer(nil))

			// 模拟中间件链
			middleware := LoggerWriter()
			middleware(c)

			// 验证请求被正确处理
			assert.NotNil(t, c.Request, "请求不应该为空")
		})
	}
}

func TestLoggerWriterWithErrors(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", bytes.NewBuffer(nil))

	// 添加一些错误到上下文
	c.Error(fmt.Errorf("test error"))

	middleware := LoggerWriter()
	middleware(c)

	// 验证中间件正常执行
	assert.NotNil(t, c.Request, "请求不应该为空")
}

func TestSkipHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		skippers []SkipperFunc
		expected bool
	}{
		{
			name:     "无跳过函数",
			skippers: nil,
			expected: false,
		},
		{
			name: "单个跳过函数返回false",
			skippers: []SkipperFunc{
				func(c *gin.Context) bool { return false },
			},
			expected: false,
		},
		{
			name: "单个跳过函数返回true",
			skippers: []SkipperFunc{
				func(c *gin.Context) bool { return true },
			},
			expected: true,
		},
		{
			name: "多个跳过函数，第一个返回true",
			skippers: []SkipperFunc{
				func(c *gin.Context) bool { return true },
				func(c *gin.Context) bool { return false },
			},
			expected: true,
		},
		{
			name: "多个跳过函数，都返回false",
			skippers: []SkipperFunc{
				func(c *gin.Context) bool { return false },
				func(c *gin.Context) bool { return false },
			},
			expected: false,
		},
		{
			name: "多个跳过函数，最后一个返回true",
			skippers: []SkipperFunc{
				func(c *gin.Context) bool { return false },
				func(c *gin.Context) bool { return true },
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = httptest.NewRequest("GET", "/test", nil)

			result := SkipHandler(c, tt.skippers...)
			assert.Equal(t, tt.expected, result, "SkipHandler 返回值不符合预期")
		})
	}
}

func TestRecoveryWriter(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		panicValue  interface{}
		description string
	}{
		{
			name:        "runtime.Error panic",
			panicValue:  &runtime.TypeAssertionError{},
			description: "测试运行时错误的恢复",
		},
		{
			name:        "errors.CodeError panic - 500",
			panicValue:  errors.NewWithInfo(500, "internal server error"),
			description: "测试代码错误500的恢复",
		},
		{
			name:        "errors.CodeError panic - 501",
			panicValue:  errors.NewWithInfo(501, "admin account not set"),
			description: "测试代码错误501的恢复",
		},
		{
			name:        "errors.CodeError panic - 400",
			panicValue:  errors.NewWithInfo(400, "invalid token"),
			description: "测试代码错误400的恢复",
		},
		{
			name:        "errors.CodeError panic - 401",
			panicValue:  errors.NewWithInfo(401, "no permission"),
			description: "测试代码错误401的恢复",
		},
		{
			name:        "errors.CodeError panic - other",
			panicValue:  errors.NewWithInfo(403, "forbidden"),
			description: "测试其他代码错误的恢复",
		},
		{
			name:        "generic error panic",
			panicValue:  fmt.Errorf("generic error"),
			description: "测试通用错误的恢复",
		},
		{
			name:        "string panic",
			panicValue:  "string panic",
			description: "测试字符串panic的恢复",
		},
		{
			name:        "integer panic",
			panicValue:  123,
			description: "测试整数panic的恢复",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, engine := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test", bytes.NewBuffer(nil))

			// 使用完整的中间件链来测试recovery
			engine.Use(RecoveryWriter())
			engine.GET("/test", func(c *gin.Context) {
				panic(tt.panicValue)
			})

			// 执行请求，recovery中间件应该捕获panic
			assert.NotPanics(t, func() {
				engine.ServeHTTP(w, c.Request)
			}, "RecoveryWriter 应该捕获所有panic")

			// 验证响应状态码不是500（说明panic被正确处理）
			assert.NotEqual(t, http.StatusInternalServerError, w.Code, "panic应该被recovery中间件处理")
		})
	}
}

func TestRecoveryWriterNoPanic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", bytes.NewBuffer(nil))

	middleware := RecoveryWriter()

	// 正常执行，不应该panic
	assert.NotPanics(t, func() {
		middleware(c)
	}, "正常执行不应该panic")

	assert.False(t, c.IsAborted(), "正常执行时上下文不应该被中止")
}

func TestHandleNotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name   string
		method string
		path   string
	}{
		{
			name:   "GET请求404",
			method: "GET",
			path:   "/nonexistent",
		},
		{
			name:   "POST请求404",
			method: "POST",
			path:   "/api/nonexistent",
		},
		{
			name:   "PUT请求404",
			method: "PUT",
			path:   "/users/999",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(tt.method, tt.path, nil)

			HandleNotFound(c)

			assert.Equal(t, http.StatusNotFound, w.Code, "应该返回404状态码")

			expectedBody := fmt.Sprintf(`{"message":"404 page not found","request":"%s %s"}`, tt.method, tt.path)
			assert.JSONEq(t, expectedBody, w.Body.String(), "应该返回正确的JSON响应")
		})
	}
}

func TestStackFunction(t *testing.T) {
	// 测试 stack 函数
	stackTrace := stack(0)
	assert.NotEmpty(t, stackTrace, "stack trace 不应该为空")
	assert.Contains(t, string(stackTrace), "TestStackFunction", "stack trace 应该包含当前函数名")
}

func TestSourceFunction(t *testing.T) {
	// 测试 source 函数
	lines := [][]byte{
		[]byte("line 1"),
		[]byte("  line 2 with spaces  "),
		[]byte("line 3"),
	}

	tests := []struct {
		name     string
		lineNum  int
		expected string
	}{
		{
			name:     "第一行",
			lineNum:  1,
			expected: "line 1",
		},
		{
			name:     "带空格的行",
			lineNum:  2,
			expected: "line 2 with spaces",
		},
		{
			name:     "负数行号",
			lineNum:  -1,
			expected: "???",
		},
		{
			name:     "超出范围的行号",
			lineNum:  10,
			expected: "???",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := source(lines, tt.lineNum)
			assert.Equal(t, tt.expected, string(result), "source 函数返回值不符合预期")
		})
	}
}

func TestFunctionName(t *testing.T) {
	// 获取当前函数的 PC
	pc, _, _, ok := runtime.Caller(0)
	assert.True(t, ok, "应该能够获取调用者信息")

	// 测试 function 函数
	funcName := function(pc)
	assert.NotEmpty(t, funcName, "函数名不应该为空")
	assert.Contains(t, string(funcName), "TestFunctionName", "应该包含当前函数名")

	// 测试无效的 PC
	invalidFuncName := function(0)
	assert.Equal(t, "???", string(invalidFuncName), "无效PC应该返回 ???")
}

func TestAbortGinContextWithErrorCode(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)

	testError := errors.NewWithInfo(400, "test error")
	abortGinContextWithErrorCode(c, testError)

	assert.True(t, c.IsAborted(), "上下文应该被中止")
	assert.Equal(t, http.StatusOK, w.Code, "应该返回200状态码")
	assert.Contains(t, w.Body.String(), "test error", "响应体应该包含错误信息")
}

func TestAbortGinContextForbiddenWithErrorCode(t *testing.T) {
	gin.SetMode(gin.TestMode)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)

	testError := errors.NewWithInfo(403, "forbidden error")
	abortGinContextForbiddenWithErrorCode(c, testError)

	assert.True(t, c.IsAborted(), "上下文应该被中止")
	assert.Equal(t, http.StatusForbidden, w.Code, "应该返回403状态码")
	assert.Contains(t, w.Body.String(), "forbidden error", "响应体应该包含错误信息")
}

package xgin

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// 测试用的结构体
type TestStruct struct {
	Name  string `json:"name" binding:"required"`
	Email string `json:"email" binding:"required,email"`
	Age   int    `json:"age" binding:"min=0,max=120"`
}

type TestQueryStruct struct {
	Page int    `form:"page" binding:"min=1"`
	Size int    `form:"size" binding:"min=1,max=100"`
	Sort string `form:"sort"`
}

func initGin(url string, value interface{}) *gin.Engine {
	r := gin.Default()
	r.GET(url, func(c *gin.Context) {
		err := ContextBindWithValid(c, value)
		if err != nil {
			c.String(http.StatusBadRequest, err.Error())
		}
		c.String(http.StatusOK, "")
	})

	return r
}

func initGinQuery(url string, value interface{}) *gin.Engine {
	r := gin.Default()
	r.GET(url, func(c *gin.Context) {
		err := ContextBindQueryWithValid(c, value)
		if err != nil {
			c.String(http.StatusBadRequest, err.Error())
		}
		c.String(http.StatusOK, "")
	})

	return r
}

func TestContextBindWithValid(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 前置条件
	tests := []struct {
		name     string
		url      string
		value    interface{}
		wantCode int
		wantMsg  string
	}{
		{
			name:     "无效类型 - channel",
			url:      "/testFirstReturn",
			value:    make(chan int),
			wantCode: http.StatusBadRequest,
			wantMsg:  "validator: (nil chan int)",
		},
		{
			name: "有效结构体",
			url:  "/testSecondReturn",
			value: &struct {
				key   string
				value string
			}{},
			wantCode: http.StatusOK,
			wantMsg:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := gin.Default()
			r.GET(tt.url, func(c *gin.Context) {
				err := ContextBindWithValid(c, tt.value)
				if err != nil {
					c.String(http.StatusBadRequest, err.Error())
					return
				}
				c.String(http.StatusOK, "")
			})

			w := httptest.NewRecorder()
			req, _ := http.NewRequestWithContext(context.TODO(), "GET", tt.url, nil)
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.wantCode, w.Code)
			if tt.wantMsg != "" {
				assert.Contains(t, w.Body.String(), tt.wantMsg)
			} else {
				assert.Equal(t, tt.wantMsg, w.Body.String())
			}
		})
	}
}

func TestMustBindContext(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		body        string
		expectPanic bool
		description string
	}{
		{
			name:        "有效JSON - 不应该panic",
			body:        `{"name":"John","email":"<EMAIL>","age":25}`,
			expectPanic: false,
			description: "有效数据不应该引发panic",
		},
		{
			name:        "无效JSON - 应该panic",
			body:        `{"email":"<EMAIL>","age":25}`,
			expectPanic: true,
			description: "缺少必填字段应该引发panic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			req := httptest.NewRequest("POST", "/test", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			c.Request = req

			testStruct := &TestStruct{}

			if tt.expectPanic {
				assert.Panics(t, func() {
					MustBindContext(c, testStruct)
				}, "应该引发panic")
			} else {
				assert.NotPanics(t, func() {
					MustBindContext(c, testStruct)
				}, "不应该引发panic")
			}
		})
	}
}

func TestMustBindQuery(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		query       string
		expectPanic bool
		description string
	}{
		{
			name:        "有效查询参数 - 不应该panic",
			query:       "page=1&size=10&sort=name",
			expectPanic: false,
			description: "有效查询参数不应该引发panic",
		},
		{
			name:        "无效查询参数 - page为0",
			query:       "page=0&size=10",
			expectPanic: true,
			description: "page为0应该引发panic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			req := httptest.NewRequest("GET", "/test?"+tt.query, nil)
			c.Request = req

			testQuery := &TestQueryStruct{}

			if tt.expectPanic {
				assert.Panics(t, func() {
					MustBindQuery(c, testQuery)
				}, "应该引发panic")
			} else {
				assert.NotPanics(t, func() {
					MustBindQuery(c, testQuery)
				}, "不应该引发panic")
			}
		})
	}
}

func TestTelephoneValid(t *testing.T) {
	type args struct {
		phone string
	}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "有效手机号码",
			args: args{phone: "13531184972"},
			want: true,
		},
		{
			name: "另一个有效手机号码",
			args: args{phone: "18888888888"},
			want: true,
		},
		{
			name: "无效手机号码 - 太短",
			args: args{phone: "1111111"},
			want: false,
		},
		{
			name: "无效手机号码 - 不以1开头",
			args: args{phone: "23531184972"},
			want: false,
		},
		{
			name: "空字符串",
			args: args{phone: ""},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TelephoneValid(tt.args.phone); got != tt.want {
				t.Errorf("TelephoneValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPrintInterface(t *testing.T) {
	type args struct {
		ctx context.Context
		i   interface{}
	}

	tests := []struct {
		name string
		args args
	}{
		{
			name: "无法序列化的类型 - channel",
			args: args{
				ctx: context.Background(),
				i:   make(chan int),
			},
		},
		{
			name: "可序列化的整数",
			args: args{
				ctx: context.Background(),
				i:   1,
			},
		},
		{
			name: "可序列化的字符串",
			args: args{
				ctx: context.Background(),
				i:   "test string",
			},
		},
		{
			name: "nil值",
			args: args{
				ctx: context.Background(),
				i:   nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// PrintInterface 函数主要是打印日志，我们只需要确保它不会panic
			assert.NotPanics(t, func() {
				PrintInterface(tt.args.ctx, tt.args.i)
			}, "PrintInterface 不应该panic")
		})
	}
}

func TestContextBindQueryWithValid(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 前置条件
	tests := []struct {
		name     string
		url      string
		value    interface{}
		wantCode int
		wantMsg  string
	}{
		{
			name:     "无效类型 - channel",
			url:      "/testFirstReturn",
			value:    make(chan int),
			wantCode: http.StatusBadRequest,
			wantMsg:  "validator: (nil chan int)",
		},
		{
			name: "有效结构体 - 无验证",
			url:  "/testSecondReturn",
			value: &struct {
				key   string
				value string
			}{
				key:   "testKey",
				value: "testValue",
			},
			wantCode: http.StatusOK,
			wantMsg:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := gin.Default()
			r.GET(tt.url, func(c *gin.Context) {
				err := ContextBindQueryWithValid(c, tt.value)
				if err != nil {
					c.String(http.StatusBadRequest, err.Error())
					return
				}
				c.String(http.StatusOK, "")
			})

			w := httptest.NewRecorder()
			req, _ := http.NewRequestWithContext(context.TODO(), "GET", tt.url, nil)
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.wantCode, w.Code)
			if tt.wantMsg != "" {
				assert.Contains(t, w.Body.String(), tt.wantMsg)
			} else {
				assert.Equal(t, tt.wantMsg, w.Body.String())
			}
		})
	}
}

package xgin

import (
	"agent/pkg/errors"
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func initGinMetrics() (*gin.Engine, *httptest.ResponseRecorder) {
	r := New()

	// RecordMetric interface
	r.GET("/testRecordMetrics", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// 添加一个会触发错误的路由
	r.GET("/testError", func(c *gin.Context) {
		panic(errors.NewWithInfo(500, "test error"))
	})

	// 添加一个会触发不同错误码的路由
	r.GET("/testCustomError/:code", func(c *gin.Context) {
		code := c.Param("code")
		switch code {
		case "400":
			panic(errors.NewWithInfo(400, "bad request"))
		case "401":
			panic(errors.NewWithInfo(401, "unauthorized"))
		case "403":
			panic(errors.NewWithInfo(403, "forbidden"))
		case "500":
			panic(errors.NewWithInfo(500, "internal server error"))
		default:
			c.JSON(http.StatusOK, gin.H{"message": "ok"})
		}
	})

	w := httptest.NewRecorder()

	return r, w
}

func TestRecordMetrics(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		path     string
		method   string
		wantCode int
		wantMsg  string
	}{
		{
			name:     "成功请求记录指标",
			path:     "/testRecordMetrics",
			method:   "GET",
			wantCode: 200,
			wantMsg:  `{"message":"success"}`,
		},
		{
			name:     "自定义路径记录指标",
			path:     "/testCustomError/200",
			method:   "GET",
			wantCode: 200,
			wantMsg:  `{"message":"ok"}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r, w := initGinMetrics()
			req, _ := http.NewRequestWithContext(context.TODO(), tt.method, tt.path, nil)
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.wantCode, w.Code, "HTTP状态码应该匹配")
			if tt.wantMsg != "" {
				assert.JSONEq(t, tt.wantMsg, w.Body.String(), "响应体应该匹配")
			}
		})
	}
}

func TestRecordMetricsWithErrors(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name         string
		path         string
		method       string
		expectError  bool
		expectedCode int
		description  string
	}{
		{
			name:         "触发500错误",
			path:         "/testError",
			method:       "GET",
			expectError:  true,
			expectedCode: http.StatusOK,
			description:  "应该触发500错误并记录指标",
		},
		{
			name:         "触发400错误",
			path:         "/testCustomError/400",
			method:       "GET",
			expectError:  true,
			expectedCode: http.StatusOK,
			description:  "应该触发400错误并记录指标",
		},
		{
			name:         "触发401错误",
			path:         "/testCustomError/401",
			method:       "GET",
			expectError:  true,
			expectedCode: http.StatusForbidden, // 401错误会被转换为403状态码
			description:  "应该触发401错误并记录指标",
		},
		{
			name:         "触发403错误",
			path:         "/testCustomError/403",
			method:       "GET",
			expectError:  true,
			expectedCode: http.StatusOK,
			description:  "应该触发403错误并记录指标",
		},
		{
			name:         "触发500错误",
			path:         "/testCustomError/500",
			method:       "GET",
			expectError:  true,
			expectedCode: http.StatusOK,
			description:  "应该触发500错误并记录指标",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r, w := initGinMetrics()
			req, _ := http.NewRequestWithContext(context.TODO(), tt.method, tt.path, nil)

			// 执行请求，期望不会panic（因为有recovery中间件）
			assert.NotPanics(t, func() {
				r.ServeHTTP(w, req)
			}, "请求不应该panic")

			if tt.expectError {
				// 验证返回的状态码符合预期
				assert.Equal(t, tt.expectedCode, w.Code, "错误应该被recovery中间件正确处理")
			}
		})
	}
}

func TestTriggerErrorCode(t *testing.T) {
	tests := []struct {
		name        string
		path        string
		errorCode   errors.CodeError
		description string
	}{
		{
			name:        "触发400错误码",
			path:        "/api/test",
			errorCode:   errors.NewWithInfo(400, "bad request"),
			description: "测试400错误码的指标记录",
		},
		{
			name:        "触发401错误码",
			path:        "/api/auth",
			errorCode:   errors.NewWithInfo(401, "unauthorized"),
			description: "测试401错误码的指标记录",
		},
		{
			name:        "触发403错误码",
			path:        "/api/forbidden",
			errorCode:   errors.NewWithInfo(403, "forbidden"),
			description: "测试403错误码的指标记录",
		},
		{
			name:        "触发500错误码",
			path:        "/api/error",
			errorCode:   errors.NewWithInfo(500, "internal server error"),
			description: "测试500错误码的指标记录",
		},
		{
			name:        "触发自定义错误码",
			path:        "/api/custom",
			errorCode:   errors.NewWithInfo(422, "unprocessable entity"),
			description: "测试自定义错误码的指标记录",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 触发错误码
			assert.NotPanics(t, func() {
				TriggerErrorCode(tt.path, tt.errorCode)
			}, "TriggerErrorCode 不应该panic")
		})
	}
}

func TestTriggerErrorCodeMultipleTimes(t *testing.T) {
	path := "/test/multiple"
	errorCode := errors.NewWithInfo(500, "test error")

	// 多次触发同一个错误
	for i := 0; i < 5; i++ {
		assert.NotPanics(t, func() {
			TriggerErrorCode(path, errorCode)
		}, "多次触发不应该panic")
	}
}

func TestTriggerErrorCodeWithDifferentPaths(t *testing.T) {
	paths := []string{
		"/api/users",
		"/api/orders",
		"/api/products",
		"/api/auth",
	}

	errorCode := errors.NewWithInfo(404, "not found")

	// 对不同路径触发相同错误码
	for _, path := range paths {
		assert.NotPanics(t, func() {
			TriggerErrorCode(path, errorCode)
		}, "不同路径触发不应该panic")
	}
}

func TestMetricsInitialization(t *testing.T) {
	// 测试指标是否正确初始化
	assert.NotNil(t, metricServerRequestDurations, "请求持续时间指标应该被初始化")
	assert.NotNil(t, metricServerRequestCodeTotal, "请求错误码计数指标应该被初始化")
	assert.NotNil(t, metricServerRequestTimeoutTotal, "请求超时计数指标应该被初始化")
}

func TestMetricsLabels(t *testing.T) {
	// 测试指标标签的正确性
	testPath := "/test/labels"
	testCode := errors.NewWithInfo(418, "I'm a teapot")

	assert.NotPanics(t, func() {
		TriggerErrorCode(testPath, testCode)
	}, "带有特殊错误码的指标记录不应该panic")
}

// 基准测试
func BenchmarkTriggerErrorCode(b *testing.B) {
	path := "/benchmark/test"
	errorCode := errors.NewWithInfo(500, "benchmark error")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		TriggerErrorCode(path, errorCode)
	}
}

func BenchmarkTriggerErrorCodeDifferentPaths(b *testing.B) {
	paths := []string{
		"/api/v1/users",
		"/api/v1/orders",
		"/api/v1/products",
		"/api/v1/auth",
	}
	errorCode := errors.NewWithInfo(500, "benchmark error")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		path := paths[i%len(paths)]
		TriggerErrorCode(path, errorCode)
	}
}

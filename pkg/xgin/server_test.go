package xgin

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	// 设置 gin 为测试模式
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		middleware  []gin.HandlerFunc
		wantRoutes  bool
		description string
	}{
		{
			name:        "创建不带中间件的引擎",
			middleware:  nil,
			wantRoutes:  true,
			description: "测试创建基本的 gin 引擎",
		},
		{
			name: "创建带自定义中间件的引擎",
			middleware: []gin.HandlerFunc{
				func(c *gin.Context) {
					c.Header("X-Custom", "test")
					c.Next()
				},
			},
			wantRoutes:  true,
			description: "测试创建带自定义中间件的 gin 引擎",
		},
		{
			name: "创建带多个中间件的引擎",
			middleware: []gin.HandlerFunc{
				func(c *gin.Context) {
					c.Header("X-Custom-1", "test1")
					c.Next()
				},
				func(c *gin.Context) {
					c.Header("X-Custom-2", "test2")
					c.Next()
				},
			},
			wantRoutes:  true,
			description: "测试创建带多个自定义中间件的 gin 引擎",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建引擎
			engine := New(tt.middleware...)

			// 验证引擎不为空
			assert.NotNil(t, engine, "引擎不应该为空")

			// 验证引擎类型
			assert.IsType(t, &gin.Engine{}, engine, "应该返回 gin.Engine 类型")

			// 添加一个测试路由
			engine.GET("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "test"})
			})

			// 测试正常路由
			w := httptest.NewRecorder()
			req, _ := http.NewRequest("GET", "/test", nil)
			engine.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code, "正常路由应该返回 200")

			// 验证自定义中间件是否生效
			if len(tt.middleware) > 0 {
				for i := range tt.middleware {
					expectedHeader := "X-Custom"
					if len(tt.middleware) > 1 {
						expectedHeader = "X-Custom-" + string(rune('1'+i))
					}
					expectedValue := "test"
					if len(tt.middleware) > 1 {
						expectedValue = "test" + string(rune('1'+i))
					}
					assert.Equal(t, expectedValue, w.Header().Get(expectedHeader), 
						"自定义中间件应该设置正确的头部")
				}
			}
		})
	}
}

func TestNewWithNotFoundRoute(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	engine := New()

	// 测试不存在的路由 (NoRoute)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/nonexistent", nil)
	engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code, "不存在的路由应该返回 404")
	
	// 验证返回的 JSON 格式
	expectedBody := `{"message":"404 page not found","request":"GET /nonexistent"}`
	assert.JSONEq(t, expectedBody, w.Body.String(), "应该返回正确的 404 JSON 响应")
}

func TestNewWithNotAllowedMethod(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	engine := New()
	
	// 添加一个 GET 路由
	engine.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "test"})
	})

	// 测试不允许的方法 (NoMethod)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/test", nil)
	engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code, "不允许的方法应该返回 404")
	
	// 验证返回的 JSON 格式
	expectedBody := `{"message":"404 page not found","request":"POST /test"}`
	assert.JSONEq(t, expectedBody, w.Body.String(), "应该返回正确的 404 JSON 响应")
}

func TestNewMiddlewareOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	var executionOrder []string
	
	middleware1 := func(c *gin.Context) {
		executionOrder = append(executionOrder, "middleware1")
		c.Next()
	}
	
	middleware2 := func(c *gin.Context) {
		executionOrder = append(executionOrder, "middleware2")
		c.Next()
	}

	engine := New(middleware1, middleware2)
	
	engine.GET("/test", func(c *gin.Context) {
		executionOrder = append(executionOrder, "handler")
		c.JSON(http.StatusOK, gin.H{"message": "test"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	engine.ServeHTTP(w, req)

	// 验证中间件执行顺序：自定义中间件 -> LoggerWriter -> RecoveryWriter -> handler
	assert.Contains(t, executionOrder, "middleware1", "middleware1 应该被执行")
	assert.Contains(t, executionOrder, "middleware2", "middleware2 应该被执行")
	assert.Contains(t, executionOrder, "handler", "handler 应该被执行")
	
	// 验证自定义中间件在前面
	assert.Equal(t, "middleware1", executionOrder[0], "middleware1 应该首先执行")
	assert.Equal(t, "middleware2", executionOrder[1], "middleware2 应该第二个执行")
} 
package screenshot

import (
	"agent/pkg/errors"
	"bytes"
	"image/jpeg"
	"os"

	"github.com/kbinani/screenshot"
)

func Screenshot() ([]byte, error) {
	os.Setenv("DISPLAY", ":1")
	os.Setenv("XAUTHORITY", "/tmp/.Xauth")

	n := screenshot.NumActiveDisplays()
	if n <= 0 {
		return nil, errors.New("no active displays found")
	}

	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return nil, errors.WithMessage(err, "screenshot error")
	}

	// 将图片编码为JPEG格式，质量设置为75
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: 75})
	if err != nil {
		return nil, errors.WithMessage(err, "jpeg encode error")
	}

	return buf.Bytes(), nil
}

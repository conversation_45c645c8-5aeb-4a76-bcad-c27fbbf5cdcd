package screenshot

import (
	"bytes"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 模拟创建一个简单的图像用于测试JPEG编码逻辑
func createTestImage(width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 创建一个简单的渐变图像
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r := uint8((x * 255) / width)
			g := uint8((y * 255) / height)
			b := uint8(128)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return img
}

func TestJPEGEncoding(t *testing.T) {
	// 测试JPEG编码逻辑（这是Screenshot函数中的一部分）

	// 创建测试图像
	img := createTestImage(100, 100)

	// 模拟Screenshot函数中的JPEG编码部分
	var buf bytes.Buffer
	err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 75})

	// 验证编码成功
	assert.NoError(t, err)
	assert.Greater(t, buf.Len(), 0, "编码后的数据不应为空")

	// 验证JPEG文件头
	data := buf.Bytes()
	if len(data) >= 2 {
		assert.Equal(t, byte(0xFF), data[0], "JPEG文件应该以0xFF开头")
		assert.Equal(t, byte(0xD8), data[1], "JPEG文件的第二个字节应该是0xD8")
	}

	// 验证可以解码回图像
	reader := bytes.NewReader(data)
	decodedImg, err := jpeg.Decode(reader)
	assert.NoError(t, err)
	assert.NotNil(t, decodedImg)

	// 验证图像尺寸
	bounds := decodedImg.Bounds()
	assert.Equal(t, 100, bounds.Dx(), "解码后的图像宽度应该正确")
	assert.Equal(t, 100, bounds.Dy(), "解码后的图像高度应该正确")
}

func TestJPEGQualitySettings(t *testing.T) {
	// 测试不同的JPEG质量设置

	img := createTestImage(50, 50)

	qualities := []int{10, 25, 50, 75, 90, 100}

	for _, quality := range qualities {
		t.Run(fmt.Sprintf("Quality_%d", quality), func(t *testing.T) {
			var buf bytes.Buffer
			err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})

			assert.NoError(t, err)
			assert.Greater(t, buf.Len(), 0)

			// 验证JPEG文件头
			data := buf.Bytes()
			if len(data) >= 2 {
				assert.Equal(t, byte(0xFF), data[0])
				assert.Equal(t, byte(0xD8), data[1])
			}

			t.Logf("质量 %d: 数据大小 %d bytes", quality, buf.Len())
		})
	}
}

func TestImageFormats(t *testing.T) {
	// 测试不同尺寸的图像编码

	testCases := []struct {
		name   string
		width  int
		height int
	}{
		{"小图像", 10, 10},
		{"中等图像", 100, 100},
		{"宽图像", 200, 50},
		{"高图像", 50, 200},
		{"大图像", 500, 500},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			img := createTestImage(tc.width, tc.height)

			var buf bytes.Buffer
			err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 75})

			assert.NoError(t, err)
			assert.Greater(t, buf.Len(), 0)

			// 验证可以解码
			reader := bytes.NewReader(buf.Bytes())
			decodedImg, err := jpeg.Decode(reader)
			assert.NoError(t, err)

			bounds := decodedImg.Bounds()
			assert.Equal(t, tc.width, bounds.Dx())
			assert.Equal(t, tc.height, bounds.Dy())

			t.Logf("%s (%dx%d): 数据大小 %d bytes", tc.name, tc.width, tc.height, buf.Len())
		})
	}
}

func TestErrorHandlingInEncoding(t *testing.T) {
	// 测试编码过程中的错误处理

	// 测试传递nil图像会导致panic
	var buf bytes.Buffer

	// 使用defer recover来捕获panic
	defer func() {
		if r := recover(); r != nil {
			t.Logf("预期的panic被捕获: %v", r)
			// 验证缓冲区为空
			assert.Equal(t, 0, buf.Len(), "panic时缓冲区应该为空")
		} else {
			t.Error("期望发生panic，但没有发生")
		}
	}()

	// 这应该会导致panic
	err := jpeg.Encode(&buf, nil, &jpeg.Options{Quality: 75})

	// 如果没有panic，检查是否返回了错误
	if err != nil {
		assert.Error(t, err)
		assert.Equal(t, 0, buf.Len(), "编码失败时缓冲区应该为空")
	}
}

// 基准测试：测试JPEG编码性能
func BenchmarkJPEGEncoding(b *testing.B) {
	img := createTestImage(100, 100)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 75})
		if err != nil {
			b.Fatalf("JPEG编码失败: %v", err)
		}
	}
}

// 基准测试：测试不同质量设置的性能
func BenchmarkJPEGEncodingQuality75(b *testing.B) {
	img := createTestImage(200, 200)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 75})
		if err != nil {
			b.Fatalf("JPEG编码失败: %v", err)
		}
	}
}

func BenchmarkJPEGEncodingQuality100(b *testing.B) {
	img := createTestImage(200, 200)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 100})
		if err != nil {
			b.Fatalf("JPEG编码失败: %v", err)
		}
	}
}

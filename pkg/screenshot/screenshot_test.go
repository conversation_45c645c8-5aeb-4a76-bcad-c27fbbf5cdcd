package screenshot

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestScreenshot_Success(t *testing.T) {
	// 测试成功截图的情况
	// 注意：这个测试在有显示器的环境中才能通过

	// 设置环境变量
	originalDisplay := os.Getenv("DISPLAY")
	originalXAuth := os.Getenv("XAUTHORITY")

	// 清理函数，恢复原始环境变量
	defer func() {
		if originalDisplay != "" {
			os.Setenv("DISPLAY", originalDisplay)
		} else {
			os.Unsetenv("DISPLAY")
		}
		if originalXAuth != "" {
			os.Setenv("XAUTHORITY", originalXAuth)
		} else {
			os.Unsetenv("XAUTHORITY")
		}
	}()

	// 调用截图函数
	data, err := Screenshot()

	// 在有显示器的环境中，应该成功返回数据
	if err == nil {
		assert.NotNil(t, data)
		assert.Greater(t, len(data), 0, "截图数据不应为空")

		// 验证返回的是JPEG格式的数据
		// JPEG文件的前两个字节应该是0xFF, 0xD8
		if len(data) >= 2 {
			assert.Equal(t, byte(0xFF), data[0], "JPEG文件应该以0xFF开头")
			assert.Equal(t, byte(0xD8), data[1], "JPEG文件的第二个字节应该是0xD8")
		}
	} else {
		// 在没有显示器的环境中，应该返回特定的错误
		t.Logf("截图失败（可能是因为没有显示器）: %v", err)
	}
}

func TestScreenshot_EnvironmentVariables(t *testing.T) {
	// 测试环境变量设置

	// 保存原始环境变量
	originalDisplay := os.Getenv("DISPLAY")
	originalXAuth := os.Getenv("XAUTHORITY")

	// 清理函数
	defer func() {
		if originalDisplay != "" {
			os.Setenv("DISPLAY", originalDisplay)
		} else {
			os.Unsetenv("DISPLAY")
		}
		if originalXAuth != "" {
			os.Setenv("XAUTHORITY", originalXAuth)
		} else {
			os.Unsetenv("XAUTHORITY")
		}
	}()

	// 清除环境变量
	os.Unsetenv("DISPLAY")
	os.Unsetenv("XAUTHORITY")

	// 调用截图函数
	_, err := Screenshot()

	// 验证环境变量被正确设置
	assert.Equal(t, ":1", os.Getenv("DISPLAY"))
	assert.Equal(t, "/tmp/.Xauth", os.Getenv("XAUTHORITY"))

	// 错误可能是因为没有显示器，这是正常的
	if err != nil {
		t.Logf("截图失败（预期的，因为测试环境可能没有显示器）: %v", err)
	}
}

func TestScreenshot_NoActiveDisplays(t *testing.T) {
	// 测试没有活动显示器的情况
	// 这个测试主要验证错误处理逻辑

	// 保存原始环境变量
	originalDisplay := os.Getenv("DISPLAY")
	originalXAuth := os.Getenv("XAUTHORITY")

	// 清理函数
	defer func() {
		if originalDisplay != "" {
			os.Setenv("DISPLAY", originalDisplay)
		} else {
			os.Unsetenv("DISPLAY")
		}
		if originalXAuth != "" {
			os.Setenv("XAUTHORITY", originalXAuth)
		} else {
			os.Unsetenv("XAUTHORITY")
		}
	}()

	// 设置一个无效的DISPLAY环境变量
	os.Setenv("DISPLAY", ":999")
	os.Setenv("XAUTHORITY", "/tmp/.Xauth")

	// 调用截图函数
	data, err := Screenshot()

	// 应该返回错误
	if err != nil {
		assert.Nil(t, data)
		t.Logf("预期的错误: %v", err)
	} else {
		// 如果意外成功了，至少验证数据不为空
		assert.NotNil(t, data)
		assert.Greater(t, len(data), 0)
	}
}

func TestScreenshot_ErrorHandling(t *testing.T) {
	// 测试错误处理的逻辑

	tests := []struct {
		name        string
		display     string
		xauthority  string
		expectError bool
	}{
		{
			name:        "有效的显示器设置",
			display:     ":1",
			xauthority:  "/tmp/.Xauth",
			expectError: false, // 可能成功也可能失败，取决于环境
		},
		{
			name:        "无效的显示器设置",
			display:     ":999",
			xauthority:  "/tmp/.Xauth",
			expectError: true,
		},
		{
			name:        "空的显示器设置",
			display:     "",
			xauthority:  "/tmp/.Xauth",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 保存原始环境变量
			originalDisplay := os.Getenv("DISPLAY")
			originalXAuth := os.Getenv("XAUTHORITY")

			// 清理函数
			defer func() {
				if originalDisplay != "" {
					os.Setenv("DISPLAY", originalDisplay)
				} else {
					os.Unsetenv("DISPLAY")
				}
				if originalXAuth != "" {
					os.Setenv("XAUTHORITY", originalXAuth)
				} else {
					os.Unsetenv("XAUTHORITY")
				}
			}()

			// 设置测试环境变量
			if tt.display != "" {
				os.Setenv("DISPLAY", tt.display)
			} else {
				os.Unsetenv("DISPLAY")
			}
			os.Setenv("XAUTHORITY", tt.xauthority)

			// 调用截图函数
			data, err := Screenshot()

			if tt.expectError {
				// 期望有错误
				if err == nil {
					t.Logf("意外成功，但在某些环境中这可能是正常的")
				} else {
					assert.Error(t, err)
					assert.Nil(t, data)
				}
			} else {
				// 不一定期望成功，因为取决于环境
				if err != nil {
					t.Logf("截图失败（可能是环境原因）: %v", err)
				} else {
					assert.NotNil(t, data)
					assert.Greater(t, len(data), 0)
				}
			}
		})
	}
}

func TestScreenshot_JPEGQuality(t *testing.T) {
	// 测试JPEG质量设置
	// 这个测试主要验证编码逻辑

	// 调用截图函数
	data, err := Screenshot()

	if err == nil && data != nil {
		// 验证JPEG格式
		assert.Greater(t, len(data), 10, "JPEG数据应该有合理的大小")

		// 验证JPEG文件头
		if len(data) >= 2 {
			assert.Equal(t, byte(0xFF), data[0], "JPEG文件应该以0xFF开头")
			assert.Equal(t, byte(0xD8), data[1], "JPEG文件的第二个字节应该是0xD8")
		}

		// 验证JPEG文件尾（可选，因为有些JPEG可能没有标准结尾）
		if len(data) >= 2 {
			// JPEG文件通常以0xFF 0xD9结尾，但这不是强制的
			t.Logf("JPEG数据大小: %d bytes", len(data))
		}
	} else {
		t.Logf("截图失败（可能是环境原因）: %v", err)
	}
}

func TestScreenshot_Integration(t *testing.T) {
	// 集成测试：测试完整的截图流程

	// 保存原始环境变量
	originalDisplay := os.Getenv("DISPLAY")
	originalXAuth := os.Getenv("XAUTHORITY")

	// 清理函数
	defer func() {
		if originalDisplay != "" {
			os.Setenv("DISPLAY", originalDisplay)
		} else {
			os.Unsetenv("DISPLAY")
		}
		if originalXAuth != "" {
			os.Setenv("XAUTHORITY", originalXAuth)
		} else {
			os.Unsetenv("XAUTHORITY")
		}
	}()

	// 测试多次调用截图函数
	for i := 0; i < 3; i++ {
		data, err := Screenshot()

		if err == nil {
			assert.NotNil(t, data)
			assert.Greater(t, len(data), 0)
			t.Logf("第%d次截图成功，数据大小: %d bytes", i+1, len(data))
		} else {
			t.Logf("第%d次截图失败: %v", i+1, err)
		}

		// 验证环境变量在每次调用后都被正确设置
		assert.Equal(t, ":1", os.Getenv("DISPLAY"))
		assert.Equal(t, "/tmp/.Xauth", os.Getenv("XAUTHORITY"))
	}
}

// 基准测试
func BenchmarkScreenshot(b *testing.B) {
	// 跳过基准测试如果没有显示器
	_, err := Screenshot()
	if err != nil {
		b.Skipf("跳过基准测试，因为截图失败: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := Screenshot()
		if err != nil {
			b.Fatalf("基准测试中截图失败: %v", err)
		}
	}
}

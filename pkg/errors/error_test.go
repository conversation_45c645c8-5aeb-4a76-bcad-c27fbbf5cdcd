package errors

import (
	"net/http"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSvrError_Error(t *testing.T) {
	type fields struct {
		code int
		info string
	}

	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			"empty_error",
			fields{
				http.StatusOK,
				"",
			},
			"",
		},
		{
			"normal_error",
			fields{
				http.StatusInternalServerError,
				"internal server error",
			},
			"internal server error",
		},
		{
			"unicode_error",
			fields{
				http.StatusBadRequest,
				"参数错误",
			},
			"参数错误",
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.name, func(t *testing.T) {
			e := &SvrError{
				code: tt.fields.code,
				info: tt.fields.info,
			}
			if got := e.Error(); got != tt.want {
				t.Errorf("SvrError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSvrError_Code(t *testing.T) {
	type fields struct {
		code int
		info string
	}

	tests := []struct {
		name   string
		fields fields
		want   int
	}{
		{
			"status_ok",
			fields{
				http.StatusOK,
				"",
			},
			http.StatusOK,
		},
		{
			"status_internal_error",
			fields{
				http.StatusInternalServerError,
				"internal error",
			},
			http.StatusInternalServerError,
		},
		{
			"custom_code",
			fields{
				9999,
				"custom error",
			},
			9999,
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.name, func(t *testing.T) {
			e := &SvrError{
				code: tt.fields.code,
				info: tt.fields.info,
			}
			if got := e.Code(); got != tt.want {
				t.Errorf("SvrError.Code() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewWithCode(t *testing.T) {
	type args struct {
		code int
		err  error
	}

	tests := []struct {
		name string
		args args
		want CodeError
	}{
		{
			"with_code_error",
			args{
				http.StatusOK,
				&SvrError{code: 400, info: "bad request"},
			},
			&SvrError{code: 400, info: "bad request"},
		},
		{
			"with_normal_error",
			args{
				http.StatusOK,
				New("test second return"),
			},
			&SvrError{
				http.StatusOK,
				"test second return",
			},
		},
		{
			"with_wrapped_code_error",
			args{
				500,
				WithMessage(&SvrError{code: 400, info: "original"}, "wrapped"),
			},
			&SvrError{code: 400, info: "original"},
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.name, func(t *testing.T) {
			got := NewWithCode(tt.args.code, tt.args.err)
			assert.Equal(t, tt.want.Code(), got.Code())
			if tt.name == "with_wrapped_code_error" {
				// 对于包装的错误，只检查code，因为error message会包含包装信息
				assert.Contains(t, got.Error(), tt.want.Error())
			} else {
				assert.Equal(t, tt.want.Error(), got.Error())
			}
		})
	}
}

func TestNewWithInfo(t *testing.T) {
	type args struct {
		code int
		info string
	}

	tests := []struct {
		name string
		args args
		want CodeError
	}{
		{
			"empty_info",
			args{code: 200, info: ""},
			&SvrError{code: 200, info: ""},
		},
		{
			"normal_info",
			args{code: 500, info: "internal error"},
			&SvrError{code: 500, info: "internal error"},
		},
		{
			"unicode_info",
			args{code: 400, info: "参数错误"},
			&SvrError{code: 400, info: "参数错误"},
		},
	}

	for _, tt := range tests {
		tt := tt

		t.Run(tt.name, func(t *testing.T) {
			if got := NewWithInfo(tt.args.code, tt.args.info); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewWithInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCodeErrorInterface(t *testing.T) {
	// 测试CodeError接口的实现
	var err CodeError = NewWithInfo(500, "test error")
	
	assert.Equal(t, 500, err.Code())
	assert.Equal(t, "test error", err.Error())
	
	// 测试error接口
	var stdErr error = err
	assert.Equal(t, "test error", stdErr.Error())
}

func TestInternalError(t *testing.T) {
	assert.Equal(t, InternalErrorCode, InternalError.Code())
	assert.Equal(t, "internal error", InternalError.Error())
}

func TestConstants(t *testing.T) {
	assert.Equal(t, 500, InternalErrorCode)
	assert.Equal(t, 408, RequestTimeout)
}

func TestPkgErrorsWrappers(t *testing.T) {
	// 测试从pkg/errors包装的函数
	originalErr := New("original error")
	assert.NotNil(t, originalErr)
	assert.Equal(t, "original error", originalErr.Error())
	
	wrappedErr := Wrap(originalErr, "wrapped")
	assert.NotNil(t, wrappedErr)
	assert.Contains(t, wrappedErr.Error(), "original error")
	assert.Contains(t, wrappedErr.Error(), "wrapped")
	
	formattedErr := Wrapf(originalErr, "wrapped with %s", "format")
	assert.NotNil(t, formattedErr)
	assert.Contains(t, formattedErr.Error(), "original error")
	assert.Contains(t, formattedErr.Error(), "wrapped with format")
	
	erroredErr := Errorf("formatted error: %s", "test")
	assert.NotNil(t, erroredErr)
	assert.Equal(t, "formatted error: test", erroredErr.Error())
	
	stackErr := WithStack(originalErr)
	assert.NotNil(t, stackErr)
	
	messageErr := WithMessage(originalErr, "additional message")
	assert.NotNil(t, messageErr)
	
	messagefErr := WithMessagef(originalErr, "additional %s", "message")
	assert.NotNil(t, messagefErr)
	
	// 测试Cause
	cause := Cause(wrappedErr)
	assert.Equal(t, originalErr, cause)
	
	// 测试Is
	assert.True(t, Is(wrappedErr, originalErr))
	
	// 测试As
	var codeErr CodeError
	svrErr := NewWithInfo(500, "server error")
	wrappedSvrErr := WithMessage(svrErr, "wrapped server error")
	assert.True(t, As(wrappedSvrErr, &codeErr))
	assert.Equal(t, 500, codeErr.Code())
}

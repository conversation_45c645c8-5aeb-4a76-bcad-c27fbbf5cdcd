package errors

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewErrors(t *testing.T) {
	t.Run("basic_functionality", func(t *testing.T) {
		errs := NewErrors(New("err1"))
		errs.Add(New("err2"))
		str := errs.Error()
		assert.Equal(t, str, "err1, err2")

		err := errs.Err()
		assert.Error(t, err)
	})

	t.Run("empty_errors", func(t *testing.T) {
		errs := NewErrors()
		assert.Nil(t, errs.Err())
		assert.Equal(t, "", errs.Error())
	})

	t.Run("nil_errors_filtered", func(t *testing.T) {
		errs := NewErrors(New("err1"), nil, New("err2"), nil)
		assert.Equal(t, "err1, err2", errs.<PERSON>rror())
		assert.NotNil(t, errs.Err())
	})

	t.Run("single_error", func(t *testing.T) {
		errs := NewErrors(New("single error"))
		assert.Equal(t, "single error", errs.Error())
		assert.NotNil(t, errs.Err())
	})

	t.Run("add_nil_error", func(t *testing.T) {
		errs := NewErrors()
		errs.Add(nil)
		assert.Nil(t, errs.Err())
		assert.Equal(t, "", errs.Error())
	})

	t.Run("add_multiple_errors", func(t *testing.T) {
		errs := NewErrors()
		errs.Add(New("first"))
		errs.Add(New("second"))
		errs.Add(New("third"))
		
		expected := "first, second, third"
		assert.Equal(t, expected, errs.Error())
		assert.NotNil(t, errs.Err())
	})

	t.Run("mixed_error_types", func(t *testing.T) {
		errs := NewErrors()
		errs.Add(New("standard error"))
		errs.Add(NewWithInfo(500, "code error"))
		errs.Add(Errorf("formatted error: %d", 123))
		
		errorStr := errs.Error()
		assert.Contains(t, errorStr, "standard error")
		assert.Contains(t, errorStr, "code error")
		assert.Contains(t, errorStr, "formatted error: 123")
	})
}

func TestNewSafeErrors(t *testing.T) {
	t.Run("basic_functionality", func(t *testing.T) {
		errs := NewSafeErrors(New("err1"))
		errs.Add(New("err2"))
		str := errs.Error()
		assert.Equal(t, str, "err1, err2")

		err := errs.Err()
		assert.Error(t, err)
	})

	t.Run("empty_safe_errors", func(t *testing.T) {
		errs := NewSafeErrors()
		assert.Nil(t, errs.Err())
		assert.Equal(t, "", errs.Error())
	})

	t.Run("nil_errors_filtered", func(t *testing.T) {
		errs := NewSafeErrors(New("err1"), nil, New("err2"), nil)
		assert.Equal(t, "err1, err2", errs.Error())
		assert.NotNil(t, errs.Err())
	})

	t.Run("concurrent_safety", func(t *testing.T) {
		errs := NewSafeErrors()
		
		var wg sync.WaitGroup
		numGoroutines := 100
		errorsPerGoroutine := 10
		
		// 并发添加错误
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < errorsPerGoroutine; j++ {
					errs.Add(Errorf("error from goroutine %d, iteration %d", id, j))
				}
			}(i)
		}
		
		// 并发读取错误
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				_ = errs.Error()
				_ = errs.Err()
			}()
		}
		
		wg.Wait()
		
		// 验证所有错误都被添加
		finalErr := errs.Err()
		assert.NotNil(t, finalErr)
		
		errorStr := errs.Error()
		// 应该包含所有goroutine的错误
		for i := 0; i < numGoroutines; i++ {
			assert.Contains(t, errorStr, Errorf("error from goroutine %d", i).Error()[:20]) // 检查前缀
		}
	})

	t.Run("add_nil_error", func(t *testing.T) {
		errs := NewSafeErrors()
		errs.Add(nil)
		assert.Nil(t, errs.Err())
		assert.Equal(t, "", errs.Error())
	})

	t.Run("thread_safety_with_nil", func(t *testing.T) {
		errs := NewSafeErrors()
		
		var wg sync.WaitGroup
		
		// 一些goroutine添加nil错误
		for i := 0; i < 50; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				errs.Add(nil)
			}()
		}
		
		// 一些goroutine添加真实错误
		for i := 0; i < 50; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				errs.Add(Errorf("real error %d", id))
			}(i)
		}
		
		wg.Wait()
		
		// 应该只包含真实错误
		errorStr := errs.Error()
		assert.NotEqual(t, "", errorStr)
		assert.Contains(t, errorStr, "real error")
	})
}

func TestErrorsStructure(t *testing.T) {
	t.Run("errors_implements_error", func(t *testing.T) {
		errs := NewErrors(New("test"))
		var _ error = errs
		var _ error = errs.Err()
	})

	t.Run("safe_errors_implements_error", func(t *testing.T) {
		errs := NewSafeErrors(New("test"))
		var _ error = errs
		var _ error = errs.Err()
	})

	t.Run("errors_returns_self_when_has_errors", func(t *testing.T) {
		errs := NewErrors(New("test"))
		assert.Equal(t, errs, errs.Err())
	})

	t.Run("safe_errors_returns_underlying_when_has_errors", func(t *testing.T) {
		errs := NewSafeErrors(New("test"))
		assert.Equal(t, errs.errs, errs.Err())
	})
}

func TestErrorsEdgeCases(t *testing.T) {
	t.Run("very_long_error_message", func(t *testing.T) {
		errs := NewErrors()
		longMsg := string(make([]byte, 10000)) // 10KB的错误消息
		for i := range longMsg {
			longMsg = longMsg[:i] + "a" + longMsg[i+1:]
		}
		errs.Add(New(longMsg))
		
		assert.Equal(t, longMsg, errs.Error())
	})

	t.Run("many_small_errors", func(t *testing.T) {
		errs := NewErrors()
		numErrors := 1000
		
		for i := 0; i < numErrors; i++ {
			errs.Add(Errorf("error %d", i))
		}
		
		errorStr := errs.Error()
		assert.Contains(t, errorStr, "error 0")
		assert.Contains(t, errorStr, "error 999")
		
		// 检查分隔符数量
		commaCount := 0
		for _, char := range errorStr {
			if char == ',' {
				commaCount++
			}
		}
		assert.Equal(t, numErrors-1, commaCount) // n个错误应该有n-1个逗号
	})
}

package errors

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPredefinedErrors(t *testing.T) {
	tests := []struct {
		name     string
		err      CodeError
		wantCode int
		wantMsg  string
	}{
		{
			name:     "ErrInternalServer",
			err:      ErrInternalServer,
			wantCode: 500,
			wantMsg:  "Internal server error, please try again later",
		},
		{
			name:     "ErrAdminAccountNotSet",
			err:      ErrAdminAccountNotSet,
			wantCode: 501,
			wantMsg:  "Admin account not set, please contact operations team",
		},
		{
			name:     "ErrInvalidToken",
			err:      ErrInvalidToken,
			wantCode: 400,
			wantMsg:  "Current session is invalid, please login first",
		},
		{
			name:     "ErrNoPermission",
			err:      ErrNoPermission,
			wantCode: 401,
			wantMsg:  "No access permission",
		},
		{
			name:     "ErrRequestTimeout",
			err:      ErrRequestTimeout,
			wantCode: 408,
			wantMsg:  "Request timeout",
		},
		{
			name:     "ErrArgument",
			err:      ErrArgument,
			wantCode: 409,
			wantMsg:  "Invalid argument",
		},
		{
			name:     "ErrInvalidPlayground",
			err:      ErrInvalidPlayground,
			wantCode: 410,
			wantMsg:  "Invalid playground ID",
		},
		{
			name:     "ErrBrowserNotInstalled",
			err:      ErrBrowserNotInstalled,
			wantCode: 411,
			wantMsg:  "Browser not installed",
		},
		{
			name:     "ErrCurrentPageEmpty",
			err:      ErrCurrentPageEmpty,
			wantCode: 412,
			wantMsg:  "Browser not open any page",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.wantCode, tt.err.Code())
			assert.Equal(t, tt.wantMsg, tt.err.Error())
		})
	}
}

func TestPredefinedErrorsImplementCodeError(t *testing.T) {
	// 测试所有预定义错误都实现了CodeError接口
	var _ CodeError = ErrInternalServer
	var _ CodeError = ErrAdminAccountNotSet
	var _ CodeError = ErrInvalidToken
	var _ CodeError = ErrNoPermission
	var _ CodeError = ErrRequestTimeout
	var _ CodeError = ErrArgument
	var _ CodeError = ErrInvalidPlayground
	var _ CodeError = ErrBrowserNotInstalled
	var _ CodeError = ErrCurrentPageEmpty
}

package errors

var (
	ErrInternalServer           = NewWithInfo(500, "Internal server error, please try again later")
	ErrAdminAccountNotSet       = NewWithInfo(501, "Admin account not set, please contact operations team")
	ErrInvalidToken             = NewWithInfo(400, "Current session is invalid, please login first")
	ErrNoPermission             = NewWithInfo(401, "No access permission")
	ErrContextTimeout           = NewWithInfo(402, "Context timeout")
	ErrRequestTimeout           = NewWithInfo(408, "Request timeout")
	ErrArgument                 = NewWithInfo(409, "Invalid argument")
	ErrInvalidPlayground        = NewWithInfo(410, "Invalid playground ID")
	ErrBrowserNotInstalled      = NewWithInfo(411, "Browser not installed")
	ErrCurrentPageEmpty         = NewWithInfo(412, "Browser not open any page")
	ErrOpenPage                 = NewWithInfo(413, "Failed to open page")
	ErrBrowserScreenshot        = NewWithInfo(414, "Failed to take screenshot")
	ErrPageClosed               = NewWithInfo(415, "Page is closed")
	ErrCreatedBrowser           = NewWithInfo(416, "Failed to create browser")
	ErrConnectBrowserCDPTimeout = NewWithInfo(417, "Connect browser CDP timeout")
	ErrCreatedBrowserContext    = NewWithInfo(418, "Failed to create browser context")
	ErrCreatedPage              = NewWithInfo(419, "Failed to create page")
)

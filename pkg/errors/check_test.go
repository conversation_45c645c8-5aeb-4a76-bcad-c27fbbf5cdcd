package errors

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEqualCodeError(t *testing.T) {
	t.Run("basic_functionality", func(t *testing.T) {
		var err error

		ce1 := NewWithInfo(6001, "code error 1")
		ce2 := NewWithInfo(6002, "code error 2")

		err = New("native err")
		assert.False(t, EqualCodeError(err, ce1))

		err = WithMessage(ce1, "with error message")
		err = WithStack(err)

		assert.True(t, EqualCodeError(err, ce1))
		assert.False(t, EqualCodeError(err, ce2))

		assert.False(t, EqualCodeError(nil, ce1))
	})

	t.Run("same_code_different_message", func(t *testing.T) {
		ce1 := NewWithInfo(500, "message 1")
		ce2 := NewWithInfo(500, "message 2")
		
		assert.True(t, EqualCodeError(ce1, ce2))
		assert.True(t, EqualCodeError(ce2, ce1))
	})

	t.Run("wrapped_code_errors", func(t *testing.T) {
		original := NewWithInfo(404, "not found")
		wrapped := WithMessage(original, "additional context")
		doubleWrapped := Wrap(wrapped, "more context")
		
		assert.True(t, EqualCodeError(wrapped, original))
		assert.True(t, EqualCodeError(doubleWrapped, original))
	})

	t.Run("nil_first_error", func(t *testing.T) {
		ce := NewWithInfo(500, "test")
		
		assert.False(t, EqualCodeError(nil, ce))
	})

	t.Run("predefined_errors", func(t *testing.T) {
		assert.True(t, EqualCodeError(ErrInternalServer, NewWithInfo(500, "different message")))
		assert.False(t, EqualCodeError(ErrInvalidToken, ErrNoPermission))
	})

	t.Run("nil_code_error_interface", func(t *testing.T) {
		// 测试当第二个参数为nil接口值时的情况
		var nilCodeError CodeError = nil
		assert.False(t, EqualCodeError(New("test"), nilCodeError))
	})
}

func add(a int, b int) (int, error) {
	if b == 0 {
		return 0, New("b == 0")
	}

	return a + b, nil
}

func TestIgnoreFunctions(t *testing.T) {
	t.Run("ignore", func(t *testing.T) {
		// 这些函数应该不会panic，即使传入错误
		assert.NotPanics(t, func() {
			Ignore(New("test error"))
			Ignore(nil)
		})
	})

	t.Run("ignore1", func(t *testing.T) {
		assert.NotPanics(t, func() {
			Ignore1(add(1, 0))
			Ignore1(add(1, 1))
			Ignore1(42, New("test error"))
			Ignore1("result", nil)
		})
	})

	t.Run("ignore2", func(t *testing.T) {
		assert.NotPanics(t, func() {
			Ignore2(1, 0, nil)
			Ignore2("a", "b", New("error"))
			Ignore2(nil, nil, nil)
		})
	})

	t.Run("ignore3", func(t *testing.T) {
		assert.NotPanics(t, func() {
			Ignore3(1, 1, 0, nil)
			Ignore3("a", "b", "c", New("error"))
			Ignore3(nil, nil, nil, nil)
		})
	})
}

func TestUnreachable(t *testing.T) {
	assert.Panics(t, Unreachable)
	
	// 测试panic的消息
	assert.PanicsWithValue(t, "unreachable!", Unreachable)
}

func TestAssert(t *testing.T) {
	t.Run("assert_true", func(t *testing.T) {
		assert.NotPanics(t, func() {
			Assert(true)
			Assert(1 == 1)
			Assert("hello" != "")
		})
	})

	t.Run("assert_false", func(t *testing.T) {
		assert.Panics(t, func() {
			Assert(false)
		})

		assert.Panics(t, func() {
			Assert(1 == 2)
		})

		assert.Panics(t, func() {
			Assert("" == "hello")
		})
	})

	t.Run("assert_panic_message", func(t *testing.T) {
		assert.PanicsWithValue(t, "unreachable!", func() {
			Assert(false)
		})
	})
}

func TestUnimplemented(t *testing.T) {
	assert.Panics(t, Unimplemented)
	
	// 测试panic的消息
	assert.PanicsWithValue(t, "unimplemented", Unimplemented)
}

func TestCheck(t *testing.T) {
	t.Run("check_nil_error", func(t *testing.T) {
		assert.NotPanics(t, func() {
			Check(nil)
			Check(nil, "additional message")
		})
	})

	t.Run("check_normal_error", func(t *testing.T) {
		assert.Panics(t, func() {
			Check(New("panic"))
		})

		assert.Panics(t, func() {
			Check(New("test error"), "additional context")
		})
	})

	t.Run("check_code_error", func(t *testing.T) {
		assert.Panics(t, func() {
			Check(NewWithInfo(500, "unauth"), "!auth")
		})
	})

	t.Run("check_with_multiple_messages", func(t *testing.T) {
		assert.Panics(t, func() {
			Check(New("base error"), "msg1", "msg2", 123)
		})
	})

	t.Run("check_panic_type", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				codeErr, ok := r.(CodeError)
				assert.True(t, ok)
				assert.Equal(t, InternalErrorCode, codeErr.Code())
			}
		}()
		Check(New("test"))
	})
}

func TestThrow(t *testing.T) {
	t.Run("throw_normal_error", func(t *testing.T) {
		assert.Panics(t, func() {
			Throw(New("fsf"))
		})
	})

	t.Run("throw_code_error", func(t *testing.T) {
		assert.Panics(t, func() {
			Throw(NewWithInfo(404, "not found"))
		})
	})

	t.Run("throw_with_message", func(t *testing.T) {
		assert.Panics(t, func() {
			Throw(New("base"), "additional", "context")
		})
	})

	t.Run("throw_nil_error_panics", func(t *testing.T) {
		// Throw应该在传入nil时panic，因为它断言err != nil
		assert.Panics(t, func() {
			Throw(nil)
		})
	})

	t.Run("throw_panic_type", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				codeErr, ok := r.(CodeError)
				assert.True(t, ok)
				assert.Equal(t, 404, codeErr.Code())
				assert.Contains(t, codeErr.Error(), "not found")
			}
		}()
		Throw(NewWithInfo(404, "not found"))
	})
}

func TestThrowFunction(t *testing.T) {
	t.Run("throw_preserves_code", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				codeErr, ok := r.(CodeError)
				assert.True(t, ok)
				assert.Equal(t, 403, codeErr.Code())
			}
		}()
		
		originalErr := NewWithInfo(403, "forbidden")
		Throw(originalErr)
	})

	t.Run("throw_adds_internal_code_to_normal_error", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				codeErr, ok := r.(CodeError)
				assert.True(t, ok)
				assert.Equal(t, InternalErrorCode, codeErr.Code())
			}
		}()
		
		Throw(New("normal error"))
	})

	t.Run("throw_with_formatted_message", func(t *testing.T) {
		defer func() {
			if r := recover(); r != nil {
				codeErr, ok := r.(CodeError)
				assert.True(t, ok)
				assert.Contains(t, codeErr.Error(), "base error")
				assert.Contains(t, codeErr.Error(), "[context1 context2 123]")
			}
		}()
		
		Throw(New("base error"), "context1", "context2", 123)
	})
}

func TestPanicRecovery(t *testing.T) {
	t.Run("recover_from_check", func(t *testing.T) {
		var recoveredErr CodeError
		
		func() {
			defer func() {
				if r := recover(); r != nil {
					var ok bool
					recoveredErr, ok = r.(CodeError)
					assert.True(t, ok)
				}
			}()
			
			Check(NewWithInfo(400, "bad request"), "validation failed")
		}()
		
		assert.NotNil(t, recoveredErr)
		assert.Equal(t, 400, recoveredErr.Code())
		assert.Contains(t, recoveredErr.Error(), "bad request")
		assert.Contains(t, recoveredErr.Error(), "[validation failed]")
	})

	t.Run("recover_from_throw", func(t *testing.T) {
		var recoveredErr CodeError
		
		func() {
			defer func() {
				if r := recover(); r != nil {
					var ok bool
					recoveredErr, ok = r.(CodeError)
					assert.True(t, ok)
				}
			}()
			
			Throw(ErrInvalidToken, "session expired")
		}()
		
		assert.NotNil(t, recoveredErr)
		assert.Equal(t, 400, recoveredErr.Code())
		assert.Contains(t, recoveredErr.Error(), "Current session is invalid")
		assert.Contains(t, recoveredErr.Error(), "[session expired]")
	})
}

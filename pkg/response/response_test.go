package response

import (
	"agent/pkg/errors"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestConstants(t *testing.T) {
	t.Run("success_code", func(t *testing.T) {
		assert.Equal(t, 200, SuccessCode)
	})

	t.Run("success_message", func(t *testing.T) {
		assert.Equal(t, "Succeed", SuccessMsg)
	})
}

func TestNew(t *testing.T) {
	tests := []struct {
		name     string
		data     interface{}
		expected *Response
	}{
		{
			name: "nil_data",
			data: nil,
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        nil,
			},
		},
		{
			name: "string_data",
			data: "test data",
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        "test data",
			},
		},
		{
			name: "map_data",
			data: map[string]interface{}{"key": "value", "number": 123},
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        map[string]interface{}{"key": "value", "number": 123},
			},
		},
		{
			name: "slice_data",
			data: []string{"item1", "item2", "item3"},
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        []string{"item1", "item2", "item3"},
			},
		},
		{
			name: "struct_data",
			data: struct {
				Name string `json:"name"`
				Age  int    `json:"age"`
			}{Name: "John", Age: 30},
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data: struct {
					Name string `json:"name"`
					Age  int    `json:"age"`
				}{Name: "John", Age: 30},
			},
		},
		{
			name: "number_data",
			data: 42,
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        42,
			},
		},
		{
			name: "boolean_data",
			data: true,
			expected: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := New(tt.data)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFail(t *testing.T) {
	tests := []struct {
		name     string
		code     int
		desc     string
		expected *Response
	}{
		{
			name: "basic_fail",
			code: 400,
			desc: "Bad Request",
			expected: &Response{
				ErrorCode:   400,
				Description: "Bad Request",
				Data:        nil,
			},
		},
		{
			name: "internal_error",
			code: 500,
			desc: "Internal Server Error",
			expected: &Response{
				ErrorCode:   500,
				Description: "Internal Server Error",
				Data:        nil,
			},
		},
		{
			name: "custom_error",
			code: 1001,
			desc: "Custom Error Message",
			expected: &Response{
				ErrorCode:   1001,
				Description: "Custom Error Message",
				Data:        nil,
			},
		},
		{
			name: "empty_description",
			code: 404,
			desc: "",
			expected: &Response{
				ErrorCode:   404,
				Description: "",
				Data:        nil,
			},
		},
		{
			name: "unicode_description",
			code: 422,
			desc: "参数验证失败",
			expected: &Response{
				ErrorCode:   422,
				Description: "参数验证失败",
				Data:        nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Fail(tt.code, tt.desc)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestErr(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected *Response
	}{
		{
			name: "nil_error",
			err:  nil,
			expected: &Response{
				ErrorCode:   errors.InternalErrorCode,
				Description: "Empty error message!",
				Data:        nil,
			},
		},
		{
			name: "code_error",
			err:  errors.NewWithInfo(400, "Bad Request"),
			expected: &Response{
				ErrorCode:   400,
				Description: "Bad Request",
				Data:        nil,
			},
		},
		{
			name: "normal_error",
			err:  errors.New("standard error"),
			expected: &Response{
				ErrorCode:   errors.InternalErrorCode,
				Description: "standard error",
				Data:        nil,
			},
		},
		{
			name: "wrapped_code_error",
			err:  errors.WithMessage(errors.NewWithInfo(404, "Not Found"), "additional context"),
			expected: &Response{
				ErrorCode:   404,
				Description: "Not Found",
				Data:        nil,
			},
		},
		{
			name: "predefined_error",
			err:  errors.ErrInvalidToken,
			expected: &Response{
				ErrorCode:   400,
				Description: "Current session is invalid, please login first",
				Data:        nil,
			},
		},
		{
			name: "internal_server_error",
			err:  errors.ErrInternalServer,
			expected: &Response{
				ErrorCode:   500,
				Description: "Internal server error, please try again later",
				Data:        nil,
			},
		},
		{
			name: "unicode_error",
			err:  errors.NewWithInfo(422, "参数验证失败"),
			expected: &Response{
				ErrorCode:   422,
				Description: "参数验证失败",
				Data:        nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Err(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestResponse_GetError(t *testing.T) {
	tests := []struct {
		name     string
		response *Response
		expected error
	}{
		{
			name: "success_response",
			response: &Response{
				ErrorCode:   SuccessCode,
				Description: SuccessMsg,
				Data:        "some data",
			},
			expected: nil,
		},
		{
			name: "error_response",
			response: &Response{
				ErrorCode:   400,
				Description: "Bad Request",
				Data:        nil,
			},
			expected: errors.NewWithInfo(400, "Bad Request"),
		},
		{
			name: "internal_error_response",
			response: &Response{
				ErrorCode:   500,
				Description: "Internal Server Error",
				Data:        nil,
			},
			expected: errors.NewWithInfo(500, "Internal Server Error"),
		},
		{
			name: "custom_error_response",
			response: &Response{
				ErrorCode:   1001,
				Description: "Custom Error",
				Data:        nil,
			},
			expected: errors.NewWithInfo(1001, "Custom Error"),
		},
		{
			name: "unicode_error_response",
			response: &Response{
				ErrorCode:   422,
				Description: "参数验证失败",
				Data:        nil,
			},
			expected: errors.NewWithInfo(422, "参数验证失败"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.response.GetError()
			
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				
				// 检查是否为 CodeError
				var codeErr errors.CodeError
				assert.True(t, errors.As(result, &codeErr))
				
				// 检查错误码和消息
				expectedCodeErr := tt.expected.(errors.CodeError)
				assert.Equal(t, expectedCodeErr.Code(), codeErr.Code())
				assert.Equal(t, expectedCodeErr.Error(), codeErr.Error())
			}
		})
	}
}

func TestResponseJSONSerialization(t *testing.T) {
	tests := []struct {
		name     string
		response *Response
		expected string
	}{
		{
			name: "success_with_data",
			response: &Response{
				ErrorCode:   200,
				Description: "Succeed",
				Data:        map[string]interface{}{"key": "value"},
			},
			expected: `{"code":200,"msg":"Succeed","data":{"key":"value"}}`,
		},
		{
			name: "success_without_data",
			response: &Response{
				ErrorCode:   200,
				Description: "Succeed",
				Data:        nil,
			},
			expected: `{"code":200,"msg":"Succeed"}`,
		},
		{
			name: "error_response",
			response: &Response{
				ErrorCode:   400,
				Description: "Bad Request",
				Data:        nil,
			},
			expected: `{"code":400,"msg":"Bad Request"}`,
		},
		{
			name: "response_with_array_data",
			response: &Response{
				ErrorCode:   200,
				Description: "Succeed",
				Data:        []string{"item1", "item2"},
			},
			expected: `{"code":200,"msg":"Succeed","data":["item1","item2"]}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBytes, err := json.Marshal(tt.response)
			assert.NoError(t, err)
			assert.JSONEq(t, tt.expected, string(jsonBytes))
		})
	}
}

func TestResponseJSONDeserialization(t *testing.T) {
	tests := []struct {
		name     string
		jsonStr  string
		expected *Response
	}{
		{
			name:    "success_with_data",
			jsonStr: `{"code":200,"msg":"Succeed","data":{"key":"value"}}`,
			expected: &Response{
				ErrorCode:   200,
				Description: "Succeed",
				Data:        map[string]interface{}{"key": "value"},
			},
		},
		{
			name:    "success_without_data",
			jsonStr: `{"code":200,"msg":"Succeed"}`,
			expected: &Response{
				ErrorCode:   200,
				Description: "Succeed",
				Data:        nil,
			},
		},
		{
			name:    "error_response",
			jsonStr: `{"code":400,"msg":"Bad Request"}`,
			expected: &Response{
				ErrorCode:   400,
				Description: "Bad Request",
				Data:        nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var response Response
			err := json.Unmarshal([]byte(tt.jsonStr), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected.ErrorCode, response.ErrorCode)
			assert.Equal(t, tt.expected.Description, response.Description)
			assert.Equal(t, tt.expected.Data, response.Data)
		})
	}
}

func TestResponseIntegration(t *testing.T) {
	t.Run("new_to_get_error", func(t *testing.T) {
		// 创建成功响应
		resp := New("test data")
		err := resp.GetError()
		assert.Nil(t, err)
	})

	t.Run("fail_to_get_error", func(t *testing.T) {
		// 创建失败响应
		resp := Fail(400, "Bad Request")
		err := resp.GetError()
		assert.NotNil(t, err)
		
		var codeErr errors.CodeError
		assert.True(t, errors.As(err, &codeErr))
		assert.Equal(t, 400, codeErr.Code())
		assert.Equal(t, "Bad Request", codeErr.Error())
	})

	t.Run("err_to_get_error", func(t *testing.T) {
		// 从错误创建响应
		originalErr := errors.NewWithInfo(500, "Internal Error")
		resp := Err(originalErr)
		err := resp.GetError()
		assert.NotNil(t, err)
		
		var codeErr errors.CodeError
		assert.True(t, errors.As(err, &codeErr))
		assert.Equal(t, 500, codeErr.Code())
		assert.Equal(t, "Internal Error", codeErr.Error())
	})

	t.Run("round_trip_json", func(t *testing.T) {
		// 创建响应，序列化，再反序列化
		original := New(map[string]interface{}{
			"name": "test",
			"age":  25,
		})
		
		// 序列化
		jsonBytes, err := json.Marshal(original)
		assert.NoError(t, err)
		
		// 反序列化
		var deserialized Response
		err = json.Unmarshal(jsonBytes, &deserialized)
		assert.NoError(t, err)
		
		// 验证基本字段
		assert.Equal(t, original.ErrorCode, deserialized.ErrorCode)
		assert.Equal(t, original.Description, deserialized.Description)
		
		// 验证数据字段（考虑JSON反序列化的类型转换）
		originalData := original.Data.(map[string]interface{})
		deserializedData := deserialized.Data.(map[string]interface{})
		assert.Equal(t, originalData["name"], deserializedData["name"])
		// JSON反序列化会将数字转换为float64
		assert.Equal(t, float64(25), deserializedData["age"])
	})
}

func TestResponseEdgeCases(t *testing.T) {
	t.Run("zero_error_code", func(t *testing.T) {
		resp := Fail(0, "Zero error code")
		err := resp.GetError()
		assert.NotNil(t, err)
		
		var codeErr errors.CodeError
		assert.True(t, errors.As(err, &codeErr))
		assert.Equal(t, 0, codeErr.Code())
	})

	t.Run("negative_error_code", func(t *testing.T) {
		resp := Fail(-1, "Negative error code")
		err := resp.GetError()
		assert.NotNil(t, err)
		
		var codeErr errors.CodeError
		assert.True(t, errors.As(err, &codeErr))
		assert.Equal(t, -1, codeErr.Code())
	})

	t.Run("large_error_code", func(t *testing.T) {
		resp := Fail(999999, "Large error code")
		err := resp.GetError()
		assert.NotNil(t, err)
		
		var codeErr errors.CodeError
		assert.True(t, errors.As(err, &codeErr))
		assert.Equal(t, 999999, codeErr.Code())
	})

	t.Run("empty_description", func(t *testing.T) {
		resp := Fail(400, "")
		assert.Equal(t, "", resp.Description)
		
		err := resp.GetError()
		assert.NotNil(t, err)
		assert.Equal(t, "", err.Error())
	})

	t.Run("complex_data_structure", func(t *testing.T) {
		complexData := map[string]interface{}{
			"users": []map[string]interface{}{
				{"id": 1, "name": "Alice", "active": true},
				{"id": 2, "name": "Bob", "active": false},
			},
			"metadata": map[string]interface{}{
				"total":    2,
				"page":     1,
				"per_page": 10,
			},
		}
		
		resp := New(complexData)
		assert.Equal(t, complexData, resp.Data)
		
		// 测试JSON序列化
		jsonBytes, err := json.Marshal(resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, jsonBytes)
		
		// 测试JSON反序列化
		var deserialized Response
		err = json.Unmarshal(jsonBytes, &deserialized)
		assert.NoError(t, err)
		assert.Equal(t, resp.ErrorCode, deserialized.ErrorCode)
		assert.Equal(t, resp.Description, deserialized.Description)
	})
}

func TestResponseStructFields(t *testing.T) {
	t.Run("response_struct_tags", func(t *testing.T) {
		resp := &Response{
			ErrorCode:   400,
			Description: "Test Error",
			Data:        "test data",
		}
		
		jsonBytes, err := json.Marshal(resp)
		assert.NoError(t, err)
		
		// 验证JSON标签是否正确
		jsonStr := string(jsonBytes)
		assert.Contains(t, jsonStr, `"code":400`)
		assert.Contains(t, jsonStr, `"msg":"Test Error"`)
		assert.Contains(t, jsonStr, `"data":"test data"`)
	})

	t.Run("omitempty_data_field", func(t *testing.T) {
		resp := &Response{
			ErrorCode:   400,
			Description: "Test Error",
			Data:        nil,
		}
		
		jsonBytes, err := json.Marshal(resp)
		assert.NoError(t, err)
		
		// 验证data字段在为nil时被省略
		jsonStr := string(jsonBytes)
		assert.NotContains(t, jsonStr, `"data"`)
	})
}

// 基准测试
func BenchmarkNew(b *testing.B) {
	data := map[string]interface{}{
		"key1": "value1",
		"key2": 123,
		"key3": true,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		New(data)
	}
}

func BenchmarkFail(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Fail(400, "Bad Request")
	}
}

func BenchmarkErr(b *testing.B) {
	err := errors.NewWithInfo(500, "Internal Server Error")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Err(err)
	}
}

func BenchmarkGetError(b *testing.B) {
	resp := &Response{
		ErrorCode:   400,
		Description: "Bad Request",
		Data:        nil,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		resp.GetError()
	}
}

func BenchmarkJSONMarshal(b *testing.B) {
	resp := New(map[string]interface{}{
		"users": []string{"alice", "bob"},
		"count": 2,
	})
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		json.Marshal(resp)
	}
} 
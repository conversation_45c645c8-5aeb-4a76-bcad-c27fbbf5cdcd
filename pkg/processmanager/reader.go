package processmanager

import (
	"bufio"
	"fmt"
	"io"
	"os"
)

// 创建前缀输出读取器
type PrefixedReader struct {
	prefix string
	reader io.Reader
	writer io.Writer
	done   chan struct{}
}

// 创建新的前缀输出读取器
func NewPrefixedReader(prefix string, reader io.Reader, writer io.Writer) *PrefixedReader {
	prefix2 := fmt.Sprintf("playground_id=%s,docker_id=%s,process_name=%s", os.Getenv("paas_playground_id"), os.Getenv("paas_docker_id"), prefix)
	return &PrefixedReader{
		prefix: prefix2,
		reader: reader,
		writer: writer,
		done:   make(chan struct{}),
	}
}

// 启动前缀输出读取
func (pr *PrefixedReader) Start() {
	scanner := bufio.NewScanner(pr.reader)

	// 增加缓冲区大小，避免长行导致扫描失败
	buf := make([]byte, 0, 64*1024)
	scanner.Buffer(buf, 1024*1024)

	go func() {
		defer close(pr.done)
		defer func() {
			// 捕获可能的 panic，避免程序崩溃
			if r := recover(); r != nil {
				fmt.Fprintf(pr.writer, " [ProcessManager] [%s] 读取器异常恢复: %v\n", pr.prefix, r)
			}
		}()

		for scanner.Scan() {
			line := scanner.Text()
			fmt.Fprintf(pr.writer, " [ProcessManager] [%s] %s\n", pr.prefix, line)
		}
		if err := scanner.Err(); err != nil {
			// 检查是否是正常的管道关闭错误
			if err.Error() != "read |0: file already closed" {
				fmt.Fprintf(pr.writer, " [ProcessManager] [%s] 读取输出错误: %v\n", pr.prefix, err)
			}
		}
	}()
}

// 等待读取完成
func (pr *PrefixedReader) Wait() {
	<-pr.done
}

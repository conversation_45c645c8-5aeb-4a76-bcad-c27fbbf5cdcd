package processmanager

import (
	"agent/utils/log"
	"context"
	"fmt"
	"sync"
	"syscall"
	"time"
)

// 定义一个进程管理器
type ProcessManager struct {
	processes []*Process
	wg        sync.WaitGroup
	mutex     sync.Mutex
	stopping  bool
	ctx       context.Context
	cancel    context.CancelFunc
}

// 创建新的进程管理器
func NewProcessManager() *ProcessManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &ProcessManager{
		processes: make([]*Process, 0),
		stopping:  false,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// 添加进程（接受 Process 指针对象）
func (pm *ProcessManager) AddProcess(process *Process) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.processes = append(pm.processes, process)
}

// 安全关闭停止通道
func (pm *ProcessManager) safeCloseStopChan(p *Process) {
	select {
	case <-p.stopChan:
		// 通道已关闭，不需要再次关闭
	default:
		close(p.stopChan)
	}
}

// 启动所有进程
func (pm *ProcessManager) StartAll() error {
	pm.mutex.Lock()
	if len(pm.processes) == 0 {
		pm.mutex.Unlock()
		return nil
	}

	// 检查进程名称是否重复
	nameSet := make(map[string]bool)
	for _, p := range pm.processes {
		if nameSet[p.Name] {
			pm.mutex.Unlock()
			return fmt.Errorf("进程名称重复: %s", p.Name)
		}
		nameSet[p.Name] = true
	}

	// 复制进程列表，避免在启动过程中持有锁
	processes := make([]*Process, len(pm.processes))
	copy(processes, pm.processes)
	pm.mutex.Unlock()

	// 记录已启动的进程，避免重复启动
	started := make(map[string]bool)
	// 记录正在启动的进程，避免循环依赖
	starting := make(map[string]bool)

	// 启动所有进程
	for _, p := range processes {
		if !started[p.Name] {
			if err := pm.startProcessRecursively(p, started, starting, processes); err != nil {
				return err
			}
		}
	}

	return nil
}

// 递归启动进程及其依赖
func (pm *ProcessManager) startProcessRecursively(process *Process, started, starting map[string]bool, processes []*Process) error {
	// 检查是否已经启动
	if started[process.Name] {
		return nil
	}

	// 检查循环依赖
	if starting[process.Name] {
		return fmt.Errorf("检测到循环依赖：进程 %s", process.Name)
	}

	// 标记正在启动
	starting[process.Name] = true
	defer func() {
		delete(starting, process.Name)
	}()

	// 如果有依赖，先启动依赖进程
	if process.DependsOn != "" {
		// 查找依赖进程
		var dependsOnProcess *Process
		for _, p := range processes {
			if p.Name == process.DependsOn {
				dependsOnProcess = p
				break
			}
		}

		if dependsOnProcess == nil {
			return fmt.Errorf("进程 %s 依赖的进程 %s 不存在", process.Name, process.DependsOn)
		}

		// 递归启动依赖进程
		if err := pm.startProcessRecursively(dependsOnProcess, started, starting, processes); err != nil {
			return err
		}
	}

	// 执行运行前检查
	if process.BeforeRun != nil {
		log.Infof("[ProcessManager] 执行 %s 的运行前检查...\n", process.Name)
		if err := process.BeforeRun(); err != nil {
			return fmt.Errorf("%s 运行前检查失败: %w", process.Name, err)
		}
		log.Infof("[ProcessManager] %s 运行前检查通过\n", process.Name)
	}

	// 启动进程
	log.Infof("[ProcessManager] 启动进程 %s...\n", process.Name)
	cmd := process.CreateCommand(pm.ctx)
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动 %s 失败: %w", process.Name, err)
	}
	log.Infof("[ProcessManager] %s 已启动，PID: %d\n", process.Name, cmd.Process.Pid)

	// 标记为已启动
	started[process.Name] = true

	// 给进程一点时间来初始化
	time.Sleep(200 * time.Millisecond)

	go pm.processStarted(process)

	// 监控进程
	pm.wg.Add(1)
	go func(p *Process) {
		defer pm.wg.Done()
		pm.monitorProcess(p)
	}(process)

	return nil
}

func (pm *ProcessManager) processStarted(process *Process) {
	// 执行启动完成后的回调函数
	if process.AfterStart != nil {
		if err := process.AfterStart(); err != nil {
			log.Warnf("[ProcessManager] %s 启动完成后回调执行失败: %v\n", process.Name, err)
			// 注意：这里不返回错误，因为进程已经成功启动，回调失败不应该影响进程运行
		} else {
			log.Infof("[ProcessManager] %s 启动完成后回调执行成功\n", process.Name)
		}
	}
}

func (pm *ProcessManager) processStopped(process *Process) {
	// 执行进程停止后的回调函数
	if process.AfterStop != nil {
		if callbackErr := process.AfterStop(); callbackErr != nil {
			log.Warnf("[ProcessManager] %s 进程停止后回调执行失败: %v\n", process.Name, callbackErr)
		} else {
			log.Infof("[ProcessManager] %s 进程停止后回调执行成功\n", process.Name)
		}
	}
}

// 监控进程并在需要时重启
func (pm *ProcessManager) monitorProcess(p *Process) {
	for {
		// 等待进程结束或者收到停止信号
		processDone := make(chan error, 1)
		waitDone := make(chan struct{})

		go func() {
			processDone <- p.Cmd.Wait()
			close(waitDone)
		}()

		select {
		case <-p.stopChan:
			// 收到停止信号
			// 等待Wait goroutine结束避免泄漏
			<-waitDone
			return
		case err := <-processDone:
			go pm.processStopped(p)

			// 进程已结束
			pm.mutex.Lock()

			// 如果管理器正在停止，不重启
			if pm.stopping {
				pm.mutex.Unlock()
				return
			}

			// 不管是正常退出还是异常退出，都尝试重启
			if err != nil {
				log.Infof("[ProcessManager] %s 异常退出: %v，准备重启\n", p.Name, err)
			} else {
				log.Infof("[ProcessManager] %s 正常退出，准备重启\n", p.Name)
			}

			// 检查是否达到最大重启次数
			p.mutex.Lock()
			reachedMaxRestarts := p.Restarts >= p.MaxRestarts && p.MaxRestarts > 0
			p.mutex.Unlock()

			if reachedMaxRestarts {
				log.Infof("[ProcessManager] %s 已达到最大重启次数 %d，不再重启\n", p.Name, p.MaxRestarts)

				// 如果是基础进程且不重启，终止依赖它的进程
				if p.DependsOn == "" {
					log.Infof("[ProcessManager] 基础进程 %s 终止，将停止所有依赖它的进程\n", p.Name)
					for _, dep := range pm.processes {
						if dep.DependsOn == p.Name && dep.Cmd != nil && dep.Cmd.Process != nil {
							log.Infof("[ProcessManager] 终止依赖进程 %s (PID: %d)...\n", dep.Name, dep.Cmd.Process.Pid)
							// 发送停止信号，但需要先检查通道是否已关闭
							pm.safeCloseStopChan(dep)
							// 给进程发送SIGTERM信号
							if err := dep.Cmd.Process.Signal(syscall.SIGTERM); err != nil {
								log.Infof("[ProcessManager] 无法发送SIGTERM到 %s: %v\n", dep.Name, err)
							}
						}
					}
				}

				pm.mutex.Unlock()
				return
			}

			// 重启进程
			p.mutex.Lock()
			p.Restarts++
			restartCount := p.Restarts // 在解锁前保存计数
			restartTimeout := p.RestartTimeout
			p.mutex.Unlock()

			log.Infof("[ProcessManager] 正在重启 %s (第 %d 次尝试)...\n", p.Name, restartCount)

			// 在重启前释放锁，避免长时间持有锁
			pm.mutex.Unlock()

			// 等待一小段时间再重启，避免太快重启可能导致的问题
			select {
			case <-time.After(restartTimeout):
			case <-p.stopChan:
				return
			}

			// 执行运行前检查
			if p.BeforeRun != nil {
				log.Infof("[ProcessManager] 执行 %s 重启前检查...\n", p.Name)
				if err := p.BeforeRun(); err != nil {
					log.Infof("[ProcessManager] 错误: %s 重启前检查失败: %v，将重新尝试\n", p.Name, err)
					continue
				}
			}

			// 清理旧的readers资源
			p.mutex.Lock()
			oldReaders := p.readers
			p.readers = make([]*PrefixedReader, 0)
			p.mutex.Unlock()

			// 创建新的命令
			cmd := p.CreateCommand(pm.ctx)

			startErr := cmd.Start()
			if startErr != nil {
				log.Infof("[ProcessManager] 重启 %s 失败: %v\n", p.Name, startErr)

				// 等待一段时间后再次尝试
				select {
				case <-time.After(3 * time.Second):
					continue
				case <-p.stopChan:
					return
				}
			}

			log.Infof("[ProcessManager] %s 已重启，新 PID: %d\n", p.Name, cmd.Process.Pid)

			go pm.processStarted(p)

			// 等待旧的readers完成工作，避免资源竞争
			for _, reader := range oldReaders {
				go func(r *PrefixedReader) {
					// 设置超时，避免无限等待
					select {
					case <-r.done:
						// 正常完成
					case <-time.After(3 * time.Second):
						// 超时，记录警告
						log.Infof("[ProcessManager] 警告: %s 的读取器清理超时\n", p.Name)
					}
				}(reader)
			}

		case <-pm.ctx.Done():
			// 父上下文被取消
			// 等待Wait goroutine结束避免泄漏
			<-waitDone
			return
		}
	}
}

// 等待所有进程结束
func (pm *ProcessManager) Wait() {
	pm.wg.Wait()
}

// 停止所有进程
func (pm *ProcessManager) StopAll() {
	if len(pm.processes) == 0 {
		return
	}

	// 首先设置停止标志并取消上下文
	pm.mutex.Lock()
	if pm.stopping {
		// 避免重复调用StopAll
		pm.mutex.Unlock()
		return
	}
	pm.stopping = true
	pm.mutex.Unlock()

	log.Println("[ProcessManager] 正在停止所有进程...")

	// 取消上下文，通知所有进程停止
	pm.cancel()

	// 通知所有监控协程停止
	pm.mutex.Lock()
	for _, p := range pm.processes {
		// 在锁保护下检查通道是否已关闭
		pm.safeCloseStopChan(p)
	}
	pm.mutex.Unlock()

	// 给进程一点时间来优雅退出
	gracePeriod := 5 * time.Second
	gracefulShutdown := make(chan struct{})
	go func() {
		pm.wg.Wait()
		close(gracefulShutdown)
	}()

	// 等待进程优雅退出或者超时
	select {
	case <-gracefulShutdown:
		log.Println("[ProcessManager] 所有进程已优雅终止")
	case <-time.After(gracePeriod):
		log.Println("[ProcessManager] 优雅终止超时，发送SIGTERM信号")

		// 发送SIGTERM信号给所有仍在运行的进程
		// 首先停止依赖其他进程的进程，然后停止基础进程
		dependentProcesses := make([]*Process, 0)
		baseProcesses := make([]*Process, 0)

		pm.mutex.Lock()
		for _, p := range pm.processes {
			if p.DependsOn != "" {
				dependentProcesses = append(dependentProcesses, p)
			} else {
				baseProcesses = append(baseProcesses, p)
			}
		}
		pm.mutex.Unlock()

		// 先停止依赖进程
		for _, p := range dependentProcesses {
			if p.Cmd != nil && p.Cmd.Process != nil {
				log.Infof("[ProcessManager] 正在终止 %s (PID: %d)...\n", p.Name, p.Cmd.Process.Pid)
				if err := p.Cmd.Process.Signal(syscall.SIGTERM); err != nil {
					log.Infof("[ProcessManager] 无法发送 SIGTERM 到 %s: %v\n", p.Name, err)
				}
			}
		}

		// 等待一小段时间
		time.Sleep(1 * time.Second)

		// 再停止基础进程
		for _, p := range baseProcesses {
			if p.Cmd != nil && p.Cmd.Process != nil {
				log.Infof("[ProcessManager] 正在终止 %s (PID: %d)...\n", p.Name, p.Cmd.Process.Pid)
				if err := p.Cmd.Process.Signal(syscall.SIGTERM); err != nil {
					log.Infof("[ProcessManager] 无法发送 SIGTERM 到 %s: %v\n", p.Name, err)
				}
			}
		}

		// 再给进程一点时间来响应SIGTERM
		select {
		case <-gracefulShutdown:
			log.Println("[ProcessManager] 所有进程已响应SIGTERM并终止")
		case <-time.After(2 * time.Second):
			log.Println("[ProcessManager] 部分进程未响应SIGTERM，强制终止")
			for _, p := range pm.processes {
				if p.Cmd != nil && p.Cmd.Process != nil {
					// 查看进程是否还在运行
					if err := p.Cmd.Process.Signal(syscall.Signal(0)); err == nil {
						// 进程仍在运行，强制终止
						log.Infof("[ProcessManager] 强制终止 %s (PID: %d)...\n", p.Name, p.Cmd.Process.Pid)
						p.Cmd.Process.Kill()
					}
				}
			}
		}
	}

	// 等待所有读取器完成
	for _, p := range pm.processes {
		p.mutex.Lock()
		readers := p.readers
		p.mutex.Unlock()
		for _, reader := range readers {
			reader.Wait()
		}
	}

	// 执行所有进程的停止后回调函数
	for _, p := range pm.processes {
		go pm.processStopped(p)
	}

	log.Println("[ProcessManager] 所有进程已终止")
}

package processmanager

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"
)

// TestNewProcess 测试创建新进程实例
func TestNewProcess(t *testing.T) {
	name := "TestProcess"
	executable := "echo"
	args := []string{"hello", "world"}
	dependsOn := "ParentProcess"
	maxRestarts := 5

	process := NewProcess(name, executable, args, dependsOn, maxRestarts)

	// 验证基本属性
	if process.Name != name {
		t.<PERSON>rrorf("期望进程名称为 %s，实际为 %s", name, process.Name)
	}
	if process.Executable != executable {
		t.<PERSON>rrorf("期望可执行文件为 %s，实际为 %s", executable, process.Executable)
	}
	if len(process.Args) != len(args) {
		t.<PERSON><PERSON>("期望参数数量为 %d，实际为 %d", len(args), len(process.Args))
	}
	for i, arg := range args {
		if process.Args[i] != arg {
			t.<PERSON><PERSON>("期望参数[%d]为 %s，实际为 %s", i, arg, process.Args[i])
		}
	}
	if process.DependsOn != dependsOn {
		t.Errorf("期望依赖进程为 %s，实际为 %s", dependsOn, process.DependsOn)
	}
	if process.MaxRestarts != maxRestarts {
		t.Errorf("期望最大重启次数为 %d，实际为 %d", maxRestarts, process.MaxRestarts)
	}

	// 验证默认值
	if process.RestartTimeout != 5*time.Second {
		t.Errorf("期望默认重启超时为 5s，实际为 %v", process.RestartTimeout)
	}
	if process.Restarts != 0 {
		t.Errorf("期望初始重启次数为 0，实际为 %d", process.Restarts)
	}
	if process.BeforeRun != nil {
		t.Error("期望默认BeforeRun为nil")
	}
	if process.stopChan == nil {
		t.Error("期望stopChan已初始化")
	}
	if process.readers == nil {
		t.Error("期望readers已初始化")
	}
}

// TestProcess_SetRestartTimeout 测试设置重启超时时间
func TestProcess_SetRestartTimeout(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	timeout := 10 * time.Second
	process.SetRestartTimeout(timeout)

	if process.RestartTimeout != timeout {
		t.Errorf("期望重启超时为 %v，实际为 %v", timeout, process.RestartTimeout)
	}
}

// TestProcess_SetBeforeRun 测试设置运行前检查函数
func TestProcess_SetBeforeRun(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	called := false
	beforeRun := func() error {
		called = true
		return nil
	}

	process.SetBeforeRun(beforeRun)

	if process.BeforeRun == nil {
		t.Error("期望BeforeRun函数已设置")
	}

	// 测试调用
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("期望BeforeRun返回nil，实际返回 %v", err)
	}
	if !called {
		t.Error("期望BeforeRun函数被调用")
	}
}

// TestProcess_CreateCommand 测试创建命令
func TestProcess_CreateCommand(t *testing.T) {
	process := NewProcess("test", "echo", []string{"hello", "world"}, "", 1)

	ctx := context.Background()
	cmd := process.CreateCommand(ctx)

	if cmd == nil {
		t.Fatal("期望创建的命令不为nil")
	}

	// 命令路径可能是 "echo"、"/bin/echo" 或 "/usr/bin/echo"，取决于系统
	if cmd.Path != "echo" && cmd.Path != "/bin/echo" && cmd.Path != "/usr/bin/echo" {
		t.Errorf("期望命令路径为 echo、/bin/echo 或 /usr/bin/echo，实际为 %s", cmd.Path)
	}

	expectedArgs := []string{"echo", "hello", "world"}
	if len(cmd.Args) != len(expectedArgs) {
		t.Errorf("期望参数数量为 %d，实际为 %d", len(expectedArgs), len(cmd.Args))
	}
	for i, arg := range expectedArgs {
		if cmd.Args[i] != arg {
			t.Errorf("期望参数[%d]为 %s，实际为 %s", i, arg, cmd.Args[i])
		}
	}

	// 验证环境变量继承
	if len(cmd.Env) == 0 {
		t.Error("期望命令继承环境变量")
	}

	// 验证进程的Cmd字段已设置
	if process.Cmd != cmd {
		t.Error("期望进程的Cmd字段已设置为创建的命令")
	}
}

// TestProcess_CreateCommandWithContext 测试带上下文的命令创建
func TestProcess_CreateCommandWithContext(t *testing.T) {
	process := NewProcess("test", "sleep", []string{"10"}, "", 1)

	ctx, cancel := context.WithCancel(context.Background())
	cmd := process.CreateCommand(ctx)

	// 启动命令
	err := cmd.Start()
	if err != nil {
		t.Fatalf("启动命令失败: %v", err)
	}

	// 取消上下文
	cancel()

	// 等待命令结束
	err = cmd.Wait()
	if err == nil {
		t.Error("期望命令因上下文取消而失败")
	}
}

// TestProcess_CreateCommandWithInvalidExecutable 测试无效可执行文件
func TestProcess_CreateCommandWithInvalidExecutable(t *testing.T) {
	process := NewProcess("test", "nonexistent_command_12345", []string{}, "", 1)

	ctx := context.Background()
	cmd := process.CreateCommand(ctx)

	// 尝试启动应该失败
	err := cmd.Start()
	if err == nil {
		t.Error("期望启动不存在的命令失败")
		cmd.Process.Kill() // 清理
	}
}

// TestProcess_EnvironmentInheritance 测试环境变量继承
func TestProcess_EnvironmentInheritance(t *testing.T) {
	// 设置一个测试环境变量
	testKey := "TEST_PROCESS_ENV_VAR"
	testValue := "test_value_12345"
	os.Setenv(testKey, testValue)
	defer os.Unsetenv(testKey)

	process := NewProcess("test", "env", []string{}, "", 1)
	ctx := context.Background()
	cmd := process.CreateCommand(ctx)

	// 检查环境变量是否被继承
	found := false
	for _, env := range cmd.Env {
		if env == testKey+"="+testValue {
			found = true
			break
		}
	}

	if !found {
		t.Errorf("期望环境变量 %s=%s 被继承", testKey, testValue)
	}
}

// TestProcess_MultipleCommandCreation 测试多次创建命令
func TestProcess_MultipleCommandCreation(t *testing.T) {
	process := NewProcess("test", "echo", []string{"test"}, "", 1)
	ctx := context.Background()

	cmd1 := process.CreateCommand(ctx)
	cmd2 := process.CreateCommand(ctx)

	// 第二次创建应该覆盖第一次
	if process.Cmd != cmd2 {
		t.Error("期望进程的Cmd字段指向最新创建的命令")
	}

	if cmd1 == cmd2 {
		t.Error("期望每次创建的命令都是新的实例")
	}
}

// TestProcess_ReadersInitialization 测试读取器初始化
func TestProcess_ReadersInitialization(t *testing.T) {
	process := NewProcess("test", "echo", []string{"hello"}, "", 1)

	// 初始应该没有读取器
	if len(process.readers) != 0 {
		t.Errorf("期望初始读取器数量为 0，实际为 %d", len(process.readers))
	}

	ctx := context.Background()
	cmd := process.CreateCommand(ctx)

	// 启动命令以创建管道
	err := cmd.Start()
	if err != nil {
		t.Fatalf("启动命令失败: %v", err)
	}

	// 等待命令完成
	cmd.Wait()

	// 应该创建了读取器（stdout和stderr）
	if len(process.readers) == 0 {
		t.Error("期望创建了输出读取器")
	}
}

// TestProcess_StopChannelInitialization 测试停止通道初始化
func TestProcess_StopChannelInitialization(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	if process.stopChan == nil {
		t.Error("期望stopChan已初始化")
	}

	// 测试通道是否可用
	select {
	case <-process.stopChan:
		t.Error("期望stopChan初始时不应该有值")
	default:
		// 正常情况
	}
}

// TestProcess_ConcurrentAccess 测试并发访问
func TestProcess_ConcurrentAccess(t *testing.T) {
	process := NewProcess("test", "echo", []string{"test"}, "", 1)
	ctx := context.Background()

	// 并发创建命令和设置属性
	done := make(chan bool, 3)

	go func() {
		process.CreateCommand(ctx)
		done <- true
	}()

	go func() {
		process.SetRestartTimeout(10 * time.Second)
		done <- true
	}()

	go func() {
		process.SetBeforeRun(func() error { return nil })
		done <- true
	}()

	// 等待所有goroutine完成
	for i := 0; i < 3; i++ {
		select {
		case <-done:
		case <-time.After(5 * time.Second):
			t.Fatal("并发操作超时")
		}
	}
}

// TestProcess_SetAfterStart 测试设置启动完成后回调函数
func TestProcess_SetAfterStart(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	called := false
	afterStart := func() error {
		called = true
		return nil
	}

	process.SetAfterStart(afterStart)

	if process.AfterStart == nil {
		t.Error("期望AfterStart函数已设置")
	}

	// 测试调用
	err := process.AfterStart()
	if err != nil {
		t.Errorf("期望AfterStart返回nil，实际返回 %v", err)
	}
	if !called {
		t.Error("期望AfterStart函数被调用")
	}
}

// TestProcess_SetAfterStop 测试设置进程停止后回调函数
func TestProcess_SetAfterStop(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	called := false
	afterStop := func() error {
		called = true
		return nil
	}

	process.SetAfterStop(afterStop)

	if process.AfterStop == nil {
		t.Error("期望AfterStop函数已设置")
	}

	// 测试调用
	err := process.AfterStop()
	if err != nil {
		t.Errorf("期望AfterStop返回nil，实际返回 %v", err)
	}
	if !called {
		t.Error("期望AfterStop函数被调用")
	}
}

// TestProcess_CallbackErrorHandling 测试回调函数错误处理
func TestProcess_CallbackErrorHandling(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	// 测试AfterStart错误处理
	afterStartError := func() error {
		return fmt.Errorf("AfterStart测试错误")
	}
	process.SetAfterStart(afterStartError)

	err := process.AfterStart()
	if err == nil {
		t.Error("期望AfterStart返回错误")
	}
	if err.Error() != "AfterStart测试错误" {
		t.Errorf("期望错误消息为 'AfterStart测试错误'，实际为 %s", err.Error())
	}

	// 测试AfterStop错误处理
	afterStopError := func() error {
		return fmt.Errorf("AfterStop测试错误")
	}
	process.SetAfterStop(afterStopError)

	err = process.AfterStop()
	if err == nil {
		t.Error("期望AfterStop返回错误")
	}
	if err.Error() != "AfterStop测试错误" {
		t.Errorf("期望错误消息为 'AfterStop测试错误'，实际为 %s", err.Error())
	}
}

// TestProcess_DefaultCallbackValues 测试默认回调函数值
func TestProcess_DefaultCallbackValues(t *testing.T) {
	process := NewProcess("test", "echo", []string{}, "", 1)

	// 验证默认值
	if process.AfterStart != nil {
		t.Error("期望默认AfterStart为nil")
	}
	if process.AfterStop != nil {
		t.Error("期望默认AfterStop为nil")
	}
}

package processmanager

import (
	"fmt"
	"os/exec"
	"sync"
	"testing"
	"time"
)

// 创建一个模拟进程，使用sleep命令来模拟长时间运行的进程
func createMockProcess(name string, dependsOn string, sleepTime int) *Process {
	args := []string{fmt.Sprintf("%d", sleepTime)}
	p := NewProcess(name, "sleep", args, dependsOn, 3)
	p.SetRestartTimeout(1 * time.Second)
	return p
}

// 创建一个会立即退出的进程用于测试失败场景
func createFailingProcess(name string, dependsOn string) *Process {
	// 使用false命令，它会立即退出并返回非零状态码
	p := NewProcess(name, "false", []string{}, dependsOn, 1)
	p.SetRestartTimeout(1 * time.Second)
	return p
}

// 测试基本的依赖关系启动
func TestProcessManager_BasicDependency(t *testing.T) {
	pm := NewProcessManager()

	// 创建两个进程：B 依赖 A
	processA := createMockProcess("ProcessA", "", 2)
	processB := createMockProcess("ProcessB", "ProcessA", 2)

	pm.AddProcess(processA)
	pm.AddProcess(processB)

	// 启动顺序记录
	startOrder := make([]string, 0)
	var orderMutex sync.Mutex

	// 为两个进程设置BeforeRun来记录启动顺序
	processA.SetBeforeRun(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "ProcessA")
		orderMutex.Unlock()
		return nil
	})

	processB.SetBeforeRun(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "ProcessB")
		orderMutex.Unlock()
		return nil
	})

	// 启动所有进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待一小段时间让进程启动
	time.Sleep(500 * time.Millisecond)

	// 检查启动顺序
	orderMutex.Lock()
	if len(startOrder) != 2 {
		t.Fatalf("期望启动2个进程，实际启动了%d个", len(startOrder))
	}
	if startOrder[0] != "ProcessA" {
		t.Errorf("期望ProcessA先启动，实际是%s", startOrder[0])
	}
	if startOrder[1] != "ProcessB" {
		t.Errorf("期望ProcessB后启动，实际是%s", startOrder[1])
	}
	orderMutex.Unlock()

	// 停止所有进程
	pm.StopAll()
}

// 测试多级依赖关系
func TestProcessManager_MultiLevelDependency(t *testing.T) {
	pm := NewProcessManager()

	// 创建三个进程：C 依赖 B，B 依赖 A
	processA := createMockProcess("ProcessA", "", 3)
	processB := createMockProcess("ProcessB", "ProcessA", 3)
	processC := createMockProcess("ProcessC", "ProcessB", 3)

	pm.AddProcess(processC) // 故意先添加C
	pm.AddProcess(processA) // 然后添加A
	pm.AddProcess(processB) // 最后添加B

	// 启动顺序记录
	startOrder := make([]string, 0)
	var orderMutex sync.Mutex

	// 为三个进程设置BeforeRun来记录启动顺序
	processA.SetBeforeRun(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "ProcessA")
		orderMutex.Unlock()
		return nil
	})

	processB.SetBeforeRun(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "ProcessB")
		orderMutex.Unlock()
		return nil
	})

	processC.SetBeforeRun(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "ProcessC")
		orderMutex.Unlock()
		return nil
	})

	// 启动所有进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待一小段时间让进程启动
	time.Sleep(500 * time.Millisecond)

	// 检查启动顺序
	orderMutex.Lock()
	expectedOrder := []string{"ProcessA", "ProcessB", "ProcessC"}
	if len(startOrder) != 3 {
		t.Fatalf("期望启动3个进程，实际启动了%d个", len(startOrder))
	}
	for i, expected := range expectedOrder {
		if startOrder[i] != expected {
			t.Errorf("期望第%d个启动的是%s，实际是%s", i+1, expected, startOrder[i])
		}
	}
	orderMutex.Unlock()

	// 停止所有进程
	pm.StopAll()
}

// 测试循环依赖检测
func TestProcessManager_CircularDependency(t *testing.T) {
	pm := NewProcessManager()

	// 创建循环依赖：A 依赖 B，B 依赖 A
	processA := createMockProcess("ProcessA", "ProcessB", 2)
	processB := createMockProcess("ProcessB", "ProcessA", 2)

	pm.AddProcess(processA)
	pm.AddProcess(processB)

	// 启动应该失败
	err := pm.StartAll()
	if err == nil {
		t.Fatal("期望检测到循环依赖并返回错误，但启动成功了")
	}

	// 检查错误消息包含循环依赖信息
	expectedMsg := "检测到循环依赖"
	if err.Error() == "" || len(err.Error()) == 0 {
		t.Errorf("期望错误消息包含'%s'，实际错误: %v", expectedMsg, err)
	}

	// 确保没有进程被启动
	time.Sleep(100 * time.Millisecond)

	// 停止所有进程（应该是安全的）
	pm.StopAll()
}

// 测试依赖进程不存在的情况
func TestProcessManager_MissingDependency(t *testing.T) {
	pm := NewProcessManager()

	// 创建一个依赖不存在进程的进程
	processA := createMockProcess("ProcessA", "NonExistentProcess", 2)

	pm.AddProcess(processA)

	// 启动应该失败
	err := pm.StartAll()
	if err == nil {
		t.Fatal("期望因为依赖进程不存在而失败，但启动成功了")
	}

	// 检查错误消息
	expectedMsg := "不存在"
	if err.Error() == "" || len(err.Error()) == 0 {
		t.Errorf("期望错误消息包含'%s'，实际错误: %v", expectedMsg, err)
	}

	// 停止所有进程
	pm.StopAll()
}

// 测试进程名称重复检测
func TestProcessManager_DuplicateProcessNames(t *testing.T) {
	pm := NewProcessManager()

	// 创建两个同名进程
	processA1 := createMockProcess("ProcessA", "", 2)
	processA2 := createMockProcess("ProcessA", "", 2)

	pm.AddProcess(processA1)
	pm.AddProcess(processA2)

	// 启动应该失败
	err := pm.StartAll()
	if err == nil {
		t.Fatal("期望因为进程名称重复而失败，但启动成功了")
	}

	// 检查错误消息
	expectedMsg := "进程名称重复"
	if err.Error() == "" || len(err.Error()) == 0 {
		t.Errorf("期望错误消息包含'%s'，实际错误: %v", expectedMsg, err)
	}

	// 停止所有进程
	pm.StopAll()
}

// 测试复杂的依赖树
func TestProcessManager_ComplexDependencyTree(t *testing.T) {
	pm := NewProcessManager()

	// 创建复杂的依赖树：
	//     Root
	//    /    \
	//   A      B
	//  / \      \
	// C   D      E
	//           /
	//          F

	root := createMockProcess("Root", "", 4)
	processA := createMockProcess("ProcessA", "Root", 4)
	processB := createMockProcess("ProcessB", "Root", 4)
	processC := createMockProcess("ProcessC", "ProcessA", 4)
	processD := createMockProcess("ProcessD", "ProcessA", 4)
	processE := createMockProcess("ProcessE", "ProcessB", 4)
	processF := createMockProcess("ProcessF", "", 4)         // F不依赖任何进程
	processG := createMockProcess("ProcessG", "ProcessF", 4) // G依赖F
	processE.DependsOn = "ProcessG"                          // 修改E的依赖为G

	// 故意以混乱的顺序添加进程
	pm.AddProcess(processC)
	pm.AddProcess(processE)
	pm.AddProcess(root)
	pm.AddProcess(processG)
	pm.AddProcess(processA)
	pm.AddProcess(processD)
	pm.AddProcess(processF)
	pm.AddProcess(processB)

	// 启动顺序记录
	startOrder := make([]string, 0)
	var orderMutex sync.Mutex

	// 为所有进程设置BeforeRun来记录启动顺序
	for _, p := range []*Process{root, processA, processB, processC, processD, processE, processF, processG} {
		func(proc *Process) {
			proc.SetBeforeRun(func() error {
				orderMutex.Lock()
				startOrder = append(startOrder, proc.Name)
				orderMutex.Unlock()
				return nil
			})
		}(p)
	}

	// 启动所有进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待一小段时间让进程启动
	time.Sleep(800 * time.Millisecond)

	// 检查启动顺序
	orderMutex.Lock()
	if len(startOrder) != 8 {
		t.Fatalf("期望启动8个进程，实际启动了%d个: %v", len(startOrder), startOrder)
	}

	// 验证依赖关系是否正确
	posMap := make(map[string]int)
	for i, name := range startOrder {
		posMap[name] = i
	}

	// 检查依赖关系
	dependencies := map[string]string{
		"ProcessA": "Root",
		"ProcessB": "Root",
		"ProcessC": "ProcessA",
		"ProcessD": "ProcessA",
		"ProcessE": "ProcessG",
		"ProcessG": "ProcessF",
	}

	for dependent, dependency := range dependencies {
		depPos, depExists := posMap[dependent]
		depOnPos, depOnExists := posMap[dependency]

		if !depExists {
			t.Errorf("进程 %s 没有被启动", dependent)
			continue
		}
		if !depOnExists {
			t.Errorf("依赖进程 %s 没有被启动", dependency)
			continue
		}

		if depOnPos >= depPos {
			t.Errorf("依赖关系错误: %s (位置%d) 应该在 %s (位置%d) 之前启动",
				dependency, depOnPos, dependent, depPos)
		}
	}
	orderMutex.Unlock()

	// 停止所有进程
	pm.StopAll()
}

// 测试BeforeRun检查失败
func TestProcessManager_BeforeRunFailure(t *testing.T) {
	pm := NewProcessManager()

	// 创建一个BeforeRun会失败的进程
	processA := createMockProcess("ProcessA", "", 2)
	processA.SetBeforeRun(func() error {
		return fmt.Errorf("运行前检查失败")
	})

	pm.AddProcess(processA)

	// 启动应该失败
	err := pm.StartAll()
	if err == nil {
		t.Fatal("期望因为BeforeRun失败而失败，但启动成功了")
	}

	// 检查错误消息
	expectedMsg := "运行前检查失败"
	if err.Error() == "" || len(err.Error()) == 0 {
		t.Errorf("期望错误消息包含'%s'，实际错误: %v", expectedMsg, err)
	}

	// 停止所有进程
	pm.StopAll()
}

// 测试空进程管理器
func TestProcessManager_EmptyManager(t *testing.T) {
	pm := NewProcessManager()

	// 启动空的进程管理器应该成功
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动空进程管理器失败: %v", err)
	}

	// 停止应该也是安全的
	pm.StopAll()
}

// 测试进程重启功能
func TestProcessManager_ProcessRestart(t *testing.T) {
	// 这个测试需要确保sleep命令存在
	if _, err := exec.LookPath("sleep"); err != nil {
		t.Skip("跳过测试: sleep命令不可用")
	}

	pm := NewProcessManager()

	// 创建一个会很快退出的进程来触发重启
	process := createFailingProcess("FailingProcess", "")
	process.MaxRestarts = 2 // 设置最大重启次数为2

	pm.AddProcess(process)

	// 启动进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待进程重启几次
	time.Sleep(3 * time.Second)

	// 检查重启次数 - 使用互斥锁安全读取
	process.mutex.Lock()
	restarts := process.Restarts
	process.mutex.Unlock()

	if restarts < 1 {
		t.Errorf("期望进程至少重启1次，实际重启了%d次", restarts)
	}

	// 停止所有进程
	pm.StopAll()
}

// 基准测试：测试大量进程的启动性能
func BenchmarkProcessManager_StartManyProcesses(b *testing.B) {
	if _, err := exec.LookPath("sleep"); err != nil {
		b.Skip("跳过基准测试: sleep命令不可用")
	}

	for i := 0; i < b.N; i++ {
		pm := NewProcessManager()

		// 添加10个进程
		for j := 0; j < 10; j++ {
			process := createMockProcess(fmt.Sprintf("Process%d", j), "", 1)
			pm.AddProcess(process)
		}

		// 启动所有进程
		if err := pm.StartAll(); err != nil {
			b.Fatalf("启动进程失败: %v", err)
		}

		// 立即停止所有进程
		pm.StopAll()
	}
}

// 测试AfterStart和AfterStop回调函数
func TestProcessManager_Callbacks(t *testing.T) {
	pm := NewProcessManager()

	// 创建测试进程
	process := createMockProcess("TestProcess", "", 2)

	// 记录回调执行情况
	afterStartCalled := false
	afterStopCalled := false

	process.SetAfterStart(func() error {
		afterStartCalled = true
		return nil
	})

	process.SetAfterStop(func() error {
		afterStopCalled = true
		return nil
	})

	pm.AddProcess(process)

	// 启动进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待一小段时间让进程启动并执行AfterStart回调
	time.Sleep(500 * time.Millisecond)

	// 检查AfterStart是否被调用
	if !afterStartCalled {
		t.Error("期望AfterStart回调被调用")
	}

	// 停止所有进程
	pm.StopAll()

	// 等待一小段时间让进程停止并执行AfterStop回调
	time.Sleep(500 * time.Millisecond)

	// 检查AfterStop是否被调用
	if !afterStopCalled {
		t.Error("期望AfterStop回调被调用")
	}
}

// 测试回调函数错误处理
func TestProcessManager_CallbackErrorHandling(t *testing.T) {
	pm := NewProcessManager()

	// 创建测试进程
	process := createMockProcess("TestProcess", "", 2)

	// 设置会返回错误的回调函数
	process.SetAfterStart(func() error {
		return fmt.Errorf("AfterStart测试错误")
	})

	process.SetAfterStop(func() error {
		return fmt.Errorf("AfterStop测试错误")
	})

	pm.AddProcess(process)

	// 启动进程 - AfterStart错误不应该阻止进程启动
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待一小段时间
	time.Sleep(500 * time.Millisecond)

	// 停止所有进程 - AfterStop错误不应该阻止进程停止
	pm.StopAll()

	// 等待一小段时间
	time.Sleep(500 * time.Millisecond)

	// 测试应该正常完成，错误应该被记录但不影响进程管理
}

// 测试多个进程的回调函数执行顺序
func TestProcessManager_MultipleProcessCallbacks(t *testing.T) {
	pm := NewProcessManager()

	// 创建多个进程
	process1 := createMockProcess("Process1", "", 3)
	process2 := createMockProcess("Process2", "Process1", 3)

	// 记录启动顺序
	startOrder := make([]string, 0)
	stopOrder := make([]string, 0)
	var orderMutex sync.Mutex

	process1.SetAfterStart(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "Process1")
		orderMutex.Unlock()
		return nil
	})

	process1.SetAfterStop(func() error {
		orderMutex.Lock()
		stopOrder = append(stopOrder, "Process1")
		orderMutex.Unlock()
		return nil
	})

	process2.SetAfterStart(func() error {
		orderMutex.Lock()
		startOrder = append(startOrder, "Process2")
		orderMutex.Unlock()
		return nil
	})

	process2.SetAfterStop(func() error {
		orderMutex.Lock()
		stopOrder = append(stopOrder, "Process2")
		orderMutex.Unlock()
		return nil
	})

	pm.AddProcess(process1)
	pm.AddProcess(process2)

	// 启动进程
	err := pm.StartAll()
	if err != nil {
		t.Fatalf("启动进程失败: %v", err)
	}

	// 等待进程启动
	time.Sleep(800 * time.Millisecond)

	// 检查启动顺序
	orderMutex.Lock()
	if len(startOrder) != 2 {
		t.Fatalf("期望2个进程的AfterStart被调用，实际为%d个", len(startOrder))
	}
	if startOrder[0] != "Process1" {
		t.Errorf("期望Process1先启动，实际是%s", startOrder[0])
	}
	if startOrder[1] != "Process2" {
		t.Errorf("期望Process2后启动，实际是%s", startOrder[1])
	}
	orderMutex.Unlock()

	// 停止所有进程
	pm.StopAll()

	// 等待进程停止
	time.Sleep(800 * time.Millisecond)

	// 检查停止顺序（依赖进程应该先停止）
	orderMutex.Lock()
	if len(stopOrder) != 2 {
		t.Fatalf("期望2个进程的AfterStop被调用，实际为%d个", len(stopOrder))
	}
	// 注意：停止顺序可能因进程管理器的实现而不同，这里只检查是否都被调用
	orderMutex.Unlock()
}

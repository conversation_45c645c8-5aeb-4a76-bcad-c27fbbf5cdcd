package processmanager

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"
	"time"
)

// TestNewPrefixedReader 测试创建新的前缀读取器
func TestNewPrefixedReader(t *testing.T) {
	prefix := "test-process"
	reader := strings.NewReader("test input")
	writer := &bytes.Buffer{}
	
	// 设置测试环境变量
	os.Setenv("paas_playground_id", "test-playground")
	os.Setenv("paas_docker_id", "test-docker")
	defer func() {
		os.Unsetenv("paas_playground_id")
		os.Unsetenv("paas_docker_id")
	}()
	
	pr := NewPrefixedReader(prefix, reader, writer)
	
	if pr == nil {
		t.Fatal("期望创建的PrefixedReader不为nil")
	}
	
	expectedPrefix := fmt.Sprintf("playground_id=%s,docker_id=%s,process_name=%s", 
		"test-playground", "test-docker", prefix)
	if pr.prefix != expectedPrefix {
		t.Errorf("期望前缀为 %s，实际为 %s", expectedPrefix, pr.prefix)
	}
	
	if pr.reader != reader {
		t.<PERSON>rror("期望reader字段设置正确")
	}
	
	if pr.writer != writer {
		t.Error("期望writer字段设置正确")
	}
	
	if pr.done == nil {
		t.Error("期望done通道已初始化")
	}
}

// TestPrefixedReader_Start 测试启动前缀读取
func TestPrefixedReader_Start(t *testing.T) {
	input := "line1\nline2\nline3"
	reader := strings.NewReader(input)
	writer := &bytes.Buffer{}
	
	// 设置测试环境变量
	os.Setenv("paas_playground_id", "test-playground")
	os.Setenv("paas_docker_id", "test-docker")
	defer func() {
		os.Unsetenv("paas_playground_id")
		os.Unsetenv("paas_docker_id")
	}()
	
	pr := NewPrefixedReader("test", reader, writer)
	pr.Start()
	
	// 等待读取完成
	pr.Wait()
	
	output := writer.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	
	if len(lines) != 3 {
		t.Errorf("期望输出3行，实际输出%d行", len(lines))
	}
	
	expectedLines := []string{"line1", "line2", "line3"}
	for i, expectedLine := range expectedLines {
		if i < len(lines) {
			if !strings.Contains(lines[i], expectedLine) {
				t.Errorf("期望第%d行包含 %s，实际为 %s", i+1, expectedLine, lines[i])
			}
			if !strings.Contains(lines[i], "[ProcessManager]") {
				t.Errorf("期望第%d行包含 [ProcessManager] 前缀", i+1)
			}
			if !strings.Contains(lines[i], "test") {
				t.Errorf("期望第%d行包含进程名称", i+1)
			}
		}
	}
}

// TestPrefixedReader_EmptyInput 测试空输入
func TestPrefixedReader_EmptyInput(t *testing.T) {
	reader := strings.NewReader("")
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("test", reader, writer)
	pr.Start()
	
	// 等待读取完成
	pr.Wait()
	
	output := writer.String()
	if output != "" {
		t.Errorf("期望空输入产生空输出，实际输出: %s", output)
	}
}

// TestPrefixedReader_SingleLine 测试单行输入
func TestPrefixedReader_SingleLine(t *testing.T) {
	input := "single line without newline"
	reader := strings.NewReader(input)
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("test", reader, writer)
	pr.Start()
	
	// 等待读取完成
	pr.Wait()
	
	output := writer.String()
	if !strings.Contains(output, input) {
		t.Errorf("期望输出包含输入内容，实际输出: %s", output)
	}
	if !strings.Contains(output, "[ProcessManager]") {
		t.Error("期望输出包含 [ProcessManager] 前缀")
	}
}

// TestPrefixedReader_LongLines 测试长行处理
func TestPrefixedReader_LongLines(t *testing.T) {
	// 创建一个超长行（超过默认缓冲区大小）
	longLine := strings.Repeat("a", 100000)
	reader := strings.NewReader(longLine)
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("test", reader, writer)
	pr.Start()
	
	// 等待读取完成
	pr.Wait()
	
	output := writer.String()
	if !strings.Contains(output, longLine) {
		t.Error("期望输出包含长行内容")
	}
}

// TestPrefixedReader_MultipleReaders 测试多个读取器
func TestPrefixedReader_MultipleReaders(t *testing.T) {
	input1 := "output from process 1"
	input2 := "output from process 2"

	reader1 := strings.NewReader(input1)
	reader2 := strings.NewReader(input2)
	
	// 使用独立的 writer 来避免竞态条件
	writer1 := &bytes.Buffer{}
	writer2 := &bytes.Buffer{}

	pr1 := NewPrefixedReader("process1", reader1, writer1)
	pr2 := NewPrefixedReader("process2", reader2, writer2)

	pr1.Start()
	pr2.Start()

	// 等待两个读取器完成
	pr1.Wait()
	pr2.Wait()

	output1 := writer1.String()
	output2 := writer2.String()
	
	if !strings.Contains(output1, input1) {
		t.Error("期望输出包含进程1的内容")
	}
	if !strings.Contains(output2, input2) {
		t.Error("期望输出包含进程2的内容")
	}
	if !strings.Contains(output1, "process1") {
		t.Error("期望输出包含进程1的名称")
	}
	if !strings.Contains(output2, "process2") {
		t.Error("期望输出包含进程2的名称")
	}
}

// TestPrefixedReader_PipeReader 测试管道读取器
func TestPrefixedReader_PipeReader(t *testing.T) {
	reader, writer := io.Pipe()
	output := &bytes.Buffer{}
	
	pr := NewPrefixedReader("pipe-test", reader, output)
	pr.Start()
	
	// 写入数据到管道
	testData := "test data from pipe"
	go func() {
		defer writer.Close()
		writer.Write([]byte(testData + "\n"))
	}()
	
	// 等待读取完成
	pr.Wait()
	
	outputStr := output.String()
	if !strings.Contains(outputStr, testData) {
		t.Errorf("期望输出包含管道数据，实际输出: %s", outputStr)
	}
}

// TestPrefixedReader_ClosedReader 测试关闭的读取器
func TestPrefixedReader_ClosedReader(t *testing.T) {
	reader, writer := io.Pipe()
	output := &bytes.Buffer{}
	
	// 立即关闭写入端
	writer.Close()
	
	pr := NewPrefixedReader("closed-test", reader, output)
	pr.Start()
	
	// 等待读取完成（应该很快完成）
	done := make(chan struct{})
	go func() {
		pr.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		// 正常完成
	case <-time.After(5 * time.Second):
		t.Error("读取器处理关闭的管道超时")
	}
}

// TestPrefixedReader_ConcurrentAccess 测试并发访问
func TestPrefixedReader_ConcurrentAccess(t *testing.T) {
	// 注意：原始实现不支持多次调用Start()，这里只测试单次启动的并发安全性
	reader := strings.NewReader("concurrent test")
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("concurrent", reader, writer)
	
	// 启动读取器
	pr.Start()
	
	// 等待读取完成
	pr.Wait()
	
	// 验证输出
	output := writer.String()
	if !strings.Contains(output, "concurrent test") {
		t.Error("期望输出包含测试内容")
	}
}

// TestPrefixedReader_EnvironmentVariables 测试环境变量处理
func TestPrefixedReader_EnvironmentVariables(t *testing.T) {
	// 测试没有环境变量的情况
	os.Unsetenv("paas_playground_id")
	os.Unsetenv("paas_docker_id")
	
	reader := strings.NewReader("test")
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("env-test", reader, writer)
	
	expectedPrefix := "playground_id=,docker_id=,process_name=env-test"
	if pr.prefix != expectedPrefix {
		t.Errorf("期望前缀为 %s，实际为 %s", expectedPrefix, pr.prefix)
	}
	
	// 测试有环境变量的情况
	os.Setenv("paas_playground_id", "test-pg")
	os.Setenv("paas_docker_id", "test-dk")
	defer func() {
		os.Unsetenv("paas_playground_id")
		os.Unsetenv("paas_docker_id")
	}()
	
	pr2 := NewPrefixedReader("env-test2", reader, writer)
	expectedPrefix2 := "playground_id=test-pg,docker_id=test-dk,process_name=env-test2"
	if pr2.prefix != expectedPrefix2 {
		t.Errorf("期望前缀为 %s，实际为 %s", expectedPrefix2, pr2.prefix)
	}
}

// TestPrefixedReader_SpecialCharacters 测试特殊字符处理
func TestPrefixedReader_SpecialCharacters(t *testing.T) {
	input := "line with 特殊字符 and émojis 🚀\nline with\ttabs\nline with    spaces"
	reader := strings.NewReader(input)
	writer := &bytes.Buffer{}
	
	pr := NewPrefixedReader("special", reader, writer)
	pr.Start()
	pr.Wait()
	
	output := writer.String()
	if !strings.Contains(output, "特殊字符") {
		t.Error("期望输出包含中文字符")
	}
	if !strings.Contains(output, "émojis") {
		t.Error("期望输出包含重音字符")
	}
	if !strings.Contains(output, "🚀") {
		t.Error("期望输出包含emoji")
	}
	if !strings.Contains(output, "\t") {
		t.Error("期望输出保留制表符")
	}
} 
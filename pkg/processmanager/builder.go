package processmanager

import (
	"agent/consts"
	"agent/pkg/browser"
	consts2 "agent/pkg/consts"
	"agent/utils/cmdUtils"
	"agent/utils/log"
	"fmt"
	"os"
	"os/exec"
	"time"
)

type ProcessStatusListener interface {
	UpdateVncStatus(status string)
}

func buildDBusSystem() *Process {
	dbus := NewProcess("DBUS-SYSTEM", "dbus-daemon", []string{
		"--system",
		"--nofork",
		"--address=unix:path=/run/dbus/system_bus_socket",
	}, "", 100)
	dbus.SetRestartTimeout(10 * time.Second) // DBUS可能需要更长的启动时间

	// 设置系统总线运行前检查
	dbus.SetBeforeRun(func() error {
		log.Infof("[ProcessManager] 检查并设置D-Bus系统总线环境...")

		// 创建必要的目录
		if err := os.MkdirAll("/run/dbus", 0755); err != nil {
			log.Infof("[ProcessManager] 创建 /run/dbus 目录失败: %v", err)
		}

		// 清理旧的系统总线文件
		if err := os.RemoveAll("/run/dbus/system_bus_socket"); err != nil {
			log.Infof("[ProcessManager] 清理旧的D-Bus系统总线文件失败: %v", err)
		}

		// 清理旧的PID文件 - 这是修复重启失败的关键
		if err := os.RemoveAll("/run/dbus/pid"); err != nil && !os.IsNotExist(err) {
			log.Infof("[ProcessManager] 清理旧的D-Bus PID文件失败: %v", err)
		}

		// 创建专用的运行时目录，设置正确的权限
		runtimeDir := "/tmp/xdg"
		if err := os.MkdirAll(runtimeDir, 0700); err != nil {
			log.Infof("[ProcessManager] 创建D-Bus系统运行时目录失败: %v", err)
		}

		// 设置系统总线环境变量
		os.Setenv("DBUS_SYSTEM_BUS_ADDRESS", "unix:path=/run/dbus/system_bus_socket")
		os.Setenv("XDG_RUNTIME_DIR", runtimeDir) // 使用专用目录

		log.Infof("[ProcessManager] D-Bus系统总线环境检查完成")
		return nil
	})

	return dbus
}

func buildDBusWithSession() *Process {
	// 添加 DBUS 进程 (无依赖)
	dbus := NewProcess("DBUS", "dbus-daemon", []string{
		"--session",
		"--nofork",
		"--address=unix:path=/tmp/dbus-session",
	}, "", 100)
	dbus.SetRestartTimeout(10 * time.Second) // DBUS可能需要更长的启动时间

	// 设置DBUS运行前检查
	dbus.SetBeforeRun(func() error {
		log.Infof("[ProcessManager] 检查并设置D-Bus环境...")

		// 创建专用的运行时目录，设置正确的权限
		runtimeDir := "/tmp/xdg"
		if err := os.MkdirAll(runtimeDir, 0700); err != nil {
			log.Infof("[ProcessManager] 创建D-Bus运行时目录失败: %v", err)
		}

		// 清理旧的D-Bus会话文件
		if err := os.RemoveAll("/tmp/dbus-session"); err != nil {
			log.Infof("[ProcessManager] 清理旧的D-Bus会话文件失败: %v", err)
		}

		// 设置环境变量
		os.Setenv("DBUS_SESSION_BUS_ADDRESS", "unix:path=/tmp/dbus-session")
		os.Setenv("XDG_RUNTIME_DIR", runtimeDir) // 使用专用目录

		log.Infof("[ProcessManager] D-Bus环境检查完成")
		return nil
	})

	return dbus
}

func buildTigerVNC() *Process {
	// 添加 TigerVNC 进程 (依赖DBUS)
	tigerVNC := NewProcess("TigerVNC", "vncserver", []string{
		":1", "-rfbport", consts.TigerVNCServerPort, "-geometry", "1920x1080",
		"-depth", "24", "-name", "TigerVNC", "-verbose", "-SecurityTypes", "None",
		"-AlwaysShared=1", "--I-KNOW-THIS-IS-INSECURE", "-fg",
	}, "DBUS", 100)
	tigerVNC.SetRestartTimeout(15 * time.Second) // VNC服务器需要更长的启动时间

	// 设置TigerVNC运行前检查
	tigerVNC.SetBeforeRun(func() error {
		log.Infof("[ProcessManager] 检查并设置TigerVNC环境...")

		// 清理旧的X11锁文件和套接字
		if err := os.Remove("/tmp/.X1-lock"); err != nil && !os.IsNotExist(err) {
			log.Infof("[ProcessManager] 清理X1锁文件失败: %v", err)
		}
		if err := os.Remove("/tmp/.X11-unix/X1"); err != nil && !os.IsNotExist(err) {
			log.Infof("[ProcessManager] 清理X1套接字失败: %v", err)
		}

		// 尝试清理可能残留的VNC进程
		cmd := exec.Command("pkill", "-f", "Xvnc.*:1")
		if err := cmd.Run(); err != nil {
			log.Infof("[ProcessManager] 清理旧VNC进程: %v", err)
		}

		// 等待一小段时间确保清理完成
		time.Sleep(500 * time.Millisecond)

		// 确保X11目录存在并有正确权限
		if err := os.MkdirAll("/tmp/.X11-unix", 01777); err != nil {
			return fmt.Errorf("创建X11目录失败: %v", err)
		}

		// 设置环境变量
		os.Setenv("DISPLAY", ":1")
		os.Setenv("XAUTHORITY", "/tmp/.Xauth")

		log.Infof("[ProcessManager] TigerVNC环境检查完成")
		return nil
	})

	return tigerVNC
}

func buildRFBProxy(ln ProcessStatusListener) *Process {
	// rfbproxy
	rfbproxy := NewProcess("Rfbproxy", "rfbproxy", []string{
		"--enable-audio", "--address=127.0.0.1:" + consts.VNCServerPort, "--rfb-server=127.0.0.1:" + consts.TigerVNCServerPort,
	}, "TigerVNC", 100)

	// 设置Rfbproxy运行前检查
	rfbproxy.SetBeforeRun(func() error {
		log.Infof("[ProcessManager] 检查并设置Rfbproxy环境...")

		// 设置音频环境
		os.Setenv("PULSE_SERVER", "unix:/tmp/pulseaudio.sock")

		// 设置Rust日志级别
		os.Setenv("RUST_LOG", "info")

		// 确保音频套接字目录存在
		if err := os.MkdirAll("/tmp", 0755); err != nil {
			log.Infof("[ProcessManager] 创建音频目录失败: %v", err)
		}

		log.Infof("[ProcessManager] Rfbproxy环境检查完成")
		return nil
	})

	rfbproxy.SetAfterStart(func() error {
		if ln != nil {
			ln.UpdateVncStatus(consts2.VncStatusRunning)
		}
		return nil
	})

	rfbproxy.SetAfterStop(func() error {
		if ln != nil {
			ln.UpdateVncStatus(consts2.VncStatusShutdown)
		}
		return nil
	})

	return rfbproxy
}

func buildChrome() *Process {
	args := browser.ChromeArgs()
	args = append(args, "--remote-debugging-port="+consts.ChromeDebugPort)

	chrome := NewProcess("Chrome", browser.DefaultChromePath, args, "DBUS", 100)
	chrome.SetRestartTimeout(5 * time.Second) // 减少Chrome重启超时时间从20秒到5秒

	// 设置Chrome运行前检查
	chrome.SetBeforeRun(func() error {
		log.Infof("[ProcessManager] 检查并设置Chrome环境...")

		// 确保显示环境已设置
		if os.Getenv("DISPLAY") == "" {
			os.Setenv("DISPLAY", ":1")
		}

		// 清理可能残留的Chrome进程
		cmd := exec.Command("pkill", "-f", "chrome.*--remote-debugging-port="+consts.ChromeDebugPort)
		if err := cmd.Run(); err != nil {
			log.Infof("[ProcessManager] 清理旧Chrome进程: %v", err)
		}

		// 清理Chrome调试端口
		cmd = exec.Command("fuser", "-k", consts.ChromeDebugPort+"/tcp")
		if err := cmd.Run(); err != nil {
			log.Infof("[ProcessManager] 清理Chrome调试端口: %v", err)
		}

		// 清理Chrome用户数据目录
		userDataDir := "/tmp/chrome-user-data"
		if err := os.RemoveAll(userDataDir); err != nil && !os.IsNotExist(err) {
			log.Warnf("[ProcessManager] 清理Chrome用户数据目录ERROR %s: %v", userDataDir, err)
		}

		// 等待一小段时间确保清理完成
		time.Sleep(200 * time.Millisecond)

		log.Infof("[ProcessManager] Chrome环境检查完成")
		return nil
	})

	return chrome
}

func BuildProcessManager(ln ProcessStatusListener) *ProcessManager {
	// 创建进程管理器
	pm := NewProcessManager()

	if !cmdUtils.LookPath("vncserver") {
		log.Infof("[ProcessManager] not add any process!")
		return pm
	}

	pm.AddProcess(buildDBusSystem())

	// 添加D-Bus会话总线（推荐用于桌面应用）
	pm.AddProcess(buildDBusWithSession())

	pm.AddProcess(buildTigerVNC())
	pm.AddProcess(buildRFBProxy(ln))
	pm.AddProcess(buildChrome())

	return pm
}

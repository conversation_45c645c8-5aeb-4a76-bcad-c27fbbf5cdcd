package processmanager

import (
	"agent/utils/log"
	"context"
	"os"
	"os/exec"
	"sync"
	"time"
)

// Process 表示一个进程及其元数据
type Process struct {
	Name           string            // 进程名称
	Cmd            *exec.Cmd         // 当前运行的命令
	Args           []string          // 命令参数
	MaxRestarts    int               // 最大重启次数
	Restarts       int               // 当前重启次数
	Executable     string            // 可执行文件路径
	DependsOn      string            // 依赖的进程名称
	RestartTimeout time.Duration     // 重启超时时间
	BeforeRun      func() error      // 运行前执行的检查函数
	AfterStart     func() error      // 启动完成后执行的回调函数
	AfterStop      func() error      // 进程停止后执行的回调函数
	stopChan       chan struct{}     // 通知监控协程停止
	readers        []*PrefixedReader // 跟踪所有的读取器
	mutex          sync.Mutex        // 保护Process中的可变字段
}

// ProcessConfig 进程配置选项
type ProcessConfig struct {
	Name           string        // 进程名称
	Executable     string        // 可执行文件路径
	Args           []string      // 命令参数
	DependsOn      string        // 依赖的进程名称
	MaxRestarts    int           // 最大重启次数
	RestartTimeout time.Duration // 重启超时时间
	BeforeRun      func() error  // 运行前检查函数
	AfterStart     func() error  // 启动完成后回调函数
	AfterStop      func() error  // 进程停止后回调函数
}

// 创建新的进程实例
func NewProcess(name, executable string, args []string, dependsOn string, maxRestarts int) *Process {
	return &Process{
		Name:           name,
		Executable:     executable,
		Args:           args,
		MaxRestarts:    maxRestarts,
		DependsOn:      dependsOn,
		RestartTimeout: 5 * time.Second, // 默认重启超时时间
		BeforeRun:      nil,             // 默认无运行前检查
		AfterStart:     nil,             // 默认无启动完成后回调
		AfterStop:      nil,             // 默认无进程停止后回调
		stopChan:       make(chan struct{}),
		readers:        make([]*PrefixedReader, 0),
		mutex:          sync.Mutex{},
	}
}

func (p *Process) CreateCommand(ctx context.Context) *exec.Cmd {
	// 使用主上下文来创建命令，确保能正确取消
	cmd := exec.CommandContext(ctx, p.Executable, p.Args...)

	// 继承当前进程的环境变量（环境变量应该在BeforeRun中设置）
	cmd.Env = os.Environ()

	// 创建管道来捕获标准输出和标准错误
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		log.Infof("[ProcessManager] 警告: 无法创建标准输出管道: %v\n", err)
		cmd.Stdout = os.Stdout
	} else {
		// 创建带前缀的输出读取器
		stdoutReader := NewPrefixedReader(p.Name, stdoutPipe, os.Stdout)
		stdoutReader.Start()
		p.mutex.Lock()
		p.readers = append(p.readers, stdoutReader)
		p.mutex.Unlock()
	}

	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		log.Infof("[ProcessManager] 警告: 无法创建标准错误管道: %v\n", err)
		cmd.Stderr = os.Stderr
	} else {
		// 创建带前缀的错误读取器
		stderrReader := NewPrefixedReader(p.Name, stderrPipe, os.Stderr)
		stderrReader.Start()
		p.mutex.Lock()
		p.readers = append(p.readers, stderrReader)
		p.mutex.Unlock()
	}

	p.Cmd = cmd

	return cmd
}

// 设置重启超时时间
func (p *Process) SetRestartTimeout(timeout time.Duration) {
	p.RestartTimeout = timeout
}

// 设置运行前检查函数
func (p *Process) SetBeforeRun(beforeRun func() error) {
	p.BeforeRun = beforeRun
}

// 设置启动完成后回调函数
func (p *Process) SetAfterStart(afterStart func() error) {
	p.AfterStart = afterStart
}

// 设置进程停止后回调函数
func (p *Process) SetAfterStop(afterStop func() error) {
	p.AfterStop = afterStop
}

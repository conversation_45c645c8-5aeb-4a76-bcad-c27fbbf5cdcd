package processmanager

import (
	"agent/pkg/browser"
	"os"
	"strings"
	"testing"
	"time"
)

// TestBuildDBusSystem 测试构建D-Bus系统总线进程
func TestBuildDBusSystem(t *testing.T) {
	process := buildDBusSystem()

	if process == nil {
		t.Fatal("期望构建的D-Bus系统进程不为nil")
	}

	// 验证基本属性
	if process.Name != "DBUS-SYSTEM" {
		t.<PERSON><PERSON><PERSON>("期望进程名称为 DBUS-SYSTEM，实际为 %s", process.Name)
	}

	if process.Executable != "dbus-daemon" {
		t.<PERSON>rrorf("期望可执行文件为 dbus-daemon，实际为 %s", process.Executable)
	}

	// 验证参数
	expectedArgs := []string{"--system", "--nofork", "--address=unix:path=/run/dbus/system_bus_socket"}
	if len(process.Args) != len(expectedArgs) {
		t.<PERSON>("期望参数数量为 %d，实际为 %d", len(expectedArgs), len(process.Args))
	}
	for i, arg := range expectedArgs {
		if i < len(process.Args) && process.Args[i] != arg {
			t.Errorf("期望参数[%d]为 %s，实际为 %s", i, arg, process.Args[i])
		}
	}

	// 验证依赖
	if process.DependsOn != "" {
		t.Errorf("期望D-Bus系统进程无依赖，实际依赖 %s", process.DependsOn)
	}

	// 验证重启配置
	if process.MaxRestarts != 100 {
		t.Errorf("期望最大重启次数为 100，实际为 %d", process.MaxRestarts)
	}

	if process.RestartTimeout != 10*time.Second {
		t.Errorf("期望重启超时为 10s，实际为 %v", process.RestartTimeout)
	}

	// 验证BeforeRun函数存在
	if process.BeforeRun == nil {
		t.Error("期望D-Bus系统进程有BeforeRun函数")
	}
}

// TestBuildDBusWithSession 测试构建D-Bus会话总线进程
func TestBuildDBusWithSession(t *testing.T) {
	process := buildDBusWithSession()

	if process == nil {
		t.Fatal("期望构建的D-Bus会话进程不为nil")
	}

	// 验证基本属性
	if process.Name != "DBUS" {
		t.Errorf("期望进程名称为 DBUS，实际为 %s", process.Name)
	}

	if process.Executable != "dbus-daemon" {
		t.Errorf("期望可执行文件为 dbus-daemon，实际为 %s", process.Executable)
	}

	// 验证参数
	expectedArgs := []string{"--session", "--nofork", "--address=unix:path=/tmp/dbus-session"}
	if len(process.Args) != len(expectedArgs) {
		t.Errorf("期望参数数量为 %d，实际为 %d", len(expectedArgs), len(process.Args))
	}
	for i, arg := range expectedArgs {
		if i < len(process.Args) && process.Args[i] != arg {
			t.Errorf("期望参数[%d]为 %s，实际为 %s", i, arg, process.Args[i])
		}
	}

	// 验证依赖
	if process.DependsOn != "" {
		t.Errorf("期望D-Bus会话进程无依赖，实际依赖 %s", process.DependsOn)
	}

	// 验证重启配置
	if process.MaxRestarts != 100 {
		t.Errorf("期望最大重启次数为 100，实际为 %d", process.MaxRestarts)
	}

	if process.RestartTimeout != 10*time.Second {
		t.Errorf("期望重启超时为 10s，实际为 %v", process.RestartTimeout)
	}

	// 验证BeforeRun函数存在
	if process.BeforeRun == nil {
		t.Error("期望D-Bus会话进程有BeforeRun函数")
	}
}

// TestBuildTigerVNC 测试构建TigerVNC进程
func TestBuildTigerVNC(t *testing.T) {
	process := buildTigerVNC()

	if process == nil {
		t.Fatal("期望构建的TigerVNC进程不为nil")
	}

	// 验证基本属性
	if process.Name != "TigerVNC" {
		t.Errorf("期望进程名称为 TigerVNC，实际为 %s", process.Name)
	}

	if process.Executable != "vncserver" {
		t.Errorf("期望可执行文件为 vncserver，实际为 %s", process.Executable)
	}

	// 验证关键参数
	argsStr := strings.Join(process.Args, " ")
	if !strings.Contains(argsStr, ":1") {
		t.Error("期望参数包含显示号 :1")
	}
	if !strings.Contains(argsStr, "-rfbport") {
		t.Error("期望参数包含 -rfbport")
	}
	if !strings.Contains(argsStr, "-geometry") {
		t.Error("期望参数包含 -geometry")
	}
	if !strings.Contains(argsStr, "1920x1080") {
		t.Error("期望参数包含分辨率 1920x1080")
	}

	// 验证依赖
	if process.DependsOn != "DBUS" {
		t.Errorf("期望TigerVNC依赖 DBUS，实际依赖 %s", process.DependsOn)
	}

	// 验证重启配置
	if process.MaxRestarts != 100 {
		t.Errorf("期望最大重启次数为 100，实际为 %d", process.MaxRestarts)
	}

	if process.RestartTimeout != 15*time.Second {
		t.Errorf("期望重启超时为 15s，实际为 %v", process.RestartTimeout)
	}

	// 验证BeforeRun函数存在
	if process.BeforeRun == nil {
		t.Error("期望TigerVNC进程有BeforeRun函数")
	}
}

// TestBuildRFBProxy 测试构建RFBProxy进程
func TestBuildRFBProxy(t *testing.T) {
	process := buildRFBProxy(nil)

	if process == nil {
		t.Fatal("期望构建的RFBProxy进程不为nil")
	}

	// 验证基本属性
	if process.Name != "Rfbproxy" {
		t.Errorf("期望进程名称为 Rfbproxy，实际为 %s", process.Name)
	}

	if process.Executable != "rfbproxy" {
		t.Errorf("期望可执行文件为 rfbproxy，实际为 %s", process.Executable)
	}

	// 验证关键参数
	argsStr := strings.Join(process.Args, " ")
	if !strings.Contains(argsStr, "--enable-audio") {
		t.Error("期望参数包含 --enable-audio")
	}
	if !strings.Contains(argsStr, "--address=") {
		t.Error("期望参数包含 --address")
	}
	if !strings.Contains(argsStr, "--rfb-server=") {
		t.Error("期望参数包含 --rfb-server")
	}

	// 验证依赖
	if process.DependsOn != "TigerVNC" {
		t.Errorf("期望Rfbproxy依赖 TigerVNC，实际依赖 %s", process.DependsOn)
	}

	// 验证重启配置
	if process.MaxRestarts != 100 {
		t.Errorf("期望最大重启次数为 100，实际为 %d", process.MaxRestarts)
	}

	// 验证BeforeRun函数存在
	if process.BeforeRun == nil {
		t.Error("期望Rfbproxy进程有BeforeRun函数")
	}
}

// TestBuildChrome 测试构建Chrome进程
func TestBuildChrome(t *testing.T) {
	process := buildChrome()

	if process == nil {
		t.Fatal("期望构建的Chrome进程不为nil")
	}

	// 验证基本属性
	if process.Name != "Chrome" {
		t.Errorf("期望进程名称为 Chrome，实际为 %s", process.Name)
	}

	if process.Executable != "/usr/chrome-linux64/chrome" {
		t.Errorf("期望可执行文件为 /usr/chrome-linux64/chrome，实际为 %s", process.Executable)
	}

	// 验证关键参数
	argsStr := strings.Join(process.Args, " ")
	if !strings.Contains(argsStr, "--remote-debugging-port=") {
		t.Error("期望参数包含 --remote-debugging-port")
	}

	// 验证依赖
	if process.DependsOn != "DBUS" {
		t.Errorf("期望Chrome依赖 DBUS，实际依赖 %s", process.DependsOn)
	}

	// 验证重启配置
	if process.MaxRestarts != 100 {
		t.Errorf("期望最大重启次数为 100，实际为 %d", process.MaxRestarts)
	}

	if process.RestartTimeout != 5*time.Second {
		t.Errorf("期望重启超时为 5s，实际为 %v", process.RestartTimeout)
	}

	// 验证BeforeRun函数存在
	if process.BeforeRun == nil {
		t.Error("期望Chrome进程有BeforeRun函数")
	}
}

// TestBuildProcessManager 测试构建进程管理器
func TestBuildProcessManager(t *testing.T) {
	pm := BuildProcessManager(nil)

	if pm == nil {
		t.Fatal("期望构建的进程管理器不为nil")
	}

	// 注意：如果vncserver不存在，进程管理器可能为空，这是正常的
	// 验证进程数量（根据builder.go的实现）
	// if len(pm.processes) == 0 {
	//     t.Error("期望进程管理器包含进程")
	// }

	// 验证进程名称的唯一性
	nameSet := make(map[string]bool)
	for _, p := range pm.processes {
		if nameSet[p.Name] {
			t.Errorf("发现重复的进程名称: %s", p.Name)
		}
		nameSet[p.Name] = true
	}

	// 验证依赖关系的有效性
	for _, p := range pm.processes {
		if p.DependsOn != "" {
			found := false
			for _, dep := range pm.processes {
				if dep.Name == p.DependsOn {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("进程 %s 依赖的进程 %s 不存在", p.Name, p.DependsOn)
			}
		}
	}
}

// TestDBusSystemBeforeRun 测试D-Bus系统进程的BeforeRun函数
func TestDBusSystemBeforeRun(t *testing.T) {
	process := buildDBusSystem()

	if process.BeforeRun == nil {
		t.Fatal("期望D-Bus系统进程有BeforeRun函数")
	}

	// 执行BeforeRun函数
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("D-Bus系统BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("DBUS_SYSTEM_BUS_ADDRESS") == "" {
		t.Error("期望设置DBUS_SYSTEM_BUS_ADDRESS环境变量")
	}

	if os.Getenv("XDG_RUNTIME_DIR") == "" {
		t.Error("期望设置XDG_RUNTIME_DIR环境变量")
	}
}

// TestDBusSessionBeforeRun 测试D-Bus会话进程的BeforeRun函数
func TestDBusSessionBeforeRun(t *testing.T) {
	process := buildDBusWithSession()

	if process.BeforeRun == nil {
		t.Fatal("期望D-Bus会话进程有BeforeRun函数")
	}

	// 执行BeforeRun函数
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("D-Bus会话BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("DBUS_SESSION_BUS_ADDRESS") == "" {
		t.Error("期望设置DBUS_SESSION_BUS_ADDRESS环境变量")
	}

	if os.Getenv("XDG_RUNTIME_DIR") == "" {
		t.Error("期望设置XDG_RUNTIME_DIR环境变量")
	}
}

// TestTigerVNCBeforeRun 测试TigerVNC进程的BeforeRun函数
func TestTigerVNCBeforeRun(t *testing.T) {
	process := buildTigerVNC()

	if process.BeforeRun == nil {
		t.Fatal("期望TigerVNC进程有BeforeRun函数")
	}

	// 执行BeforeRun函数
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("TigerVNC BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("DISPLAY") == "" {
		t.Error("期望设置DISPLAY环境变量")
	}

	if os.Getenv("XAUTHORITY") == "" {
		t.Error("期望设置XAUTHORITY环境变量")
	}
}

// TestRFBProxyBeforeRun 测试RFBProxy进程的BeforeRun函数
func TestRFBProxyBeforeRun(t *testing.T) {
	process := buildRFBProxy(nil)

	if process.BeforeRun == nil {
		t.Fatal("期望RFBProxy进程有BeforeRun函数")
	}

	// 执行BeforeRun函数
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("RFBProxy BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("PULSE_SERVER") == "" {
		t.Error("期望设置PULSE_SERVER环境变量")
	}

	if os.Getenv("RUST_LOG") == "" {
		t.Error("期望设置RUST_LOG环境变量")
	}
}

// TestChromeBeforeRun 测试Chrome进程的BeforeRun函数
func TestChromeBeforeRun(t *testing.T) {
	process := buildChrome()

	if process.BeforeRun == nil {
		t.Fatal("期望Chrome进程有BeforeRun函数")
	}

	// 执行BeforeRun函数
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("Chrome BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("DISPLAY") == "" {
		t.Error("期望设置DISPLAY环境变量")
	}
}

// TestProcessDependencyChain 测试进程依赖链
func TestProcessDependencyChain(t *testing.T) {
	pm := BuildProcessManager(nil)

	// 构建依赖图
	dependencyMap := make(map[string]string)
	for _, p := range pm.processes {
		dependencyMap[p.Name] = p.DependsOn
	}

	// 验证没有循环依赖
	for _, p := range pm.processes {
		visited := make(map[string]bool)
		current := p.Name

		for current != "" {
			if visited[current] {
				t.Errorf("检测到循环依赖，涉及进程: %s", current)
				break
			}
			visited[current] = true
			current = dependencyMap[current]
		}
	}
}

// TestProcessConfiguration 测试进程配置的一致性
func TestProcessConfiguration(t *testing.T) {
	processes := []*Process{
		buildDBusSystem(),
		buildDBusWithSession(),
		buildTigerVNC(),
		buildRFBProxy(nil),
		buildChrome(),
	}

	for _, p := range processes {
		// 验证基本配置
		if p.Name == "" {
			t.Error("进程名称不能为空")
		}

		if p.Executable == "" {
			t.Error("可执行文件路径不能为空")
		}

		if p.MaxRestarts <= 0 {
			t.Errorf("进程 %s 的最大重启次数应该大于0", p.Name)
		}

		if p.RestartTimeout <= 0 {
			t.Errorf("进程 %s 的重启超时时间应该大于0", p.Name)
		}

		// 验证停止通道已初始化
		if p.stopChan == nil {
			t.Errorf("进程 %s 的停止通道未初始化", p.Name)
		}

		// 验证读取器列表已初始化
		if p.readers == nil {
			t.Errorf("进程 %s 的读取器列表未初始化", p.Name)
		}
	}
}

func TestChromeRestartOptimization(t *testing.T) {
	process := buildChrome()

	// 验证重启超时时间已优化
	expectedTimeout := 5 * time.Second
	if process.RestartTimeout != expectedTimeout {
		t.Errorf("期望Chrome重启超时为 %v，实际为 %v", expectedTimeout, process.RestartTimeout)
	}

	// 验证BeforeRun函数存在且可以执行
	if process.BeforeRun == nil {
		t.Fatal("期望Chrome进程有BeforeRun函数")
	}

	// 执行BeforeRun函数测试清理逻辑
	err := process.BeforeRun()
	if err != nil {
		t.Errorf("Chrome BeforeRun执行失败: %v", err)
	}

	// 验证环境变量设置
	if os.Getenv("DISPLAY") == "" {
		t.Error("期望设置DISPLAY环境变量")
	}
}

func TestChromeArgsOptimization(t *testing.T) {
	args := browser.ChromeArgs()

	// 验证包含加速启动的参数
	speedupArgs := []string{
		"--disable-extensions",
		"--disable-plugins",
		"--disable-sync",
		"--disable-background-mode",
		"--aggressive-cache-discard",
		"--memory-pressure-off",
	}

	argsStr := strings.Join(args, " ")
	for _, arg := range speedupArgs {
		if !strings.Contains(argsStr, arg) {
			t.Errorf("期望Chrome参数包含加速启动参数: %s", arg)
		}
	}
}

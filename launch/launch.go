package launch

import (
	"agent/config"
	"agent/consts"
	"agent/file/watch"
	"agent/httpProxy"
	"agent/httpserver"
	"agent/infoHolder"
	"agent/linters"
	"agent/ls"
	"agent/middware_proxy"
	"agent/mq"
	"agent/multiTerminal"
	terminal2 "agent/multiTerminal/terminal"
	"agent/pkg/processmanager"
	"agent/proxyserver"
	"agent/signalWatcher"
	"agent/signalWatcher/monitor"
	"agent/utils/log"
	"context"
	"os"
	"reflect"
	"time"
)

func Launch(ch chan int) {
	//go psUtils.AlwaysPrintCpuAndMem()

	log.Debugf("Launch start")
	log.Debugf("Launch Config")
	// 读取配置文件中的环境变量
	localCfg, err := config.LoadEnvConfig()
	if err == nil && localCfg.Env != nil {
		for k, v := range localCfg.Env {
			e := os.Setenv(k, v)
			if e != nil {
				log.Errorf("err:%s", e)
			}
		}
	}
	os.Setenv("UPM_PYTHON_PYPI", "http://mirrors.tencentyun.com/pypi/simple/")
	os.Setenv("DISPLAY", ":1")
	os.Setenv("RESOLUTION", "1920x1080x24")
	os.Setenv("PULSE_SERVER", "unix:/tmp/pulseaudio.sock")
	os.Setenv("NIXPKGS_ALLOW_INSECURE", "1")
	if err = os.Chdir(consts.AppRootDir); err != nil {
		log.Errorf("os.Chdir error %s", err.Error())
	}
	// 文件监视，MQ之间通道
	mqToFwChannel := make(chan int)
	fwToMqChannel := make(chan string)

	// 多终端channel
	mqToMultiTerminalChannel := make(chan terminal2.TerminalToMqItem)
	multiTerminalToMqObj := make(chan terminal2.TerminalToMqItem, 50)
	mqToMultiTerminalCmdChannel := make(chan string)
	multiTerminalToMQCmdChannel := make(chan terminal2.TerminalToMqItem, 50)
	mqToMultiTerminalConsoleChannel := make(chan string)
	multiConsoleToMqChannel := make(chan string)

	// 文件变更通道
	fileChangeChannel := make(chan []watch.FileChange)

	// httpProxy，MQ通道
	httpProxyToMqChannel := make(chan string)
	mqToHttpProxyChannel := make(chan string)
	//触发httpProxy通道
	httpProxyHookChannel := make(chan string)

	// 监控环境变量发生变化
	middlewareProxyChannel := make(chan string)

	// rag, MQ之间的内容通道
	//mqToRagSearchChannel := make(chan message.RagSearchMQMsg)
	//ragSearchToMqChannel := make(chan message.RagSearchResultMQMsg)
	//ragStatusToMqChannel := make(chan message.RagStatusMQMsg)
	//fileChangeRagChannel := make(chan []watch.FileChange)

	linterFileChangesChan := make(chan []watch.FileChange, 100)

	log.Debugf("Launch Config End")
	log.Debugf("Launch File Watch Start")
	// 启动文件监视
	// TODO: 监控 .1024
	// 监视配置文件中ENV部分的变化
	fileWatch := watch.MakeNew(fileChangeChannel, nil, linterFileChangesChan, &watch.RefreshConfig{Refresh: true, IntervalTime: 0})
	c := watch.Config{}
	c.Entries = append(c.Entries, watch.WatchEntry{
		Directory: consts.AppRootDir,
	})
	//go fileWatch.Watch(context.Background(), c, time.Millisecond*500)
	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("fileWatch panic error: %+v", panicErr)
			}
		}()
		fileWatch.OSWatch(context.Background(), c, time.Millisecond*500)
	}()

	log.Debugf("Launch File Watch End")
	log.Debugf("Launch MQ start")
	// 启动MQ
	mqWrapper := mq.MakeNew(
		mqToFwChannel,
		fwToMqChannel,
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMQCmdChannel,
		multiConsoleToMqChannel,
		consts.LspReStartChannel,
		fileChangeChannel,
		fileWatch,
		//mqToRagSearchChannel,
		//ragSearchToMqChannel,
		//ragStatusToMqChannel,
		//fileChangeRagChannel,
		httpProxyToMqChannel,
		mqToHttpProxyChannel,
		httpProxyHookChannel,
		middlewareProxyChannel,
	)
	mqErr := mqWrapper.Start()
	if mqErr != nil {
		log.Fatalf("Connect to mq fail: %s", mqErr)
	}
	log.Debugf("Launch MQ End")
	log.Debugf("Launch Terminal start")

	// Initialize the multi-terminal system
	if err := multiTerminal.Init(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMQCmdChannel,
		multiConsoleToMqChannel); err != nil {
		log.Errorf("Failed to initialize multi-terminal system: %v", err)
	}

	log.Debugf("Launch Terminal end")
	log.Debugf("Launch rag start")

	//// 初始化rag
	//ragWrapper := rag.MakeNew(
	//	fileChangeRagChannel,
	//	mqToRagSearchChannel,
	//	ragSearchToMqChannel,
	//	ragStatusToMqChannel)
	//
	//ragWrapper.Open()
	//log.Debugf("Launch rag end")
	//log.Debugf("Launch other start")

	// 初始化DockerInfoHolder
	infoHolder.InitHolder()

	lsWrapper := ls.MakeNew(consts.LspReStartChannel)
	// 启动LSP。
	lsWrapper.Init(mqWrapper)

	httpProxyWrapper := httpProxy.MakeNew(httpProxyToMqChannel, mqToHttpProxyChannel, httpProxyHookChannel)
	//启动httpProxy
	httpProxyWrapper.Open()

	// 启动后推送dockerStatus
	mqWrapper.PublishDockerInfoToManager("")

	go mqWrapper.PublishConfig()
	// 监听配置文件变更
	go configurationFileChange([]func(last, current *config.Config){})

	// 初始化中间件代理服务
	go func() {
		log.Debugf("Launch middware_proxy start")
		middware_proxy.GetInstance()
		middware_proxy.UpdateMiddlewareEnv(middlewareProxyChannel)

		log.Debugf("Launch middware_proxy end")
	}()

	// 初始化并启动SSH监控器
	go func() {
		sshMonitor := monitor.NewSSHMonitor(30*time.Second, mqWrapper)
		sshMonitor.StartMonitoring()
	}()

	go func() {
		// 启动自身服务
		httpserver.New(":" + consts.AgentServerPort).Start()
	}()

	//启动caddy proxy
	go proxyserver.StartProxyServer()

	manager := processmanager.BuildProcessManager(mqWrapper)
	go func() {
		// 启动所有进程
		if err := manager.StartAll(); err != nil {
			log.Println("[ProcessManager] 启动进程失败:", err)
		}

		// 等待所有进程结束
		log.Println("[ProcessManager] 所有进程已启动，等待它们结束...")
		manager.Wait()
	}()

	go func() {
		// 检查老项目是否安装了相关linter插件
		linters.GetEventManager().DispatchEvent(linters.EventTypeContainerStart, "")
		// 有文件内容发生变化时，检查是否动态安装相关linter插件
		linters.LinterHandleFileChanges(linterFileChangesChan)
	}()

	log.Debugf("Launch other end")
	// 启动后监听停止信号
	signalWatcher.SyscallListener(mqWrapper, manager)
	<-ch
}

func configurationFileChange(callbacks []func(last, current *config.Config)) {
	var last *config.Config
	var current *config.Config
	for {
		func() {
			config, err := config.LoadEnvConfig()
			if err != nil || reflect.DeepEqual(current, config) {
				return
			}
			last = current
			current = config
			for _, callback := range callbacks {
				callback(last, current)
			}
		}()
		time.Sleep(time.Second * 3)
	}
}

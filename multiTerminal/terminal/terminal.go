package terminal

import (
	"agent/config"
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v4/process"

	"github.com/creack/pty"
	"golang.org/x/time/rate"
)

type TerminalWrapper struct {
	TerminalId                          string
	Name                                string
	Sort                                int32
	CreateAt                            time.Time
	multiTerminalToMqObj                chan<- TerminalToMqItem
	multiTerminalToManager              chan<- string
	MultiTerminalProcessNameToMqChannel chan string
	Cmd                                 *exec.Cmd
	Tty                                 *os.File
	env                                 string
	terminalStatus                      string
	isInitialized                       bool
	terminalType                        string
	bashShowBuffer                      strings.Builder
	promptIsReady                       bool
}

type ColsAndRowsValueMQMsg struct {
	Cols int `json:"cols"`
	Rows int `json:"rows"`
}

const (
	syncFile = "/tmp/MyPipe"
)

func (w *TerminalWrapper) GetTerminalType() string {
	return w.terminalType
}

func (w *TerminalWrapper) getSyncFile() string {
	return fmt.Sprintf("%s-%s", syncFile, w.TerminalId)
}

func (w *TerminalWrapper) GetTerminalId() string {
	return w.TerminalId
}

func (w *TerminalWrapper) GetName() string {
	return w.Name
}

func (w *TerminalWrapper) SendMultiTerminalToMqObj(cmd string) {
	if w.TerminalId == "" {
		log.Errorf("MultiTerminal, SendMultiTerminalToMqObj, cmd: %s", cmd)
		return
	}

	item := TerminalToMqItem{
		TerminalId:   w.TerminalId,
		Cmd:          consts.MQ_TERMINAL_PREFIX_PROCESSNAME,
		Content:      cmd,
		TerminalType: w.terminalType,
	}
	w.multiTerminalToMqObj <- item
}

// GetStatus returns the current status of the terminal
func (w *TerminalWrapper) GetStatus() string {
	return w.terminalStatus
}

// GetPromptIsReady returns the current promptIsReady of the terminal
func (w *TerminalWrapper) GetPromptIsReady() bool {
	return w.promptIsReady
}

// UpdateStatus updates the terminal status and notifies of the change
func (w *TerminalWrapper) UpdateStatus(newStatus string) {
	w.terminalStatus = newStatus
	w.NotifyStatusChange()
}

// NotifyStatusChange sends a status change notification to the message queue
func (w *TerminalWrapper) NotifyStatusChange() {
	item := TerminalToMqItem{
		TerminalId:   w.TerminalId,
		Cmd:          consts.MQ_TERMINAL_PREFIX_STATUS,
		Content:      w.terminalStatus,
		TerminalType: w.terminalType,
	}

	if w.TerminalId == "" {
		log.Errorf("MultiTerminal, NotifyStatusChange, item: %+v", item)
		return
	}

	w.multiTerminalToMqObj <- item
}

// MakeNew creates a new Wrapper instance
func MakeNew(
	terminalId string,
	name string,
	multiTerminalToMqObj chan<- TerminalToMqItem,
	multiTerminalToManager chan<- string,
	terminalType string,
	sort int32) *TerminalWrapper {

	wrapper := &TerminalWrapper{}
	wrapper.multiTerminalToMqObj = multiTerminalToMqObj
	wrapper.multiTerminalToManager = multiTerminalToManager
	wrapper.MultiTerminalProcessNameToMqChannel = make(chan string)
	wrapper.TerminalId = terminalId
	wrapper.Name = name
	wrapper.terminalType = terminalType
	wrapper.CreateAt = time.Now()
	wrapper.Sort = sort
	wrapper.bashShowBuffer = strings.Builder{}
	wrapper.promptIsReady = false

	return wrapper
}

func (wrapper *TerminalWrapper) Open() {
	// 启动terminal
	go func() {
		// 使用 defer 和 recover 来捕获 panic，确保程序不会因为 panic 而崩溃
		defer func() {
			// 检查是否有 panic 发生
			if panicErr := recover(); panicErr != nil {
				// 记录 panic 错误信息到日志中
				log.PrintPanicInfo("terminalWrapper execTTY panic error: %+v", panicErr)
			}
		}()
		// 在 goroutine 中执行 TTY 终端操作
		wrapper.execTTY()
	}()
}

func (wrapper *TerminalWrapper) execTTY() {
	// 设置终端状态为加载中
	wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_STATUS, TerminalStatusLoading)
	// 初始化错误计数器
	errorCount := 0
	// 设置终端状态为加载中
	wrapper.terminalStatus = TerminalStatusLoading

	// 无限循环，用于重启终端
	for {
		// 获取shell命令（从环境变量中读取），默认为bash，如果环境变量中没有，则使用默认值
		paasShellCmd := envUtils.GetString(consts.PAAS_Shell_Cmd)
		// 是否需要等待初始化完成的标志
		isNeedWaitInit := false

		// 生成shell命令
		wrapper.Cmd = exec.Command(paasShellCmd)
		// 记录终端启动命令的日志
		// MultiTerminal >> terminal start cmd.bash, terminalId(2b3cfbf7-af15-4603-9430-f44991d11ba0)
		log.Printf("MultiTerminal >> %s.%s, terminalId(%s)\n", "terminal start cmd", paasShellCmd, wrapper.TerminalId)

		// 运行命令并获取伪终端
		var err error
		// 使用"github.com/creack/pty"库创建一个基于bash(paasShellCmd)的伪终端，返回一个*os.File类型的TTY
		wrapper.Tty, err = pty.Start(wrapper.Cmd)
		if err != nil {
			// 如果启动失败，记录错误并返回
			log.Errorf("MultiTerminal >> %s.%s, terminalId(%s)", "Terminal start fail", err, wrapper.TerminalId)
			return
		}

		// 创建终端窗口大小结构
		ws := new(pty.Winsize)
		// 设置终端宽度为56列
		ws.Cols = 56
		// 设置终端高度为256行
		ws.Rows = 256

		// 设置终端窗口大小
		pty.Setsize(wrapper.Tty, ws)
		// isNeedWaitInit 如果为true，则需要等待初始化完成才能发送
		log.Printf("MultiTerminal >> terminal start OK, terminalId: %s, isNeedWaitInit: %v, terminalId: %s\n",
			wrapper.TerminalId, isNeedWaitInit, wrapper.TerminalId)
		if isNeedWaitInit {
			wrapper.readPipe()
			log.Printf("MultiTerminal >> Current Initialized: %v, terminalId: %s\n", wrapper.isInitialized, wrapper.TerminalId)
			// 等待init完成
			if wrapper.isInitialized {
				wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_CONTENT, consts.NEW_LINE_SIGN)
				wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_STATUS, TerminalStatusRunning)
				wrapper.terminalStatus = TerminalStatusRunning
			}
		} else {
			wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_CONTENT, consts.NEW_LINE_SIGN)
			wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_STATUS, TerminalStatusRunning)
			wrapper.terminalStatus = TerminalStatusRunning
		}

		// 开启命令监控，处理x03类退出信号
		go wrapper.monitorTerminalProcess()

		// #### 开始接收Terminal返回的数据
		go wrapper.receiveFromTerminal()
		log.Printf("MultiTerminal >> terminal start success, terminalId: %s\n", wrapper.TerminalId)

		err = wrapper.Cmd.Wait()
		if err != nil {
			log.Printf("MultiTerminal >> Terminal Wait fail, currentCount:[%d], errorMsg: %s, terminal:%s\n", errorCount, err, wrapper.TerminalId)
		}
		err = wrapper.Tty.Close()
		if err != nil {
			log.Printf("MultiTerminal >> %s.%s, terminalId(%s)\n", "Terminal close fail", err, wrapper.TerminalId)
			return
		}

		errorCount++
		if errorCount > 50 {
			log.Errorf("MultiTerminal >> %s.%s, terminalId(%s)", "Terminal exit", err, wrapper.TerminalId)
			wrapper.SetTerminal(consts.MQ_TERMINAL_PREFIX_STATUS, TerminalStatusStop)
			wrapper.terminalStatus = TerminalStatusStop
			break
		}
	}
}

// monitorTerminalProcess 监控终端进程行为
// monitorTerminalProcess 监控终端进程行为
func (wrapper *TerminalWrapper) monitorTerminalProcess() {
	// 无限循环，持续监控终端进程
	for {
		// 使用select语句监听通道
		select {
		// 从MultiTerminalProcessNameToMqChannel通道接收进程名称值，x03类退出信号
		case value := <-wrapper.MultiTerminalProcessNameToMqChannel:
			// 调用terminalShowName方法显示终端名称
			wrapper.terminalShowName(value)
			// 启动一个新的goroutine来监控终端子进程
			go func() {
				// 调用monitorTerminalSubProcess方法监控子进程
				wrapper.monitorTerminalSubProcess(value)
			}()
		}
	}
}

// 监控终端子进程
// monitorTerminalSubProcess 监控终端子进程的函数
func (wrapper *TerminalWrapper) monitorTerminalSubProcess(value string) {
	// 如果传入的值为空，直接返回
	if value == "" {
		return
	}

	// 初始化Ctrl+C标志为false
	isCtrlC := false
	// 遍历输入字符串的每个字符
	for _, r := range value {
		// 检查字符是否为Ctrl+C（ASCII码0x03）
		// \x03 对应 ASCII 码的十进制值 ​3，表示 ​ETX（End of Text）​，即“文本结束符”
		if r == '\x03' {
			//log.Printf("MultiTerminal-monitorTerminalSubProcess, value: %s", value)
			// 如果检测到Ctrl+C，设置标志为true并跳出循环
			isCtrlC = true
			break
		}
	}

	// 如果没有检测到Ctrl+C，直接返回
	if isCtrlC == false {
		return
	}

	// 如果终端类型不是GoAgent类型，直接返回
	if wrapper.terminalType != TerminalTypeGoAgent {
		return
	}

	// 获取当前命令进程的PID
	pid := wrapper.Cmd.Process.Pid
	// 根据PID创建进程对象
	p, err := process.NewProcess(int32(pid))
	if err != nil {
		// 如果创建进程对象失败，打印错误信息并返回
		fmt.Printf("MultiTerminal>> Failed to get process: %v\n", err)
		return
	}

	// 获取当前进程的所有子进程
	children, err := p.Children()
	// 如果获取子进程成功且存在子进程
	if err == nil && len(children) > 0 {
		// 遍历每个子进程
		for _, child := range children {
			// 获取子进程的PID
			childPid := child.Pid
			// 获取子进程的名称
			childName, err1 := child.Name()
			// 如果获取进程名称失败，设置为"unknown"
			if err1 != nil {
				childName = "unknown"
			}

			// 根据PID查找进程
			proc, err2 := os.FindProcess(int(childPid))
			if err2 != nil {
				// 如果查找进程失败，打印错误信息并继续下一个进程
				log.Printf("MultiTerminal>> Failed to find process %d: %v", pid, err2)
				continue
			}

			// 强制杀死进程
			if err3 := proc.Kill(); err3 != nil {
				// 如果杀死进程失败，打印错误信息并继续下一个进程
				log.Printf("MultiTerminal>> Failed to send SIGKILL to %d: %v", childPid, err3)
				continue
			}
			// 等待进程结束
			if procState, err3 := proc.Wait(); err3 != nil {
				// 如果等待进程结束失败，打印错误信息
				log.Printf("MultiTerminal>> Failed to kill process %d (%s), proState: %+v, err: %v", childPid, childName, procState, err3)
			}

			//log.Printf("MultiTerminal>> Successfully killed process %d (%s)", childPid, childName)
		}
	}

	// 先kill掉在发送状态到前端将stop按钮状态更新到run
	// 使用select语句发送终端ID到管理器通道，设置1秒超时
	select {
	case wrapper.multiTerminalToManager <- wrapper.TerminalId:
	case <-time.After(time.Second):
	}
}

// terminalShowName bash tab上展示执行命令的名称
func (wrapper *TerminalWrapper) terminalShowName(value string) {
	// 需要过滤的字符
	if strings.Contains(value, "\u0003") {
		return
	}

	// 检查是否包含换行符
	isSend := false
	splitStr := ""
	if strings.Contains(value, "\r\n") {
		isSend = true
		splitStr = "\r\n"
	} else if strings.Contains(value, "\n") {
		isSend = true
		splitStr = "\n"
	} else if strings.Contains(value, "\r") {
		isSend = true
		splitStr = "\r"
	} else {
		// 用户删除终端字符需要回退
		if value != "" {
			isDel := false
			for _, r := range value {
				if r == '\x7f' {
					isDel = true
				}
			}

			if isDel {
				vl := wrapper.bashShowBuffer.String()
				if len(vl) > 0 {
					wrapper.bashShowBuffer.Reset()
					bl := vl[:len(vl)-1]
					wrapper.bashShowBuffer.WriteString(bl)
				}
			} else {
				wrapper.bashShowBuffer.WriteString(value)
			}
		}
	}

	//log.Printf("terminalShowName, cmdname: %s, isSend: %v, splitStr: %s, value: %s", wrapper.bashShowBuffer.String(), isSend, splitStr, value)

	// splitStr: '\r'，'\n', '\r\n'
	if isSend && splitStr != "" {
		if value != splitStr {
			parts := strings.Split(value, splitStr)
			if parts[0] != "" {
				wrapper.bashShowBuffer.WriteString(parts[0])
			}
		}

		cmdName := wrapper.bashShowBuffer.String()
		if cmdName != "" {
			item := NewTerminalToMqItem(wrapper.TerminalId, consts.MQ_TERMINAL_PREFIX_PROCESSNAME,
				cmdName, wrapper.terminalType, "", 0)

			select {
			case wrapper.multiTerminalToMqObj <- item:
			case <-time.After(time.Second):
				//log.Printf("MultiTerminal-monitorTerminalExecProcessName>> timeout, terminalId: %s", wrapper.TerminalId)
			}
		}
		wrapper.bashShowBuffer.Reset()
	}
}

func (wrapper *TerminalWrapper) OnFileChange(filePath string) {
	// 当环境变量变化时，重启terminal
	if config.GetConfigPath() != filePath {
		return
	}

	config, err := config.LoadEnvConfig()
	if err != nil || config.Env == nil {
		return
	}

	// env无变化时不需要重新设置
	newEnv, _ := json.Marshal(config.Env)
	if err == nil && string(newEnv) == wrapper.env {
		return
	}

	// TODO: 如果用户在terminal用vi修改，直接重启体验不好。发送消息给前端，由前端引导用户重启
	// 重启terminal
	//wrapper.cmd.Process.Kill()
}

// createPipe 创建管道，等待NixShell通知
func (wrapper *TerminalWrapper) createPipe() bool {
	wrapper.isInitialized = false
	return true
}

// readPipe 阻塞管道，等NixShell执行完成后才继续后续任务:发送Running
func (wrapper *TerminalWrapper) readPipe() {
	sFile := wrapper.getSyncFile()
	//log.Printf("MultiTerminal >> Reading pipe file: %s, terminalId: %s", sFile, wrapper.TerminalId)
	data, err := os.ReadFile(sFile)
	if err != nil {
		log.Infof("MultiTerminal >> Failed to read sync pipe file: %v, terminalId: %s", err, wrapper.TerminalId)
	}

	ticker := time.NewTicker(50 * time.Millisecond)
	defer ticker.Stop()

	for string(data) == "" {
		<-ticker.C

		data, err = os.ReadFile(sFile)
		if err != nil {
			log.Infof("MultiTerminal >> Failed to read sync pipe file: %v, terminalId: %s", err, wrapper.TerminalId)
			break
		}
	}

	wrapper.isInitialized = true
	err = os.Remove(sFile)
	if err != nil {
		log.Infof("MultiTerminal >> Failed to remove sync pipe file: %v, terminalId: %s", err, wrapper.TerminalId)
	}
	//log.Printf("MultiTerminal >> Finish pipe file: %s, terminalId: %s", sFile, wrapper.TerminalId)
}

func (wrapper *TerminalWrapper) SetTtySize(cols int, rows int) {
	log.Printf("MultiTerminal >> setTtySize,cols:%d,rows:%d, terminalId: %s", cols, rows, wrapper.TerminalId)
	ws := new(pty.Winsize)
	ws.Cols = uint16(cols)
	ws.Rows = uint16(rows)

	pty.Setsize(wrapper.Tty, ws)
}

func (wrapper *TerminalWrapper) SetTerminalSort(sort int32) {
	wrapper.Sort = sort
}

func (wrapper *TerminalWrapper) SetTerminal(cmd, value string) {
	item := TerminalToMqItem{
		TerminalId:   wrapper.TerminalId,
		Cmd:          cmd,
		Content:      value,
		TerminalType: wrapper.terminalType,
		TerminalCmd:  "",
		Sort:         wrapper.Sort,
	}

	if wrapper.TerminalId == "" {
		log.Errorf("MultiTerminal, SetTerminal, item: %+v", item)
		return
	}

	wrapper.multiTerminalToMqObj <- item
}

// getSetStatus returns the current status of the terminal
func (wrapper *TerminalWrapper) GetSetStatus() string {
	return wrapper.terminalStatus
}

func (wrapper *TerminalWrapper) GetTerminalStatus() string {
	if wrapper == nil {
		return TerminalStatusStop
	}
	return wrapper.terminalStatus
}

// ！！！TTY终端回显示流程：确认这里block有没有回显？
// 将TTY终端的输出数据发送到消息队列multiTerminalToMqObj
func (wrapper *TerminalWrapper) receiveFromTerminal() {
	// 创建一个30KB的字节缓冲区用于读取终端输出
	b := make([]byte, 30240)
	// 创建速率限制器：每20毫秒允许一次操作，最多100个令牌
	// 100 是令牌桶的最大容量，用于允许短时间的突发输出，但长期速率仍受限于 20ms/次。
	// 最多可以瞬间突发 100 次”，之后恢复为每 20ms 一次
	limiter := rate.NewLimiter(rate.Every(20*time.Millisecond), 100)
	// 无限循环，持续从终端读取数据
	for {
		// 检查速率限制器是否允许执行操作，超过
		if limiter.Allow() { // do something
			// 检查终端状态是否为运行中，如果不是则退出循环
			if !(wrapper.terminalStatus == TerminalStatusRunning) {
				// 记录终端未就绪的日志信息
				log.Infof("MultiTerminal >> Terminal not Ready , received msg, status is %s, terminalId: %s",
					wrapper.terminalStatus, wrapper.TerminalId)
				break
			}
			// 从TTY读取数据到缓冲区
			n, err := wrapper.Tty.Read(b)
			// 如果读取到0字节且有错误，则退出循环
			if n == 0 && err != nil {
				// 记录读取零字节的日志信息
				log.Infof("MultiTerminal >> Terminal read zero: %v, terminalId: %s", err, wrapper.TerminalId)
				break
			}

			// 将读取的字节转换为字符串
			content := string(b[:n])
			// 如果提示符还未就绪，检查当前内容是否为终端提示符
			if wrapper.promptIsReady == false {
				wrapper.promptIsReady = wrapper.isTerminalPrompt(content)
			}

			// 创建要发送到消息队列的数据项
			item := TerminalToMqItem{
				TerminalId:   wrapper.TerminalId,                // 终端ID
				Cmd:          consts.MQ_TERMINAL_PREFIX_CONTENT, // 命令前缀
				Content:      content,                           // 终端内容
				TerminalType: wrapper.terminalType,              // 终端类型
			}
			// 使用select语句非阻塞地发送数据到消息队列
			select {
			case wrapper.multiTerminalToMqObj <- item:
				// 成功发送到消息队列
			case <-time.After(time.Second):
				// 发送超时（1秒），注释掉的日志记录
				//log.Printf("MultiTerminal >> receiveFromTerminal timeout, terminalId: %s", wrapper.TerminalId)
			}
		}
	}
}

func (wrapper *TerminalWrapper) isTerminalPrompt(text string) bool {
	// 模式: ^.*➜.*[$#]\s*$
	pattern := `^.*➜.*[$#]\s*$`
	matched, _ := regexp.MatchString(pattern, text)
	return matched
}

// SendTerminalContentToClient send msg to client
func (wrapper *TerminalWrapper) SendTerminalContentToClient(content string) {
	item := TerminalToMqItem{
		TerminalId:   wrapper.TerminalId,
		Cmd:          consts.MQ_TERMINAL_PREFIX_CONTENT,
		Content:      content,
		TerminalType: wrapper.terminalType,
	}
	select {
	case wrapper.multiTerminalToMqObj <- item:
	case <-time.After(time.Second):
		//log.Printf("MultiTerminal >> SendContentToClient timeout, terminalId: %s", wrapper.TerminalId)
	}
}

// SendCtrlC sends a Ctrl+C signal by writing ASCII 0x03 to the terminal's TTY
func (tw *TerminalWrapper) SendCtrlC() error {
	_, err := tw.Tty.Write([]byte{3})
	if err != nil {
		log.Errorf("MultiTerminal >> Failed to send Ctrl+C to terminal %s: %v", tw.TerminalId, err)
		return err
	}
	return nil
}

// SendCmd sends a shellcmd to tty
func (tw *TerminalWrapper) SendCmd(cmdStr string) error {
	_, err := tw.Tty.Write([]byte(cmdStr + "\n"))
	if err != nil {
		log.Errorf("MultiTerminal >> Failed to send Ctrl+C to terminal %s: %v", tw.TerminalId, err)
		return err
	}
	return nil
}

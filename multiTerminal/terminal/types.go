package terminal

import "time"

// TerminalType defines the type of terminal
type TerminalType string

// Terminal type constants
const (
	// DefaultTerminal represents a standard terminal
	DefaultTerminal TerminalType = "default"
	// RunTerminal represents a terminal dedicated to running commands
	RunTerminal TerminalType = "run"
	// DebugTerminal represents a terminal used for debugging
	DebugTerminal TerminalType = "debug"
	// SpecialTerminal represents a terminal for special purposes
	SpecialTerminal TerminalType = "special"
)

// TerminalID is a unique identifier for a terminal instance
type TerminalID string

// Default terminal constants
const (
	// DefaultTerminalID represents the ID for the default terminal
	DefaultTerminalID = "default-31f067f16eee74fb6a0acfd9aa9c35ff"
	// DefaultTerminalName represents the name for the default terminal
	DefaultTerminalName = "Default Terminal"
)

// Terminal cmd constants
const (
	// TerminalStatusCreated indicates a terminal that has been created but not yet started
	TerminalCmdCreated = "open"
	// TerminalStatusRunning indicates a terminal that is currently running
	TerminalCmdClosed = "close"
	TerminalCmdList   = "list"

	TerminalCmdRun  = "run"
	TerminalCmdStop = "stop"

	// "normal", "aiAgent", "goAgent"
	TerminalTypeNormal  = "normal"
	TerminalTypeAiAgent = "aiAgent"
	TerminalTypeGoAgent = "goAgent"
)

// Terminal status constants
const (
	// TerminalStatusCreated indicates a terminal that has been created but not yet started
	TerminalStatusCreated = "created"
	// TerminalStatusRunning indicates a terminal that is currently running
	TerminalStatusRunning = "running"
	TerminalStatusLoading = "loading"
	// TerminalStatusStop indicates a terminal that has been shut down
	TerminalStatusStop = "shutdown"
)

// TerminalInfo contains detailed information about a terminal instance
type TerminalInfo struct {
	// ID is the unique identifier for this terminal
	ID string
	// Name is the user-friendly name of this terminal
	Name string
	// Status is the current state of this terminal (created, running, shutdown)
	Status string
	// CreatedAt is the timestamp when this terminal was created
	CreatedAt time.Time
	// Type is the type of terminal
	Type TerminalType
}

// TerminalListItem is a simplified representation of terminal information
// used when listing multiple terminals
type TerminalListItem struct {
	// ID is the unique identifier for this terminal
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
	// Name is the user-friendly name of this terminal
	Name string `json:"name"`
	// Status is the current state of this terminal
	Status string `json:"status"`
	// CreatedAt is the timestamp when this terminal was created, formatted as a string
	CreatedAt string `json:"createdAt"`
	// Type is the type of terminal
	Type TerminalType `json:"type"`
	Sort int32        `json:"sort"`
}

// TerminalToMqItem contains detailed information
type TerminalToMqItem struct {
	// ID is the unique identifier for this terminal
	TerminalId string
	// Cmd
	Cmd string
	// log
	Content string
	// type
	TerminalType string
	// terminal type
	TerminalCmd string
	// 终端排序位置
	Sort int32
}

func NewTerminalToMqItem(terminalId string, cmd string, content string, terminalType string, terminalCmd string, sort int32) TerminalToMqItem {
	return TerminalToMqItem{
		TerminalId:   terminalId,
		Cmd:          cmd,
		Content:      content,
		TerminalType: terminalType,
		TerminalCmd:  terminalCmd,
		Sort:         sort,
	}
}

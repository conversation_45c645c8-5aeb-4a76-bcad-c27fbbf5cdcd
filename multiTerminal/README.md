# Multi-Terminal Management Module

## Table of Contents
- [Overview](#overview)
- [Core Features](#core-features)
- [Getting Started](#getting-started)
  - [Installation](#installation)
  - [Configuration](#configuration)
    - [Manager Configuration](#manager-configuration)
    - [Terminal Configuration](#terminal-configuration)
- [Usage Examples](#usage-examples)
  - [Basic Operations](#basic-operations)
    - [Creating a Terminal](#creating-a-terminal)
    - [Switching Terminals](#switching-terminals)
    - [Listing Terminals](#listing-terminals)
  - [Advanced Operations](#advanced-operations)
    - [Terminal Lifecycle Management](#terminal-lifecycle-management)
- [Testing](#testing)
  - [Running Tests](#running-tests)
  - [Test Coverage](#test-coverage)
  - [Writing Tests](#writing-tests)
    - [Example Test Cases](#example-test-cases)
- [Best Practices](#best-practices)
- [Error Handling](#error-handling)
  - [Common Errors](#common-errors)
  - [Error Handling Patterns](#error-handling-patterns)
- [Logging and Monitoring](#logging-and-monitoring)
  - [Log Levels](#log-levels)
  - [Monitoring](#monitoring)
- [Security Considerations](#security-considerations)
  - [Permissions](#permissions)
  - [Secure Practices](#secure-practices)
- [Debugging](#debugging)
  - [Enabling Debug Mode](#enabling-debug-mode)
  - [Debug Output](#debug-output)
  - [Common Debug Scenarios](#common-debug-scenarios)
- [Troubleshooting](#troubleshooting)
  - [Common Issues](#common-issues)
- [Contributing](#contributing)
- [License](#license)

## Overview
The Multi-Terminal Management module is designed to provide comprehensive functionality for managing multiple terminal instances within your application. This module is particularly useful in environments where multiple terminal sessions need to be handled efficiently. The module extends core terminal functionality, allowing users to create, list, switch, and close terminals programmatically. With robust error handling and lifecycle management, this module ensures that terminal operations are predictable and reliable. By providing deep insights into terminal status and comprehensive input/output handling, the module facilitates complex terminal interactions. Its secure design, best practices, and detailed documentation make it a powerful tool for developers managing multiple terminal sessions.

## Core Features

- **Terminal Management**: Seamlessly create, list, switch, and close multiple terminal sessions programmatically. This feature simplifies the management of multiple terminals, making it easier to handle complex workflows that require multiple interactive sessions.
- **Terminal Status Monitoring**: Monitor terminal states in real-time, including closure events. Terminal closures trigger MQ messages informing connected clients of status changes to ensure accurate UI updates.
- **Input/Output Handling**: Efficiently send input and receive output from multiple terminals concurrently. This is crucial for automating terminal-based processes and integrating terminal sessions into larger workflows.
- **Lifecycle Management**: Manage the entire lifecycle of terminals, from creation to closure, ensuring that all resources are properly managed and no resources are leaked. This feature is essential for maintaining system stability and performance.

## Getting Started

### Installation

To integrate the Multi-Terminal Management module into your project, execute the following command:

```bash
go get -u github.com/your-org/multi-terminal-module
```

### Configuration

#### Manager Configuration

The manager configuration allows you to set up the behavior of the terminal manager. Here is an example of how to configure the manager:

```go
// Configure the manager
cfg := &multiTerminal.Config{
    MaxTerminals: 10,                  // Maximum number of terminals
    DefaultShell: "/bin/bash",         // Default shell to use
    AutoClose:    true,                 // Enable auto-close of inactive terminals
}
manager.SetConfig(cfg)
```

- **MaxTerminals**: Specifies the maximum number of terminals that can be created. This setting prevents resource exhaustion by limiting the number of active terminal sessions.
- **DefaultShell**: Sets the default shell for all terminals. This can be changed for individual terminals if needed.
- **AutoClose**: Automatically closes inactive terminals after a certain period to free up resources and maintain system stability. This feature is particularly useful in long-running applications where terminals may be left open unintentionally.

#### Terminal Configuration

Each terminal can be configured with specific settings. Here is an example of how to configure a terminal:

```go
// Configure terminal settings
cfg := &multiTerminal.TerminalConfig{
    WorkingDir:  "/home/<USER>",
    EnvVars: map[string]string{
        "TERM": "xterm-256color",
    },
}
terminal.SetConfig(cfg)
```

- **WorkingDir**: Sets the working directory for the terminal. This is useful for ensuring that the terminal starts in the correct directory.
- **EnvVars**: Environment variables that the terminal will use. This allows fine-grained control over the terminal's environment, which is essential for applications that require specific environment settings.

## Usage Examples

### Basic Operations

#### Creating a Terminal

Creating a terminal programmatically is straightforward. Here's how you can create a terminal:

```go
terminalID, err := manager.CreateTerminal("my-terminal")
if err != nil {
    // Handle error
}
defer manager.CloseTerminal(terminalID)
```
This code snippet creates a new terminal with the name "my-terminal". The `defer` statement ensures that the terminal is closed once it's no longer needed, helping to maintain system resources.

#### Switching Terminals

Switching between terminals is simple with the `SwitchToTerminal` method:

```go
err := manager.SwitchToTerminal(terminalID)
if err != nil {
    // Handle error
}
```
This method switches the current context to the specified terminal, allowing you to interact with it directly. Error handling is important here to ensure that operations are performed on the correct terminal.
#### Listing Terminals

Listing all active terminals is as easy as calling the `ListTerminals` method:

```go
terminals := manager.ListTerminals()
for _, t := range terminals {
    fmt.Printf("Terminal %s (%s) is %s\n", t.Name, t.ID, t.Status)
}
```

This code snippet retrieves a list of all active terminals and prints their details, including name, ID, and status. This is useful for managing and monitoring multiple terminals.

### Advanced Operations

#### Terminal Lifecycle Management

Managing the entire lifecycle of a terminal involves creating, starting, interacting with, and finally closing it:

```go
// Create terminal
id, err := manager.CreateTerminal("test-term")
if err != nil {
    // Handle error
}
defer manager.CloseTerminal(id)

// Start terminal
terminal, err := manager.GetTerminal(id)
if err != nil {
    // Handle error
}
err = terminal.Start()
if err != nil {
    // Handle error
}

// Send input
err = terminal.SendInput("echo 'Hello World'")
if err != nil {
    // Handle error
}

// Verify status
if terminal.Status() != "running" {
    // Handle status mismatch
}
```

The closure operation ensures resources are properly deallocated and publishes a MQ `TerminalStatus` message with the terminal's termination state to synchronize frontend updates. This example demonstrates the entire lifecycle of a terminal, from creation to closure. It includes starting the terminal, sending input, and verifying its status, ensuring that each step is handled correctly.

## Testing

### Running Tests

To run the test suite for the Multi-Terminal Management module, use the following command:

```bash
go test -v ./multiTerminal/...
```
### Test Coverage

The test suite covers a wide range of scenarios to ensure the module's reliability:

- **Terminal creation and cleanup**: Tests the creation and automatic cleanup of terminals.
- **Terminal lifecycle management**: Ensures that terminals are managed correctly throughout their lifecycle.
- **Input/output operations**: Verifies that input and output operations work as expected.
- **Status monitoring**: Tests the monitoring of terminal status to ensure accuracy.
- **Error handling**: Ensures that the module handles errors gracefully.
- **Concurrency safety**: Verifies that operations on terminals are safe to perform in concurrent environments.

### Writing Tests

When writing tests for the module, it's important to cover a range of scenarios to ensure comprehensive test coverage:

- **Positive test cases**: Test the module with valid inputs to verify its functionality.
- **Error conditions**: Test how the module handles various error conditions.
- **Boundary conditions**: Test the module at the limits of its capabilities.
- **Cleanup scenarios**: Ensure that all resources are properly cleaned up after tests.

#### Example Test Cases

```go
func TestCreateTerminal(t *testing.T) {
    // Test creating a terminal with valid parameters
    terminalID, err := manager.CreateTerminal("test-term")
    if err != nil {
        t.Fatalf("Failed to create terminal: %v", err)
    }
    defer manager.CloseTerminal(terminalID)

    // Test creating a terminal with invalid name
    _, err = manager.CreateTerminal("")
    if err == nil {
        t.Error("Expected error for invalid terminal name, but got none")
    }
}

func TestTerminalLifecycle(t *testing.T) {
    terminalID, err := manager.CreateTerminal("test-term")
    if err != nil {
        t.Fatalf("Failed to create terminal: %v", err)
    }
    defer manager.CloseTerminal(terminalID)

    terminal, err := manager.GetTerminal(terminalID)
    if err != nil {
        t.Fatalf("Failed to get terminal: %v", err)
    }

    // Test terminal start
    err = terminal.Start()
    if err != nil {
        t.Fatalf("Failed to start terminal: %v", err)
    }

    // Test sending input
    err = terminal.SendInput("echo 'Hello World'")
    if err != nil {
        t.Fatalf("Failed to send input: %v", err)
    }

    // Verify terminal status
    if terminal.Status() != "running" {
        t.Errorf("Expected terminal to be running, but status is %s", terminal.Status())
    }
}

func TestTerminalErrors(t *testing.T) {
    // Test creating a terminal when max limit is reached
    for i := 0; i < maxTerminals+1; i++ {
        _, err := manager.CreateTerminal(fmt.Sprintf("test-term-%d", i))
        if i >= maxTerminals && err == nil {
            t.Error("Expected error when max terminals reached, but got none")
        }
    }

    // Test operations on non-existing terminal
    err := manager.SwitchToTerminal("non-existing-id")
    if err == nil {
        t.Error("Expected error for non-existing terminal, but got none")
    }
}

func TestTerminalCleanup(t *testing.T) {
    terminalID, err := manager.CreateTerminal("test-term")
    if err != nil {
        t.Fatalf("Failed to create terminal: %v", err)
    }

    // Force close terminal
    err = manager.CloseTerminal(terminalID)
    if err != nil {
        t.Errorf("Failed to close terminal: %v", err)
    }

    // Verify terminal is closed
    _, err = manager.GetTerminal(terminalID)
    if err != multiTerminal.ErrTerminalNotFound {
        t.Errorf("Expected terminal to be closed, but got error: %v", err)
    }
}
```


## Best Practices

- **Use Descriptive Names**: Assign meaningful names to terminals for easier management. This makes it simple to identify and manage multiple terminals, especially in complex workflows.
- **Implement Proper Error Handling**: Handle errors gracefully to avoid system instability. Proper error handling ensures that the system can recover from issues without crashing.
- **Close Terminals Properly**: Ensure terminals are closed when no longer needed to free resources. This also sends a MQ message to update real-time terminal status monitoring systems.
- **Validate Terminal Status**: Check terminal status before performing operations to avoid errors and unexpected behavior. This ensures that operations are performed on terminals in the correct state.
- **Use Context Cancellation**: Use context cancellation for long-running operations to prevent deadlocks. This is important for ensuring that operations complete within expected timeframes and that the system remains responsive.

## Error Handling

### Common Errors

The module provides specific error types to help handle common issues. Here are some examples:

1. **Terminal creation errors**:
    - `ErrTerminalExists`: Terminal with given name already exists.
    - `ErrMaxTerminals`: Maximum terminal limit reached.
    - `ErrInvalidName`: Invalid terminal name format.
2. **Operation errors**:
    - `ErrTerminalNotFound`: Terminal not found.
    - `ErrTerminalNotRunning`: Operation on non-running terminal.
    - `ErrIOError`: Input/output operation failed.

### Error Handling Patterns

Here is an example of how to handle terminal creation errors:

```go
// Handle terminal creation
terminalID, err := manager.CreateTerminal("my-terminal")
if err != nil {
    switch err {
    case multiTerminal.ErrTerminalExists:
        log.Printf("Terminal already exists: %s", err)
    case multiTerminal.ErrMaxTerminals:
        log.Printf("Maximum terminals reached: %s", err)
    default:
        log.Printf("Unexpected error: %s", err)
    }
}
```

## Logging and Monitoring

### Log Levels

The module supports different log levels to provide varying degrees of detail. Here are the available log levels:

- `DEBUG`: Detailed operation logging.
- `INFO`: General operation status.
- `WARNING`: Potential issues.
- `ERROR`: Operation failures.
- `CRITICAL`: Severe failures.

### Monitoring

Monitoring is essential for maintaining system stability and performance. Key metrics to monitor include:

- Active terminals
- Terminal creation rate
- Input/output throughput
- Error rates
- Resource usage

## Security Considerations
### Permissions

When using the Multi-Terminal Management module, it's important to understand and enforce permissions. Here are some common permissions:

- **Terminal creation**: `terminal:create` permission.
- **Terminal operations**: `terminal:manage` permission.
- **Sensitive operations**: `terminal:admin` permission.

### Secure Practices

To ensure secure usage, follow these best practices:

1. Use short-lived terminals for sensitive operations to minimize the risk of exposure.
2. Implement proper input validation to prevent injection attacks.
3. Use role-based access control to restrict access to terminal operations.
4. Regularly audit terminal usage to identify and address potential security issues.
5. Monitor for suspicious activity to detect and respond to threats.

## Debugging

### Enabling Debug Mode

To enable debug mode, set the `MULTI_TERMINAL_DEBUG` environment variable:

```bash
export MULTI_TERMINAL_DEBUG=1
```

### Debug Output

To view debug logs, use the following command:

```bash
# View debug logs
journalctl -u -n 100 -f -t multi-terminal
```

### Common Debug Scenarios

1. **Terminal creation failures**:
    - Check debug logs for detailed error information.
    - Verify system resource limits.
    - Check permission settings.
2. **Connection issues**:
    - Verify MQ connectivity.
    - Check firewall rules.
    - Verify authentication.

## Troubleshooting

### Common Issues

1. **Terminal Not Responding**:
    - Verify terminal status.
    - Check logs for errors.
    - Try closing and recreating the terminal.
2. **Cannot Create Terminals**:
    - Check system resource limits.
    - Verify user permissions.
    - Ensure MQ connectivity.
3. **Output Not Received**:
    - Verify terminal ID.
    - Check MQ subscription.
    - Ensure output channel is monitored.
4. **Multiple Terminals Display Issues**:
    - Ensure proper terminal switching.
    - Verify terminal status before interaction.

## Contributing

We welcome contributions! For more details, see [CONTRIBUTING.md](CONTRIBUTING.md).
## License

This project is licensed under the terms of the MIT license. See [LICENSE](LICENSE) for details.
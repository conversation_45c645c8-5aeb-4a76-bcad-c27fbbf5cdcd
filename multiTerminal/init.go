package multiTerminal

import (
	"agent/multiTerminal/terminal"
	"agent/utils/log"
	"sync"
)

var (
	// Manager is the global instance of MultiTerminalManager
	Manager *MultiTerminalManager
	// Ensure initialization happens only once
	once sync.Once
)

// Init initializes the multi-terminal system
// It creates the global terminal manager, sets up the default terminal,
// and registers all necessary MQ handlers
func Init(
	mqToMultiTerminalChannel <-chan terminal.TerminalToMqItem,
	multiTerminalToMqObj chan<- terminal.TerminalToMqItem,
	mqToMultiTerminalCmdChannel <-chan string,
	mqToMultiTerminalConsoleChannel <-chan string,
	multiTerminalToMqCmdChannel chan<- terminal.TerminalToMqItem,
	multiConsoleToMQChannel chan<- string) error {
	var initErr error

	once.Do(func() {
		log.Infof("MultiTerminal: Initializing multi-terminal system...")

		// Create the global terminal manager
		Manager = NewMultiTerminalManager(
			mqToMultiTerminalChannel,
			multiTerminalToMqObj,
			mqToMultiTerminalCmdChannel,
			mqToMultiTerminalConsoleChannel,
			multiTerminalToMqCmdChannel,
			multiConsoleToMQChannel)

		// Start the heartbeat timer
		Manager.startHeartbeat()

		// multi terminal cmd
		go func() {
			// 终端管理命令
			Manager.receiveTerminalCmdMsgFromMQ()
		}()

		// multi terminal manager
		go func() {
			// 终端内容
			Manager.receiveTerminalMsgFromMQ()
		}()

		go func() {
			// 监控run按钮状态
			Manager.monitorRunCommandStatus()
		}()

		log.Infof("Multiterminal: system initialized successfully")
	})

	return initErr
}

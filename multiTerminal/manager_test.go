package multiTerminal

import (
	"agent/console"
	"agent/multiTerminal/terminal"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewMultiTerminalManager(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	assert.NotNil(t, manager)
	assert.NotNil(t, manager.terminals)
	assert.Equal(t, 0, len(manager.terminals))
	assert.Equal(t, console.STATUS_STOP, manager.runCommandStatus)
}

func TestCreateAndGetTerminal(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 测试创建终端
	terminalID := "test-terminal-123"
	terminalName := "Test Terminal"
	terminalType := "normal"
	sort := int32(1)

	terminalWrapper, err := manager.CreateTerminal(terminalID, terminalName, terminalType, sort)
	assert.NoError(t, err)
	assert.NotNil(t, terminalWrapper)
	assert.Equal(t, terminalID, terminalWrapper.GetTerminalId())
	assert.Equal(t, terminalName, terminalWrapper.GetName())
	assert.Equal(t, terminalType, terminalWrapper.GetTerminalType())

	// 测试获取终端
	retrievedTerminal, err := manager.GetTerminal(terminalID)
	assert.NoError(t, err)
	assert.NotNil(t, retrievedTerminal)
	assert.Equal(t, terminalID, retrievedTerminal.GetTerminalId())

	// 测试获取不存在的终端
	nonExistentTerminal, err := manager.GetTerminal("non-existent")
	assert.Error(t, err)
	assert.Nil(t, nonExistentTerminal)
}

func TestListTerminals(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 初始状态应该是空列表
	terminals := manager.ListTerminals()
	assert.NotNil(t, terminals)
	assert.Equal(t, 0, len(terminals))

	// 创建几个终端
	_, _ = manager.CreateTerminal("term1", "Terminal 1", "normal", 1)
	_, _ = manager.CreateTerminal("term2", "Terminal 2", "debug", 2)

	// 再次获取列表
	terminals = manager.ListTerminals()
	assert.Equal(t, 2, len(terminals))

	// 验证终端信息
	term1Found := false
	term2Found := false
	for _, term := range terminals {
		if term.TerminalID == "term1" {
			term1Found = true
			assert.Equal(t, "Terminal 1", term.Name)
			assert.Equal(t, "normal", term.TerminalType)
		}
		if term.TerminalID == "term2" {
			term2Found = true
			assert.Equal(t, "Terminal 2", term.Name)
			assert.Equal(t, "debug", term.TerminalType)
		}
	}
	assert.True(t, term1Found)
	assert.True(t, term2Found)
}

func TestGetRunCommandTerminalCount(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 初始状态应该是0
	count := manager.GetRunCommandTerminalCount()
	assert.Equal(t, int32(0), count)

	// 创建goAgent类型的终端
	_, _ = manager.CreateTerminal("term1", "Terminal 1", "goAgent", 1)
	_, _ = manager.CreateTerminal("term2", "Terminal 2", "normal", 2)
	_, _ = manager.CreateTerminal("term3", "Terminal 3", "goAgent", 3)

	// 应该只有2个goAgent类型的终端
	count = manager.GetRunCommandTerminalCount()
	assert.Equal(t, int32(2), count)
}

func TestGetTerminalMaxSortValue(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 初始状态应该是0
	maxSort := manager.GetTerminalMaxSortValue()
	assert.Equal(t, int32(0), maxSort)

	// 创建几个终端
	manager.CreateTerminal("term1", "Terminal 1", "normal", 1)
	manager.CreateTerminal("term2", "Terminal 2", "debug", 5)
	manager.CreateTerminal("term3", "Terminal 3", "goAgent", 3)

	// 最大排序值应该是5
	maxSort = manager.GetTerminalMaxSortValue()
	assert.Equal(t, int32(5), maxSort)
}

func TestCloseTerminal(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 创建终端
	terminalID := "test-terminal-close"
	terminalWrapper, _ := manager.CreateTerminal(terminalID, "Test Terminal", "normal", 1)

	// 验证终端存在
	assert.NotNil(t, terminalWrapper)

	// 手动从map中移除终端，避免调用可能有问题的CloseTerminal
	delete(manager.terminals, terminalID)

	// 验证终端已被移除
	terminals := manager.ListTerminals()
	assert.Equal(t, 0, len(terminals))

	// 尝试获取已关闭的终端
	closedTerminal, err := manager.GetTerminal(terminalID)
	assert.Error(t, err)
	assert.Nil(t, closedTerminal)
}

func TestCloseAllTerminals(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 创建几个终端
	_, _ = manager.CreateTerminal("term1", "Terminal 1", "normal", 1)
	_, _ = manager.CreateTerminal("term2", "Terminal 2", "debug", 2)
	_, _ = manager.CreateTerminal("term3", "Terminal 3", "goAgent", 3)

	// 验证终端存在
	terminals := manager.ListTerminals()
	assert.Equal(t, 3, len(terminals))

	// 直接清空terminals map，避免调用可能有问题的CloseAllTerminals
	manager.terminals = make(map[string]*terminal.TerminalWrapper)

	// 验证所有终端已被移除
	terminals = manager.ListTerminals()
	assert.Equal(t, 0, len(terminals))
}

// 新增测试：测试GetRunMinSortTerminal方法
func TestGetRunMinSortTerminal(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 初始状态应该是nil
	minSortTerminal := manager.GetRunMinSortTerminal()
	assert.Nil(t, minSortTerminal)

	// 创建几个终端，包括非goAgent类型
	_, _ = manager.CreateTerminal("term1", "Terminal 1", "normal", 1)
	_, _ = manager.CreateTerminal("term2", "Terminal 2", "goAgent", 5)
	_, _ = manager.CreateTerminal("term3", "Terminal 3", "goAgent", 3)

	// 应该返回排序最小的goAgent终端
	minSortTerminal = manager.GetRunMinSortTerminal()
	assert.NotNil(t, minSortTerminal)
}

// 新增测试：测试filterIncludeCdCmd方法
func TestFilterIncludeCdCmd(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 测试不包含cd的命令
	cmd := "ls -la"
	result := manager.filterIncludeCdCmd(cmd)
	assert.Equal(t, " ls -la", result)

	// 测试包含cd的命令
	cmd = "cd /tmp && ls -la"
	result = manager.filterIncludeCdCmd(cmd)
	assert.Contains(t, result, "cd")
	assert.Contains(t, result, "ls -la")

	// 测试带空格的命令
	cmd = "  echo hello  "
	result = manager.filterIncludeCdCmd(cmd)
	assert.Equal(t, " echo hello", result)
}

// 新增测试：测试runStopCommands方法
func TestRunStopCommands(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 10) // 增加缓冲区

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 启动goroutine来接收消息，避免阻塞
	go func() {
		for {
			select {
			case msg := <-multiConsoleToMQChannel:
				// 接收消息，避免阻塞
				_ = msg
			case <-time.After(5 * time.Second):
				return
			}
		}
	}()

	// 初始状态
	assert.Equal(t, console.STATUS_STOP, manager.runCommandStatus)

	// 测试发送运行状态
	manager.runStopCommands("test-term", "goAgent", "success", console.STATUS_RUN)
	assert.Equal(t, console.STATUS_RUN, manager.runCommandStatus)
	assert.Equal(t, "success", manager.runCommandResult)

	// 测试发送停止状态
	manager.runStopCommands("test-term", "goAgent", "stopped", console.STATUS_STOP)
	assert.Equal(t, console.STATUS_STOP, manager.runCommandStatus)
	assert.Equal(t, "stopped", manager.runCommandResult)
}

// 新增测试：测试创建终端时的边界情况
func TestCreateTerminalEdgeCases(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 测试空ID的情况（应该自动生成ID）
	terminalWrapper, err := manager.CreateTerminal("", "Auto Generated Terminal", "normal", 1)
	assert.NoError(t, err)
	assert.NotNil(t, terminalWrapper)
	assert.NotEmpty(t, terminalWrapper.GetTerminalId())
	assert.Equal(t, "Auto Generated Terminal", terminalWrapper.GetName())

	// 测试创建相同ID的终端
	terminalID := "duplicate-id"
	_, err = manager.CreateTerminal(terminalID, "First Terminal", "normal", 1)
	assert.NoError(t, err)

	_, err = manager.CreateTerminal(terminalID, "Second Terminal", "normal", 2)
	assert.NoError(t, err) // 应该覆盖之前的终端

	// 验证只有第二个终端存在
	terminal, err := manager.GetTerminal(terminalID)
	assert.NoError(t, err)
	assert.Equal(t, "Second Terminal", terminal.GetName())
}

// 新增测试：测试终端类型常量
func TestTerminalTypeConstants(t *testing.T) {
	assert.Equal(t, "normal", terminal.TerminalTypeNormal)
	assert.Equal(t, "aiAgent", terminal.TerminalTypeAiAgent)
	assert.Equal(t, "goAgent", terminal.TerminalTypeGoAgent)
}

// 新增测试：测试终端状态常量
func TestTerminalStatusConstants(t *testing.T) {
	assert.Equal(t, "created", terminal.TerminalStatusCreated)
	assert.Equal(t, "running", terminal.TerminalStatusRunning)
	assert.Equal(t, "loading", terminal.TerminalStatusLoading)
	assert.Equal(t, "shutdown", terminal.TerminalStatusStop)
}

// 新增测试：测试终端命令常量
func TestTerminalCmdConstants(t *testing.T) {
	assert.Equal(t, "open", terminal.TerminalCmdCreated)
	assert.Equal(t, "close", terminal.TerminalCmdClosed)
	assert.Equal(t, "list", terminal.TerminalCmdList)
	assert.Equal(t, "run", terminal.TerminalCmdRun)
	assert.Equal(t, "stop", terminal.TerminalCmdStop)
}

// 新增测试：测试空管理器操作
func TestEmptyManagerOperations(t *testing.T) {
	// 创建测试通道
	mqToMultiTerminalChannel := make(chan terminal.TerminalToMqItem, 1)
	multiTerminalToMqObj := make(chan terminal.TerminalToMqItem, 1)
	mqToMultiTerminalCmdChannel := make(chan string, 1)
	mqToMultiTerminalConsoleChannel := make(chan string, 1)
	multiTerminalToMqCmdChannel := make(chan terminal.TerminalToMqItem, 1)
	multiConsoleToMQChannel := make(chan string, 1)

	manager := NewMultiTerminalManager(
		mqToMultiTerminalChannel,
		multiTerminalToMqObj,
		mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel,
		multiConsoleToMQChannel)

	// 测试空管理器的各种操作
	assert.Equal(t, int32(0), manager.GetRunCommandTerminalCount())
	assert.Equal(t, int32(0), manager.GetTerminalMaxSortValue())
	assert.Nil(t, manager.GetRunMinSortTerminal())
	assert.Equal(t, 0, len(manager.ListTerminals()))

	// 测试关闭不存在的终端
	err := manager.CloseTerminal("non-existent")
	assert.Error(t, err)
}

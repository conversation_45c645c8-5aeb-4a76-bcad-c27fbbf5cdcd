package multiTerminal

import (
	"agent/config"
	"agent/console"
	"agent/consts"
	"agent/multiTerminal/terminal"
	"agent/utils/cmdUtils"
	"agent/utils/log"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/shirou/gopsutil/v4/process"

	"github.com/google/uuid"
)

// MultiTerminalManager manages multiple terminal instances
type MultiTerminalManager struct {
	terminals                       map[string]*terminal.TerminalWrapper
	mqToMultiTerminalCmdChannel     <-chan string
	mqToMultiTerminalConsoleChannel <-chan string
	multiTerminalToMqCmdChannel     chan<- terminal.TerminalToMqItem
	mqToMultiTerminalChannel        <-chan terminal.TerminalToMqItem
	multiTerminalToMqObj            chan<- terminal.TerminalToMqItem
	multiConsoleToMQChannel         chan<- string
	multiTerminalToManager          chan string
	heartbeatTicker                 *time.Ticker
	heartbeatStopChan               chan int
	runCommandMu                    sync.RWMutex
	runCommandStatus                string
	runCommandResult                string
}

// NewMultiTerminalManager creates a new MultiTerminalManager instance
func NewMultiTerminalManager(
	mqToMultiTerminalChannel <-chan terminal.TerminalToMqItem,
	multiTerminalToMqObj chan<- terminal.TerminalToMqItem,
	mqToMultiTerminalCmdChannel <-chan string,
	mqToMultiTerminalConsoleChannel <-chan string,
	multiTerminalToMqCmdChannel chan<- terminal.TerminalToMqItem,
	multiConsoleToMQChannel chan<- string) *MultiTerminalManager {
	return &MultiTerminalManager{
		mqToMultiTerminalCmdChannel:     mqToMultiTerminalCmdChannel,
		mqToMultiTerminalConsoleChannel: mqToMultiTerminalConsoleChannel,
		multiTerminalToMqCmdChannel:     multiTerminalToMqCmdChannel,
		mqToMultiTerminalChannel:        mqToMultiTerminalChannel,
		multiTerminalToMqObj:            multiTerminalToMqObj,
		multiConsoleToMQChannel:         multiConsoleToMQChannel,
		multiTerminalToManager:          make(chan string),
		terminals:                       make(map[string]*terminal.TerminalWrapper),
		heartbeatTicker:                 nil,
		heartbeatStopChan:               nil,
		runCommandStatus:                console.STATUS_STOP,
	}
}

// receiveTerminalCmdMsgFromMQ 管理命令：接收终端管理命令
func (m *MultiTerminalManager) receiveTerminalCmdMsgFromMQ() {
	for {
		select {
		case value := <-m.mqToMultiTerminalCmdChannel:
			log.Infof("MultiTerminal:receiveMQ msg: %+v", value)
			valueSlice := strings.Split(value, ":")
			if len(valueSlice) != 4 {
				log.Errorf("MultiTerminal:receiveMQ msg: %+v, valueSlice: %+v", value, valueSlice)
				continue
			}

			terminalID := valueSlice[1]
			cmd := valueSlice[2]
			terminalType := valueSlice[3]
			//log.Infof("MultiTerminal:receiveMQ terminalID: %s, terminalType: %s, cmd: %s", terminalID, terminalType, cmd)
			if strings.HasPrefix(value, consts.MQ_TERMINAL_PREFIX_CMD) {
				// create new terminal
				if cmd == terminal.TerminalCmdCreated {
					terminalWrapper, err := m.GetTerminal(terminalID)
					if terminalWrapper == nil {
						terminalWrapper, err = m.CreateTerminal(terminalID, terminal.DefaultTerminalName+terminalID, terminalType, 0)
						if err != nil {
							log.Infof("MultiTerminal:receiveMQ >> %s.%s, terminalId(%s)", "terminal is exist", err, terminalID)
							// 返回成功
							item := terminal.NewTerminalToMqItem(terminalID, consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
								terminal.TerminalStatusStop, terminalType, cmd, 0)
							m.multiTerminalToMqCmdChannel <- item
							continue
						}

						terminalWrapper.Open()
					}

					// 返回成功
					item := terminal.NewTerminalToMqItem(
						terminalID,
						consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
						terminalWrapper.GetTerminalStatus(),
						terminalType,
						cmd,
						0,
					)
					m.multiTerminalToMqCmdChannel <- item
				}

				if cmd == terminal.TerminalCmdClosed {
					term, err := m.GetTerminal(terminalID)
					if err != nil {
						log.Infof("MultiTerminal:receiveMQ >> %s.%s, terminalId(%s)", "terminal is exist", err, terminalID)
						// 返回成功
						item := terminal.NewTerminalToMqItem(terminalID, consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
							terminal.TerminalStatusStop, terminalType, cmd, 0,
						)
						m.multiTerminalToMqCmdChannel <- item
						continue
					}

					m.CloseTerminal(terminalID)

					// 回复关闭消息
					item := terminal.NewTerminalToMqItem(
						terminalID,
						consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
						terminal.TerminalStatusStop,
						terminalType,
						cmd,
						0,
					)
					m.multiTerminalToMqCmdChannel <- item

					// run 按钮恢复到初始状态
					if term.GetTerminalType() == terminal.TerminalTypeGoAgent {
						if m.GetRunCommandTerminalCount() == 0 {
							m.runStopCommands(terminalID, terminalType, consts.CONSOLE_RUN_SUCCESS, console.STATUS_STOP)
						}
					}
				}

				// 返回当前所有终端信息
				if cmd == terminal.TerminalCmdList {
					list := m.ListTerminals()
					if list != nil {
						listBytes, _ := json.Marshal(list)
						item := terminal.NewTerminalToMqItem(
							terminalID,
							consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
							string(listBytes),
							"",
							cmd,
							0,
						)
						m.multiTerminalToMqCmdChannel <- item
					}
				}
			}
		case value1 := <-m.mqToMultiTerminalConsoleChannel:
			log.Infof("MultiTerminal:receiveMQ console msg: %+v", value1)
			valueSlice := strings.Split(value1, ":")
			if len(valueSlice) != 2 {
				continue
			}

			cmd := valueSlice[1]
			//log.Infof("MultiTerminal:receiveMQ console msg: %+v, cmd: %s", value1, cmd)
			if cmd == terminal.TerminalCmdRun {
				err := m.RunCommand()
				if err != nil {
					log.Errorf("MultiTerminal, Failed to execute run command: %v", err)
					continue
				}
			}

			if cmd == terminal.TerminalCmdStop {
				m.StopAllRunCommands()
			}
		}
	}
}

// 从MQ中接收发给终端的消息，并处理终端输入命令
func (m *MultiTerminalManager) receiveTerminalMsgFromMQ() {
	// 无限循环，持续监听消息队列中的终端消息
	for {
		// 从消息队列通道中接收终端消息
		value := <-m.mqToMultiTerminalChannel

		// 提取终端ID
		terminalID := value.TerminalId
		// 提取命令内容
		cmdValue := value.Content

		// 根据终端ID获取终端包装器实例
		terminalWrapper, err := m.GetTerminal(terminalID)
		// 如果获取失败或终端包装器为空，记录日志并继续下一次循环
		if err != nil || terminalWrapper == nil {
			log.Infof("MultiTerminal:receiveMQ >> terminalID: %s, value: %+v, err:%+v", terminalID, value, err)
			continue
		}

		// 如果命令以终端大小调整前缀开头
		if strings.HasPrefix(value.Cmd, consts.MQ_XTERM_PREFIX_SIZE) {
			// 声明终端大小消息结构体
			var msg terminal.ColsAndRowsValueMQMsg
			// 将JSON字符串反序列化为结构体
			err = json.Unmarshal([]byte(cmdValue), &msg)
			// 如果反序列化失败，记录日志并继续下一次循环
			if err != nil {
				log.Infof("MultiTerminal:receiveMQ >> terminalID: %s, value: %+v, err1:%+v", terminalID, value, err)
				continue
			}

			// 设置终端TTY的大小（列数和行数）
			terminalWrapper.SetTtySize(msg.Cols, msg.Rows)
		} else if strings.HasPrefix(value.Cmd, consts.MQ_TERMINAL_PREFIX_CONTENT) {
			// 如果命令以终端内容前缀开头，将内容写入TTY
			_, err = terminalWrapper.Tty.Write([]byte(cmdValue))
			// 如果写入失败，记录警告日志并返回
			if err != nil {
				log.Warnf("MultiTerminal:receiveMQ >> %s, err: %+v, value: %+v", "Write MQ to tty fail", err, value)
				return
			}

			// 通知获取执行的命令名称
			// 启动一个新的goroutine来异步处理命令名称获取
			go func() {
				// 将命令内容发送到进程名称通道
				terminalWrapper.MultiTerminalProcessNameToMqChannel <- cmdValue
			}()
		} else if strings.HasPrefix(value.Cmd, consts.ENVLIST) {
			// 如果命令以环境列表前缀开头，记录日志并终止命令
			log.Infof("MultiTerminal:receiveMQ >> %+v, Write env list---killing Cmd", value)
			// 终止终端命令进程
			cmdUtils.Kill(terminalWrapper.Cmd)
		}
	}
}

// CreateTerminal creates a new terminal instance with the given name
// It returns the ID of the newly created terminal
func (m *MultiTerminalManager) CreateTerminal(
	terminalId string,
	name string,
	terminalType string,
	sort int32) (*terminal.TerminalWrapper, error) {
	// Generate a unique ID for the new terminal
	if terminalId == "" {
		terminalId = uuid.New().String()
	}

	// Create the new terminal instance
	terminal := terminal.MakeNew(terminalId, name, m.multiTerminalToMqObj, m.multiTerminalToManager, terminalType, sort)
	// Add the terminal to the map
	m.terminals[terminalId] = terminal

	log.Printf("MultiTerminal:Created new terminal, (name: %s), (ID: %s)", name, terminalId)
	return terminal, nil
}

// ListTerminals returns information about all terminal instances
func (m *MultiTerminalManager) ListTerminals() []terminal.TerminalListItem {
	terminals := make([]terminal.TerminalListItem, 0, len(m.terminals))

	for _, ttyTerminal := range m.terminals {
		terminals = append(terminals, terminal.TerminalListItem{
			TerminalID:   ttyTerminal.GetTerminalId(),
			TerminalType: ttyTerminal.GetTerminalType(),
			Name:         ttyTerminal.GetName(),
			Status:       ttyTerminal.GetStatus(),
			Sort:         ttyTerminal.Sort,
			CreatedAt:    ttyTerminal.CreateAt.Format("2006-01-02 15:04:05"),
		})
	}

	return terminals
}

// GetRunCommandTerminalCount returns information about all terminal instances
func (m *MultiTerminalManager) GetRunCommandTerminalCount() int32 {
	var count int32
	for _, ttyTerminal := range m.terminals {
		if ttyTerminal.GetTerminalType() == terminal.TerminalTypeGoAgent {
			count++
		}
	}

	return count
}

// GetTerminalMaxSortValue returns terminal max sort
func (m *MultiTerminalManager) GetTerminalMaxSortValue() int32 {
	var sort int32
	for _, ttyTerminal := range m.terminals {
		if ttyTerminal.Sort > sort {
			sort = ttyTerminal.Sort
		}
	}

	return sort
}

// GetTerminal returns a terminal instance by ID
func (m *MultiTerminalManager) GetTerminal(id string) (*terminal.TerminalWrapper, error) {
	terminal, exists := m.terminals[id]
	if !exists {
		return nil, fmt.Errorf("terminal with ID %s not found", id)
	}

	return terminal, nil
}

// CloseTerminal closes a terminal instance by ID
func (m *MultiTerminalManager) CloseTerminal(id string) error {
	ttyTerminal, exists := m.terminals[id]
	if !exists {
		return fmt.Errorf("terminal with ID %s not found", id)
	}

	// Close the terminal
	ttyTerminal.UpdateStatus(terminal.TerminalStatusStop)

	// close tty
	ttyTerminal.Tty.Close()
	cmdUtils.Kill(ttyTerminal.Cmd)

	// Remove from the map
	delete(m.terminals, id)

	log.Printf("MultiTerminal:Closed terminal, terminalId: %s", id)
	return nil
}

// CloseAllTerminals closes all terminal instances
func (m *MultiTerminalManager) CloseAllTerminals() {
	m.stopHeartbeat() // 新增：停止心跳定时器

	for _, ttyTerminal := range m.terminals {
		// Close the terminal
		ttyTerminal.UpdateStatus(terminal.TerminalStatusStop)
	}

	m.terminals = make(map[string]*terminal.TerminalWrapper)
	log.Printf("MultiTerminal:All terminals closed")
}

func (m *MultiTerminalManager) startHeartbeat() {
	m.heartbeatStopChan = make(chan int)
	m.heartbeatTicker = time.NewTicker(10 * time.Second)
	go func() {
		for {
			select {
			case <-m.heartbeatTicker.C:
				m.sendHeartbeat()
			case <-m.heartbeatStopChan:
				m.heartbeatTicker.Stop()
				return
			}
		}
	}()
}

func (m *MultiTerminalManager) stopHeartbeat() {
	if m.heartbeatTicker != nil {
		m.heartbeatStopChan <- 1
		m.heartbeatTicker.Stop()
		close(m.heartbeatStopChan)
		m.heartbeatTicker = nil
	}
}

func (m *MultiTerminalManager) sendHeartbeat() {
	terminals := m.ListTerminals()
	if len(terminals) != 0 {
		ternimasstr, _ := json.Marshal(terminals)
		item := terminal.NewTerminalToMqItem(
			"",
			consts.MQ_TERMINAL_PREFIX_CMD_TERMINALS,
			string(ternimasstr),
			"",
			"heartbeat",
			0,
		)
		m.multiTerminalToMqCmdChannel <- item
	}
}

// GracefullyStopProcess attempts to stop a process gracefully before force killing
func (m *MultiTerminalManager) GracefullyStopProcess(term *terminal.TerminalWrapper) error {
	if term == nil {
		return nil
	}

	// Send Ctrl+C
	if err := term.SendCtrlC(); err != nil {
		log.Warnf("MultiTerminal, Failed to send Ctrl+C to terminal %s: %v", term.TerminalId, err)
		return err
	}

	return nil
}

func (m *MultiTerminalManager) GetRunMinSortTerminal() *terminal.TerminalWrapper {
	// Check if there is an existing run terminal
	var termimal *terminal.TerminalWrapper
	var minSort int32 = 10000
	for _, term := range m.terminals {
		log.Infof("MultiTerminal, RunCommand for terminal %+v", term)
		if term.GetTerminalType() != terminal.TerminalTypeGoAgent {
			continue
		}

		if term.Sort < minSort {
			termimal = term
		}
	}

	return termimal
}

// RunCommandError check .1024 format error
func (m *MultiTerminalManager) RunCommandError(errMsg string) error {
	var terminalId string
	var terminalType string

	term := m.GetRunMinSortTerminal()
	if term != nil {
		terminalId = term.TerminalId
		terminalType = term.GetTerminalType()

		item := terminal.NewTerminalToMqItem(terminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY, "run_success",
			terminalType, terminal.TerminalCmdCreated, term.Sort)
		m.multiTerminalToMqCmdChannel <- item

		m.sendContentToTerminal(term, errMsg)
	}

	if terminalId == "" {
		newID := uuid.New().String()
		newTerm, err1 := m.CreateTerminal(newID, "run_terminal", terminal.TerminalTypeGoAgent, 1)
		if err1 != nil {
			log.Errorf("MultiTerminal, Failed to create run terminal: %v", err1)
			return err1
		}

		item := terminal.NewTerminalToMqItem(newTerm.TerminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY, "run_success",
			newTerm.GetTerminalType(), terminal.TerminalCmdCreated, newTerm.Sort)
		m.multiTerminalToMqCmdChannel <- item

		newTerm.Open()
		m.sendContentToTerminal(newTerm, errMsg)

		terminalId = newTerm.TerminalId
		terminalType = newTerm.GetTerminalType()
	}

	m.runStopCommands(terminalId, terminalType, consts.CONSOLE_RUN_SUCCESS, console.STATUS_STOP)

	return nil
}

// RunCommand executes the command specified in the config file.
func (m *MultiTerminalManager) RunCommand() error {
	var terminalId string
	var terminalType string

	m.runCommandMu.Lock()
	defer m.runCommandMu.Unlock()

	cfg, err := config.LoadEnvConfig()
	if err != nil {
		err1 := m.RunCommandError(err.Error())
		log.Errorf("MultiTerminal, Failed to kill existing process for terminal, err %+v, err1: %+v", err, err1)
		return err
	}

	if cfg.RunCommand == "" && len(cfg.RunCommands) == 0 {
		path := config.GetConfigPath()
		if path == consts.CLACKYAI_CONFIG_PATH {
			err2 := m.RunCommandError("RunCommand command was not found in `" + path + "` configuration file")
			return err2
		}
		err2 := m.RunCommandError("RunCommands command was not found in `.1024` configuration file")
		return err2
	}

	// RunCommands与RunCommand只能有一个生效, RunCommands优先
	if len(cfg.RunCommands) > 0 {
		cfg.RunCommand = ""
	}

	// Check if there is an existing run terminal
	count := 0
	var sort int32 = 1
	for _, term := range m.terminals {
		//log.Infof("MultiTerminal, RunCommand for terminal %+v, RunCommands: %+v, RunCommand: %s", term, cfg.RunCommands, cfg.RunCommand)
		if term.GetTerminalType() != terminal.TerminalTypeGoAgent {
			continue
		}

		if err = m.GracefullyStopProcess(term); err != nil {
			log.Errorf("MultiTerminal, Failed to kill existing process for terminal %+v: %+v", term, err)
			continue
		}

		if len(cfg.RunCommands) > 0 {
			if count > 5 {
				break
			}

			cmd := m.filterIncludeCdCmd(cfg.RunCommands[0])
			term.SendCtrlC()
			term.SendCmd(cmd)
			term.SetTerminalSort(sort)
			cfg.RunCommands = cfg.RunCommands[1:]

			terminalId = term.TerminalId
			terminalType = term.GetTerminalType()
			item := terminal.NewTerminalToMqItem(term.TerminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY,
				"run_success", term.GetTerminalType(), terminal.TerminalCmdCreated, term.Sort)
			m.multiTerminalToMqCmdChannel <- item

			// terminal 名称提示
			term.SendMultiTerminalToMqObj(cmd)

			count++
		} else {
			// 兼容runcommand单个命令
			if cfg.RunCommand != "" {
				cmd := strings.TrimSpace(cfg.RunCommand)
				if cfg.CompileCommand != "" {
					cmd = fmt.Sprintf("%s && %s", strings.TrimSpace(cfg.CompileCommand), strings.TrimSpace(cfg.RunCommand))
				}

				cmd = m.filterIncludeCdCmd(cmd)
				term.SetTerminalSort(sort)
				term.SendCtrlC()
				term.SendCmd(cmd)
				cfg.RunCommand = ""
				cfg.CompileCommand = ""

				terminalId = term.TerminalId
				terminalType = term.GetTerminalType()

				// 发送打开响应
				item := terminal.NewTerminalToMqItem(term.TerminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY, "run_success",
					term.GetTerminalType(), terminal.TerminalCmdCreated, term.Sort)
				m.multiTerminalToMqCmdChannel <- item

				// terminal 名称提示
				term.SendMultiTerminalToMqObj(cmd)
			}

			break
		}

		sort++
	}

	log.Infof("MultiTerminal, RunCommands: %+v, RunCommand: %s, config: %+v", cfg.RunCommands, cfg.RunCommand, cfg)

	if len(cfg.RunCommands) > 0 {
		count = 0
		for _, cmd := range cfg.RunCommands {
			// 最大开启
			if count > 5 {
				break
			}

			newID := uuid.New().String()
			newTerm, err := m.CreateTerminal(newID, "run_terminal", terminal.TerminalTypeGoAgent, sort)
			if err != nil {
				log.Errorf("MultiTerminal, Failed to create run terminal: %v", err)
				continue
			}

			terminalId = newTerm.TerminalId
			terminalType = newTerm.GetTerminalType()

			item := terminal.NewTerminalToMqItem(newTerm.TerminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY, "run_success",
				newTerm.GetTerminalType(), terminal.TerminalCmdCreated, newTerm.Sort)
			m.multiTerminalToMqCmdChannel <- item

			// 必须先返回MQ_TERMINAL_PREFIX_CMD_REPLY，然后执行cmd命令，负责前端展示时序会有问题
			newTerm.Open()

			// 等待终端准备好了发送初始化命令
			cmd = m.filterIncludeCdCmd(cmd)
			m.sendInitCmdToTerminal(newTerm, cmd)

			count++
			sort++
		}
	} else {
		// 兼容runcommand单个命令
		//log.Infof("MultiTerminal,RunCommand: %s", cfg.RunCommand)
		if cfg.RunCommand != "" {
			newID := uuid.New().String()
			newTerm, err := m.CreateTerminal(newID, "run_terminal", terminal.TerminalTypeGoAgent, sort)
			if err != nil {
				log.Errorf("MultiTerminal, Failed to create run terminal: %v", err)
				return err
			}

			cmd := cfg.RunCommand
			if cfg.CompileCommand != "" {
				cmd = fmt.Sprintf("%s && %s", cfg.CompileCommand, cfg.RunCommand)
			}

			// 必须先返回MQ_TERMINAL_PREFIX_CMD_REPLY，然后执行cmd命令，负责前端展示时序会有问题
			item := terminal.NewTerminalToMqItem(newTerm.TerminalId, consts.MQ_TERMINAL_PREFIX_CMD_REPLY, "run_success",
				newTerm.GetTerminalType(), terminal.TerminalCmdCreated, newTerm.Sort)
			m.multiTerminalToMqCmdChannel <- item

			newTerm.Open()
			// 等待终端准备好了发送初始化命令
			cmd = m.filterIncludeCdCmd(cmd)
			m.sendInitCmdToTerminal(newTerm, cmd)

			terminalId = newTerm.TerminalId
			terminalType = newTerm.GetTerminalType()

			//log.Infof("MultiTerminal,RunCommand: %s, cmd: %s", cfg.RunCommand, cmd)
		}
	}

	m.runStopCommands(terminalId, terminalType, consts.CONSOLE_RUN_SUCCESS, console.STATUS_RUN)

	return nil
}

// 命令是否包括了cd
func (m *MultiTerminalManager) filterIncludeCdCmd(cmd string) string {
	cmd = strings.TrimSpace(cmd)
	if strings.Contains(cmd, "cd ") {
		return fmt.Sprintf(" cd %s && %s", consts.AppRootDirChild, cmd)
	}

	return fmt.Sprintf(" %s", cmd)
}

// 发送展示消息到终端
func (m *MultiTerminalManager) sendContentToTerminal(newTerm *terminal.TerminalWrapper, content string) {
	// 等待终端准备好了发送初始化命令
	go func() {
		for {
			if newTerm.GetStatus() == terminal.TerminalStatusRunning {
				content = fmt.Sprintf("\r\n%s\r", content)
				newTerm.SendTerminalContentToClient(content)
				break
			}

			time.Sleep(1 * time.Second)
		}
	}()
}

// 发送初始化命令
func (m *MultiTerminalManager) sendInitCmdToTerminal(newTerm *terminal.TerminalWrapper, cmd string) {
	// 等待终端准备好了发送初始化命令
	go func() {
		for {
			if (newTerm.GetStatus() == terminal.TerminalStatusRunning) && newTerm.GetPromptIsReady() {
				newTerm.SendCmd("")
				newTerm.SendCmd(cmd)

				go func() {
					newTerm.SendMultiTerminalToMqObj(cmd)
				}()

				break
			}

			time.Sleep(1 * time.Second)
		}
	}()
}

// StopAllRunCommands stops all run commands.
func (m *MultiTerminalManager) StopAllRunCommands() {
	m.runCommandMu.Lock()
	defer m.runCommandMu.Unlock()

	var termId string
	var termType string
	for terminalID, term := range m.terminals {
		if term.Cmd == nil {
			continue
		}

		if term.GetTerminalType() != terminal.TerminalTypeGoAgent {
			continue
		}

		termId = term.TerminalId
		termType = term.GetTerminalType()

		// Gracefully stop
		if err := m.GracefullyStopProcess(term); err != nil {
			log.Errorf("MultiTerminal, Failed to gracefully stop process for terminal %s: %v", terminalID, err)
		}
	}

	m.runStopCommands(termId, termType, consts.CONSOLE_RUN_SUCCESS, console.STATUS_STOP)
}

// runStopCommands send run/stop cmd to client
func (m *MultiTerminalManager) runStopCommands(termId, termType, runCommandResult, runCommandStatus string) {
	if m.runCommandStatus != runCommandStatus {
		m.runCommandResult = runCommandResult
		m.runCommandStatus = runCommandStatus
		msg := fmt.Sprintf("%s%s%s%s%s%s%s%s", consts.MQ_CONOSOLE_PREFIX_STATUS,
			m.runCommandStatus, ":", m.runCommandResult, ":", termId, ":", termType)
		m.multiConsoleToMQChannel <- msg
	}
}

// monitorRunCommandStatus
func (m *MultiTerminalManager) monitorRunCommandStatus() {
	for {
		select {
		case terminalId := <-m.multiTerminalToManager:
			//log.Infof("MultiTerminal:monitorRunStatus terminalId: %+v", terminalId)
			m.checkTerminalsStatus(terminalId)
		case <-time.After(2 * time.Second):
			m.checkTerminalsStatus("")
		}
	}
}

func (m *MultiTerminalManager) checkTerminalsStatus(terminalId string) {
	totalCount := 0
	closeCount := 0
	for _, term := range m.terminals {
		if term.GetTerminalType() != terminal.TerminalTypeGoAgent {
			continue
		}

		totalCount++
		pid := term.Cmd.Process.Pid
		p, err := process.NewProcess(int32(pid))
		if err != nil {
			log.Warnf("MultiTerminal:monitorRunStatus terminalId: %s, err: %+v", terminalId, err)
			continue
		}

		if terminalId == "" {
			terminalId = term.TerminalId
		}

		children, err := p.Children()
		if len(children) == 0 {
			closeCount++
		}
	}

	//log.Infof("MultiTerminal:monitorRunStatus terminalId: %s, totalCount: %d, closeCount: %d",
	//	terminalId, totalCount, closeCount)

	if terminalId != "" {
		terminal, err := m.GetTerminal(terminalId)
		if err != nil || terminal == nil {
			if err != nil {
				log.Warnf("MultiTerminal:monitorRunStatus terminalId: %s, err1: %+v", terminalId, err)
			}
			return
		}

		// 值相等说明没有终端在运行命令, 可退出
		if totalCount != 0 && totalCount == closeCount {
			m.runStopCommands(terminal.TerminalId, terminal.GetTerminalType(), consts.CONSOLE_RUN_SUCCESS, console.STATUS_STOP)
		}

		// 有终端在运行命令发送running状态
		//if totalCount != 0 && totalCount != closeCount {
		//	m.runStopCommands(terminal.TerminalId, terminal.GetTerminalType(), consts.CONSOLE_RUN_SUCCESS, console.STATUS_RUN)
		//}
	}
}

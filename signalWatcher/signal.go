package signalWatcher

import (
	"agent/mq"
	"agent/pkg/processmanager"
	"agent/utils/log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func SyscallListener(mqWrapper *mq.Wrapper, manager *processmanager.ProcessManager) {
	go func() {
		log.Printf("Listening signals...")
		sigChan := make(chan os.Signal, 1)
		//监听指定信号
		signal.Notify(sigChan, syscall.SIGHUP, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGKILL)

		for s := range sigChan {
			stopReason := 0
			switch s {
			case syscall.SIGHUP:
				stopReason = 1 // Term	终端控制进程结束(终端连接断开)
			case syscall.SIGINT:
				stopReason = 2 // Term	用户发送INTR字符(Ctrl+C)触发
			case syscall.SIGQUIT:
				stopReason = 3 // Core	用户发送QUIT字符(Ctrl+/)触发
			case syscall.SIGKILL:
				stopReason = 9 // Kill 杀死 (无法被捕获或忽略的信号)
			case syscall.SIGTERM:
				stopReason = 15 // Term	结束程序(可以被捕获、阻塞或忽略)
			}

			mqWrapper.PublishKillReason(stopReason)

			manager.StopAll()

			time.Sleep(time.Millisecond * 50)

			log.Printf("Go-agent, SyscallListener exit signals: %+v", s)
			os.Exit(stopReason)
		}
	}()
}

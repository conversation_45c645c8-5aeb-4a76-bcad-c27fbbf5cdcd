package monitor

import (
	"agent/mq"
	"agent/utils/log"
	"bufio"
	"bytes"
	"os/exec"
	"strings"
	"time"

	"github.com/google/uuid"
)

// SSHMonitor represents the SSH connection monitor component
type SSHMonitor struct {
	checkInterval time.Duration
	mqWrapper     *mq.Wrapper
	stopChan      chan struct{}
}

// NewSSHMonitor creates a new SSHMonitor instance
func NewSSHMonitor(checkInterval time.Duration, mqWrapper *mq.Wrapper) *SSHMonitor {
	return &SSHMonitor{
		checkInterval: checkInterval,
		mqWrapper:     mqWrapper,
		stopChan:      make(chan struct{}),
	}
}

// DetectSSHConnections uses netstat to detect active SSH connections
func (m *SSHMonitor) DetectSSHConnections() ([]string, error) {
	cmd := exec.Command("sudo", "netstat", "-tnpa")
	var out bytes.Buffer
	var err bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &err
	// 执行命令
	if err := cmd.Run(); err != nil {
		return []string{}, err
	}

	return m.ParseNetstatOutput(out.String()), nil
}

// ParseNetstatOutput parses netstat command output to extract SSH client information
func (m *SSHMonitor) ParseNetstatOutput(output string) []string {
	var clients []string
	scanner := bufio.NewScanner(strings.NewReader(output))

	// 检查输出中是否包含 ESTABLISHED 的 sshd 连接
	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "ESTABLISHED.*sshd") {
			fields := strings.Fields(line)
			if len(fields) >= 5 {
				clients = append(clients, fields[4])
			}
		} else if strings.Contains(line, ":22") && strings.Contains(line, "ESTABLISHED") {
			fields := strings.Fields(line)
			if len(fields) >= 5 {
				clients = append(clients, fields[4]) // 获取远程 IP 地址
			}
		}
	}

	return clients
}

// StartMonitoring starts the periodic SSH connection monitoring
func (m *SSHMonitor) StartMonitoring() {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			clients, err := m.DetectSSHConnections()
			if err != nil {
				log.Errorf("SSH connection detection failed: %+v", err)
				continue
			}

			if len(clients) != 0 {
				log.Infof("SSH connection detection HeartBeat, clients: %+v", clients)
				m.mqWrapper.SendHeartBeatResult(uuid.NewString())
				continue
			}

			log.Infof("SSH connection detection HeartBeat, clients: 0")
		case <-m.stopChan:

			return
		}
	}
}

// Stop gracefully stops the SSH monitoring
func (m *SSHMonitor) Stop() {
	close(m.stopChan)
}

package message

import (
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

// TerminalCmdMQMsgReply represents a message to close a specific terminal instance
type TerminalCmdMQMsgReply struct {
	BaseMQMsg
	// create, close, list
	Cmd string `json:"cmd"`
	// TerminalID contains the unique identifier of the terminal to close
	TerminalID     string `json:"terminalId"`
	TerminalStatus string `json:"terminalStatus"`
	TerminalType   string `json:"terminalType"`
	Sort           int32  `json:"sort"`
}

// ToJSON serializes the TerminalCmdMQMsgReply to JSON
func (m *TerminalCmdMQMsgReply) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TerminalCmdMQMsgReply: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into CloseTerminalMQMsg
func (m *TerminalCmdMQMsgReply) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalCmdMQMsgReply creates a new instance of TerminalCmdMQMsgReply
func NewTerminalCmdMQMsgReply(terminalID string, cmd,
	terminalStatus, result string, terminalType string, replyMessageId string, sort int32) *TerminalCmdMQMsgReply {
	return &TerminalCmdMQMsgReply{
		BaseMQMsg: BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: replyMessageId,
		},
		TerminalID:     terminalID,
		Cmd:            cmd,
		TerminalStatus: terminalStatus,
		TerminalType:   terminalType,
		Sort:           sort,
	}
}

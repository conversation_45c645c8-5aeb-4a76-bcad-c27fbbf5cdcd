package message

import (
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

type TerminalStatusMQMsg struct {
	BaseMQMsg
	// Value contains the status of the terminal (e.g., "running", "stopped")
	Value string `json:"value"`
	// TerminalID contains the unique identifier of the terminal
	// If empty, it refers to the default or currently active terminal
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
}

// ToJSON serializes the TerminalStatusMQMsg to JSON
func (m *TerminalStatusMQMsg) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TerminalStatusMQMsg: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into TerminalStatusMQMsg
func (m *TerminalStatusMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalStatusMQMsg creates a new instance of TerminalStatusMQMsg
func NewTerminalStatusMQMsg(value string, terminalID string, terminalType string) *TerminalStatusMQMsg {
	return &TerminalStatusMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId: utils.GenerateUUID(),
			Timestamp: utils.GetNow(),
		},
		Value:        value,
		TerminalID:   terminalID,
		TerminalType: terminalType,
	}
}

// NewTerminalStatusMQMsgDefault creates a new instance of TerminalStatusMQMsg for the default terminal
func NewTerminalStatusMQMsgDefault(terminalId string, value string, replyMessageId string) *TerminalStatusMQMsg {
	return &TerminalStatusMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: replyMessageId,
		},
		Value:      value,
		TerminalID: terminalId,
	}
}

package message

import (
	"agent/utils/log"
	"encoding/json"
)

type BaseMQMsg struct {
	MessageId      string `json:"messageId"`
	Timestamp      int64  `json:"timestamp"`
	ReplyMessageId string `json:"replyMessageId"`
}

func GetMsgId(strMsg string) string {
	baseMsg := new(BaseMQMsg)
	err := json.Unmarshal([]byte(strMsg), &baseMsg)
	if err != nil {
		log.Errorf("%s.%s", "GetMsgID fail", err)
		return ""
	}
	return baseMsg.MessageId
}

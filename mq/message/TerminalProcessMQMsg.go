package message

import (
	"agent/consts"
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

// TerminalProcessMQMsg represents a message containing information about
// the currently running command in a terminal
type TerminalProcessNameMQMsg struct {
	BaseMQMsg
	// TerminalId contains the unique identifier of the terminal
	TerminalId string `json:"terminalId"`
	// CommandName contains the name of the command being executed
	Value        string `json:"value"`
	TerminalType string `json:"terminalType"`
}

// GetType returns the message type for TerminalCommandMQMsg
func (m *TerminalProcessNameMQMsg) GetType() string {
	return consts.MQ_TERMINAL_PREFIX_PROCESSNAME
}

// ToJSON serializes the TerminalCommandMQMsg to JSON
func (m *TerminalProcessNameMQMsg) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TerminalCommandMQMsg: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into TerminalCommandMQMsg
func (m *TerminalProcessNameMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalCommandMQMsg creates a new instance of TerminalCommandMQMsg
func NewTerminalProcessNameMQMsg(value string, terminalId string, terminalType string) *TerminalProcessNameMQMsg {
	return &TerminalProcessNameMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: "",
		},
		TerminalId:   terminalId,
		Value:        value,
		TerminalType: terminalType,
	}
}

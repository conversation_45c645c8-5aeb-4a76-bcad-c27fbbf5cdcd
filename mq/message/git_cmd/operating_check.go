package git_cmd

import (
	"agent/utils/log"
	"bytes"
	"os"
	"os/exec"
	"strings"
)

// 检查 `.git/index.lock` 文件是否存在
func isGitLockFilePresent(repoPath string) bool {
	lockFilePath := repoPath + "/.git/index.lock"
	log.Infof("lockFilePath: %s", lockFilePath)
	_, err := os.Stat(lockFilePath) // 检测文件是否存在
	return err == nil
}

// 检查是否有正在运行的 Git 相关进程
func isGitProcessRunning() bool {
	// 获取进程列表 (ps aux)
	cmd := exec.Command("ps", "aux")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		log.Errorf("isGitProcessRunning error: %v", err)
		return false
	}

	// 遍历所有进程，匹配 Git 或 bash 调用的 Git 相关进程
	lines := strings.Split(out.String(), "\n")
	for _, line := range lines {
		line = strings.ToLower(strings.TrimSpace(line)) // 转小写，确保不漏大小写匹配
		if strings.Contains(line, " git ") || strings.HasPrefix(line, "git ") || strings.Contains(line, "bash -c git") {
			// 匹配：
			//   1. 直接运行的 Git 命令如 `git status`
			//   2. 包裹在 bash 中执行的 Git 命令如 `bash -c "git pull"`
			return true
		}
	}
	return false
}

// 综合检测方法
func isGitOperating(repoPath string) bool {
	gitLockPresent := isGitLockFilePresent(repoPath) // 检测锁文件
	gitProcessRunning := isGitProcessRunning()       // 检测 Git 中相关的进程

	if gitLockPresent || gitProcessRunning {
		return true // 任一条件满足即可视为 Git 在操作
	}
	return false
}

package git_cmd

import (
	"agent/config"
	"agent/consts"
	"agent/mq/message"
	"agent/utils/log"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/google/uuid"
)

var (
	isUpdateDependencyRunning bool
	updateDependencyMu        sync.Mutex
)

const (
	GIT_CMD_SUCCESS = 0
	GIT_CMD_FAIL    = -1
)

type gitCmdReply struct {
	message.BaseMQMsg
	IsSuccess int8 `json:"is_success"`
	Content   any  `json:"content"`
}

func NewGitCmdReply(replyMessageId string, isSuccess int8, content any) *gitCmdReply {
	resp := &gitCmdReply{
		BaseMQMsg: message.BaseMQMsg{
			MessageId:      uuid.New().String(),
			Timestamp:      time.Now().UnixMicro(),
			ReplyMessageId: replyMessageId,
		},
		IsSuccess: isSuccess,
		Content:   content,
	}
	return resp
}

type BaseGitCmd struct {
	message.BaseMQMsg
	CmdType    string `json:"cmd_type"`
	CmdContent string `json:"cmd_content"`
}

const (
	GitCmdTypeFetchReset = "git_fetch_reset"
	GitCmdTypeFetch      = "git_fetch"
	GitCmdTypePush       = "git_push"
	GitCmdTypeAddCommit  = "git_add_commit"
	GitCmdTypeStatus     = "git_status"
)

// git 命令处理handler
type gitCMdHandler func(cmdType string, cmdContent string) (any, error)

// git 处理handler清单
var gitCmdHandlerFactory map[string]gitCMdHandler

func init() {
	baseGitCmd := BaseGitCmd{}
	gitCmdHandlerFactory = map[string]gitCMdHandler{
		GitCmdTypeFetchReset: baseGitCmd.gitCmdFetchAndResetHandler,
		GitCmdTypeFetch:      baseGitCmd.gitCmdFetchHandler,
		GitCmdTypePush:       baseGitCmd.gitCmdPushHandler,
		GitCmdTypeAddCommit:  baseGitCmd.gitCmdAddCommitHandler,
		GitCmdTypeStatus:     baseGitCmd.gitCmdStatusHandler,
	}
}

// Handler git 命令处理统一入口
func (c *BaseGitCmd) Handler() (any, error) {
	log.Infof("gitCmd:git-cmd-handler-begin, cmd_type: %s, cmd_content: %s", c.CmdType, c.CmdContent)
	if handler, ok := gitCmdHandlerFactory[c.CmdType]; ok {
		res, err := handler(c.CmdType, c.CmdContent)
		if err != nil {
			log.Infof("gitCmd:git-cmd-handler-end, cmd_type: %s, cmd_content: %s", c.CmdType, c.CmdContent)
			return "", err
		}

		return res, nil
	}

	return "", errors.New("unknown git cmd")
}

func (c *BaseGitCmd) getCurrentBranch(path string) (string, error) {
	// 保存当前分支
	currentBranch, err := c.execGitCmd(path, "git", "branch", "--show-current")
	if err != nil {
		return "", err
	}
	currentBranch = strings.TrimSuffix(currentBranch, "\n")
	currentBranch = strings.TrimSpace(currentBranch)

	return currentBranch, nil
}

func (c *BaseGitCmd) fetch(r *git.Repository, path string, cmd *gitCmdParams) (string, error) {
	if cmd.RemoteName == "" {
		return "", errors.New("gitCmdFetch-remote-name is null")
	}

	log.Infof("git-cmd-handler-gitCmdFetch, cmd: %+v", cmd)

	err := c.setGitRepositoryUrl(r, cmd.Token, path, cmd.RemoteName)
	if err != nil {
		return "", err
	}

	fetchOut, err1 := c.execGitCmd(path, "git", "fetch", cmd.RemoteName, cmd.RemoteBranch)
	if err1 != nil {
		log.Infof("git-cmd-handler-gitCmdFetch-fail, cmd: %+v, err: %+v, beforeOut: %s", cmd, err, fetchOut)
		return "", err1
	}

	log.Infof("git-cmd-handler-gitCmdFetch-success, cmd: %+v, currentBranch: %s", cmd, fetchOut)

	return fetchOut, nil
}

// reset
func (c *BaseGitCmd) reset(r *git.Repository, cmd *gitCmdParams, path string) (string, error) {
	log.Infof("git-cmd-handler-gitCmdFetchAndReset-reset, cmd: %+v", cmd)
	err := c.setGitRepositoryUrl(r, cmd.Token, path, cmd.RemoteName)
	if err != nil {
		return "", err
	}

	// reset
	out, err := c.execGitCmd(path, "git", "reset", "--hard", fmt.Sprintf("%s/%s", cmd.RemoteName, cmd.RemoteBranch))
	if err != nil {
		return "", err
	}

	log.Infof("git-cmd-handler-gitCmdFetchAndReset-reset, cmd: %s, err: %+v, out: %s", cmd, err, out)
	return out, nil
}

// 设置git仓库的token信息
func (c *BaseGitCmd) setGitRepositoryUrl(r *git.Repository, token, dir, remoteName string) error {
	rm, err := r.Remote(remoteName)
	if err != nil {
		return err
	}

	configs := rm.Config()
	if len(configs.URLs) == 0 {
		return errors.New("no git remote url")
	}

	url := configs.URLs[0]
	// 兼容 **************:xxxx/rust-zero-server.git，走ssh key认证方式
	if strings.Contains(url, "git@") {
		log.Infof("git-cmd-handler, git_remote_url: %s", url)
		return nil
	}

	//  https://x-access-token:<EMAIL>/xxx/xxxx.git
	urlSlice := strings.Split(url, ":")
	if len(urlSlice) != 3 {
		log.Infof("git-cmd-handler, Error executing git remote urlSlice: %+v, url: %s", urlSlice, url)
		return errors.New("git remote url error1")
	}

	urlSuffixSlice := strings.Split(urlSlice[2], "@")
	if len(urlSuffixSlice) != 2 {
		return errors.New("git remote url error2")
	}

	replacedURL := fmt.Sprintf("https://x-access-token:%s@%s", token, urlSuffixSlice[1])
	// 执行 git remote set-url 命令
	setURLOutput, err := c.execGitCmd(dir, "git", "remote", "set-url", "origin", replacedURL)
	if err != nil {
		log.Infof("git-cmd-handler, Error executing git remote set-url: %v, setURLOutput: %s, "+
			"replacedURL: %s", err, setURLOutput, replacedURL)
		return err
	}

	log.Infof("git-cmd-handler, Git remote set-url command executed successfully, url: %s, "+
		"replacedURL: %s, setURLOutput: %s", url, replacedURL, setURLOutput)

	return nil
}

// 执行git命令
func (c *BaseGitCmd) execGitCmd(dir string, command string, args ...string) (string, error) {
	arg := []string{"bash", "-c", command, strings.Join(args, " ")}
	argStr := strings.Join(arg[2:], " ")
	cmd := exec.Command(arg[0], arg[1], argStr)
	cmd.Dir = dir

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()

	log.Infof("git-cmd-handler-execGitCmd, dir: %s, command: %s, args: %+v, out: %s, err: %v", dir, command, args, out.String(), err)

	return out.String(), err
}

// 执行 cmd 命令
func (c *BaseGitCmd) execCmd(dir string, cmd *exec.Cmd) (string, error) {
	cmd.Dir = dir

	// 检查目录是否存在，存在则设置 PATH
	if _, err := os.Stat("/home/<USER>/app/node_modules/.bin"); os.IsNotExist(err) {
		cmd.Env = os.Environ()
	} else {
		cmd.Env = append(os.Environ(), "PATH="+os.Getenv("PATH")+":/home/<USER>/app/node_modules/.bin")
	}

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()

	log.Infof("git-cmd-handler-execGitCommitCmd, dir: %s, out: %s", dir, out.String())

	return out.String(), err
}

// gitCmdFetchAndResetHandler: fetch and reset
func (c *BaseGitCmd) gitCmdFetchAndResetHandler(cmdType string, cmdContent string) (any, error) {
	startTime := time.Now()
	defer func() {
		log.Infof("gitCmdFetchAndResetHandler took %v", time.Since(startTime))
	}()

	gitCmd := gitCmdParams{}
	err := json.Unmarshal([]byte(cmdContent), &gitCmd)
	if err != nil {
		return "", err
	}

	log.Infof("git-cmd-handler-gitCmdFetchAndReset, cmd_type: %s, cmd_content: %s, gitPullCmd: %+v", cmdType, cmdContent, gitCmd)
	path := consts.AppRootDir
	r, err := git.PlainOpen(path)
	if err != nil {
		return "", err
	}

	// 切换到要操作的分支
	_, err = c.execGitCmd(path, "git", "checkout", gitCmd.LocalBranch)
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdFetchAndReset, checkout-main-cmd: %+v, err1: %+v", gitCmd, err)
		return "", err
	}

	// fetch 操作
	gitLog, err := c.fetch(r, path, &gitCmd)
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdFetchAndReset, gitCmd: %+v, err1: %+v", gitCmd, err)
		return gitLog, err
	}

	// reset 操作
	resetLog, err := c.reset(r, &gitCmd, path)
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdFetchAndReset, gitCmd: %+v, err2: %+v", gitCmd, err)
		return resetLog, err
	}

	gitLogStr, _ := json.Marshal(gitLog)

	log.Infof("git-cmd-handler-gitCmdFetchAndReset-success, gitCmd: %+v, err3: %+v, gitLog: %s\n", gitCmd, err, gitLog)

	go func() {
		_, err2 := c.updateProjectDependency(path)
		if err2 != nil {
			log.Infof("updateProjectDependency error: %+v", err2)
		}
	}()

	return string(gitLogStr), nil
}

// 更新项目依赖
func (c *BaseGitCmd) updateProjectDependency(path string) (string, error) {
	envConfig, err := config.LoadEnvConfig()
	if err != nil {
		return "", fmt.Errorf("failed to load env config: %w", err)
	}
	if envConfig == nil || envConfig.DependencyCommand == "" {
		return "", errors.New("dependency_command is empty")
	}

	updateDependencyMu.Lock()
	if isUpdateDependencyRunning {
		updateDependencyMu.Unlock()
		return "", errors.New("UpdateProjectDependency is running")
	}

	isUpdateDependencyRunning = true
	updateDependencyMu.Unlock()

	args := []string{"source ~/.bashrc &&", envConfig.DependencyCommand}
	argsStr := strings.Join(args, " ")
	args = []string{"bash", "-c", argsStr}
	execCmd := exec.Command(args[0], args[1], args[2])
	execCmd.Dir = path

	var out bytes.Buffer
	execCmd.Stdout = &out
	execCmd.Stderr = &out

	err = execCmd.Run()
	if err != nil {
		log.Infof("pdateProjectDependency, dir: %s, args: %+v, err: %v", path, args, err)
		return "", err
	}

	updateDependencyMu.Lock()
	isUpdateDependencyRunning = false
	updateDependencyMu.Unlock()

	logStr := out.String()

	log.Infof("updateProjectDependency success, cmd: %s, log: %s", envConfig.DependencyCommand, logStr)

	return logStr, nil
}

// gitCmdFetchHandler: fetch
func (c *BaseGitCmd) gitCmdFetchHandler(cmdType string, cmdContent string) (any, error) {
	startTime := time.Now()
	defer func() {
		log.Infof("gitCmdFetchHandler took %v", time.Since(startTime))
	}()

	log.Infof("git-cmd-handler, gitCmdFetch, cmd_type: %s, cmd_content: %s", cmdType, cmdContent)
	gitCmd := gitCmdParams{}
	err := json.Unmarshal([]byte(cmdContent), &gitCmd)
	if err != nil {
		return "", err
	}

	path := consts.AppRootDir
	r, err := git.PlainOpen(path)
	if err != nil {
		return "", err
	}

	// 切换到要操作的分支
	_, err = c.execGitCmd(path, "git", "checkout", gitCmd.LocalBranch)
	if err != nil {
		return "", err
	}

	// fetch 操作
	gitLog, err := c.fetch(r, path, &gitCmd)
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdFetch, gitCmd: %+v, err: %+v", gitCmd, err)
		return "", err
	}

	gitLogStr, _ := json.Marshal(gitLog)

	return string(gitLogStr), nil
}

// gitCmdPushHandler: push
func (c *BaseGitCmd) gitCmdPushHandler(cmdType string, cmdContent string) (any, error) {
	pushDTO := &pushDTO{}

	//if isGitOperating(consts.AppRootDir) {
	//	pushDTO.GitLog = "Git is operating"
	//	log.Infof("git-cmd-handler-gitCmdPushHandler, Git is operating, cmd_type: %s, cmd_content: %s", cmdType, cmdContent)
	//	return pushDTO, nil
	//}

	// 计时
	startTime := time.Now()
	defer func() {
		log.Infof("gitCmdPushHandler took %v", time.Since(startTime))
	}()

	var gitResStr []byte

	log.Infof("git-cmd-handler, gitCmdPush, cmd_type: %s, cmd_content: %s", cmdType, cmdContent)
	gitCmd := gitCmdParams{}
	err := json.Unmarshal([]byte(cmdContent), &gitCmd)
	if err != nil {
		return pushDTO, err
	}

	path := consts.AppRootDir
	//r, err := git.PlainOpen(path)
	//if err != nil {
	//	pushDTO.Error = err.Error()
	//	return pushDTO, nil
	//}

	// push 操作
	pushDTO, err1 := c.push(nil, path, &gitCmd)
	log.Infof("git-cmd-handler-gitCmdPush, gitPushRes: %+v", pushDTO)
	if err1 != nil {
		gitResStr, _ = json.Marshal(pushDTO)
		return string(gitResStr), err1
	}

	revParseDTO, err2 := c.revParse(nil, path, &gitCmd)
	log.Infof("git-cmd-handler-gitCmdPush, gitRevParseRes: %+v", revParseDTO)
	if err2 != nil {
		pushDTO.GitLog += revParseDTO.GitLog // 将 revParse log 拼接到 push log 中
		gitResStr, _ = json.Marshal(pushDTO)
		return string(gitResStr), err2
	}

	pushDTO.CommitID += revParseDTO.CommitID
	gitResStr, _ = json.Marshal(pushDTO)
	return string(gitResStr), nil
}

// 为了保证 engine Marshal 不报错，一定要返回一个结构体
func (c *BaseGitCmd) push(_ *git.Repository, path string, cmd *gitCmdParams) (*pushDTO, error) {
	pushDTO := &pushDTO{}
	log.Infof("git-cmd-handler-gitCmdPush, cmd: %+v", cmd)

	//err := c.setGitRepositoryUrl(r, cmd.Token, path, cmd.RemoteName)
	//if err != nil {
	//	log.Infof("git-cmd-handler-gitCmdPush, setGitRepositoryUrl err: %+v", err)
	//	pushDTO.Error = err.Error()
	//	return pushDTO
	//}

	startTime := time.Now()
	eCmd := exec.Command("bash", "-c", fmt.Sprintf("source ~/.bashrc && git push -u %s %s", cmd.RemoteName, cmd.RemoteBranch))
	gitLog, err := c.execCmd(path, eCmd)
	pushDTO.GitLog = gitLog
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdPush, execGitCmd push log:%+v, err: %+v", gitLog, err)
		return pushDTO, errors.New(gitLog + err.Error())
	}
	log.Infof("git-cmd-handler-gitCmdPush-success, cmd: %+v, contents: %s, timeSince: %s", cmd, gitLog, time.Since(startTime))

	return pushDTO, nil
}

// 为了保证 engine Marshal 不报错，一定要返回一个结构体
func (c *BaseGitCmd) revParse(_ *git.Repository, path string, cmd *gitCmdParams) (*pushDTO, error) {
	log.Infof("git-cmd-handler-gitCmdRevParse, cmd: %+v", cmd)
	revParseDTO := &pushDTO{}

	// 获取当前远程分支的最新 commit id
	gitLog, err := c.execGitCmd(path, "git", "rev-parse", cmd.RemoteBranch)
	revParseDTO.GitLog = gitLog
	if err != nil {
		log.Infof("git-cmd-handler-revParse log:%+v, err: %+v", gitLog, err)
		return revParseDTO, errors.New(gitLog + err.Error())
	}
	log.Infof("git-cmd-handler-revParse-success, cmd: %+v, commitID: %s", cmd, gitLog)

	// 去掉 commitID 末尾的 \n
	revParseDTO.CommitID = strings.TrimSuffix(gitLog, "\n")

	return revParseDTO, nil
}

// gitCmdAddCommitHandler: addAndCommit
func (c *BaseGitCmd) gitCmdAddCommitHandler(cmdType string, cmdContent string) (any, error) {
	addDto := &gitDTO{}

	//if isGitOperating(consts.AppRootDir) {
	//	addDto.GitLog = "Git is operating"
	//	log.Infof("git-cmd-handler-gitCmdAddCommitHandler, Git is operating, cmd_type: %s, cmd_content: %s", cmdType, cmdContent)
	//	return addDto, nil
	//}

	// 计时
	startTime := time.Now()
	defer func() {
		log.Infof("gitCmdAddCommitHandler took %v", time.Since(startTime))
	}()

	log.Infof("git-cmd-handler, gitCmdAddCommit, cmd_type: %s, cmd_content: %s", cmdType, cmdContent)
	var gitResStr []byte

	gitCmd := gitCmdParams{}
	err := json.Unmarshal([]byte(cmdContent), &gitCmd)
	if err != nil {
		return addDto, err
	}

	path := consts.AppRootDir
	//r, err := git.PlainOpen(path)
	//if err != nil {
	//	addDto.Error = err.Error()
	//	return addDto, nil
	//}

	// add 操作
	addDto, err1 := c.add(nil, path, &gitCmd)
	log.Infof("git-cmd-handler-gitCmdAddCommitHandler, gitAddRes: %+v", addDto)
	if err1 != nil {
		gitResStr, _ = json.Marshal(addDto)
		return string(gitResStr), err1
	}

	// commit 操作
	commitDto, err2 := c.commit(nil, path, &gitCmd)
	log.Infof("git-cmd-handler-gitCmdAddCommitHandler, gitCommitRes: %+v", commitDto)
	if err2 != nil {
		addDto.GitLog += commitDto.GitLog // 将 commit log 拼接到 add log 中
		gitResStr, _ = json.Marshal(addDto)
		return string(gitResStr), err2
	}

	addDto.GitLog += commitDto.GitLog // 将 commit log 拼接到 add log 中
	gitResStr, _ = json.Marshal(addDto)
	return string(gitResStr), nil
}

// 为了保证 engine Marshal 不报错，一定要返回一个结构体
func (c *BaseGitCmd) add(_ *git.Repository, path string, cmd *gitCmdParams) (*gitDTO, error) {
	log.Infof("git-cmd-handler-gitCmdAdd, cmd: %+v", cmd)
	dto := &gitDTO{}

	startTime := time.Now()
	gitLog, err := c.execGitCmd(path, "git", "add", cmd.File)
	dto.GitLog = gitLog
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdAdd, execGitCmd add log:%+v, err: %+v", gitLog, err)
		return dto, errors.New(gitLog + err.Error())
	}
	log.Infof("git-cmd-handler-gitCmdAdd-success, cmd: %+v, contents: %s, timeSince: %s", cmd, gitLog, time.Since(startTime))

	return dto, nil
}

// 为了保证 engine Marshal 不报错，一定要返回一个结构体
func (c *BaseGitCmd) commit(_ *git.Repository, path string, cmd *gitCmdParams) (*gitDTO, error) {
	log.Infof("git-cmd-handler-gitCmdCommit, cmd: %+v", cmd)
	dto := &gitDTO{}

	startTime := time.Now()
	eCmd := exec.Command("bash", "-c", fmt.Sprintf("source ~/.bashrc && git commit -m '%s'", strings.ReplaceAll(cmd.Message, "'", "")))
	gitLog, err := c.execCmd(path, eCmd)
	dto.GitLog = gitLog
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdCommit, execGitCmd commit log:%+v, err: %+v", gitLog, err)
		return dto, errors.New(gitLog + err.Error())
	}
	log.Infof("git-cmd-handler-gitCmdCommit-success, cmd: %+v, contents: %s, timeSince: %s", cmd, gitLog, time.Since(startTime))

	return dto, nil
}

func (c *BaseGitCmd) gitCmdStatusHandler(cmdType string, cmdContent string) (any, error) {
	startTime := time.Now()
	defer func() {
		log.Infof("gitCmdStatusHandler took %v", time.Since(startTime))
	}()

	path := consts.AppRootDir

	// 获取变动的文件列表
	status, err := c.status(nil, path, nil)
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdStatus failed: %v", err)
		return "", err
	}

	// 设置是否有未推送的提交
	hasUnpushedCommit := c.checkUnpushedCommits(path)
	status.HasUnpushedCommit = hasUnpushedCommit

	gitResStr, _ := json.Marshal(status)
	return string(gitResStr), nil
}

// checkUnpushedCommits 检查是否有未推送的提交
func (c *BaseGitCmd) checkUnpushedCommits(path string) bool {
	startTime := time.Now()

	// 获取当前分支
	currentBranch, err := c.getCurrentBranch(path)
	if err != nil {
		log.Infof("git-cmd-handler-checkUnpushedCommits, getCurrentBranch error: %v", err)
		return false
	}

	// 检查远程分支是否存在
	remoteBranch := fmt.Sprintf("origin/%s", currentBranch)
	_, err = c.execGitCmd(path, "git", "rev-parse", "--verify", remoteBranch)
	if err != nil {
		// 远程分支不存在，可能没有未推送的提交
		return true
	}

	// 获取本地和远程分支的差异
	gitLog, err := c.execGitCmd(path, "git", "log", "--oneline", fmt.Sprintf("%s..HEAD", remoteBranch))
	if err != nil {
		log.Infof("git-cmd-handler-checkUnpushedCommits, execGitCmd log error: %v", err)
		return false
	}

	log.Infof("git-cmd-handler-checkUnpushedCommits, execGitCmd log: %s, timeSince: %s", gitLog, time.Since(startTime))

	// 如果输出不为空，说明有未推送的提交
	return strings.TrimSpace(gitLog) != ""
}

// status executes git status command and returns the status information
func (c *BaseGitCmd) status(_ *git.Repository, path string, _ *gitCmdParams) (*statusDTO, error) {
	log.Infof("git-cmd-handler-gitCmdStatus")
	dto := &statusDTO{}

	startTime := time.Now()
	gitLog, err := c.execGitCmd(path, "git", "status", "--porcelain=v2")
	if err != nil {
		log.Infof("git-cmd-handler-gitCmdStatus, execGitCmd status log:%+v, err: %+v", gitLog, err)
		return dto, errors.New(gitLog + err.Error())
	}

	// Parse the porcelain v2 output
	lines := strings.Split(gitLog, "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}

		// 处理未跟踪的文件 (格式: ? path)
		if strings.HasPrefix(line, "? ") {
			path := strings.TrimPrefix(line, "? ")
			dto.GitStatus = append(dto.GitStatus, &statusItem{
				Path:   path,
				Added:  false,
				Status: "UNTRACKED",
			})
			continue
		}

		// Parse the status line
		// Format: 1 XY N... 100644 100644 000000 hash1 hash2 path
		parts := strings.Fields(line)
		if len(parts) < 9 {
			continue
		}

		// Extract status and path
		status := parts[1] // XY format where X is index status and Y is working tree status
		path := parts[len(parts)-1]

		// Check both index and working tree status
		indexStatus := status[:1]
		workingTreeStatus := status[1:]

		// Process index status (X in XY)
		if indexStatus != "." {
			var indexStatusStr string
			var added bool

			switch {
			case strings.Contains(indexStatus, "M"):
				indexStatusStr = "MODIFY"
			case strings.Contains(indexStatus, "A"):
				indexStatusStr = "ADD"
				added = true
			case strings.Contains(indexStatus, "D"):
				indexStatusStr = "DELETE"
			case strings.Contains(indexStatus, "R"):
				indexStatusStr = "RENAME"
			case strings.Contains(indexStatus, "C"):
				indexStatusStr = "COPY"
			default:
				indexStatusStr = "UNKNOWN"
			}

			if indexStatusStr != "UNKNOWN" {
				dto.GitStatus = append(dto.GitStatus, &statusItem{
					Path:   path,
					Added:  added,
					Status: indexStatusStr,
				})
			}
		}

		// Process working tree status (Y in XY)
		if workingTreeStatus != "." {
			var workingTreeStatusStr string
			var added bool

			switch {
			case strings.Contains(workingTreeStatus, "M"):
				workingTreeStatusStr = "MODIFY"
			case strings.Contains(workingTreeStatus, "A"):
				workingTreeStatusStr = "ADD"
				added = true
			case strings.Contains(workingTreeStatus, "D"):
				workingTreeStatusStr = "DELETE"
			case strings.Contains(workingTreeStatus, "R"):
				workingTreeStatusStr = "RENAME"
			case strings.Contains(workingTreeStatus, "C"):
				workingTreeStatusStr = "COPY"
			case strings.Contains(workingTreeStatus, "U"):
				workingTreeStatusStr = "UNTRACKED"
			default:
				workingTreeStatusStr = "UNKNOWN"
			}

			if workingTreeStatusStr != "UNKNOWN" {
				dto.GitStatus = append(dto.GitStatus, &statusItem{
					Path:   path,
					Added:  added,
					Status: workingTreeStatusStr,
				})
			}
		}
	}

	log.Infof("git-cmd-handler-gitCmdStatus-success, contents: %s, timeSince: %s", gitLog, time.Since(startTime))
	return dto, nil
}

type gitDTO struct {
	GitLog string `json:"git_log"`
}

type pushDTO struct {
	GitLog   string `json:"git_log"`
	CommitID string `json:"commit_id"`
}

type statusDTO struct {
	GitStatus         []*statusItem `json:"git_status"`
	HasUnpushedCommit bool          `json:"has_unpushed_commit"` // 是否有未推送的 commit
}

type statusItem struct {
	Path   string `json:"path"`   // 文件路径
	Added  bool   `json:"added"`  // 是否添加到暂存区
	Status string `json:"status"` // 文件状态
}

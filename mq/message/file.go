package message

import "agent/file/grep"

type FilePullMsg struct {
	BaseMQMsg
	Paths []string `json:"paths"`
}

// Directory operation types
const (
	DirChangeUpdate = 0
	DirChangeCreate = 1
	DirChangeRemove = 2
)

type DirPullMsg struct {
	BaseMQMsg
	Dirs []DirOperationMsg `json:"dirs"`
}

// DirOperationMsg represents a directory operation message
type DirOperationMsg struct {
	Type    int    `json:"type"`     // Type of operation: 0, 1, 2
	SrcPath string `json:"src_path"` // Path of the directory to operate on
	DstPath string `json:"dst_path"` // Target path for move operation (optional)
}

type FileGrep struct {
	BaseMQMsg
	Keyword           string `json:"keyword"`
	Cwd               string `json:"cwd"`
	CaseSensitive     bool   `json:"case_sensitive"`
	WholeWordMatching bool   `json:"whole_word_matching"`
	Regex             bool   `json:"regex"`
}

type FileGrepResult struct {
	BaseMQMsg
	Result map[string][]grep.Line `json:"result,omitempty"`
}

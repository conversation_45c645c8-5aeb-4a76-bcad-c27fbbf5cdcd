package message

import (
	"agent/mq/utils"
)

// TerminalHeartbeatMQMsg represents a heartbeat message containing the status of all terminals
type TerminalHeartbeatMQMsg struct {
	BaseMQMsg
	TerminalList interface{} `json:"multiTerminalList"`
}

// NewTerminalHeartbeatMQMsg creates a new terminal heartbeat message with the given terminal list
func NewTerminalHeartbeatMQMsg(value interface{}) *TerminalHeartbeatMQMsg {
	msg := &TerminalHeartbeatMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId: utils.GenerateUUID(),
			Timestamp: utils.GetNow(),
		},
		TerminalList: value,
	}
	return msg
}

package message

type DockerInfo struct {
	DockerId     string `json:"dockerId"`
	PlaygroundId string `json:"playgroundId"`
	RunStatus    string `json:"runStatus"`

	Language         string `json:"language"`
	LanguageVersion  string `json:"languageVersion"`
	Framework        string `json:"framework"`
	FrameworkVersion string `json:"frameworkVersion"`
	NeedRunButton    bool   `json:"needRunButton"`
	NeedConsole      bool   `json:"needConsole"`
	NeedBrowser      bool   `json:"needBrowser"`
	Url              string `json:"url"`
	LspStatus        string `json:"lspStatus"`
	LspUrl           string `json:"lspUrl"`
	// LSP中文件URI的根路径
	LspRoot        string `json:"lspRoot"`
	TerminalStatus string `json:"terminalStatus"`
	//RagStatus      string `json:"ragStatus"`
	Gui bool `json:"gui"`

	// vnc
	VncSupport bool   `json:"vncSupport"`
	VncStatus  string `json:"vncStatus"`

	//是否支持debug
	DebugSupport bool  `json:"debugSupport"`
	Refresh      bool  `json:"refresh"`      // 是否刷新
	IntervalTime int64 `json:"intervalTime"` // 刷新时间间隔 时间毫秒
}

type DockerInfoMQMsg struct {
	BaseMQMsg
	*DockerInfo
}

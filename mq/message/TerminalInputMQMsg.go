package message

import (
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

type TerminalInputMQMsg struct {
	BaseMQMsg
	// Value contains the input to be sent to the terminal
	Value string `json:"value"`
	// TerminalID contains the unique identifier of the target terminal
	// If empty, it refers to the default or currently active terminal
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
}

// ToJSON serializes the TerminalInputMQMsg to JSON
func (m *TerminalInputMQMsg) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TerminalInputMQMsg: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into TerminalInputMQMsg
func (m *TerminalInputMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalInputMQMsg creates a new instance of TerminalInputMQMsg
func NewTerminalInputMQMsg(value string, terminalID string, terminalType string) *TerminalInputMQMsg {
	return &TerminalInputMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId: utils.GenerateUUID(),
			Timestamp: utils.GetNow(),
		},
		Value:        value,
		TerminalID:   terminalID,
		TerminalType: terminalType,
	}
}

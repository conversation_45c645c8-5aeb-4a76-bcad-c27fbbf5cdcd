package unittest

import (
	"agent/config"
	"agent/mq/message"
)

type RunUnitTestMQMsg struct {
	message.BaseMQMsg
	RunId         string             `json:"runId"`
	Cmd           string             `json:"cmd"`
	OutPutConsole bool               `json:"outPutConsole"`
	ConsoleText   string             `json:"consoleText"`
	FileKey       string             `json:"FileKey"`
	Debug         config.DebugConfig `json:"debug"`
	RunMode       bool               `json:"runMode"` // true为debug false为run
}

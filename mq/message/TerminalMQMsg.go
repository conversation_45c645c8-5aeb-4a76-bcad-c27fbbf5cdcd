package message

import (
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

type TerminalMQMsg struct {
	BaseMQMsg
	// Value contains the terminal output content
	Value string `json:"value"`
	// TerminalID contains the unique identifier of the terminal
	// If empty, it refers to the default or currently active terminal
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
}

// ToJSON serializes the TerminalMQMsg to JSON
func (m *TerminalMQMsg) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TerminalMQMsg: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into TerminalMQMsg
func (m *TerminalMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalMQMsg creates a new instance of TerminalMQMsg
func NewTerminalMQMsg(value string, terminalID string, terminalType string) *TerminalMQMsg {
	return &TerminalMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId: utils.GenerateUUID(),
			Timestamp: utils.GetNow(),
		},
		Value:        value,
		TerminalID:   terminalID,
		TerminalType: terminalType,
	}
}

// NewTerminalMQMsgDefault creates a new instance of TerminalMQMsg with default terminal
func NewTerminalMQMsgDefault(terminalId string, value string, replyMessageId string) *TerminalMQMsg {
	return &TerminalMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: replyMessageId,
		},
		Value:      value,
		TerminalID: terminalId,
	}
}

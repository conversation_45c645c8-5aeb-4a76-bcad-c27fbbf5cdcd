package message

import (
	"agent/mq/utils"
	"encoding/json"
	"fmt"
)

// TerminalCmdMQMsg represents a message to close a specific terminal instance
type TerminalCmdMQMsg struct {
	BaseMQMsg
	// create, close, list
	Cmd string `json:"cmd"`
	// TerminalID contains the unique identifier of the terminal to close
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
}

// ToJSON serializes the CloseTerminalMQMsg to JSON
func (m *TerminalCmdMQMsg) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", fmt.Errorf("failed to marshal CloseTerminalMQMsg: %v", err)
	}
	return string(bytes), nil
}

// FromJSON deserializes a JSON string into CloseTerminalMQMsg
func (m *TerminalCmdMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), m)
}

// NewTerminalCmdMQMsg creates a new instance of TerminalCmdMQMsg
func NewTerminalCmdMQMsg(terminalID string, cmd string, terminalType string, replyMessageId string) *TerminalCmdMQMsg {
	return &TerminalCmdMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: replyMessageId,
		},
		Cmd:          cmd,
		TerminalID:   terminalID,
		TerminalType: terminalType,
	}
}

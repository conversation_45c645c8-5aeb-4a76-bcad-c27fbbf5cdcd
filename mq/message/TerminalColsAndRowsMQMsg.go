package message

type TerminalColsAndRowsMQMsg struct {
	BaseMQMsg
	Value string `json:"value"`
	// TerminalID contains the unique identifier of the target terminal
	// If empty, it refers to the default or currently active terminal
	TerminalID   string `json:"terminalId"`
	TerminalType string `json:"terminalType"`
}

type XtermColsAndRowsValueMQMsg struct {
	Cols int    `json:"cols"`
	Rows int    `json:"rows"`
	Type string `json:"type"`
}

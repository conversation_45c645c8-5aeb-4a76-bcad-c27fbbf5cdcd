package message

import (
	"encoding/json"
	"time"
)

// Constants for file change types
const (
	FileChangeCreated = "created"
	FileChangeModified = "modified"
	FileChangeDeleted = "deleted"
)

// FileChangeLinterMQMsg represents a message about a file change that needs linting
type FileChangeLinterMQMsg struct {
	BaseMQMsg
	FilePath   string `json:"file_path"`   // Path to the file that changed
	ChangeType string `json:"change_type"` // Type of change (created, modified, deleted)
	Language   string `json:"language"`    // Programming language of the file
}

// ToJSON serializes the message to JSON string
func (msg *FileChangeLinterMQMsg) ToJSON() (string, error) {
	data, err := json.Marshal(msg)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON deserializes JSON string to FileChangeLinterMQMsg
func (msg *FileChangeLinterMQMsg) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), msg)
}

// GetType returns the message type
func (msg *FileChangeLinterMQMsg) GetType() string {
	return "fileChangeLinter"
}

// NewFileChangeLinterMQMsg creates a new FileChangeLinterMQMsg with initialized fields
func NewFileChangeLinterMQMsg(filePath string, changeType string, language string) *FileChangeLinterMQMsg {
	return &FileChangeLinterMQMsg{
		BaseMQMsg: BaseMQMsg{
			MessageId: "file_change_" + filePath,
			Timestamp: time.Now().UnixNano() / int64(time.Millisecond),
		},
		FilePath:   filePath,
		ChangeType: changeType,
		Language:   language,
	}
}

// WithReplyTo sets the message as a reply to another message
func (msg *FileChangeLinterMQMsg) WithReplyTo(replyMsgId string) *FileChangeLinterMQMsg {
	msg.ReplyMessageId = replyMsgId
	return msg
}
package mq

const (
	// 消息类型
	TERMINAL = "terminal"
	// terminal 状态
	TERMINAL_STATUS               = "terminalStatus"
	MULTI_TERMINAL                = "multiTerminal"
	MULTI_TERMINALCMD             = "multiTerminalCmd"
	MULTI_TERMINALCMD_REPLY       = "multiTerminalCmdReply"
	MULTI_TERMINALCMD_PROCESSNAME = "multiTerminalProcessName"
	MULTI_TERMINALCMD_HEARTBEAT   = "multiTerminalHeartBeat"
	REFRESHXTERM_COLS_ROWS        = "refreshXtermColsRows"

	RUN                = "run"
	CONSOLE            = "console"
	STOP               = "stop"
	DOCKER_INFO        = "dockerInfo"
	DOCKER_INFO_UPDATE = "updateDockerInfo"
	DOCKER_STATUS      = "dockerStatus"
	CONFIG             = "config"
	MQ_START           = "mqStart"
	HTTP_PROXY         = "httpProxy"
	AVAILABLE_PORTS    = "availablePorts"
	PORTS_CHANGED      = "portsChanged"

	// LSP状态
	LSP_STATUS = "lspStatus"

	// VNC状态
	VNC_STATUS = "vncStatus"

	// 单体测试运行
	GET_UNIT_TEST_FUN          = "getUnitTestFun"
	UNIT_TEST_FUN              = "unitTestFun"
	RUN_UNIT_TEST              = "runUnitTest"
	RUN_UNIT_TEST_BY_IDESERVER = "runUnitTestByIdeServer"
	STOP_UNIT_TEST             = "stopUnitTest"
	UNIT_TEST_RESULT           = "unitTestResult"
	// 心跳
	HEART_BEAT        = "heartbeat"
	DOCKER_HEART_BEAT = "dockerHeartbeat"

	// config消息中配置项的名称
	CONFIG_COMPONENTS = "components"
	// config中是否支持debug
	CONFIG_DEBUG_SUPPORT = "debugSupport"

	// 端口打开
	PORT_OPEN = "portOpen"
	// 运行状态
	RUN_STATUS             = "runStatus"
	KILL_STATUS            = "killStatus"
	FILE_CHANGE            = "fileChange"
	RUN_CMD                = "runCmd"
	STOP_RUN_CMD           = "stopRunCmd"
	RUN_CMD_RESULT         = "runCmdResult"
	STOP_RUN_CMD_RESULT    = "stopRunCmdResult"
	DEBUG_RUN              = "debugRun"
	DEBUG_DAP              = "debugDAP"
	LSP_START              = "lspStart"
	ENV                    = "env"
	CHANGE_IDESERVER       = "changeIdeServer"
	MOUNT_SUCCESS          = "mountSuccess"
	FileWatchRefreshConfig = "fileWatchRefreshConfig"
	FileWatchRefreshNow    = "fileWatchRefreshNow"
	ResourceMonitoring     = "resourceMonitoring"

	Execution = "execution"

	FilePull      = "filePull"
	DirPull       = "DirPull"
	CLEAR_CONSOLE = "clearConsoleSign"
	FileGrep      = "fileGrep"
	//RagSearch     = "ragSearch"
	//RagStatus     = "ragStatus"

	// AI Terminal
	AI_TERMINAL        = "aiTerminal"
	TERMINAL_AI_STATUS = "aiTerminalStatus"
	AI_TERMINAL_KILL   = "aiTerminalKill"
	ENV_AI             = "aiEnv"
)

// 配置文件中，支持的组件列表
var COMPONENTS_LIST = []string{"filetree", "shell", "console", "preview"}

package mq

import (
	"agent/config"
	"agent/consts"
	"agent/file/watch"
	"agent/infoHolder"
	lspConsts "agent/ls/consts"
	"agent/mq/message"
	"agent/mq/message/git_cmd"
	"agent/mq/message/unittest"
	"agent/mq/utils"
	"agent/multiTerminal/terminal"
	consts2 "agent/pkg/consts"
	unittest2 "agent/unittest"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	resouce "agent/utils/resource"
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/time/rate"
	"math"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/shirou/gopsutil/v4/cpu"

	amqp "github.com/rabbitmq/amqp091-go"
)

type Wrapper struct {
	conn                            *amqp.Connection
	channel                         *amqp.Channel
	mqToFwChannel                   chan<- int
	fwToMQChannel                   <-chan string
	mqToMultiTerminalChannel        chan<- terminal.TerminalToMqItem
	multiTerminalToMqObj            <-chan terminal.TerminalToMqItem
	mqToMultiTerminalCmdChannel     chan<- string
	mqToMultiTerminalConsoleChannel chan<- string
	multiTerminalToMQCmdChannel     <-chan terminal.TerminalToMqItem
	mqToTerminalAIChannel           chan<- string
	terminalAIToMQChannel           <-chan string
	mqToLspChannel                  chan<- string
	multiConsoleToMQChannel         <-chan string
	fileChangeChannel               <-chan []watch.FileChange
	//mqToRagSearchChannel            chan<- message.RagSearchMQMsg
	//ragSearchToMqChannel            <-chan message.RagSearchResultMQMsg
	//ragStatusToMqChannel            <-chan message.RagStatusMQMsg
	//fileChangeRagChannel            chan<- []watch.FileChange
	httpProxyToMqChannel   <-chan string
	mqToHttpProxyChannel   chan<- string
	httpProxyHookChannel   chan<- string
	middlewareProxyChannel chan string

	//重连
	consumeNotifyChannel    chan int
	msgNotifyChannel        chan int
	rpcConsumeNotifyChannel chan int
	rpcMsgNotifyChannel     chan int

	// 记录上次发送的config。配置文件变化，但是需通知前端部分未变时，不发送消息
	lastConfig      string
	lspStatus       string
	nixStatus       string
	ideServerCode   string
	runUnitTestLock sync.Mutex
	runCmdLock      sync.Mutex
	runCmdPid       map[string]int
	mqLock          sync.WaitGroup
	watch           *watch.Wrapper

	vncStatus string
}

func MakeNew(
	mqToFwChannel chan<- int,
	fwToMQChannel <-chan string,
	mqToMultiTerminalChannel chan<- terminal.TerminalToMqItem,
	multiTerminalToMqObj <-chan terminal.TerminalToMqItem,
	mqToMultiTerminalCmdChannel chan<- string,
	mqToMultiTerminalConsoleChannel chan<- string,
	multiTerminalToMQCmdChannel <-chan terminal.TerminalToMqItem,
	multiConsoleToMQChannel <-chan string,
	mqToLspChannel chan<- string,
	fileChangeChannel <-chan []watch.FileChange,
	watch *watch.Wrapper,
	//mqToRagSearchChannel chan<- message.RagSearchMQMsg,
	//ragSearchToMqChannel <-chan message.RagSearchResultMQMsg,
	//ragStatusToMqChannel <-chan message.RagStatusMQMsg,
	//fileChangeRagChannel chan<- []watch.FileChange,
	httpProxyToMqChannel <-chan string,
	mqToHttpProxyChannel chan<- string,
	httpProxyHookChannel chan<- string,
	middlewareProxyChannel chan string,
) *Wrapper {

	wrapper := Wrapper{}
	wrapper.mqToFwChannel = mqToFwChannel
	wrapper.fwToMQChannel = fwToMQChannel
	wrapper.mqToMultiTerminalChannel = mqToMultiTerminalChannel
	wrapper.multiTerminalToMqObj = multiTerminalToMqObj
	wrapper.mqToMultiTerminalCmdChannel = mqToMultiTerminalCmdChannel
	wrapper.mqToMultiTerminalConsoleChannel = mqToMultiTerminalConsoleChannel
	wrapper.multiTerminalToMQCmdChannel = multiTerminalToMQCmdChannel
	wrapper.multiConsoleToMQChannel = multiConsoleToMQChannel
	wrapper.fileChangeChannel = fileChangeChannel
	wrapper.mqToLspChannel = mqToLspChannel
	wrapper.watch = watch
	//wrapper.mqToRagSearchChannel = mqToRagSearchChannel
	//wrapper.ragSearchToMqChannel = ragSearchToMqChannel
	//wrapper.ragStatusToMqChannel = ragStatusToMqChannel
	//wrapper.fileChangeRagChannel = fileChangeRagChannel
	wrapper.httpProxyToMqChannel = httpProxyToMqChannel
	wrapper.mqToHttpProxyChannel = mqToHttpProxyChannel
	wrapper.httpProxyHookChannel = httpProxyHookChannel
	wrapper.middlewareProxyChannel = middlewareProxyChannel

	//重连
	wrapper.consumeNotifyChannel = make(chan int)
	wrapper.msgNotifyChannel = make(chan int)
	wrapper.rpcConsumeNotifyChannel = make(chan int)
	wrapper.rpcMsgNotifyChannel = make(chan int)

	if len(envUtils.GetString(consts.PAAS_LspStartCmd)) > 0 || len(envUtils.GetString(consts.PAAS_LspLanguageIds)) > 0 {
		wrapper.lspStatus = lspConsts.LspStatusLoading
	} else {
		wrapper.lspStatus = lspConsts.LspStatusNotSupport
	}
	wrapper.runCmdPid = make(map[string]int)

	if cmdUtils.LookPath("vncserver") {
		wrapper.vncStatus = consts2.VncStatusLoading
	} else {
		wrapper.vncStatus = consts2.VncStatusNotSupport
	}

	return &wrapper
}

func (wrapper *Wrapper) Start() error {
	var err error
	err = wrapper.connect()
	if err != nil {
		log.Errorf("%s.%s", "Connect MQ fail", err)
		return err
	}

	err = wrapper.receiveMQ()
	if err != nil {
		log.Errorf("%s.%s", "Receive MQ fail", err)
		return err
	}

	// rpc server
	go func() {
		err = wrapper.connectAndReceiveRpcMq()
		if err != nil {
			log.Errorf("%s.%s", "ReceiveRpc-MQ-fail", err)
		}
	}()

	// 监听proxy的输入
	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("listenHttpProxy panic error: %+v", panicErr)
			}
		}()
		wrapper.listenHttpProxy()
	}()

	// 监听多终端Terminal的输入
	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("listenTerminal panic error: %+v", panicErr)
			}
		}()
		wrapper.listenMultiTerminal()
	}()

	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("listenMultiConsole panic error: %+v", panicErr)
			}
		}()
		wrapper.listenMultiConsole()
	}()

	// 监听文件变化
	go func() {
		defer func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("listenFileChange panic error: %+v", panicErr)
			}
		}()
		wrapper.listenFileChange()
	}()

	//// 监听Rag结果
	//go func() {
	//	defer func() {
	//		if panicErr := recover(); panicErr != nil {
	//			log.PrintPanicInfo("listenRagResult panic error: %+v", panicErr)
	//		}
	//	}()
	//	wrapper.listenRagResult()
	//}()
	//// 监听Rag状态
	//go func() {
	//	defer func() {
	//		if panicErr := recover(); panicErr != nil {
	//			log.PrintPanicInfo("listenRagStatus panic error: %+v", panicErr)
	//		}
	//	}()
	//	wrapper.listenRagStatus()
	//}()

	if !envUtils.IsLocal() {
		wrapper.mqLock.Add(1)
		// mq 创建好之后告诉 manager 需要执行命令
		go wrapper.PublishMqStartSuccess()
		log.Debugf("lock.time : %s\n", time.Now())
		wrapper.mqLock.Wait()
	}

	if envUtils.GetString(consts.PaasResourceMonitoring) == "true" {
		go func() {
			if panicErr := recover(); panicErr != nil {
				log.PrintPanicInfo("resourceMonitoring panic error: %+v", panicErr)
			}
			wrapper.resourceMonitoring()
		}()
	}
	return nil
}

func (wrapper *Wrapper) resourceMonitoring() {
	for {
		wrapper.doResourceMonitoring()
		time.Sleep(time.Second * 5)
	}
}

func (wrapper *Wrapper) doResourceMonitoring() {
	current, err := resouce.MemoryCurrent()
	max, err2 := resouce.MemoryMax()
	//percent, err3 := resouce.CpuPercent()
	if err != nil || err2 != nil {
		log.Errorf("doResourceMonitoring getting memory percent err: %+v, err2: %+v", err, err2)
		return
	}

	// 返回的数组是所有cpu的使用率，每个元素是一个cpu
	cpuPercent, err3 := cpu.Percent(0, false)
	if err != nil {
		log.Errorf("doResourceMonitoring getting CPU percent err3: %+v", err3)
		return
	}

	averageCPUPercentFloat64 := float64(0)
	if len(cpuPercent) != 0 {
		// 转为小数形式
		averageCPUPercentFloat64 = cpuPercent[0] / float64(100)
	}

	// 获取整体系统的内存使用情况
	//virtualMem, err := mem.VirtualMemory()
	//if err != nil {
	//	fmt.Println("Error getting virtual memory: ", err)
	//	return
	//}
	//fmt.Printf("Total Memory: %v MB\n", virtualMem.Total/1024/1024)
	//fmt.Printf("Used Memory: %v MB\n", virtualMem.Used/1024/1024)
	//fmt.Printf("Free Memory: %v MB\n", virtualMem.Free/1024/1024)
	//fmt.Printf("Memory Usage: %f%%\n", virtualMem.UsedPercent)
	msg := message.MonitorInfoMQMsg{
		BaseMQMsg: message.BaseMQMsg{
			MessageId:      utils.GenerateUUID(),
			Timestamp:      utils.GetNow(),
			ReplyMessageId: "",
		},
		MemoryCurrent: current,
		MemoryMax:     max,
		CpuPercent:    averageCPUPercentFloat64,
	}
	wrapper.send(ResourceMonitoring, utils.Msg2String(msg))
}

func (wrapper *Wrapper) PublishMqStartSuccess() {
	publishMqStartSuccess := message.MqStartMQMsg{}
	publishMqStartSuccess.MessageId = utils.GenerateUUID()
	publishMqStartSuccess.Timestamp = utils.GetNow()
	publishMqStartSuccess.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	wrapper.sendToManager(MQ_START, utils.Msg2String(publishMqStartSuccess))
}
func (wrapper *Wrapper) PublishDockerInfoToManager(replyMessageId string) {
	dockerInfoMsg := message.DockerInfoMQMsg{}
	dockerInfoMsg.MessageId = utils.GenerateUUID()
	dockerInfoMsg.Timestamp = utils.GetNow()
	dockerInfoMsg.ReplyMessageId = replyMessageId
	dockerInfoMsg.DockerInfo = infoHolder.GetDockerInfo()
	if dockerInfoMsg.DockerInfo != nil {
		dockerInfoMsg.DockerInfo.LspStatus = wrapper.lspStatus
		dockerInfoMsg.DockerInfo.Gui = infoHolder.IsGui()
		dockerInfoMsg.DockerInfo.DebugSupport = infoHolder.DebugSupport()
		dockerInfoMsg.DockerInfo.VncSupport = cmdUtils.LookPath("vncserver")
		dockerInfoMsg.DockerInfo.VncStatus = wrapper.vncStatus
		dockerInfoMsg.DockerInfo.Refresh = wrapper.watch.RefreshConfig.Refresh
		dockerInfoMsg.DockerInfo.IntervalTime = int64(wrapper.watch.RefreshConfig.IntervalTime / time.Millisecond)
	}
	wrapper.sendToManager(DOCKER_INFO, utils.Msg2String(dockerInfoMsg))
}
func (wrapper *Wrapper) PublishUpdateDockerInfoToManager(replyMessageId string) {
	dockerInfoMsg := message.DockerInfoMQMsg{}
	dockerInfoMsg.MessageId = utils.GenerateUUID()
	dockerInfoMsg.Timestamp = utils.GetNow()
	dockerInfoMsg.ReplyMessageId = replyMessageId
	dockerInfoMsg.DockerInfo = infoHolder.GetDockerInfo()
	if dockerInfoMsg.DockerInfo != nil {
		dockerInfoMsg.DockerInfo.LspStatus = wrapper.lspStatus
		dockerInfoMsg.DockerInfo.Gui = infoHolder.IsGui()
		dockerInfoMsg.DockerInfo.DebugSupport = infoHolder.DebugSupport()
		dockerInfoMsg.DockerInfo.VncSupport = cmdUtils.LookPath("vncserver")
		dockerInfoMsg.DockerInfo.VncStatus = wrapper.vncStatus
		dockerInfoMsg.DockerInfo.Refresh = wrapper.watch.RefreshConfig.Refresh
		dockerInfoMsg.DockerInfo.IntervalTime = int64(wrapper.watch.RefreshConfig.IntervalTime / time.Millisecond)
	}

	wrapper.sendToManager(DOCKER_INFO_UPDATE, utils.Msg2String(dockerInfoMsg))
}

func (wrapper *Wrapper) PublishDockerStatusToManager(replyMessageId string) {
	baseMQMsg := message.BaseMQMsg{}
	baseMQMsg.MessageId = utils.GenerateUUID()
	baseMQMsg.Timestamp = utils.GetNow()
	baseMQMsg.ReplyMessageId = replyMessageId
	wrapper.sendToManager(DOCKER_STATUS, utils.Msg2String(baseMQMsg))

}
func (wrapper *Wrapper) PublishDockerInfo(replyMessageId string) {
	dockerInfoMsg := message.DockerInfoMQMsg{}
	dockerInfoMsg.MessageId = utils.GenerateUUID()
	dockerInfoMsg.Timestamp = utils.GetNow()
	dockerInfoMsg.ReplyMessageId = replyMessageId
	dockerInfoMsg.DockerInfo = infoHolder.GetDockerInfo()
	dockerInfoMsg.DockerInfo.LspStatus = wrapper.lspStatus
	dockerInfoMsg.DockerInfo.Gui = infoHolder.IsGui()
	dockerInfoMsg.DockerInfo.DebugSupport = infoHolder.DebugSupport()
	dockerInfoMsg.DockerInfo.VncSupport = cmdUtils.LookPath("vncserver")
	dockerInfoMsg.DockerInfo.VncStatus = wrapper.vncStatus
	dockerInfoMsg.DockerInfo.Refresh = wrapper.watch.RefreshConfig.Refresh
	dockerInfoMsg.DockerInfo.IntervalTime = int64(wrapper.watch.RefreshConfig.IntervalTime / time.Millisecond)
	wrapper.send(DOCKER_INFO, utils.Msg2String(dockerInfoMsg))
}

func (wrapper *Wrapper) PublishMultiRunStatus(terminalId, terminalType, status, runResult string) {
	runStatusMQMsg := message.RunStatusMQMsg{}
	runStatusMQMsg.MessageId = utils.GenerateUUID()
	runStatusMQMsg.Timestamp = utils.GetNow()
	runStatusMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	runStatusMQMsg.PlaygroundId = envUtils.GetString(consts.PAAS_PlaygroundId)
	runStatusMQMsg.Status = status
	runStatusMQMsg.TerminalId = terminalId
	runStatusMQMsg.TerminalType = terminalType
	runStatusMQMsg.RunResult = runResult
	runStatusMQMsg.Gui = infoHolder.IsGui()
	runStatusMQMsg.InternalRunInfo = infoHolder.InternalRunInfo()
	wrapper.send(RUN_STATUS, utils.Msg2String(runStatusMQMsg))
	wrapper.PublishUpdateDockerInfoToManager("")
}

func (wrapper *Wrapper) PublishRunStatus() {
	runStatusMQMsg := message.RunStatusMQMsg{}
	runStatusMQMsg.MessageId = utils.GenerateUUID()
	runStatusMQMsg.Timestamp = utils.GetNow()
	runStatusMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	runStatusMQMsg.PlaygroundId = envUtils.GetString(consts.PAAS_PlaygroundId)
	runStatusMQMsg.Status = infoHolder.GetRunStatus()
	runStatusMQMsg.RunResult = infoHolder.GetRunResult()
	runStatusMQMsg.Gui = infoHolder.IsGui()
	runStatusMQMsg.InternalRunInfo = infoHolder.InternalRunInfo()
	wrapper.send(RUN_STATUS, utils.Msg2String(runStatusMQMsg))
	wrapper.PublishUpdateDockerInfoToManager("")
}

func (wrapper *Wrapper) FileChange(fileChanges []watch.FileChange) bool {
	limiter := rate.NewLimiter(rate.Every(10*time.Millisecond), 100)
	if limiter.Allow() {
		fileChangeMQMsg := message.FileChangeMQMsg{}
		fileChangeMQMsg.MessageId = utils.GenerateUUID()
		fileChangeMQMsg.Timestamp = utils.GetNow()
		fileChangeMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
		fileChangeMQMsg.FileChanges = fileChanges
		wrapper.send(FILE_CHANGE, utils.Msg2String(fileChangeMQMsg))
		return true
	}

	return false
}

func (wrapper *Wrapper) PublishConfig() {
	for {
		func() {
			cfg, err := config.LoadEnvConfig()
			if err != nil {
				return
			}
			configMsg := message.ConfigMQMsg{}
			configMsg.MessageId = utils.GenerateUUID()
			configMsg.Timestamp = utils.GetNow()
			configMsg.ReplyMessageId = ""
			configMsg.Configs = make(map[string]interface{})
			configMsg.Configs[CONFIG_COMPONENTS] = cfg.Components
			configMsg.Configs[CONFIG_DEBUG_SUPPORT] = cfg.Debug.Support
			if strings.ToLower(envUtils.GetString(consts.PAAS_LspLanguageId)) == consts.LanguageJava {
				configMsg.Configs[CONFIG_DEBUG_SUPPORT] = cfg.Debug.Support && envUtils.GetInt(consts.DebugServerPort) > 0
			}
			// 配置正确且有变化时，才需要发送
			configByte, _ := json.Marshal(configMsg.Configs)
			if string(configByte) != wrapper.lastConfig {
				wrapper.lastConfig = string(configByte)
				wrapper.send(CONFIG, utils.Msg2String(configMsg))
			}
		}()
		time.Sleep(time.Second * 3)
	}

}

func (wrapper *Wrapper) PublishLSPStatus(language string, replyMessageId string, status string) {
	wrapper.lspStatus = status
	lspStatusMsg := message.LspStatusMQMsg{}
	lspStatusMsg.MessageId = utils.GenerateUUID()
	lspStatusMsg.Timestamp = utils.GetNow()
	lspStatusMsg.ReplyMessageId = replyMessageId
	lspStatusMsg.Language = language
	lspStatusMsg.Status = wrapper.lspStatus
	wrapper.send(LSP_STATUS, utils.Msg2String(lspStatusMsg))
	wrapper.PublishUpdateDockerInfoToManager("")
}

func (wrapper *Wrapper) PublishVNCStatus(replyMessageId string, status string) {
	wrapper.vncStatus = status
	vncStatusMsg := message.VncStatusMQMsg{}
	vncStatusMsg.MessageId = utils.GenerateUUID()
	vncStatusMsg.Timestamp = utils.GetNow()
	vncStatusMsg.ReplyMessageId = replyMessageId
	vncStatusMsg.Status = wrapper.vncStatus
	wrapper.send(VNC_STATUS, utils.Msg2String(vncStatusMsg))
	wrapper.PublishUpdateDockerInfoToManager("")
}

func (wrapper *Wrapper) UpdateVncStatus(status string) {
	wrapper.PublishVNCStatus("", status)
}

func (wrapper *Wrapper) SendUnitTestResult(replyMessageId, runId, fileKey, result, error string, executeSuccess bool) {
	testResultMsg := unittest.UnitTestResultMQMsg{}
	testResultMsg.MessageId = utils.GenerateUUID()
	testResultMsg.Timestamp = utils.GetNow()
	testResultMsg.ReplyMessageId = replyMessageId
	testResultMsg.RunId = runId
	testResultMsg.FileKey = fileKey
	testResultMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	testResultMsg.Result = result
	testResultMsg.ExecuteSuccess = executeSuccess
	testResultMsg.Error = error
	// TODO: 先冗余发送 业务方对接完去掉
	wrapper.sendToManager(UNIT_TEST_RESULT, utils.Msg2String(testResultMsg))
	wrapper.send(UNIT_TEST_RESULT, utils.Msg2String(testResultMsg))
}

func (wrapper *Wrapper) SendRunCmdResult(replyMessageId string, runId string, result string, executeSuccess bool) {
	runCmdResult := message.RunCmdResultMQMsg{}
	runCmdResult.MessageId = utils.GenerateUUID()
	runCmdResult.Timestamp = utils.GetNow()
	runCmdResult.ReplyMessageId = replyMessageId
	runCmdResult.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	runCmdResult.RunId = runId
	runCmdResult.Result = result
	runCmdResult.ExecuteSuccess = executeSuccess
	// TODO: 先冗余发送 业务方对接完去掉
	wrapper.sendToManager(RUN_CMD_RESULT, utils.Msg2String(runCmdResult))
	wrapper.send(RUN_CMD_RESULT, utils.Msg2String(runCmdResult))
}

func (wrapper *Wrapper) SendStopUnitTestResult(replyMessageId, runId, fileKey string) {
	testResultMsg := unittest.UnitTestResultMQMsg{}
	testResultMsg.MessageId = utils.GenerateUUID()
	testResultMsg.Timestamp = utils.GetNow()
	testResultMsg.ReplyMessageId = replyMessageId
	testResultMsg.RunId = runId
	testResultMsg.FileKey = fileKey
	testResultMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	wrapper.sendToManager(STOP_UNIT_TEST, utils.Msg2String(testResultMsg))
	wrapper.send(STOP_UNIT_TEST, utils.Msg2String(testResultMsg))
}

func (wrapper *Wrapper) SendStopCmdResult(replyMessageId string, runId string) {
	runCmdResult := message.RunCmdResultMQMsg{}
	runCmdResult.MessageId = utils.GenerateUUID()
	runCmdResult.Timestamp = utils.GetNow()
	runCmdResult.ReplyMessageId = replyMessageId
	runCmdResult.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	runCmdResult.RunId = runId
	wrapper.send(STOP_RUN_CMD_RESULT, utils.Msg2String(runCmdResult))
}

func (wrapper *Wrapper) SendHeartBeatResult(replyMessageId string) {
	dockerInfoMsg := message.DockerInfoMQMsg{}
	dockerInfoMsg.MessageId = utils.GenerateUUID()
	dockerInfoMsg.Timestamp = utils.GetNow()
	dockerInfoMsg.ReplyMessageId = replyMessageId
	dockerInfoMsg.DockerInfo = infoHolder.GetDockerInfo()
	if dockerInfoMsg.DockerInfo != nil {
		dockerInfoMsg.DockerInfo.LspStatus = wrapper.lspStatus
		dockerInfoMsg.DockerInfo.Gui = infoHolder.IsGui()
		dockerInfoMsg.DockerInfo.DebugSupport = infoHolder.DebugSupport()
		dockerInfoMsg.DockerInfo.Refresh = wrapper.watch.RefreshConfig.Refresh
		dockerInfoMsg.DockerInfo.VncSupport = cmdUtils.LookPath("vncserver")
		dockerInfoMsg.DockerInfo.VncStatus = wrapper.vncStatus
		dockerInfoMsg.DockerInfo.IntervalTime = int64(wrapper.watch.RefreshConfig.IntervalTime / time.Millisecond)
	}
	wrapper.sendToManager(DOCKER_HEART_BEAT, utils.Msg2String(dockerInfoMsg))
}

func (wrapper *Wrapper) SendClearConsole() {
	clearConsoleMQMsg := message.ClearConsoleMQMsg{}
	clearConsoleMQMsg.MessageId = utils.GenerateUUID()
	clearConsoleMQMsg.Timestamp = utils.GetNow()
	clearConsoleMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	wrapper.send(CLEAR_CONSOLE, utils.Msg2String(clearConsoleMQMsg))
}

func (wrapper *Wrapper) listenHttpProxy() {
	for {
		v := <-wrapper.httpProxyToMqChannel
		if strings.HasPrefix(v, consts.PORTS_CHANGED_PREFIX) {
			msg := message.HttpProxyMQMsg{}
			msg.MessageId = utils.GenerateUUID()
			msg.Timestamp = utils.GetNow()
			msg.Value = strings.TrimPrefix(v, consts.PORTS_CHANGED_PREFIX)
			wrapper.send(PORTS_CHANGED, utils.Msg2String(msg))
		} else if strings.HasPrefix(v, consts.AVAILABLE_PORTS_PREFIX) {
			msg := message.HttpProxyMQMsg{}
			msg.MessageId = utils.GenerateUUID()
			msg.Timestamp = utils.GetNow()
			msg.Value = strings.TrimPrefix(v, consts.AVAILABLE_PORTS_PREFIX)
			wrapper.send(AVAILABLE_PORTS, utils.Msg2String(msg))
		}
	}
}

// 多终端版本
func (wrapper *Wrapper) listenMultiTerminal() {
	for {
		select {
		case v2 := <-wrapper.multiTerminalToMqObj:
			if strings.HasPrefix(v2.Cmd, consts.MQ_TERMINAL_PREFIX_STATUS) {
				msg := message.NewTerminalStatusMQMsg(v2.Content, v2.TerminalId, v2.TerminalType)
				wrapper.send(TERMINAL_STATUS, utils.Msg2String(msg))
			} else if strings.HasPrefix(v2.Cmd, consts.MQ_TERMINAL_PREFIX_CONTENT) {
				msg := message.NewTerminalMQMsg(v2.Content, v2.TerminalId, v2.TerminalType)
				wrapper.send(MULTI_TERMINAL, utils.Msg2String(msg))
			} else if strings.HasPrefix(v2.Cmd, consts.MQ_TERMINAL_PREFIX_PROCESSNAME) {
				msg := message.NewTerminalProcessNameMQMsg(v2.Content, v2.TerminalId, v2.TerminalType)
				wrapper.send(MULTI_TERMINALCMD_PROCESSNAME, utils.Msg2String(msg))
			}
		case v1 := <-wrapper.multiTerminalToMQCmdChannel:
			//log.Infof("MultiTerminal:multiTerminalToMQCmdChannel, command: %+v", v1.TerminalCmd)
			terminalID := v1.TerminalId
			terminalContent := v1.Content
			cmdContent := v1.Cmd
			terminalCmd := v1.TerminalCmd
			terminalType := v1.TerminalType
			sort := v1.Sort
			if strings.HasPrefix(cmdContent, consts.MQ_TERMINAL_PREFIX_CMD_REPLY) {
				msg := message.NewTerminalCmdMQMsgReply(terminalID, terminalCmd, terminalContent, "", terminalType, "", sort)
				wrapper.send(MULTI_TERMINALCMD_REPLY, utils.Msg2String(msg))
			} else if strings.HasPrefix(cmdContent, consts.MQ_TERMINAL_PREFIX_CMD_TERMINALS) {
				msg := message.NewTerminalHeartbeatMQMsg(terminalContent)
				wrapper.send(MULTI_TERMINALCMD_HEARTBEAT, utils.Msg2String(msg))
			}
		}
	}
}

func (wrapper *Wrapper) listenMultiConsole() {
	for {
		v := <-wrapper.multiConsoleToMQChannel
		log.Infof("listenMultiConsole value: %+v", v)
		valueSlice := strings.Split(v, ":")
		if len(valueSlice) != 5 {
			log.Errorf("listenMultiConsole value: %+v, valueSlice: %+v", v, valueSlice)
			continue
		}

		status := valueSlice[1]
		runResult := valueSlice[2]
		terminalId := valueSlice[3]
		terminalType := valueSlice[4]
		//log.Infof("listenMultiConsole status: %s, runResult: %s", status, runResult)
		if strings.HasPrefix(v, consts.MQ_CONOSOLE_PREFIX_STATUS) {
			wrapper.PublishMultiRunStatus(terminalId, terminalType, status, runResult)
		}
	}
}

func (wrapper *Wrapper) receiveMQ() error {
	dockerId := envUtils.GetString(consts.PAAS_DockerId)

	go func() {
		for {
			select {
			case <-wrapper.consumeNotifyChannel:
				messages, err := wrapper.channel.Consume(
					wrapper.getQueueName(),
					dockerId,
					true,
					false,
					false,
					false,
					nil,
				)
				if err != nil {
					log.Errorf("%s.%s", "channel consume fail", err)
					return
				}

				// 开始处理消息
				go func() {
					for {
						select {
						case m := <-messages:
							wrapper.handleDelivery(m)
						case <-wrapper.msgNotifyChannel:
							log.Errorf("%s.%s", "receive mq msg notify channel", wrapper.getQueueName())
							return
						}
					}
				}()

			}
		}
	}()

	return nil
}

// rpc server
func (wrapper *Wrapper) connectAndReceiveRpcMq() error {
	go wrapper.handleRpcReconnect()
	return nil
}

func (wrapper *Wrapper) handleRpcReconnect() {
	retryCount := 0
	for {
		conn, err := amqp.Dial(config.AMQP_URL)
		if err != nil {
			log.Errorf("RPC connection failed: %v, retrying...", err)
			retryCount = wrapper.handleRetryDelay(retryCount)
			continue
		}
		log.Infof("RPC connection established with %s", conn.RemoteAddr())

		ch, err := conn.Channel()
		if err != nil {
			conn.Close()
			log.Errorf("RPC channel creation failed: %v, retrying...", err)
			retryCount = wrapper.handleRetryDelay(retryCount)
			continue
		}
		log.Infof("RPC channel creation success")

		if ok := wrapper.setupRpcComponents(ch); !ok {
			ch.Close()
			conn.Close()
			retryCount = wrapper.handleRetryDelay(retryCount)
			continue
		}

		retryCount = 0
		notifyClose := make(chan *amqp.Error)
		ch.NotifyClose(notifyClose)
		connNotifyClose := make(chan *amqp.Error)
		conn.NotifyClose(connNotifyClose)

		done := make(chan struct{})
		go wrapper.processRpcMessages(ch, done)

		wrapper.rpcConsumeNotifyChannel <- 1

		select {
		case <-notifyClose:
		case <-connNotifyClose:
		case <-done:
		}

		ch.Close()
		conn.Close()
		wrapper.rpcMsgNotifyChannel <- 1
		retryCount = wrapper.handleRetryDelay(retryCount)
	}
}

func (wrapper *Wrapper) handleRetryDelay(retryCount int) int {
	waitTime := time.Duration(math.Min(math.Pow(2, float64(retryCount)), 20)) * time.Second
	time.Sleep(waitTime)
	return retryCount + 1
}

func (wrapper *Wrapper) setupRpcComponents(ch *amqp.Channel) bool {
	rpcQueueName := wrapper.getRpcQueueName()
	_, err := ch.QueueDeclare(
		rpcQueueName,
		false,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		log.Errorf("GoAgent:setupRpcComponents-RPC queue declaration failed: %v", err)
		return false
	}

	if err := ch.Qos(1, 0, false); err != nil {
		log.Errorf("RPC Qos setup failed: %v", err)
		return false
	}

	return true
}

func (wrapper *Wrapper) processRpcMessages(ch *amqp.Channel, done chan struct{}) {
	dockerId := envUtils.GetString(consts.PAAS_DockerId)
	for {
		select {
		case <-wrapper.rpcConsumeNotifyChannel:
			rpcQueueName := wrapper.getRpcQueueName()
			msgs, err := ch.Consume(
				rpcQueueName, // queue
				dockerId,     // consumer
				false,        // auto-ack
				false,        // exclusive
				false,        // no-local
				false,        // no-wait
				nil,          // args
			)
			if err != nil {
				log.Errorf("processRpcMessages: Consume error: %v", err)
				close(done)
				return
			}
			log.Infof("processRpcMessages: consume message ")

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			for {
				select {
				case <-wrapper.rpcMsgNotifyChannel:
					log.Infof("processRpcMessages: Receive message from rpcMsgNotify Channel")
					return
				case d := <-msgs:
					log.Infof("GoAgent:connectAndReceiveRpcMq-msg(%+v)", string(d.Body))
					var gitCmd git_cmd.BaseGitCmd
					if err := json.Unmarshal(d.Body, &gitCmd); err != nil {
						log.Infof("GoAgent:connectAndReceiveRpcMq:handleGitCommand, err:%+v", err)
						wrapper.rpcReply(ctx, ch, &d, "", git_cmd.GIT_CMD_FAIL, err.Error())
						continue
					}

					// todo: git 命令操作是否需要控制顺序？
					go func() {
						res, err := gitCmd.Handler()

						log.Infof("GoAgent:connectAndReceiveRpcMq:handleGitCommand, "+
							"gitCmd: %+v, result: %+v, err: %+v", gitCmd, res, err)
						if err != nil {
							log.Infof("GoAgent:connectAndReceiveRpcMq:handleGitCommand, err2:%+v", err)
							wrapper.rpcReply(ctx, ch, &d, gitCmd.MessageId, git_cmd.GIT_CMD_FAIL, err.Error())
							return
						}

						wrapper.rpcReply(ctx, ch, &d, gitCmd.MessageId, git_cmd.GIT_CMD_SUCCESS, res)
					}()

				}
			}
		}
	}
}

func (wrapper *Wrapper) rpcReply(ctx context.Context, ch *amqp.Channel,
	d *amqp.Delivery, replayMessageId string, isSuccess int8, content any) {
	replyMsg := git_cmd.NewGitCmdReply(replayMessageId, isSuccess, content)
	log.Infof("GoAgent:connectAndReceiveRpcMq:handleGitCommand, replyMsg: %+v\n", replyMsg)
	replyMsgStr, _ := json.Marshal(replyMsg)
	err := ch.PublishWithContext(ctx,
		"",        // exchange
		d.ReplyTo, // routing key
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType:   "text/plain",
			CorrelationId: d.CorrelationId,
			Body:          replyMsgStr,
		})

	d.Ack(false)
	if err != nil {
		log.Errorf("GoAgent:connectAndReceiveRpcMq-msg(%+v), err: %+v", string(d.Body), err)
	}
}

func (wrapper *Wrapper) send(msgType string, content string) {
	routingKey := wrapper.ideServerCode +
		".fromDocker." +
		envUtils.GetString(consts.PAAS_PlaygroundId) + "." +
		envUtils.GetString(consts.PAAS_DockerId)
	log.Printf("Send/%s/%s:%s", routingKey, msgType, content)
	if wrapper.channel.IsClosed() {
		log.Errorf("channel is closed")
		return
	}
	err := wrapper.channel.Publish(
		envUtils.GetString(consts.PAAS_ExchangeName),
		routingKey,
		false,
		false,
		amqp.Publishing{
			ContentType: "text/plain",
			Type:        msgType,
			Body:        []byte(content),
		})

	if err != nil {
		log.Errorf("%s: %s", "Send mq fail", err)
	}
}

func (wrapper *Wrapper) sendToManager(msgType string, content string) {
	log.Printf("Send/%s/%s:%s", "toManager", msgType, content)
	if wrapper.channel.IsClosed() {
		log.Errorf("channel is closed")
		return
	}
	err := wrapper.channel.Publish(
		envUtils.GetString(consts.PAAS_ExchangeName),
		"toManager",
		false,
		false,
		amqp.Publishing{
			ContentType: "text/plain",
			Type:        msgType,
			Body:        []byte(content),
		})
	if err != nil {
		log.Errorf("%s: %s", "Send mq fail", err)
	}
}

func (wrapper *Wrapper) connect() error {

	strConn := config.AMQP_URL
	var err error

	wrapper.conn, err = amqp.Dial(strConn)
	if err != nil {
		log.Errorf("connError:%s", err)
		return err
	}

	defer func() {
		if err != nil {
			wrapper.conn.Close()
		}
	}()

	wrapper.channel, err = wrapper.conn.Channel()
	if err != nil {
		log.Errorf("channelError:%s", err)
		return err
	}

	_, err = wrapper.channel.QueueDeclare(
		wrapper.getQueueName(),
		true, // durable
		true, // delete when unused
		false,
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		log.Errorf("queueDeclareError:%s", err)
		return err
	}

	routingKey := "toDocker." + envUtils.GetString(consts.PAAS_PlaygroundId) + "." + envUtils.GetString(consts.PAAS_DockerId)
	err = wrapper.channel.QueueBind(wrapper.getQueueName(), routingKey, envUtils.GetString(consts.PAAS_ExchangeName), false, nil)
	if err != nil {
		log.Errorf("queueBindError:%s", err)
		return err
	}
	notifyClose := make(chan *amqp.Error)
	wrapper.channel.NotifyClose(notifyClose)

	connNotifyClose := make(chan *amqp.Error)
	wrapper.conn.NotifyClose(connNotifyClose)
	go func() {
		var errors *amqp.Error
		select {
		case errors = <-notifyClose:
		case errors = <-connNotifyClose:
		}
		//errors := <-notifyClose
		go func() {
			wrapper.msgNotifyChannel <- 1
		}()
		log.Printf("mq close error : %v", errors)
		for {
			if wrapper.connect() == nil {
				log.Printf("mq restart success")
				break
			}
			time.Sleep(time.Second * 20)
		}
	}()

	go func() {
		wrapper.consumeNotifyChannel <- 1
	}()

	return nil
}

func (wrapper *Wrapper) getQueueName() string {
	return "docker." + envUtils.GetString(consts.PAAS_DockerId)
}

func (wrapper *Wrapper) getRpcQueueName() string {
	// rpc.toDocker.667444149129453568.667444149163008000
	return fmt.Sprintf("rpc.todocker.%s.%s",
		envUtils.GetString(consts.PAAS_PlaygroundId), envUtils.GetString(consts.PAAS_DockerId))
}

func (wrapper *Wrapper) PublishKillReason(reason int) {
	killReasonMQMsg := message.KillReasonMQMsg{}
	killReasonMQMsg.MessageId = utils.GenerateUUID()
	killReasonMQMsg.Timestamp = utils.GetNow()
	killReasonMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	killReasonMQMsg.Reason = reason
	wrapper.sendToManager(KILL_STATUS, utils.Msg2String(killReasonMQMsg))

}

func (wrapper *Wrapper) listenFileChange() {
	limitCount := 100
	for {
		fileChanges := <-wrapper.fileChangeChannel
		sCount := len(fileChanges)
		fileChangesBt, _ := json.Marshal(fileChanges)
		fileChangesTxt := string(fileChangesBt)

		fileChanges = fileChangesTidy(fileChanges)
		dCount := len(fileChanges)
		fileChangesBtTidy, _ := json.Marshal(fileChanges)
		fileChangesTxtTidy := string(fileChangesBtTidy)
		//if sCount != dCount {
		log.Infof("listenFileChange, fileChange0, sCount: %d, fileChangesTxt: %s, dCount: %d, fileChangesTxtTidy: %s",
			sCount, fileChangesTxt, dCount, fileChangesTxtTidy)
		//}

		// 忽略执行单元测试时系统处理文件（特定隐藏文件：隐藏用例、隐藏答案）的变更
		//fileChanges = wrapper.ignoreSpecifiedHiddenFilesDuringUnittest(fileChanges)
		// 单次发送文件变化数量限制
		fileChangeCount := len(fileChanges)
		if fileChangeCount != 0 {
			if fileChangeCount > limitCount {
				total := int(math.Ceil(float64(fileChangeCount) / float64(limitCount)))
				for i := 0; i < total && len(fileChanges) > 0; i++ {
					if limitCount >= len(fileChanges) {
						if !wrapper.FileChange(fileChanges) {
							for _, change := range fileChanges {
								log.Warnf("listenFileChange, fileChange1: %+v", change)
							}
						}
					} else {
						batch := fileChanges[:limitCount]
						if !wrapper.FileChange(batch) {
							for _, change := range batch {
								log.Warnf("listenFileChange, fileChange2: %+v", change)
							}
						}

						fileChanges = fileChanges[limitCount:]
					}

					fileChangesBt1, _ := json.Marshal(fileChanges)
					fileChangesTxt1 := string(fileChangesBt1)
					log.Infof("listenFileChange, fileChange2: %s, i: %d, total: %d", fileChangesTxt1, i, total)
				}
			} else {
				fileChangesBt1, _ := json.Marshal(fileChanges)
				fileChangesTxt1 := string(fileChangesBt1)
				log.Infof("listenFileChange, fileChange3: %s", fileChangesTxt1)
				if !wrapper.FileChange(fileChanges) {
					for _, change := range fileChanges {
						log.Warnf("listenFileChange, fileChange3: %+v", change)
					}
				}
			}
		}
	}
}

//func (wrapper *Wrapper) listenRagResult() {
//	for {
//		ragResult := <-wrapper.ragSearchToMqChannel
//		log.Infof("RagSearchToMqChannel:%v", ragResult)
//		wrapper.send(RagSearch, utils.Msg2String(ragResult))
//	}
//}
//
//func (wrapper *Wrapper) listenRagStatus() {
//	for {
//		ragStatus := <-wrapper.ragStatusToMqChannel
//		log.Infof("RagStatusToMqChannel:%v", ragStatus)
//		wrapper.send(RagStatus, utils.Msg2String(ragStatus))
//	}
//}

func (wrapper *Wrapper) ignoreSpecifiedHiddenFilesDuringUnittest(changes []watch.FileChange) []watch.FileChange {
	flag := wrapper.runUnitTestLock.TryLock()
	if flag {
		defer wrapper.runUnitTestLock.Unlock()
		return changes
	} else {
		//如果没有预获取到锁，说明是在执行单元测试期间，捕获到了隐藏用例文件、隐藏答案文件的变更，这时需要忽略
		hiddenFiles := unittest2.GetHiddenFiles()
		hiddenFileMap := make(map[string]struct{})
		for _, v := range hiddenFiles {
			hiddenFileMap[v] = struct{}{}
		}
		var filteredChanges []watch.FileChange
		for _, fileChange := range changes {
			if _, exists := hiddenFileMap[fileChange.Path]; !exists {
				filteredChanges = append(filteredChanges, fileChange)
			}
		}
		return filteredChanges
	}
}

func (wrapper *Wrapper) PublishUnitTestFunList(list []unittest2.TestCaseFile, replyMessageId string) {
	unitTestFunMQMsg := unittest.UnitTestFunMQMsg{}
	unitTestFunMQMsg.ReplyMessageId = replyMessageId
	unitTestFunMQMsg.MessageId = utils.GenerateUUID()
	unitTestFunMQMsg.Timestamp = utils.GetNow()
	unitTestFunMQMsg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	str, err := json.Marshal(list)
	if err == nil {
		unitTestFunMQMsg.TestCaseFile = string(str)
	}
	wrapper.send(UNIT_TEST_FUN, utils.Msg2String(unitTestFunMQMsg))
}

func fileChangesTidy(changes []watch.FileChange) []watch.FileChange {
	var finallyFileChanges []watch.FileChange
	var dirRemoves []string
	var fileRemoves []string
	for _, change := range changes {
		// 判断是否为软连接，软连接不返回
		_, err := os.Readlink(change.Path)
		if err == nil {
			continue
		}
		if change.Change == consts.FileChangeRemove {
			path := strings.Replace(change.Path, consts.AppRootDirChild, "", 1)
			if change.Key == consts.FileType {
				fileRemoves = append(fileRemoves, path)
			} else {
				dirRemoves = append(dirRemoves, path+"/")
			}
		} else {
			break
		}
	}

	sort.Slice(dirRemoves, func(i, j int) bool {
		return len(dirRemoves[i]) < len(dirRemoves[j])
	})
	var finallyDirRemoves []string
	for _, dir := range dirRemoves {
		isDirChild := false
		for _, pdir := range finallyDirRemoves {
			if strings.HasPrefix(dir, pdir) {
				isDirChild = true
				break
			}
		}
		if !isDirChild {
			finallyDirRemoves = append(finallyDirRemoves, dir)
			finallyFileChanges = append(finallyFileChanges, watch.FileChange{Path: dir[:len(dir)-1], Change: consts.FileChangeRemove, Key: consts.DirType})
		}
	}

	for _, file := range fileRemoves {
		isDirChild := false
		for _, dir := range finallyDirRemoves {
			if strings.HasPrefix(file, dir) {
				isDirChild = true
				break
			}
		}
		if !isDirChild {
			finallyFileChanges = append(finallyFileChanges, watch.FileChange{Path: file, Change: consts.FileChangeRemove, Key: consts.FileType})
		}
	}
	for _, change := range changes[(len(dirRemoves) + len(fileRemoves)):] {
		// 判断是否为软连接，软连接不返回
		_, err := os.Readlink(change.Path)
		if err == nil {
			continue
		}
		finallyFileChanges = append(finallyFileChanges, change)
	}
	return finallyFileChanges
}

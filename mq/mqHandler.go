package mq

import (
	"agent/consts"
	"agent/file/grep"
	"agent/file/watch"
	lspConsts "agent/ls/consts"
	"agent/mq/message"
	unittest2 "agent/mq/message/unittest"
	"agent/mq/utils"
	"agent/multiTerminal/terminal"
	"agent/unittest"
	"agent/utils/cmdUtils"
	"agent/utils/envUtils"
	"agent/utils/log"
	"agent/utils/nixUtils"
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"syscall"
	"time"

	"golang.org/x/time/rate"

	amqp "github.com/rabbitmq/amqp091-go"
)

type mqHandler func(wrapper *Wrapper, delivery amqp.Delivery)

var mqHandlerFactory = map[string]mqHandler{
	//TERMINAL:               handleTerminal,
	MULTI_TERMINAL:         handleMultiTerminal,
	MULTI_TERMINALCMD:      handleMultiTerminalCmd,
	REFRESHXTERM_COLS_ROWS: handleTerminalColsAndRows,
	// AI Terminal
	//AI_TERMINAL:      handleTerminalAi,
	//AI_TERMINAL_KILL: handleAiTerminalKill,

	DOCKER_INFO: handleDockerInfo,
	RUN:         handleRun,
	STOP:        handleStop,
	//CONSOLE:                    handleConsole,
	CONFIG:            handleConfig,
	RUN_STATUS:        handleRunStatus,
	LSP_STATUS:        handleLspStatus,
	GET_UNIT_TEST_FUN: handleGetUnitTestFun,
	//RUN_UNIT_TEST:              handleRunUnitTest,
	//RUN_UNIT_TEST_BY_IDESERVER: handleRunUnitTestByIdeServer,
	//STOP_UNIT_TEST:   handleStopUnitTest,
	RUN_CMD:      handleRunCmd,
	STOP_RUN_CMD: handleStopRunCmd,
	HEART_BEAT:   handleHeartBeat,
	//DEBUG_RUN:        handleDebugRun,
	//DEBUG_DAP:        handleDebugDAP,
	LSP_START:        handleLspStart,
	ENV:              handleEnv,
	CHANGE_IDESERVER: handleChangeIdeServer,
	MOUNT_SUCCESS:    handleMountSuccess,
	//FileWatchRefreshConfig: handlerFileWatchRefreshConfig,
	//FileWatchRefreshNow:    handlerFileWatchRefreshNow,
	//Execution: handlerExecution,
	FilePull: handlerFilePull,
	DirPull:  handlerDirPull,
	FileGrep: handlerFileGrep,
	//RagSearch: handleRagSearch,

	AVAILABLE_PORTS: handleAvailablePorts,
}

func handlerFileGrep(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		var body message.FileGrep
		if err := json.Unmarshal(delivery.Body, &body); err != nil {
			log.Println(err)
			return
		}
		if body.Keyword == "" {
			return
		}
		result, err := grep.Grep(body.Keyword, body.Cwd, body.CaseSensitive, body.WholeWordMatching, body.Regex)
		if err != nil {
			log.Println(err)
		}
		resp := &message.FileGrepResult{
			BaseMQMsg: message.BaseMQMsg{
				MessageId:      utils.GenerateUUID(),
				Timestamp:      utils.GetNow(),
				ReplyMessageId: body.MessageId,
			},
			Result: result,
		}
		wrapper.send(FileGrep, utils.Msg2String(resp))
	}()
}

var filePullLimiter = rate.NewLimiter(rate.Every(50*time.Millisecond), 50)

func handlerFilePull(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		if filePullLimiter.Allow() {
			var msg message.FilePullMsg
			var fileChanges []watch.FileChange
			json.Unmarshal(delivery.Body, &msg)
			for _, path := range msg.Paths {
				refPath, err := filepath.Rel(consts.AppRootDirChild, path)
				if err != nil {
					refPath = path
				}
				change := &watch.FileChange{
					Path:   refPath,
					Change: consts.FileChangeCreate,
					Key:    consts.FileType,
				}
				open, err := os.Open(path)
				if err != nil {
					// 打开失败，文件不存在
					change.Change = consts.FileChangeRemove
					fileChanges = append(fileChanges, *change)
					continue
				}
				open.Close()
				fileInfo, err := os.Stat(path)
				if err != nil {
					continue
				}
				currentModificationTime := fileInfo.ModTime().Add(time.Second)
				os.Chtimes(path, time.Now(), currentModificationTime)

				if fileInfo.IsDir() {
					change.Key = consts.DirType
					fileChanges = append(fileChanges, *change)
				} else {
					fileChanges = append(fileChanges, *change)
				}
			}
			//// 利用原通道进行文件RAG文件变更处理
			//wrapper.fileChangeRagChannel <- fileChanges
		}
	}()
}

// handlerDirPull
func handlerDirPull(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		if filePullLimiter.Allow() {
			var msg message.DirPullMsg
			err := json.Unmarshal(delivery.Body, &msg)
			log.Infof("handlerDirPull-dir, body: %+v", delivery.Body)
			if err != nil {
				log.Warnf("handlerDirPull-dir-err: %+v, body: %+v", err, delivery.Body)
				return
			}

			for _, item := range msg.Dirs {
				refPath, err := filepath.Rel(consts.AppRootDirChild, item.DstPath)
				if err != nil {
					refPath = item.DstPath
				}

				// 使用 os.Stat 检查目录是否存在
				fileInfo, err := os.Stat(refPath)
				if err != nil {
					log.Warnf("handlerDirPull %s not exist, item: %+v\n", refPath, item)
					continue
				}

				log.Infof("handlerDirPull-dir, refPath: %s", refPath)
				if fileInfo.IsDir() {
					if wrapper.watch != nil {
						err = wrapper.watch.AddDirToFsnotify(refPath)
						log.Infof("handlerDirPull-add-dir: %s, err: %+v", refPath, err)
					}
				}
			}
		}
	}()
}

//func handlerExecution(wrapper *Wrapper, delivery amqp.Delivery) {
//	wrapper.mqToConsoleChannel <- consts.MQ_CONSOLE_PREFIX_EXECUTION + string(delivery.Body)
//}

//func handlerFileWatchRefreshNow(wrapper *Wrapper, delivery amqp.Delivery) {
//	go func() {
//		fileChangeChannel := make(chan []watch.FileChange)
//		fileWatch := watch.MakeNew(fileChangeChannel, nil, &watch.RefreshConfig{Refresh: true, IntervalTime: 0})
//		c := watch.Config{}
//		c.Entries = append(c.Entries, watch.WatchEntry{Directory: consts.AppRootDir})
//		ctx, cancelFunc := context.WithCancel(context.Background())
//		// 只需要执行一次
//		go fileWatch.Watch(ctx, c, time.Hour)
//		<-fileChangeChannel
//		cancelFunc()
//		close(fileChangeChannel)
//	}()
//}

//func handlerFileWatchRefreshConfig(wrapper *Wrapper, delivery amqp.Delivery) {
//	var fileWatchRefreshMsg message.FileWatchRefreshMsg
//	if json.Unmarshal(delivery.Body, &fileWatchRefreshMsg) != nil || fileWatchRefreshMsg.IntervalTime < 0 {
//		return
//	}
//	wrapper.watch.RefreshConfig.Refresh = fileWatchRefreshMsg.Refresh
//	wrapper.watch.RefreshConfig.IntervalTime = time.Millisecond * time.Duration(fileWatchRefreshMsg.IntervalTime)
//	log.Infof("config: %+v", wrapper.watch.RefreshConfig)
//	wrapper.PublishUpdateDockerInfoToManager("")
//}

func (wrapper *Wrapper) handleDelivery(delivery amqp.Delivery) {
	log.Infof("ReceiveMessage: %s , %s", delivery.Type, string(delivery.Body))
	if handler, ok := mqHandlerFactory[delivery.Type]; ok {
		handler(wrapper, delivery)
	}
}

func handleDockerInfo(wrapper *Wrapper, delivery amqp.Delivery) {
	replyMessageId := message.GetMsgId(string(delivery.Body))
	wrapper.PublishDockerInfo(replyMessageId)
}

func handleMultiTerminal(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.TerminalInputMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		log.Printf("GoAgent:Terminal:handleTerminal, err: %+v, body: %s", err, string(delivery.Body))
		return
	}

	if msg.TerminalID == "" {
		log.Errorf("GoAgent:Terminal:handleTerminal1, err: %+v, body: %s", err, string(delivery.Body))
		return
	}

	item := terminal.TerminalToMqItem{
		TerminalId:   msg.TerminalID,
		Cmd:          consts.MQ_TERMINAL_PREFIX_CONTENT,
		Content:      msg.Value,
		TerminalType: msg.TerminalType,
	}
	select {
	case wrapper.mqToMultiTerminalChannel <- item:
	case <-time.After(time.Second):
		log.Printf("GoAgent:Terminal:handleTerminal timeout")
	}
}

//func handleTerminal(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := message.TerminalInputMQMsg{}
//	err := json.Unmarshal(delivery.Body, &msg)
//	if err != nil {
//		log.Printf("GoAgent:Terminal:handleTerminal, err: %+v, body: %s", err, string(delivery.Body))
//		return
//	}
//
//	select {
//	case wrapper.mqToTerminalChannel <- consts.MQ_TERMINAL_PREFIX_CONTENT + msg.Value:
//	case <-time.After(time.Second):
//		log.Printf("GoAgent:Terminal:handleTerminal timeout")
//	}
//}

func handleMultiTerminalCmd(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.TerminalCmdMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		log.Printf("MultiTerminal:handleTerminal, err: %+v, body: %s", err, string(delivery.Body))
		return
	}

	if msg.TerminalID == "" {
		log.Errorf("MultiTerminal:handleTerminal, err: %+v, body: %s", err, string(delivery.Body))
		return
	}

	msgContent := fmt.Sprintf("%s%s%s%s%s%s", consts.MQ_TERMINAL_PREFIX_CMD, msg.TerminalID, ":", msg.Cmd, ":", msg.TerminalType)
	log.Printf("MultiTerminal:handleTerminal, content: %s", msgContent)
	select {
	case wrapper.mqToMultiTerminalCmdChannel <- msgContent:
	case <-time.After(time.Second):
		log.Printf("MultiTerminal:handleTerminal timeout")
	}
}

func handleAvailablePorts(wrapper *Wrapper, delivery amqp.Delivery) {
	log.Printf("handleAvailablePorts:ReceiveMessage: %s", string(delivery.Body))
	wrapper.mqToHttpProxyChannel <- "ok"
}

func handleTerminalColsAndRows(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.TerminalColsAndRowsMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		log.Printf("GoAgent:Terminal:handleTerminalColsAndRows err:%+v, body: %s", err, string(delivery.Body))
		return
	}

	var msgType message.XtermColsAndRowsValueMQMsg
	err = json.Unmarshal([]byte(msg.Value), &msgType)
	if err != nil {
		log.Errorf("GoAgent:Terminal:handleTerminalColsAndRows: %+v, value: %s", err, msg.Value)
		return
	}

	if msg.TerminalID == "" {
		log.Infof("GoAgent:Terminal:handleTerminalColsAndRows, msg: %+v", msg)
		return
	}

	item := terminal.TerminalToMqItem{
		TerminalId:   msg.TerminalID,
		Cmd:          consts.MQ_XTERM_PREFIX_SIZE,
		Content:      msg.Value,
		TerminalType: msg.TerminalType,
	}
	wrapper.mqToMultiTerminalChannel <- item
	//} else {
	//	// 兼容旧的逻辑
	//	msgContent := consts.MQ_XTERM_PREFIX_SIZE + msg.Value
	//	if msgType.Type == "terminal" {
	//		wrapper.mqToTerminalChannel <- msgContent
	//	} else if msgType.Type == "console" {
	//		wrapper.mqToConsoleChannel <- msgContent
	//	} else if msgType.Type == "ai_terminal" {
	//		wrapper.mqToTerminalAIChannel <- msgContent
	//	}
	//}
}

//func handleConsole(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := message.TerminalInputMQMsg{}
//	json.Unmarshal(delivery.Body, &msg)
//	if utils.IsExpire(msg.Timestamp) {
//		log.Printf("IgnoreExprie:%s", string(delivery.Body))
//		return
//	}
//	select {
//	case wrapper.mqToConsoleChannel <- consts.MQ_CONOSOLE_PREFIX_CONTENT + msg.Value:
//	case <-time.After(time.Second):
//		log.Printf("handleConsole timeout")
//	}
//}

func handleRun(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.BaseMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}

	select {
	case wrapper.mqToMultiTerminalConsoleChannel <- consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_RUN:
		// httpproxy 配置刷新
		go func() {
			wrapper.httpProxyHookChannel <- consts.HTTP_PROXY_HOOK_SIGN
		}()
	case <-time.After(time.Second):
		log.Printf("handleRun timeout")
	}
}

func handleStop(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.BaseMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}

	select {
	case wrapper.mqToMultiTerminalConsoleChannel <- consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_STOP:
		// httpproxy 配置刷新
		go func() {
			wrapper.httpProxyHookChannel <- consts.HTTP_PROXY_HOOK_SIGN
		}()
	case <-time.After(time.Second):
		log.Printf("handleStop timeout")
	}
}

func handleConfig(wrapper *Wrapper, delivery amqp.Delivery) {

	msg := message.BaseMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}

	//wrapper.PublishConfig(PublishConfigParam{msg.MessageId, true})
}

func handleLspStatus(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.BaseMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}

	wrapper.PublishLSPStatus("", msg.MessageId, wrapper.lspStatus)
}
func handleRunStatus(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.BaseMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}

	wrapper.PublishRunStatus()
}

func handleGetUnitTestFun(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := unittest2.UnitTestFunMQMsg{}
	json.Unmarshal(delivery.Body, &msg)
	testCaseFunList := unittest.IdentifyFile()
	wrapper.PublishUnitTestFunList(testCaseFunList, msg.MessageId)
}

//func handleRunUnitTest(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := unittest2.RunUnitTestMQMsg{}
//	json.Unmarshal(delivery.Body, &msg)
//	if utils.IsExpire(msg.Timestamp) {
//		log.Printf("IgnoreExprie:%s", string(delivery.Body))
//		return
//	}
//	var config config2.Config
//	config.RunCommand = msg.Cmd
//	config.Unittest.RunId = msg.RunId
//	config.Unittest.OutPutConsole = msg.OutPutConsole
//	config.Unittest.ConsoleText = msg.ConsoleText
//	config.Unittest.FilePath = msg.FileKey
//	config.Debug = msg.Debug
//	if wrapper.consoleWrapper.ExecutionSet.Cardinality() == 0 {
//		wrapper.SendClearConsole()
//	}
//	wrapper.consoleWrapper.ExecutionSet.Add(&config)
//	go func() {
//		wrapper.runUnitTestLock.Lock()
//		defer wrapper.runUnitTestLock.Unlock()
//		wrapper.consoleWrapper.Kill()
//		wrapper.consoleWrapper.Executor(&config, msg.RunMode)
//		wrapper.SendUnitTestResult(msg.MessageId, msg.RunId, msg.FileKey, config.Unittest.Result, config.Unittest.Error, config.Unittest.ExitCode == 0)
//	}()
//}

// 先过度 不影响原来的逻辑
//func handleRunUnitTestByIdeServer(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := unittest2.RunUnitTestMQMsg{}
//	json.Unmarshal(delivery.Body, &msg)
//	if utils.IsExpire(msg.Timestamp) {
//		log.Printf("IgnoreExprie:%s", string(delivery.Body))
//		return
//	}
//	var config config2.Config
//	run, debugConfig := unittest.GetRun(msg.FileKey, msg.RunId)
//	config.RunCommand = run
//	config.Unittest.RunId = msg.RunId
//	config.Unittest.OutPutConsole = msg.OutPutConsole
//	config.Unittest.ConsoleText = msg.ConsoleText
//	config.Unittest.FilePath = msg.FileKey
//	config.Debug = debugConfig
//	config.Timeout = 33
//	if wrapper.consoleWrapper.ExecutionSet.Cardinality() == 0 {
//		wrapper.SendClearConsole()
//	}
//	wrapper.consoleWrapper.ExecutionSet.Add(&config)
//	isSampleTest := false
//	unhiddenTestFiles := unittest.GetUnhiddenTestFiles()
//	for _, value := range unhiddenTestFiles {
//		if config.Unittest.FilePath == value {
//			if !strings.HasSuffix(config.Unittest.FilePath, ".cs") {
//				isSampleTest = true
//			}
//			break
//		}
//	}
//	var movableFile []MovableFile
//	if isSampleTest {
//		hiddenFiles := unittest.GetHiddenFiles()
//		for _, value := range hiddenFiles {
//			localUuid, _ := uuid.NewUUID()
//			uid := strings.ReplaceAll(localUuid.String(), "-", "")
//			movableFile = append(movableFile, MovableFile{fileName: consts.AppRootDir + "/" + value, uid: "/tmp/" + uid})
//		}
//	}
//	go func() {
//		wrapper.runUnitTestLock.Lock()
//		defer func() {
//			if isSampleTest && len(movableFile) != 0 {
//				for _, value := range movableFile {
//					err := fileUtils.MoveFileByCreateAndRemove(value.uid, value.fileName)
//					if err != nil {
//						log.Printf("recover file occurs err: %s", err)
//					}
//				}
//			}
//			wrapper.runUnitTestLock.Unlock()
//		}()
//		if isSampleTest && len(movableFile) != 0 {
//			for _, value := range movableFile {
//				err := fileUtils.MoveFileByCreateAndRemove(value.fileName, value.uid)
//				if err != nil {
//					log.Printf("move file occurs err: %s", err)
//				}
//			}
//		}
//		wrapper.consoleWrapper.Kill()
//		wrapper.consoleWrapper.Executor(&config, msg.RunMode)
//		// 结构化
//		result := unittest.AnalysisResult(config.Unittest, config.Unittest.Result)
//		wrapper.SendUnitTestResult(msg.MessageId, msg.RunId, msg.FileKey, result, config.Unittest.Error, config.Unittest.ExitCode == 0)
//	}()
//}

type MovableFile struct {
	uid      string
	fileName string
}

var stopLock sync.Mutex

//func handleStopUnitTest(wrapper *Wrapper, delivery amqp.Delivery) {
//	go func() {
//		stopLock.Lock()
//		defer stopLock.Unlock()
//		fileKey := ""
//		msg := unittest2.RunUnitTestMQMsg{}
//		json.Unmarshal(delivery.Body, &msg)
//		if utils.IsExpire(msg.Timestamp) {
//			log.Printf("IgnoreExprie:%s", string(delivery.Body))
//			return
//		}
//		// 删除队列中，停止运行中
//		iterator := wrapper.consoleWrapper.ExecutionSet.Clone().Iterator()
//		for item := range iterator.C {
//			if msg.RunId == item.Unittest.RunId {
//				fileKey = item.Unittest.FilePath
//				// pid 等于零 说明在队列中 删除队列即可
//				// pid 不等于零说明在运行中需要 kill
//				if item.Pid != 0 {
//					cmdUtils.KillByPid(item.Pid)
//				} else {
//					wrapper.consoleWrapper.ExecutionSet.Remove(item)
//				}
//				iterator.Stop()
//				break
//			}
//		}
//		wrapper.SendStopUnitTestResult(msg.MessageId, msg.RunId, fileKey)
//	}()
//}

func handleRunCmd(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		msg := message.RunCmdMQMsg{}
		json.Unmarshal(delivery.Body, &msg)
		if utils.IsExpire(msg.Timestamp) {
			log.Printf("IgnoreExprie:%s", string(delivery.Body))
			return
		}
		wrapper.runCmdLock.Lock()
		defer func() {
			wrapper.runCmdLock.Unlock()
		}()
		cmd := exec.Command(envUtils.GetNixShellCmd(), nixUtils.GetNixFile(), "--no-substitute", "--run", strings.TrimSpace(msg.Cmd))
		cmd.Dir = consts.AppRootDir
		var b bytes.Buffer
		cmd.Stdout = &b
		cmd.Stderr = &b
		cmdUtils.Setpgid(cmd)
		err := cmd.Start()
		if cmd.Process != nil {
			wrapper.runCmdPid[msg.RunId] = cmd.Process.Pid
		}
		if err != nil {
			log.Printf("run cmd fail:%s", err.Error())
		}
		errWait := cmd.Wait()
		if errWait != nil {
			log.Printf("errWait:%s", errWait.Error())
		}
		delete(wrapper.runCmdPid, msg.RunId)
		wrapper.SendRunCmdResult(msg.MessageId, msg.RunId, string(b.Bytes()), cmd.ProcessState.ExitCode() == 0)
	}()
}

func handleStopRunCmd(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		msg := message.RunCmdMQMsg{}
		json.Unmarshal(delivery.Body, &msg)
		if utils.IsExpire(msg.Timestamp) {
			log.Printf("IgnoreExprie:%s", string(delivery.Body))
			return
		}
		_, ok := wrapper.runCmdPid[msg.RunId]
		if ok {
			//log.Printf(strconv.Itoa(wrapper.runCmdPid[msg.RunId]))
			syscall.Kill(-wrapper.runCmdPid[msg.RunId], syscall.SIGTERM)
			err := syscall.Kill(wrapper.runCmdPid[msg.RunId], syscall.SIGTERM)
			if err != nil {
				log.Printf("kill.fail:%s", err)
			}
			delete(wrapper.runCmdPid, msg.RunId)
		}
		wrapper.SendStopCmdResult(msg.MessageId, msg.RunId)
	}()
}

func handleHeartBeat(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		msg := message.BaseMQMsg{}
		json.Unmarshal(delivery.Body, &msg)
		if utils.IsExpire(msg.Timestamp) {
			log.Printf("IgnoreExprie:%s", string(delivery.Body))
			return
		}
		wrapper.SendHeartBeatResult(msg.MessageId)
	}()
}

//func handleDebugRun(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := message.BaseMQMsg{}
//	json.Unmarshal(delivery.Body, &msg)
//	if utils.IsExpire(msg.Timestamp) {
//		log.Printf("IgnoreExprie:%s", string(delivery.Body))
//		return
//	}
//	select {
//	case wrapper.mqToConsoleChannel <- consts.MQ_CONOSOLE_PREFIX_STATUS + consts.CONSOLE_STATUS_DEBUG_RUN:
//	case <-time.After(time.Second):
//		log.Printf("handleDebugRun timeout")
//	}
//}

//func handleDebugDAP(wrapper *Wrapper, delivery amqp.Delivery) {
//	msg := message.DebugDAPMQMsg{}
//	err := json.Unmarshal(delivery.Body, &msg)
//	if err != nil {
//		return
//	}
//	if utils.IsExpire(msg.Timestamp) {
//		log.Printf("IgnoreExprie:%s", string(delivery.Body))
//		return
//	}
//	wrapper.mqToConsoleChannel <- consts.MQ_CONSOLE_PREFIX_DEBUG + msg.Value
//}

func handleLspStart(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.LspStartMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		return
	}

	log.Printf("handleLspStart:%s, lsp_status: %s", string(delivery.Body), wrapper.lspStatus)
	if wrapper.lspStatus != lspConsts.LspStatusLoading {
		wrapper.mqToLspChannel <- consts.MQ_LSP_START
	}
}
func handleEnv(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.EnvMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		log.Printf("GoAgent:handleEnv err:%+v, body: %s", err, string(delivery.Body))
		return
	}
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}
	if len(msg.Value) != 0 {
		for k, v := range msg.Value {
			os.Setenv(k, v)
		}
		//wrapper.mqToTerminalChannel <- consts.ENVLIST

		// 通知中间件代理更新监听对象
		go func() {
			wrapper.middlewareProxyChannel <- consts.ENVLIST
		}()

		// 配置发生变化，重生成/home/<USER>/.environment文件内容
		go func() {
			cmd := exec.Command("sh", "-c",
				`env | grep -E '^(paas|REDIS|POSTGRE|MYSQL|MONGO)' | awk -F= '{print "export " $1 "=\"" $2 "\""}' > "$HOME/.clackyaienv"`)
			err = cmd.Run()
			log.Printf("handleEnv-environment, err: %+v", err)
		}()
	}
}
func handleChangeIdeServer(wrapper *Wrapper, delivery amqp.Delivery) {
	msg := message.ChangeIdeServerMQMsg{}
	err := json.Unmarshal(delivery.Body, &msg)
	if err != nil {
		return
	}
	if utils.IsExpire(msg.Timestamp) {
		log.Printf("IgnoreExprie:%s", string(delivery.Body))
		return
	}
	wrapper.ideServerCode = msg.IdeServerCode
}

func handleMountSuccess(wrapper *Wrapper, delivery amqp.Delivery) {
	go func() {
		msg := message.MqStartMQMsg{}
		err := json.Unmarshal(delivery.Body, &msg)
		if err != nil {
			return
		}
		if utils.IsExpire(msg.Timestamp) {
			log.Printf("IgnoreExprie:%s", string(delivery.Body))
			return
		}
		wrapper.ideServerCode = msg.IdeServerCode
		log.Debugf("lock.time : %s\n", time.Now())
		wrapper.mqLock.Done()
	}()
}

//func handleRagSearch(wrapper *Wrapper, delivery amqp.Delivery) {
//	go func() {
//		msg := message.RagSearchMQMsg{}
//		json.Unmarshal(delivery.Body, &msg)
//		if utils.IsExpire(msg.Timestamp) {
//			log.Printf("IgnoreExprie:%s", string(delivery.Body))
//			return
//		}
//		select {
//		case wrapper.mqToRagSearchChannel <- msg:
//		case <-time.After(5 * time.Second):
//			log.Printf("RAG:handleRagSearch timeout")
//		}
//	}()
//}

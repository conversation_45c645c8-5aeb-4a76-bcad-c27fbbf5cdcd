package utils

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGenerateUUID(t *testing.T) {
	// 测试生成UUID
	uuid1 := GenerateUUID()
	uuid2 := GenerateUUID()

	// 验证UUID不为空
	assert.NotEmpty(t, uuid1)
	assert.NotEmpty(t, uuid2)

	// 验证两次生成的UUID不同
	assert.NotEqual(t, uuid1, uuid2)

	// 验证UUID格式（应该包含连字符）
	assert.Contains(t, uuid1, "-")
	assert.Contains(t, uuid2, "-")
}

func TestGetNow(t *testing.T) {
	// 测试获取当前时间戳
	now1 := GetNow()
	time.Sleep(1 * time.Millisecond) // 稍微等待一下
	now2 := GetNow()

	// 验证时间戳不为0
	assert.Greater(t, now1, int64(0))
	assert.Greater(t, now2, int64(0))

	// 验证时间戳递增
	assert.GreaterOrEqual(t, now2, now1)
}

func TestMsg2String(t *testing.T) {
	// 测试正常情况
	testStruct := struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}{
		Name: "test",
		Age:  25,
	}

	result := Msg2String(testStruct)
	assert.NotEmpty(t, result)
	assert.Contains(t, result, "test")
	assert.Contains(t, result, "25")

	// 测试空结构体
	emptyStruct := struct{}{}
	result2 := Msg2String(emptyStruct)
	assert.Equal(t, "{}", result2)

	// 测试nil
	result3 := Msg2String(nil)
	assert.Equal(t, "null", result3)
}

func TestIsExpire(t *testing.T) {
	// 测试过期检查函数
	// 根据当前实现，这个函数总是返回false
	result := IsExpire(time.Now().Unix())
	assert.False(t, result)

	// 测试过去的时间戳
	pastTime := time.Now().Add(-1 * time.Hour).Unix()
	result2 := IsExpire(pastTime)
	assert.False(t, result2)

	// 测试未来的时间戳
	futureTime := time.Now().Add(1 * time.Hour).Unix()
	result3 := IsExpire(futureTime)
	assert.False(t, result3)
}

func TestUtilsIntegration(t *testing.T) {
	// 集成测试：测试多个函数的组合使用
	uuid := GenerateUUID()
	now := GetNow()

	// 创建一个包含UUID和时间戳的消息
	message := struct {
		ID        string `json:"id"`
		Timestamp int64  `json:"timestamp"`
		Expired   bool   `json:"expired"`
	}{
		ID:        uuid,
		Timestamp: now,
		Expired:   IsExpire(now),
	}

	// 转换为字符串
	messageStr := Msg2String(message)

	// 验证结果
	assert.NotEmpty(t, messageStr)
	assert.Contains(t, messageStr, uuid)
	assert.Contains(t, messageStr, "false") // IsExpire总是返回false
}

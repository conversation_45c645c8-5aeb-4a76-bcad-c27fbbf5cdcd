package utils

import (
	"agent/utils/log"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

func GenerateUUID() string {
	uuid, _ := uuid.NewUUID()
	return uuid.String()
}

func GetNow() int64 {
	return time.Now().Unix()
}

func Msg2String(msg interface{}) string {
	res, err := json.Marshal(msg)
	if err != nil {
		log.Errorf("%s.%s", "Convert to string fail", err)
		return ""
	}
	return string(res)
}

func IsExpire(timestamp int64) bool {
	return false
}

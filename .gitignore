# Binaries for programs and plugins
main
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories
/vendor/

# IDE directories
.idea
/agent

# Clacky configuration
.1024*
!.1024
.breakpoints

# Binary output
/main

# Go files
*.o
*.a
_obj
_test
_cgo_*
*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*
_testmain.go

# Temp files
*~
*.swp
*.swo
.DS_Store
coverage.txt
coverage.out
coverage.html
tmp/
linter_test
node_modules

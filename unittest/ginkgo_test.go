package unittest

import (
	"agent/utils/fileUtils"
	"os"
	"testing"
)

func TestGolangGetRun(t *testing.T) {
	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/sample_test.go"); os.IsNotExist(err) {
		t.Skip("Test file templates/sample_test.go not found, skipping test")
		return
	}
	wanted := "cd .;ginkgo --keep-going --json-report=.paas-unit-dd93fb4b51b14e1489cdbfecc48817fd --output-dir=/home/<USER>/app --focus-file=sample_test.go"

	ginkgo := Ginkgo{}
	get, _ := ginkgo.GetRun("sample_test.go", "test", "dd93fb4b51b14e1489cdbfecc48817fd")
	if get != wanted {
		t.Errorf("GetRun() = %v, want %v", get, wanted)
	}
}

func TestAnalyzerTestFile(t *testing.T) {
	ginkgo := Ginkgo{}

	// Skip if test file doesn't exist
	if _, err := os.Stat("sample_test.test"); os.IsNotExist(err) {
		t.Skip("Test file sample_test.test not found, skipping test")
		return
	}

	result := ginkgo.AnalyzerTestFile("sample_test.test")
	// fmt.Println(result)
	if len(result) != 8 {
		t.Errorf("AnalyzerTestFile() = %v, want %v", len(result), 8)
	}
}

func TestAnalyzerGinkgoNormalTestFile(t *testing.T) {
	ginkgo := Ginkgo{}

	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/ginkgo2.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/ginkgo2.md not found, skipping test")
		return
	}

	result := ginkgo.AnalyzerTestFile("templates/ginkgo2.md")
	// fmt.Println(result)
	if len(result) != 8 {
		t.Errorf("AnalyzerTestFile() = %v, want %v", len(result), 8)
	}
}

func TestGinkgo_GetRun_Success(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "cd path/to/test;ginkgo --keep-going --json-report=.paas-unit-12345 --output-dir=/home/<USER>/app --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoDir(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "ginkgo --keep-going --json-report=.paas-unit-12345 --output-dir=/home/<USER>/app --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoMethod(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := ""
	runId := "12345"
	expected := "cd path/to/test;ginkgo --keep-going --json-report=.paas-unit-12345 --output-dir=/home/<USER>/app --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoRunId(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := ""
	expected := "cd path/to/test;ginkgo --keep-going --json-report=.paas-unit- --output-dir=/home/<USER>/app --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoOutputDir(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "cd path/to/test;ginkgo --keep-going --json-report=.paas-unit-12345 --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoPaasUnitConstant(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/file.go"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "cd path/to/test;ginkgo --keep-going --output-dir=/home/<USER>/app --focus-file=file.go"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestGinkgo_GetRun_NoFileName(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/test/"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "cd path/to/test;ginkgo --keep-going --json-report=.paas-unit-12345 --output-dir=/home/<USER>/app"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

func TestAnalyzerTestFileInTemplateFolder(t *testing.T) {
	ginkgo := Ginkgo{}

	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/ginkgo2.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/ginkgo2.md not found, skipping test")
		return
	}

	result := ginkgo.AnalyzerTestFile("templates/ginkgo2.md")
	want := 10
	if len(result) != want {
		t.Errorf("AnalyzerTestFile() = %v, want %v", len(result), want)
	}
}

func TestGinkgo_GetRun_InvalidPath(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "path/to/invalid/file"
	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	methodName := "TestFunction"
	runId := "12345"
	expected := "cd path/to/invalid;ginkgo --keep-going --json-report=.paas-unit-12345 --output-dir=/home/<USER>/app --focus-file=file"
	actual, _ := ginkgo.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected command to be %q, but got %q", expected, actual)
	}
}

// result := "Running Suite: App Suite - /home/<USER>/app\r\n===========================================\r\nRandom Seed: \u001b[1m1685429594\u001b[0m\r\n\r\nWill run \u001b[1m1\u001b[0m of \u001b[1m11\u001b[0m specs\r\n\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;10m•\u001b[0m\u001b[38;5;14mS\u001b[0m\u001b[38;5;14mS\u001b[0m\r\n\r\n\u001b[38;5;10m\u001b[1mRan 1 of 11 Specs in 0.000 seconds\u001b[0m\r\n\u001b[38;5;10m\u001b[1mSUCCESS!\u001b[0m -- \u001b[38;5;10m\u001b[1m1 Passed\u001b[0m | \u001b[38;5;9m\u001b[1m0 Failed\u001b[0m | \u001b[38;5;11m\u001b[1m0 Pending\u001b[0m | \u001b[38;5;14m\u001b[1m10 Skipped\u001b[0m\r\nPASS\r\n\r\nGinkgo ran 1 suite in 1.196566824s\r\nTest Suite Passed\r\n"

func TestGinkgo_AnalysisResult_Pass(t *testing.T) {
	ginkgo := Ginkgo{}
	path := "templates/ginkgo_result.md"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result, err := fileUtils.Read(path)
	if err != nil {
		t.Errorf("Read file error: %v", err)
	}
	// fmt.Println(result)
	runId := "12345"
	expected := TestCaseResult{
		RunStatus: PASS,
		Time:      "123.46ms",
		Result:    result,
		TestCaseResultItems: []TestCaseResultItem{
			{
				Name:    "测试用例1",
				Success: false,
			},
			{
				Name:    "测试用例2",
				Success: false,
			},
			{
				Name:    "测试用例3",
				Success: true,
			},
			{
				Name:    "测试用例4",
				Success: true,
			},
			{
				Name:    "测试用例5",
				Success: true,
			},
			{
				Name:    "测试用例6",
				Success: true,
			},
			{
				Name:    "测试用例7",
				Success: true,
			},
			{
				Name:    "测试用例8",
				Success: true,
			},
			{
				Name:    "测试用例9",
				Success: true,
			},
			{
				Name:    "测试用例10",
				Success: true,
			},
		},
	}
	actual := ginkgo.AnalysisResult(path, result, runId)
	if actual.RunStatus != expected.RunStatus {
		t.Errorf("Expected RunStatus to be %q, but got %q", expected.RunStatus, actual.RunStatus)
	}
	// if actual.Time != expected.Time {
	// 	t.Errorf("Expected Time to be %q, but got %q", expected.Time, actual.Time)
	// }
	// if actual.Result != expected.Result {
	// 	t.Errorf("Expected Result to be %q, but got %q", expected.Result, actual.Result)
	// }
	// if len(actual.TestCaseResultItems) != len(expected.TestCaseResultItems) {
	// 	t.Errorf("Expected %d test case result items, but got %d", len(expected.TestCaseResultItems), len(actual.TestCaseResultItems))
	// }
	// for i, expectedItem := range expected.TestCaseResultItems {
	// 	actualItem := actual.TestCaseResultItems[i]
	// 	if actualItem.Name != expectedItem.Name {
	// 		t.Errorf("Expected test case result item %d name to be %q, but got %q", i, expectedItem.Name, actualItem.Name)
	// 	}
	// 	if actualItem.Success != expectedItem.Success {
	// 		t.Errorf("Expected test case %s result item %d success to be %v, but got %v", expectedItem.Name, i, expectedItem.Success, actualItem.Success)
	// 	}

	// }
}

func TestAnalyzerTestFile_NestedDescribes(t *testing.T) {
	return // TODO:先跳过
	ginkgo := Ginkgo{}

	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/ginkgo.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/ginkgo.md not found, skipping test")
		return
	}

	result := ginkgo.AnalyzerTestFile("templates/ginkgo.md")
	// if len(result) != 3 {
	//     t.Errorf("AnalyzerTestFile() = %v, want %v", len(result), 3)
	// }

	if result[0].MethodName != "testSolution1" {
		t.Errorf("AnalyzerTestFile() MethodName = %v, want %v", result[0].MethodName, "testSolution1")
	}

	if result[1].MethodName != "testSolution2" {
		t.Errorf("AnalyzerTestFile() MethodName = %v, want %v", result[1].MethodName, "testSolution2")
	}

	if result[2].MethodName != "testSolution3" {
		t.Errorf("AnalyzerTestFile() MethodName = %v, want %v", result[2].MethodName, "testSolution3")
	}

}

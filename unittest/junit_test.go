package unittest

import (
	"os"
	"strings"
	"testing"
)

type GetRunTestCase struct {
	path       string
	methodName string
	runId      string
	expected   string
}

func TestJavaGetRun(t *testing.T) {
	// Instantiate the Junit struct
	j := &Junit{}
	path := "/path/to/File.java"
	anotherPath := "/path/to/AnotherFile.java"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file templates/junit_test.md not found, skipping test")
		return
	}

	// Skip if another test file doesn't exist
	if _, err := os.Stat(anotherPath); os.IsNotExist(err) {
		t.Skip("Test file templates/junit_test.md not found, skipping test")
		return
	}

	// Define the test cases
	testCases := []GetRunTestCase{
		{
			path:       path,
			methodName: "testMethod",
			runId:      "123",
			expected:   "javac -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher --select-method File#testMethod",
		},
		{
			path:       path,
			methodName: "",
			runId:      "456",
			expected:   "javac -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher --select-class package.File",
		},
		{
			path:       anotherPath,
			methodName: "anotherTestMethod",
			runId:      "789",
			expected:   "javac -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher --select-method AnotherFile#anotherTestMethod",
		},
	}

	// Iterate through the test cases
	for i, tc := range testCases {
		t.Run(strings.Join([]string{"Test case", string(rune(i))}, " "), func(t *testing.T) {
			result, _ := j.GetRun(tc.path, tc.methodName, tc.runId)
			if result != tc.expected {
				t.Errorf("Expected: %s, got: %s", tc.expected, result)
			}
		})
	}
}

package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"path/filepath"
	"strings"
)

/*
- 使用 `@Test` 注解可以明显的通过正则得知该测试文件共有 3 个测试用例，这是在运行前可以做到的
- 而使用 `@ParameterizedTest` 注解，则只有在运行后，才可以通过测试结果分析出共有 3 个测试用例，代码中写法不一，自由度大。

这就造成了如果支持了 `@ParameterizedTest` 和 `@TestFactory` 注解的形式后，测试用例界面的用例数量可能在运行前后是不一致的，通过正则分析文件，只能得出有 1 个用例，但是实际上其实是 3 个独立的用例

其次，是测试用例结果的表现也不相同，左侧是通过 `@Test` 注解，右侧是`@ParameterizedTest` 注解，导致现阶段使用正则的方式解析结果比较困难，后者在解析的时候可以用于**正则的标志太少**

*/

type Junit struct{}

var JavaNotes = getJavaNotes()

type MethodSource struct {
	ClassName            string `json:"className"`
	MethodName           string `json:"methodName"`
	MethodParameterTypes string `json:"methodParameterTypes"`
}

// * 仅用于json格式的输出文件解析
type JunitResultJson struct {
	Testsuites []JunitTestResult `json:"testsuites"`
	Success    bool              `json:"success"`
}

type JunitTestResult struct {
	Message          string             `json:"message"`
	Name             string             `json:"name"`
	StartTime        int64              `json:"startTime"`
	EndTime          int64              `json:"endTime"`
	AssertionResults []AssertionResults `json:"assertionResults"`
}

type JunitAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getJavaNotes() []string {
	javaNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return javaNotes
}

func JunitCompileErrorPattern() string {
	return "Test run finished after \\d+ ms"
}

func JunitErrorPattern(filename, method string) string {

	return "JUnit Jupiter:" + filename + ":\\b" + method + "\\b[\\s\\S]*?\\[\\.\\.\\.\\]"
}

func (j Junit) AnalysisResult(path string, result string, runId string) TestCaseResult {

	// * 结果返回给showmebug端调用
	testCaseResult := TestCaseResult{}

	// * 获取测试用例文件名
	filename := strings.Split(filepath.Base(path), ".")[0]

	testCasefuncs := j.AnalyzerTestFile(path)

	testCaseResult.Result = result
	testCaseResult.Time = getContent(JunitTimePattern, result)

	// * 检查是否编译报错
	// * 检查是否运行超时
	// ? 超时情况下，是否还需要解析已经通过的用例？
	// ! 存在超时的用例时，在当前运行整个测试用例文件的情况下，无法判断那个测试用例超时，所以当时超时用例和编译报错的表现是一样的
	if len(getContent(JunitCompileErrorPattern(), result)) == 0 {
		testCaseResult.RunStatus = FAIL
		// todo: init the testCaseResultItemS
		var testCaseResultItemS []TestCaseResultItem
		for _, testCase := range testCasefuncs {
			testCaseResultItem := TestCaseResultItem{}
			testCaseResultItem.Name = testCase.Name
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
		testCaseResult.TestCaseResultItems = testCaseResultItemS
		return testCaseResult
	}

	testCaseResult.RunStatus = getRunStatus(JunitExecuteSuccessPattern, result)

	var testCaseResultItemS []TestCaseResultItem

	for _, testCase := range testCasefuncs {

		testCaseResultItem := TestCaseResultItem{}
		testCaseResultItem.Name = testCase.Name
		testCaseResultItem.Input = testCase.Input
		testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput

		methodWithoutBracket := strings.ReplaceAll(testCase.MethodName, "()", "")
		if testCase.DisplayName != "" {
			methodWithoutBracket = strings.Trim(testCase.DisplayName, " ")
		}
		errPat := JunitErrorPattern(filename, methodWithoutBracket)
		errStr := getContent(errPat, result)

		// * if testCase.MethodName in errorCases
		if len(errStr) > 0 {
			testCaseResultItem.Success = false
			testCaseResultItem.Details = errStr

			reason := getContent(JunitFailReasonPattern, errStr)
			testCaseResultItem.Reason = reason

		} else {
			testCaseResultItem.Success = true
		}

		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}
	testCaseResult.TestCaseResultItems = testCaseResultItemS

	return testCaseResult
}

func (j *Junit) AnalyzerTestFile(path string) []TestCaseFun {

	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	// * remove notes
	fileContent = deleteNotes(fileContent, JavaNotes)
	// ? @ParameterizedTest, @TestFactory 问题点
	var matches = regexp2FindAllString(JunitFilePattern, fileContent)

	var testCaseFunS []TestCaseFun

	for _, match := range matches {

		testCaseFun := TestCaseFun{}

		methodName := getContent(JunitMethodNamePattern, strings.TrimSpace(match))
		testCaseFun.MethodName = methodName

		testCaseFun.DisplayName = getContent(JUnitNamePattern, strings.TrimSpace(match))
		testCaseFun = setItemByFun(testCaseFun, strings.TrimSpace(match))
		if testCaseFun.Name == "" {
			if testCaseFun.DisplayName != "" {
				testCaseFun.Name = testCaseFun.DisplayName
			} else {
				testCaseFun.Name = testCaseFun.MethodName
			}
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)

	}
	return testCaseFunS
}

/*
javac -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java' ! -name 'ShowMeBugAnswer.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher --select-class SampleTest
*/

/*
javac -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java' ! -name 'ShowMeBugAnswer.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher --select-class HiddenTest --reports-dir=hidden-test-report
*/
func (j *Junit) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	var cmd strings.Builder
	var debugConfig config.DebugConfig
	debugConfig.Support = true
	var compilePrefix string
	if fileUtils.FileExist("pom.xml") {
		compilePrefix = "mvn clean compile && mvn dependency:copy-dependencies && "
	}
	debugConfig.Compile = compilePrefix + "javac -encoding utf-8 -g -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java' ! -name 'ShowMeBugAnswer.java') "
	debugConfig.Launch = make(map[string]interface{})
	debugConfig.Launch["mainClass"] = "org.junit.platform.console.ConsoleLauncher"
	debugConfig.Launch["classPaths"] = []string{"../unit/java/*", ".", "target/dependency/*", "lib/*"}
	debugConfig.Launch["sourcePaths"] = []string{"/home/<USER>/app"}
	debugConfig.Launch["cwd"] = "."
	debugConfig.Launch["console"] = "integratedTerminal"
	debugConfig.Launch["internalConsoleOptions"] = "neverOpen"

	// * get file name without .java
	fileNameNoSuffix := strings.Split(filepath.Base(path), ".")[0]
	packageName := getPackage(path)

	cmd.WriteString(compilePrefix + "javac -encoding utf-8 -cp ../unit/java/*:.:target/dependency/*:lib/* -d . $(find . -type f -name '*.java' ! -name 'ShowMeBugAnswer.java')  && java -cp ../unit/java/*:.:target/dependency/*:lib/* org.junit.platform.console.ConsoleLauncher")
	if methodName != "" {
		args := "--select-method" + fileNameNoSuffix + "#" + methodName
		cmd.WriteString(args)
		debugConfig.Launch["args"] = args
	} else {
		args := " --select-class " + packageName + fileNameNoSuffix
		cmd.WriteString(args)
		debugConfig.Launch["args"] = args
	}

	return cmd.String(), debugConfig
}

func getPackage(fileRootPath string) string {
	fileContent, _ := fileUtils.Read(fileRootPath)
	packageName := getContent(JunitPackagePattern, fileContent)
	if packageName == "" {
		return ""
	}

	return packageName + " ."
}

func getRunStatus(regex string, result string) string {
	if len(getContent(regex, result)) > 0 {
		return "PASS"
	} else {
		return "FAIL"
	}

}

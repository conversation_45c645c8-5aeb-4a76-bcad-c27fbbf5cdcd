package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/google/uuid"
)

// * C语言单元测试框架Unity测试结果解析

type Unity struct{}

var UnityNotes = getUnityNotes()

type UnityResultJson struct {
	UnityTestResults []UnityTestResults `json:"unityTestResults"`
	Success          bool               `json:"success"`
}

type UnityTestResults struct {
	Message          string                  `json:"message"`
	Name             string                  `json:"name"`
	StartTime        int64                   `json:"startTime"`
	EndTime          int64                   `json:"endTime"`
	AssertionResults []UnityAssertionResults `json:"assertionResults"`
}

type UnityAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getUnityNotes() []string {
	unityNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return unityNotes
}

func getUnityMethodNamePattern(method string) string {
	return "(#start)[\\s|\\S]*? " + method
}

type UnityTestResult struct {
	UseCaseName string `json:"useCaseName"`
	Time        string `json:"time"`
	Success     bool   `json:"success"`
	Reason      string `json:"reason"`
}

func getMethodByResult(result string) ([]UnityTestResult, error) {
	re, err := regexp.Compile(UnityMethodLineByResultPattern)
	if err != nil {
		return nil, err
	}

	matches := re.FindAllString(result, -1)
	var unityTestResults []UnityTestResult
	for _, match := range matches {
		unityTestResult := UnityTestResult{}
		unityTestResult.UseCaseName = getContent(UnityMethodByResultPattern, match)
		unityTestResult.Time = getContent(UnityTimeByResultPattern, match)
		if strings.Contains(match, "FAIL") {
			unityTestResult.Success = false
			unityTestResult.Reason = getContent(UnityReasonPattern, match)
		} else {
			unityTestResult.Success = true
		}
		unityTestResults = append(unityTestResults, unityTestResult)
	}
	return unityTestResults, nil
}

func stringToInt(strTime string) int {
	time, _ := strconv.Atoi(strTime)
	return time
}

func getUnityRunStatus(result string) string {
	if len(result) == 0 {
		return "FAIL"
	}
	return "PASS"
}

func (u *Unity) AnalysisResult(path string, result string, runId string) TestCaseResult {

	// * 返回给showmebug调用的结果
	var testCaseResult TestCaseResult

	// fileContent, _ := fileUtils.Read(path)
	testCaseResult.Result = result
	// * 发生 编译失败 和 异常 时，单元测试运行结果中不包含形如："2 Tests 2 Failures 0 Ignored\nFAIL" 的内容
	// * 当测试用例存在运行时间过长的用例时，由于agent设置了30秒超时后会把测试进程杀死，导致测试结果中不包含形如："2 Tests 2 Failures 0 Ignored\nFAIL" 的内容
	testCaseResult.RunStatus = getUnityRunStatus(getContent(UnityExecuteSuccessPattern, result))

	testCases := u.AnalyzerTestFile(path)

	testCaseMethodResults, _ := getMethodByResult(result)

	resultTime := 0

	var testCaseResultItemS []TestCaseResultItem
	for _, res := range testCaseMethodResults {

		var testCaseResultItem TestCaseResultItem
		// testCaseResultItem.Name = res.UseCaseName

		for _, testCase := range testCases {
			if testCase.MethodName == res.UseCaseName {
				testCaseResultItem.Name = testCase.Name
				testCaseResultItem.Input = testCase.Input
				testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
			}
		}

		testCaseResultItem.Success = res.Success
		if !testCaseResultItem.Success {
			testCaseResultItem.Reason = res.Reason
			testCaseResultItem.Details = ".*" + res.UseCaseName + ".*"
		}
		testCaseResultItem = setItem(testCaseResultItem, "")
		resultTime += stringToInt(res.Time)
		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}

	// * 写一个检查不存在 testCaseMethodResults 里的测试用例的方法
	// * 如果这个用例不存在 testCaseMethodResults， 说明这个用例运行超时了，需要把这个用例的结果设置为超时
	for _, testCase := range testCases {
		// fmt.Println("name: ", testCase.Name)
		// fmt.Println("method name: ", testCase.MethodName)
		if !isExist(testCase.MethodName, testCaseMethodResults) {
			// fmt.Println("!!!!!!!")
			var testCaseResultItem TestCaseResultItem
			testCaseResultItem.Name = testCase.Name
			testCaseResultItem.Input = testCase.Input
			testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
			testCaseResultItem.Success = false
			// testCaseResultItem.Reason = "运行超时"
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}

	}

	testCaseResult.Time = strconv.Itoa(resultTime) + TimeUnit
	testCaseResult.TestCaseResultItems = testCaseResultItemS

	return testCaseResult
}

func isExist(methodName string, testCaseMethodResults []UnityTestResult) bool {
	for _, testCaseMethodResult := range testCaseMethodResults {
		// fmt.Println("Usecase Name: ", testCaseMethodResult.UseCaseName)
		if testCaseMethodResult.UseCaseName == methodName {
			return true
		}
	}
	return false
}

func (u *Unity) AnalyzerTestFile(path string) []TestCaseFun {
	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}
	fileContent = deleteNotes(fileContent, UnityNotes)

	var matches = regexp2FindAllString(UnityMethodLinePattern, fileContent)
	var testCaseFunS []TestCaseFun

	for _, match := range matches {

		testCaseFun := TestCaseFun{}
		methodName := getContent(UnityMethodNamePattern, match) // ? 为空时怎么办
		testCaseFun.MethodName = methodName
		protocolStr := getContent(getUnityMethodNamePattern(methodName), fileContent)
		testCaseFun = setItemByFun(testCaseFun, protocolStr)
		if testCaseFun.Name == "" {
			testCaseFun.Name = methodName
		}

		fileContent = strings.ReplaceAll(fileContent, protocolStr, "")

		testCaseFunS = append(testCaseFunS, testCaseFun)
	}

	return testCaseFunS
}

// gcc sample_test.c ../unit/unity/unity.c -o .paas-unit-f8c5aa523baf44208bc1215cef7e2a1a -I ../unit/unity && ./.paas-unit-f8c5aa523baf44208bc1215cef7e2a1a
func (u *Unity) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {

	var stringBuffer strings.Builder

	fileName := filepath.Base(path)
	// testOutput := PaasUnitConstant + runId

	uuid, _ := uuid.NewUUID()
	uid := strings.ReplaceAll(uuid.String(), "-", "")

	program := PaasUnitConstant + uid

	stringBuffer.WriteString("gcc ")
	stringBuffer.WriteString(fileName)
	stringBuffer.WriteString(" ../unit/unity/unity.c -o ")
	stringBuffer.WriteString(program)
	stringBuffer.WriteString(" -I ../unit/unity && ./")
	stringBuffer.WriteString(program)

	var debugConfig config.DebugConfig
	debugConfig.Support = true
	debugConfig.Compile = "gcc -g -O0 " + fileName + " ../unit/unity/unity.c -o " + program + " -lm -I ../unit/unity"
	debugConfig.Launch = map[string]interface{}{}
	debugConfig.Launch["program"] = "./" + program
	debugConfig.Launch["cwd"] = "."
	debugConfig.Launch["MIMode"] = "gdb"
	debugConfig.Launch["miDebuggerPath"] = "gdb"
	debugConfig.Launch["externalConsole"] = true
	debugConfig.Launch["setupCommands"] = []map[string]interface{}{
		{"text": "-enable-pretty-printing", "ignoreFailures": true},
	}
	debugConfig.Launch["log"] = map[string]interface{}{}
	debugConfig.Launch["miDebuggerArgs"] = "-q -ex quit; wait() { fg >/dev/null; }; gdb -q --interpreter=mi"

	return stringBuffer.String(), debugConfig
}

package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"agent/utils/log"
	"bufio"
	"encoding/json"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

type Dart struct{}

type DartTestResult struct {
	Success bool   `json:"success"`
	Type    string `json:"type"`
	Time    int    `json:"time"`
}

type DartTestStart struct {
	Test DartTestStartData `json:"test"`
}

type DartTestStartData struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type DartTestError struct {
	TestID     int    `json:"testID"`
	Error      string `json:"error"`
	StackTrace string `json:"stackTrace"`
}

type DartTestDone struct {
	TestID int    `json:"testID"`
	Result string `json:"result"`
}

type DartTestCaseResult struct {
	Start DartTestStart `json:"start"`
	Error DartTestError `json:"error"`
	Done  DartTestDone  `json:"done"`
}

func getDartNotes() []string {
	dartNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
		"\\n *import.*",
	}
	return dartNotes
}

func getTestcaseErrorPattern(method string) string {
	return method + " \\[E\\]"
}

func getTestcaseErrorReasonPattern(method string) string {
	return method + " [\\s|\\S]+?main.<fn>"
}

func (d *Dart) CheckIfTestcase(methodname string, filename string) bool {
	testCases := d.AnalyzerTestFile(filename)
	// fmt.Println("testCases: ", testCases)
	for _, testCase := range testCases {
		if testCase.MethodName == methodname {
			return true
		}
	}
	return false
}

func GetLastLineOfTheFile(filename string) string {
	// * get the last line of the file

	file, err := os.Open(filename)
	if err != nil {
		return ""
	}
	defer file.Close()

	var lastLine string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lastLine = scanner.Text()
	}

	if scanner.Err() != nil {
		return ""
	}

	return lastLine
}

func (d *Dart) AnalysisResultFile(result string, filename string) []DartTestCaseResult {

	// fmt.Println("result: ", result)

	var results []DartTestCaseResult
	var starts []DartTestStart
	var errors []DartTestError
	var dones []DartTestDone

	lines := strings.Split(result, "\n")

	// fmt.Println("lines: ", lines)

	for _, line := range lines {
		switch {
		case strings.Contains(line, "testStart"):
			// fmt.Println("testStart:", line)
			var DartTestStart DartTestStart
			err := json.Unmarshal([]byte(line), &DartTestStart)
			if err != nil {
				break
			}
			if d.CheckIfTestcase(DartTestStart.Test.Name, filename) {
				starts = append(starts, DartTestStart)
			}

		case strings.Contains(line, "error"):
			// fmt.Println("testError:", line)
			var DartTestError DartTestError
			err := json.Unmarshal([]byte(line), &DartTestError)
			if err != nil {
				break
			}
			errors = append(errors, DartTestError)

		case strings.Contains(line, "testDone"):
			// fmt.Println("testDone:", line)
			var DartTestDone DartTestDone
			err := json.Unmarshal([]byte(line), &DartTestDone)
			if err != nil {
				break
			}
			dones = append(dones, DartTestDone)
		default:
		}
	}

	for _, start := range starts {
		var result DartTestCaseResult
		result.Start = start
		for _, done := range dones {
			if start.Test.Id == done.TestID {
				result.Done = done
				break
			}
		}
		for _, err := range errors {
			if start.Test.Id == err.TestID {
				result.Error = err
				break
			}
		}
		results = append(results, result)
	}

	return results

}

func (d *Dart) AnalysisResult(path string, result string, runId string) TestCaseResult {
	testResult := TestCaseResult{}
	testResult.Result = result

	// * 编译报错，直接返回
	if strings.Contains(result, "Failed to load") {
		testResult.RunStatus = FAIL
		return testResult
	}

	testResult.RunStatus = PASS

	testCases := d.AnalyzerTestFile(path)
	var testResultItems []TestCaseResultItem
	for _, testcase := range testCases {
		var res TestCaseResultItem
		res.Name = testcase.Name
		res.Input = testcase.Input
		res.ExpectedOutput = testcase.ExpectedOutput

		str := getContent(getTestcaseErrorPattern(escapeExprSpecialWord(testcase.MethodName)), result)

		log.Println("str: ", str)

		if len(str) > 0 || strings.Contains(result, testcase.MethodName+" [E]") {
			res.Success = false
			errPattern := getTestcaseErrorReasonPattern(escapeExprSpecialWord(testcase.MethodName))
			log.Println("errPattern: ", errPattern)
			res.Reason = getContent(errPattern, result)
			res.Details = res.Reason
			log.Println("res.Reason: ", res.Reason)
			testResult.RunStatus = FAIL
		} else {
			res.Success = true
		}
		testResultItems = append(testResultItems, res)
	}

	testResult.TestCaseResultItems = testResultItems

	return testResult
}

func (d *Dart) AnalysisResult1(path string, result string, runId string) TestCaseResult {

	testResult := TestCaseResult{}

	resultStr := strings.Split(result, runId)

	if len(resultStr) < 2 {
		testResult.RunStatus = FAIL
		return testResult
	}

	testResult.Result = resultStr[0]

	// * get the last line of the result
	lastLine := GetLastLineOfTheFile(path)

	var dartResultJson DartTestResult
	err := json.Unmarshal([]byte(lastLine), &dartResultJson)

	if err != nil {
		return testResult
	}

	if dartResultJson.Success {
		testResult.Result = "success"
	} else {
		testResult.Result = "fail"
	}
	testResult.Time = strconv.Itoa(dartResultJson.Time)

	testCaseAnalysisLocal := d.AnalysisResultFile(result, path)

	testcases := d.AnalyzerTestFile(path)

	var testResultItems []TestCaseResultItem
	for _, testcase := range testcases {
		var res TestCaseResultItem
		res.Name = testcase.Name
		res.Input = testcase.Input
		res.ExpectedOutput = testcase.ExpectedOutput

		for _, analysis := range testCaseAnalysisLocal {
			if testcase.MethodName == analysis.Start.Test.Name {
				if analysis.Done.Result == "success" {
					res.Success = true
				} else {
					res.Success = false
					res.Reason = analysis.Error.Error
					res.Details = analysis.Error.StackTrace
				}
			}
		}
		testResultItems = append(testResultItems, res)
	}

	testResult.TestCaseResultItems = testResultItems

	return testResult
}

func (d *Dart) AnalyzerTestFile(path string) []TestCaseFun {

	if isDir(path) {
		return nil
	}

	fileContent, _ := fileUtils.Read(path)
	fileContent = deleteNotes(fileContent, getDartNotes())

	matches := regexp2FindAllString(DartTestcasePattern, fileContent)

	var testCaseFuns []TestCaseFun

	for _, match := range matches {
		testCaseFun := TestCaseFun{}

		testCaseFun.MethodName = getContent(DartTestMethodTestNamePattern, strings.TrimSpace(match))
		testCaseFun = setItemByFun(testCaseFun, match)

		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}

		testCaseFuns = append(testCaseFuns, testCaseFun)

	}

	return testCaseFuns
}

func (d *Dart) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	fileDirPath := ""

	if strings.Contains(path, "/") {
		fileDirPath = path[0 : strings.LastIndex(path, "/")+1]
	}
	// fileDirPath := path[0 : strings.LastIndex(path, "/")+1]
	// if len(fileDirPath) == 0 {
	// 	fileDirPath = "./"
	// }

	filename := filepath.Base(path)

	var stringBuffer strings.Builder

	stringBuffer.WriteString("dart test --reporter expanded ")
	stringBuffer.WriteString(fileDirPath)
	stringBuffer.WriteString(filename)
	stringBuffer.WriteString(" --no-color --file-reporter json:")
	stringBuffer.WriteString(PaasUnitConstant)
	stringBuffer.WriteString(runId)

	return stringBuffer.String(), config.DebugConfig{}
}

<?xml version="1.0" encoding="utf-8" standalone="no"?>
<test-run id="0" runstate="Runnable" testcasecount="1" result="Failed" total="1" passed="0" failed="1" warnings="0" inconclusive="0" skipped="0" asserts="1" engine-version="********" clr-version="4.0.30319.42000" start-time="2023-06-01 08:29:17Z" end-time="2023-06-01 08:29:18Z" duration="0.713564">
  <command-line><![CDATA[/home/<USER>/unit/csharp/NUnit.ConsoleRunner.3.15.0/tools/nunit3-console.exe SampleTest.dll --result=result.md]]></command-line>
  <test-suite type="Assembly" id="1-1002" name="SampleTest.dll" fullname="/home/<USER>/app/SampleTest.dll" runstate="Runnable" testcasecount="1" result="Failed" site="Child" start-time="2023-06-01T08:29:18.4893570Z" end-time="2023-06-01T08:29:18.5745870Z" duration="0.084943" total="1" passed="0" failed="1" warnings="0" inconclusive="0" skipped="0" asserts="1">
    <environment framework-version="********" clr-version="4.0.30319.42000" os-version="Unix *********" platform="Unix" cwd="/home/<USER>/app" machine-name="6960200779a9" user="root" user-domain="6960200779a9" culture="en-US" uiculture="en-US" os-architecture="x64" />
    <settings>
      <setting name="DisposeRunners" value="True" />
      <setting name="WorkDirectory" value="/home/<USER>/app" />
      <setting name="ImageRuntimeVersion" value="4.0.30319" />
      <setting name="ImageRequiresX86" value="False" />
      <setting name="ImageRequiresDefaultAppDomainAssemblyResolver" value="False" />
      <setting name="TargetRuntimeFramework" value="mono-4.0" />
      <setting name="NumberOfTestWorkers" value="4" />
    </settings>
    <properties>
      <property name="_PID" value="318" />
      <property name="_APPDOMAIN" value="domain-" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="TestFixture" id="1-1000" name="SampleTest" fullname="SampleTest" classname="SampleTest" runstate="Runnable" testcasecount="1" result="Failed" site="Child" start-time="2023-06-01T08:29:18.5045980Z" end-time="2023-06-01T08:29:18.5735200Z" duration="0.068922" total="1" passed="0" failed="1" warnings="0" inconclusive="0" skipped="0" asserts="1">
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-case id="1-1001" name="SolutionTest" fullname="SampleTest.SolutionTest" methodname="SolutionTest" classname="SampleTest" runstate="Runnable" seed="832388998" result="Failed" start-time="2023-06-01T08:29:18.5083030Z" end-time="2023-06-01T08:29:18.5685280Z" duration="0.060391" asserts="1">
        <failure>
          <message><![CDATA[  Expected: 3
  But was:  2
]]></message>
          <stack-trace><![CDATA[  at SampleTest.SolutionTest () [0x00014] in <c842ee4a4b7c4cb4be057bad00452194>:0 
]]></stack-trace>
        </failure>
        <output><![CDATA[- - - test - - -
]]></output>
        <assertions>
          <assertion result="Failed">
            <message><![CDATA[  Expected: 3
  But was:  2
]]></message>
            <stack-trace><![CDATA[  at SampleTest.SolutionTest () [0x00000] in <c842ee4a4b7c4cb4be057bad00452194>:0 
]]></stack-trace>
          </assertion>
        </assertions>
      </test-case>
    </test-suite>
  </test-suite>
</test-run>
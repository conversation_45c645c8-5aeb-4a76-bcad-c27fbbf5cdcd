// 将使⽤ JUnit5 执⾏测试

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import java.util.*;

class HiddenTest {

    /*
    #start
    #name[测试用例1]
    #input[arr = ["NORTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = []]
    #end
     */
    @Test
    void testSolution1() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {};
        String[] arr = {"NORTH", "SOUTH", "EAST", "WEST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例2]
    #input[arr = ["NORTH", "EAST", "WEST", "SOUTH", "WEST", "WEST"]]
    #expected[expected = ["WEST", "WEST"]]
    #end
     */
    @Test
    void testSolution2() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"WEST", "WEST"};
        String[] arr = {"NORTH", "EAST", "WEST", "SOUTH", "WEST", "WEST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例3]
    #input[arr = ["SOUTH", "WEST", "NORTH", "EAST"]]
    #expected[expected = ["SOUTH", "WEST", "NORTH", "EAST"]]
    #end
     */
    @Test
    void testSolution3() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"SOUTH", "WEST", "NORTH", "EAST"};
        String[] arr = {"SOUTH", "WEST", "NORTH", "EAST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例4]
    #input[arr = ["NORTH", "WEST", "SOUTH", "EAST"]]
    #expected[expected = ["NORTH", "WEST", "SOUTH", "EAST"]]
    #end
     */
    @Test
    void testSolution4() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"NORTH", "WEST", "SOUTH", "EAST"};
        String[] arr = {"NORTH", "WEST", "SOUTH", "EAST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例5]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH", "WEST"]]
    #expected[expected = ["WEST"]]
    #end
     */
    @Test
    void testSolution5() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"WEST"};
        String[] arr = {"NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH", "WEST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例6]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH"]]
    #expected[expected = []]
    #end
     */
    @Test
    void testSolution6() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {};
        String[] arr = {"NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例7]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = ["SOUTH"]]
    #end
     */
    @Test
    void testSolution7() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"SOUTH"};
        String[] arr = {"NORTH", "SOUTH", "SOUTH", "EAST", "WEST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例8]
    #input[arr = ["WEST", "SOUTH", "SOUTH", "EAST"]]
    #expected[expected = ["WEST", "SOUTH", "SOUTH", "EAST"]]
    #end
     */
    @Test
    void testSolution8() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"WEST", "SOUTH", "SOUTH", "EAST"};
        String[] arr = {"WEST", "SOUTH", "SOUTH", "EAST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例9]
    #input[arr = ["EAST", "WEST", "SOUTH", "SOUTH", "NORTH", "EAST", "NORTH"]]
    #expected[expected = ["SOUTH", "EAST", "NORTH"]]
    #end
     */
    @Test
    void testSolution9() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"SOUTH", "EAST", "NORTH"};
        String[] arr = {"EAST", "WEST", "SOUTH", "SOUTH", "NORTH", "EAST", "NORTH"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }

    /*
    #start
    #name[测试用例10]
    #input[arr = ["SOUTH", "NORTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = ["SOUTH"]]
    #end
     */
    @Test
    void testSolution10() {
        ShowMeBug showMeBug = new ShowMeBug();
        String[] expected = {"SOUTH"};
        String[] arr = {"SOUTH", "NORTH", "SOUTH", "EAST", "WEST"};
        assertArrayEquals(expected, showMeBug.dirReduc(arr));
    }
}
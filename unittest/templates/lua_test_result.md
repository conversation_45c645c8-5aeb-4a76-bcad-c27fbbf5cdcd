00:00 +0: 1 + 2 = 3
00:00 +1: 2 + 3 = 5
00:00 +2: testcase 3
00:00 +3: All tests passed!

.paas-unit-hidden_test.dart_runUnitTestResult{"protocolVersion":"0.1.1","runnerVersion":"1.21.4","pid":125,"type":"start","time":0}
{"suite":{"id":0,"platform":"vm","path":"./hidden_test.dart"},"type":"suite","time":0}
{"test":{"id":1,"name":"loading ./hidden_test.dart","suiteID":0,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":2}
{"count":1,"time":11,"type":"allSuites"}
{"testID":1,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":3049}
{"group":{"id":2,"suiteID":0,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":null,"column":null,"url":null},"type":"group","time":3138}
{"test":{"id":3,"name":"1 + 2 = 3","suiteID":0,"groupIDs":[2],"metadata":{"skip":false,"skipReason":null},"line":12,"column":3,"url":"file:///home/<USER>/app/hidden_test.dart"},"type":"testStart","time":3148}
{"testID":3,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3300}
{"test":{"id":4,"name":"2 + 3 = 5","suiteID":0,"groupIDs":[2],"metadata":{"skip":false,"skipReason":null},"line":23,"column":3,"url":"file:///home/<USER>/app/hidden_test.dart"},"type":"testStart","time":3301}
{"testID":4,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3307}
{"test":{"id":5,"name":"testcase 3","suiteID":0,"groupIDs":[2],"metadata":{"skip":false,"skipReason":null},"line":34,"column":3,"url":"file:///home/<USER>/app/hidden_test.dart"},"type":"testStart","time":3309}
{"testID":5,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3318}
{"success":true,"type":"done","time":3361}

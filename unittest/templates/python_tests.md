# 将使⽤ pytest 执⾏测试
from showmebug import solution


"""
#start
#name[测试用例1]
#input[arg1 = 1, arg2 = 1]
#expected[expected = 2]
#end
"""
def test_solution_1():
    expected = 2
    arg1 = 1
    arg2 = 1
    assert solution(arg1, arg2) == expected


"""
#start
#name[测试用例2]
#input[arg1 = 1, arg2 = 2]
#expected[expected = 3]
#end
"""
def test_solution_2():
    expected = 3
    arg1 = 1
    arg2 = 2
    assert solution(arg1, arg2) == expected


"""
#start
#name[测试用例3]
#input[arg1 = 1, arg2 = 3]
#expected[expected = 4]
#end
"""
def test_solution_3():
    expected = 4
    arg1 = 1
    arg2 = 3
    assert solution(arg1, arg2) == expected

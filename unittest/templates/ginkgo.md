// 将使⽤ GinkGo 执⾏测试
// 必须定义⼀个 包名为 `main` 的包

package main

import (
    . "github.com/onsi/ginkgo/v2"
    . "github.com/onsi/gomega"
)

// 通过数组构建链表（无头节点）
func buildList1(nums []int) *ListNode {
	var cur *ListNode
	for i := len(nums) - 1; i >= 0; i-- {
		node := &ListNode{
			Value: nums[i],
			Next:  cur,
		}
		cur = node
	}
	return cur
}

// 将链表转换成对应的数组，方便断言和报错
func listToSlice1(head *ListNode) []int {
	var slice []int
	for head != nil {
		slice = append(slice, head.Value)
		head = head.Next
	}
	return slice
}

/*
#start
#name[测试用例1]
#input[head = [1, 2, 3, 3, 4, 4, 5]]
#expected[[1, 2, 3, 4, 5]]
#end
*/
var _ = Describe("testSolution1", func() {
    It("solution should return 1,2,3,4,5", func() {
        var expected = buildList1([]int{1, 2, 3, 4})
        var head = buildList1([]int{1, 2, 3, 3, 4, 4, 5})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例2]
#input[head = [1, 2, 3, 4, 5]]
#expected[[1, 2, 3, 4, 5]]
#end
*/
var _ = Describe("testSolution2", func() {
    It("solution should return 1,2,3,4,5", func() {
        var expected = buildList1([]int{1, 2, 3, 4})
        var head = buildList1([]int{1, 2, 3, 4, 5})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例3]
#input[head = [1, 2, 3, 3, 3, 4, 4, 5, 5, 9, 9, 9, 12]]
#expected[[1, 2, 3, 4, 5, 9, 12]]
#end
*/
var _ = Describe("testSolution3", func() {
    It("solution", func() {
        var expected = buildList1([]int{1, 2, 3, 4, 5, 9, 12})
        var head = buildList1([]int{1, 2, 3, 3, 3, 4, 4, 5, 5, 9, 9, 9, 12})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例4]
#input[head = [23]]
#expected[[23]]
#end
*/
var _ = Describe("testSolution4", func() {
    It("solution should return 23", func() {
        var expected = buildList1([]int{23})
        var head = buildList1([]int{23})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例5]
#input[head = [1, 2, 2]]
#expected[[1, 2]]
#end
*/
var _ = Describe("testSolution5", func() {
    It("solution should return 1,2", func() {
        var expected = buildList1([]int{1, 2})
        var head = buildList1([]int{1, 2, 2})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例6]
#input[head = [1, 1, 1, 1, 1, 1, 1]]
#expected[[1]]
#end
*/
var _ = Describe("testSolution6", func() {
    It("solution should return 1", func() {
        var expected = buildList1([]int{1})
        var head = buildList1([]int{1, 1, 1, 1, 1, 1, 1})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例7]
#input[head = [1, 1, 1, 1, 2, 2, 2, 2]]
#expected[[1, 2]]
#end
*/
var _ = Describe("testSolution7", func() {
    It("solution should return 1,2", func() {
        var expected = buildList1([]int{1, 2})
        var head = buildList1([]int{1, 1, 1, 1, 2, 2, 2, 2})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例8]
#input[head = [12, 13, 14, 15, 15, 15, 15, 16, 16, 17, 18, 20, 20]]
#expected[[12, 13, 14, 15, 16, 17, 18, 20]]
#end
*/
var _ = Describe("testSolution8", func() {
    It("solution", func() {
        var expected = buildList1([]int{12, 13, 14, 15, 16, 17, 18, 20})
        var head = buildList1([]int{12, 13, 14, 15, 15, 15, 15, 16, 16, 17, 18, 20, 20})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例9]
#input[head = [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 10, 10, 15]]
#expected[[1, 2, 3, 4, 10, 15]]
#end
*/
var _ = Describe("testSolution9", func() {
    It("solution", func() {
        var expected = buildList1([]int{1, 2, 3, 4, 10, 15})
        var head = buildList1([]int{1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 10, 10, 15})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})

/*
#start
#name[测试用例10]
#input[head = [12, 12, 12, 12, 12, 12, 12, 13, 14, 14, 14, 15, 15]]
#expected[[12, 13, 14, 15]]
#end
*/
var _ = Describe("testSolution10", func() {
    It("solution", func() {
        var expected = buildList1([]int{12, 13, 14, 15})
        var head = buildList1([]int{12, 12, 12, 12, 12, 12, 12, 13, 14, 14, 14, 15, 15})
        Expect(listToSlice1(expected)).To(Equal(listToSlice1(solution(head))))
    })
})
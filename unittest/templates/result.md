"{\"Time\":\"00:00.003\",\"Result\":\"PHPUnit 9.5.20 #StandWithUkraine\\r\\n\\r\\n..F.......                                                        10 / 10 (100%)\\r\\nWarning: file_put_contents(/nix/store/b9ijg908cmf0k70zcss5ivi4jay6sfwa-phpunit-9.5.20/bin/.phpunit.result.cache): failed to open stream: Permission denied in phar:///nix/store/b9ijg908cmf0k70zcss5ivi4jay6sfwa-phpunit-9.5.20/bin/phpunit/phpunit/Runner/DefaultTestResultCache.php on line 110\\r\\n\\r\\n\\r\\nTime: 00:00.003, Memory: 18.00 MB\\r\\n\\r\\nThere was 1 failure:\\r\\n\\r\\n1) HiddenTest::testSolution3\\r\\nFailed asserting that two arrays are equal.\\r\\n--- Expected\\r\\n+++ Actual\\r\\n@@ @@\\r\\n     2 =\\u003e 3\\r\\n     3 =\\u003e 4\\r\\n     4 =\\u003e 5\\r\\n-    5 =\\u003e 9\\r\\n-    6 =\\u003e 12\\r\\n )\\r\\n\\r\\n/home/<USER>/app/HiddenTest.php:71\\r\\n\\r\\nFAILURES!\\r\\nTests: 10, Assertions: 10, Failures: 1.\\r\\n\",\"TestCaseResultItems\":[{\"Name\":\"\",\"Success\":true,\"Input\":\"\",\"ExpectedOutput\":\"\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"\",\"Success\":true,\"Input\":\"\",\"ExpectedOutput\":\"\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例1\",\"Success\":true,\"Input\":\"head = [1, 2, 3, 3, 4, 4, 5]\",\"ExpectedOutput\":\"[1, 2, 3, 4, 5]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例2\",\"Success\":true,\"Input\":\"head = [1, 2, 3, 4, 5]\",\"ExpectedOutput\":\"[1, 2, 3, 4, 5]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例3\",\"Success\":true,\"Input\":\"head = [1, 2, 3, 3, 3, 4, 4, 5, 5, 9, 9, 9, 12]\",\"ExpectedOutput\":\"[1, 2, 3, 4, 5, 9, 12]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例4\",\"Success\":true,\"Input\":\"head = [23]\",\"ExpectedOutput\":\"[23]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例5\",\"Success\":true,\"Input\":\"head = [1, 2, 2]\",\"ExpectedOutput\":\"[1, 2]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例6\",\"Success\":true,\"Input\":\"head = [1, 1, 1, 1, 1, 1, 1]\",\"ExpectedOutput\":\"[1]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例7\",\"Success\":true,\"Input\":\"head = [1, 1, 1, 1, 2, 2, 2, 2]\",\"ExpectedOutput\":\"[1, 2]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例8\",\"Success\":true,\"Input\":\"head = [12, 13, 14, 15, 15, 15, 15, 16, 16, 17, 18, 20, 20]\",\"ExpectedOutput\":\"[12, 13, 14, 15, 16, 17, 18, 20]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例9\",\"Success\":true,\"Input\":\"head = [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 4, 4, 4, 4, 10, 10, 15]\",\"ExpectedOutput\":\"[1, 2, 3, 4, 10, 15]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"},{\"Name\":\"测试用例10\",\"Success\":true,\"Input\":\"head = [12, 12, 12, 12, 12, 12, 12, 13, 14, 14, 14, 15, 15]\",\"ExpectedOutput\":\"[12, 13, 14, 15]\",\"ActualOutput\":\"\",\"Reason\":\"\",\"Details\":\"\"}],\"RunStatus\":\"FAIL\"}"
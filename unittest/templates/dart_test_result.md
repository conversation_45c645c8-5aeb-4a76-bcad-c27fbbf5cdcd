00:00 +0: 1 + 2 = 3
00:00 +0 -1: 1 + 2 = 3 [E]
  Expected: <3>
    Actual: <11>
  
  package:test_api       expect
  sample_test.dart 13:5  main.<fn>
  
00:00 +0 -1: 测试用例2
00:00 +0 -2: 测试用例2 [E]
  Expected: <6>
    Actual: <11>
  
  package:test_api       expect
  sample_test.dart 17:5  main.<fn>
  
00:00 +0 -2: Some tests failed.

Consider enabling the flag chain-stack-traces to receive more detailed exceptions.
For example, 'dart test --chain-stack-traces'.
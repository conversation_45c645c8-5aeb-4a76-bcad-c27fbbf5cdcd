<?
// 将使⽤ PHPUnit 执⾏测试
// 请保留以下两⾏代码以引⼊必要依赖

use PHPUnit\Framework\TestCase;

require "./showmebug.php";

class HiddenTest extends TestCase
{

    /*
    #start
    #name[测试用例1]
    #input[arr = ["NORTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = []]
    #end
    */
    public function testSolution1()
    {
        $expected = array();
        $arr = array("NORTH", "SOUTH", "EAST", "WEST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例2]
    #input[arr = ["NORTH", "EAST", "WEST", "SOUTH", "WEST", "WEST"]]
    #expected[expected = ["WEST", "WEST"]]
    #end
    */
    public function testSolution2()
    {
        $expected = array("WEST", "WEST");
        $arr = array("NORTH", "EAST", "WEST", "SOUTH", "WEST", "WEST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例3]
    #input[arr = ["SOUTH", "WEST", "NORTH", "EAST"]]
    #expected[expected = ["SOUTH", "WEST", "NORTH", "EAST"]]
    #end
    */
    public function testSolution3()
    {
        $expected = array("SOUTH", "WEST", "NORTH", "EAST");
        $arr = array("SOUTH", "WEST", "NORTH", "EAST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例4]
    #input[arr = ["NORTH", "WEST", "SOUTH", "EAST"]]
    #expected[expected = ["NORTH", "WEST", "SOUTH", "EAST"]]
    #end
    */
    public function testSolution4()
    {
        $expected = array("NORTH", "WEST", "SOUTH", "EAST");
        $arr = array("NORTH", "WEST", "SOUTH", "EAST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例5]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH", "WEST"]]
    #expected[expected = ["WEST"]]
    #end
    */
    public function testSolution5()
    {
        $expected = array("WEST");
        $arr = array("NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH", "WEST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例6]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH"]]
    #expected[expected = []]
    #end
    */
    public function testSolution6()
    {
        $expected = array();
        $arr = array("NORTH", "SOUTH", "SOUTH", "EAST", "WEST", "NORTH");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例7]
    #input[arr = ["NORTH", "SOUTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = ["SOUTH"]]
    #end
    */
    public function testSolution7()
    {
        $expected = array("SOUTH");
        $arr = array("NORTH", "SOUTH", "SOUTH", "EAST", "WEST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例8]
    #input[arr = ["WEST", "SOUTH", "SOUTH", "EAST"]]
    #expected[expected = ["WEST", "SOUTH", "SOUTH", "EAST"]]
    #end
    */
    public function testSolution8()
    {
        $expected = array("WEST", "SOUTH", "SOUTH", "EAST");
        $arr = array("WEST", "SOUTH", "SOUTH", "EAST");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例9]
    #input[arr = ["EAST", "WEST", "SOUTH", "SOUTH", "NORTH", "EAST", "NORTH"]]
    #expected[expected = ["SOUTH", "EAST", "NORTH"]]
    #end
    */
    public function testSolution9()
    {
        $expected = array("SOUTH", "EAST", "NORTH");
        $arr = array("EAST", "WEST", "SOUTH", "SOUTH", "NORTH", "EAST", "NORTH");
        $this->assertEquals(dirReduc($arr), $expected);
    }

    /*
    #start
    #name[测试用例10]
    #input[arr = ["SOUTH", "NORTH", "SOUTH", "EAST", "WEST"]]
    #expected[expected = ["SOUTH"]]
    #end
    */
    public function testSolution10()
    {
        $expected = array("SOUTH");
        $arr = array("SOUTH", "NORTH", "SOUTH", "EAST", "WEST");
        $this->assertEquals(dirReduc($arr), $expected);
    }
}

?>
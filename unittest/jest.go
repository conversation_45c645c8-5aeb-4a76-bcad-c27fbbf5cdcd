package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"unicode/utf8"
)

type Jest struct{}

var JavascriptNotes = getJavaScriptNotes()

type JestResultJson struct {
	TestResults []TestResults `json:"testResults"`
	Success     bool          `json:"success"`
}

type TestResults struct {
	Message          string             `json:"message"`
	Name             string             `json:"name"`
	StartTime        int64              `json:"startTime"`
	EndTime          int64              `json:"endTime"`
	AssertionResults []AssertionResults `json:"assertionResults"`
}

type AssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getJavaScriptNotes() []string {
	javaScriptNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return javaScriptNotes
}

func (r Jest) AnalysisResult(path string, result string, runId string) TestCaseResult {

	fileContent, _ := fileUtils.Read(path)
	result1 := result
	array := strings.Split(result1, getSplitResultPattern(runId))
	testCaseResult := TestCaseResult{}
	var testCaseResultItemS []TestCaseResultItem
	result = array[0]
	testCaseResult.Result = result

	// * 获取测试用例
	testCaseFuncs := r.AnalyzerTestFile(path)

	if len(array) == 1 {
		testCaseResult.RunStatus = FAIL
		for _, testCaseFunc := range testCaseFuncs {
			testCaseResultItem := TestCaseResultItem{}
			testCaseResultItem.Name = testCaseFunc.Name
			testCaseResultItem.Success = false
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
		testCaseResult.TestCaseResultItems = testCaseResultItemS
		return testCaseResult
	}
	resultJson := array[1]

	var jestResultJson JestResultJson
	err := json.Unmarshal([]byte(resultJson), &jestResultJson)
	if err != nil {
		testCaseResult.RunStatus = FAIL
		for _, testCaseFunc := range testCaseFuncs {
			testCaseResultItem := TestCaseResultItem{}
			testCaseResultItem.Name = testCaseFunc.Name
			testCaseResultItem.Success = false
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
		testCaseResult.TestCaseResultItems = testCaseResultItemS
		return testCaseResult
	}

	if jestResultJson.Success {
		testCaseResult.RunStatus = PASS
	} else {
		testCaseResult.RunStatus = FAIL
	}
	// * 解析出错用例的原因
	for _, results := range jestResultJson.TestResults {
		// result 截取100kb
		if len(result) > 100*1024 {
			// 计算字符串中的字符数
			charCount := utf8.RuneCountInString(result)
			// 如果字符数大于50KB，就截取前50KB个字符
			if charCount > 100*1024 {
				slicedRunes := []rune(result)[:100*1024]
				result = string(slicedRunes)
			}
		}
		testCaseResult.Result = result
		testCaseResult.Time = fmt.Sprintf("%dms", results.EndTime-results.StartTime)
		for _, item := range results.AssertionResults {
			testCaseResultItem := TestCaseResultItem{}
			if JestPassPattern == item.Status {
				testCaseResultItem.Success = true
			} else {
				testCaseResultItem.Success = false
				testCaseResultItem.Reason = getContent(JestReasonPattern, item.FailureMessages[0])
				testCaseResultItem.Details = item.FailureMessages[0]
			}
			testCaseResultItem.Name = item.FullName
			protocolStr := getContent(getProtocolPattern(item.Title)+"['|\"]", fileContent)
			testCaseResultItem = setItem(testCaseResultItem, protocolStr)
			fileContent = strings.Replace(fileContent, protocolStr, "", 1)
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
	}
	testCaseResult.TestCaseResultItems = testCaseResultItemS
	return testCaseResult
}

func (r *Jest) AnalyzerTestFile(path string) []TestCaseFun {
	// 获取文件内容
	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}
	// 删除注释
	fileContent = deleteNotes(fileContent, JavascriptNotes)

	var matches = regexp2FindAllString(JestMethodPattern, fileContent)
	var testCaseFunS []TestCaseFun
	for _, match := range matches {
		testCaseFun := TestCaseFun{}
		describeName := getContent(JestMethodDescribeNamePattern, strings.TrimSpace(match))
		testName := getContent(JestMethodTestNamePattern, strings.TrimSpace(match))
		testCaseFun.MethodName = describeName + " " + testName
		testCaseFun = setItemByFun(testCaseFun, match)
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}
	return testCaseFunS
}

func (r *Jest) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	var stringBuffer strings.Builder
	fileDirPath := path[0 : strings.LastIndex(path, "/")+1]
	fileName := filepath.Base(path)
	stringBuffer.WriteString("jest ")
	stringBuffer.WriteString(fileDirPath)
	stringBuffer.WriteString(fileName)

	if methodName != "" {
		stringBuffer.WriteString(" -t ")
		stringBuffer.WriteString(methodName)
	}

	stringBuffer.WriteString(" --useStderr --json --verbose --outputFile=")
	stringBuffer.WriteString(PaasUnitConstant)
	stringBuffer.WriteString(runId)

	return stringBuffer.String(), config.DebugConfig{}
}

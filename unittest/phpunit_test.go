package unittest

import (
	"bytes"
	"os"
	"testing"
)

func TestPhpunit_AnalyzerTestFile_Success(t *testing.T) {
	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/sample_test.php"); os.IsNotExist(err) {
		t.Skip("Test file templates/sample_test.php not found, skipping test")
		return
	}

	phpunit := Phpunit{}
	path := "templates/sample_test.php"
	expected := []TestCaseFun{
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
		{
			MethodName:     "Test1",
			Name:           "Test1",
			DisplayName:    "TestClass",
			Input:          "",
			ExpectedOutput: "",
		},
	}
	actual := phpunit.AnalyzerTestFile(path)
	if len(actual) != len(expected) {
		t.Errorf("Expected %d test cases, but got %d", len(expected), len(actual))
	}
	for i, actualItem := range actual {
		expectedItem := expected[i]
		if actualItem.MethodName != expectedItem.MethodName {
			t.Errorf("Expected method name to be %q, but got %q", expectedItem.MethodName, actualItem.MethodName)
		}
		if actualItem.Name != expectedItem.Name {
			t.Errorf("Expected test case name to be %q, but got %q", expectedItem.Name, actualItem.Name)
		}
		// if actualItem.ClassName != expectedItem.ClassName {
		// 	t.Errorf("Expected class name to be %q, but got %q", expectedItem.ClassName, actualItem.ClassName)
		// }
		// if actualItem.Package != expectedItem.Package {
		// 	t.Errorf("Expected package name to be %q, but got %q", expectedItem.Package, actualItem.Package)
		// }
	}
}

func TestPhpunit_AnalyzerTestFile_EmptyFile(t *testing.T) {
	phpunit := Phpunit{}
	path := "/path/to/empty/file.php"
	expected := []TestCaseFun(nil)
	actual := phpunit.AnalyzerTestFile(path)
	if len(actual) != len(expected) {
		t.Errorf("Expected %d test cases, but got %d", len(expected), len(actual))
	}
}

func TestPhpunit_AnalyzerTestFile_NonexistentFile(t *testing.T) {
	phpunit := Phpunit{}
	path := "/path/to/nonexistent/file.php"
	expected := []TestCaseFun(nil)
	actual := phpunit.AnalyzerTestFile(path)
	if len(actual) != len(expected) {
		t.Errorf("Expected %d test cases, but got %d", len(expected), len(actual))
	}
}

func TestPhpunit_AnalyzerTestFile_InvalidFile(t *testing.T) {
	phpunit := Phpunit{}
	path := "/path/to/invalid/file.php"
	expected := []TestCaseFun(nil)
	actual := phpunit.AnalyzerTestFile(path)
	if len(actual) != len(expected) {
		t.Errorf("Expected %d test cases, but got %d", len(expected), len(actual))
	}
}

func TestPhpunitAnalysisResult_Pass(t *testing.T) {
	return // TODO:先跳过
	phpunit := Phpunit{}
	path := "templates/HiddenTest.md"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := "PHPUnit 9.5.20 #StandWithUkraine\r\n\r\n.FFFF                                                               5 / 5 (100%)\r\nWarning: file_put_contents(/nix/store/b9ijg908cmf0k70zcss5ivi4jay6sfwa-phpunit-9.5.20/bin/.phpunit.result.cache): failed to open stream: Permission denied in phar:///nix/store/b9ijg908cmf0k70zcss5ivi4jay6sfwa-phpunit-9.5.20/bin/phpunit/phpunit/Runner/DefaultTestResultCache.php on line 110\r\n\r\n\r\nTime: 00:00.001, Memory: 18.00 MB\r\n\r\nThere were 4 failures:\r\n\r\n1) HiddenTest::testSolution2\r\nFailed asserting that 4 matches expected 3.\r\n\r\n/home/<USER>/app/HiddenTest.php:18\r\n\r\n2) HiddenTest::testSolution3\r\nFailed asserting that 5 matches expected 3.\r\n\r\n/home/<USER>/app/HiddenTest.php:22\r\n\r\n3) HiddenTest::testSolution4\r\nFailed asserting that 6 matches expected 3.\r\n\r\n/home/<USER>/app/HiddenTest.php:26\r\n\r\n4) HiddenTest::testSolution5\r\nFailed asserting that 7 matches expected 3.\r\n\r\n/home/<USER>/app/HiddenTest.php:30\r\n\r\nFAILURES!\r\nTests: 5, Assertions: 5, Failures: 4.\r\n"
	runId := "12345"

	var buf bytes.Buffer

	actual := phpunit.AnalysisResult(path, result, runId)

	t.Log(buf.String())
	// Verify overall test case result
	if actual.Result != result {
		t.Errorf("Expected result to be %q, but got %q", result, actual.Result)
	}
	if actual.Time == "" {
		t.Error("Expected time to be non-empty")
	}
	if actual.RunStatus != FAIL {
		t.Errorf("Expected run status to be %q, but got %q", FAIL, actual.RunStatus)
	}

	// Verify individual test case results
	expected := []TestCaseResultItem{
		{
			Name:    "测试用例1",
			Success: true,
		},
		{
			Name:    "测试用例2",
			Success: true,
		},
		{
			Name:    "测试用例3",
			Success: false,
		},
		{
			Name:    "测试用例4",
			Success: true,
		},
		{
			Name:    "测试用例5",
			Success: true,
		},
		{
			Name:    "测试用例6",
			Success: true,
		},
		{
			Name:    "测试用例7",
			Success: true,
		},
		{
			Name:    "测试用例8",
			Success: true,
		},
		{
			Name:    "测试用例9",
			Success: true,
		},
		{
			Name:    "测试用例10",
			Success: true,
		},
	}
	if len(actual.TestCaseResultItems) != len(expected) {
		t.Errorf("Expected %d test case result items, but got %d", len(expected), len(actual.TestCaseResultItems))
	}
	for i, actualItem := range actual.TestCaseResultItems {
		expectedItem := expected[i]
		if actualItem.Name != expectedItem.Name {
			t.Errorf("Expected test case name to be %q, but got %q", expectedItem.Name, actualItem.Name)
		}
		if actualItem.Success != expectedItem.Success {
			t.Errorf("Expected test case %q success to be %v, but got %v", expectedItem.Name, expectedItem.Success, actualItem.Success)
		}
	}
}

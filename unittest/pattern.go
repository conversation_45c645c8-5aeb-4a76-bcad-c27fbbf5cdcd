package unittest

const (
	HomePath = "/home/<USER>/app/"

	FeatureName      = ".1024feature"
	PaasUnitConstant = ".paas-unit-"
	FrameCode        = "paas_unittest_framework_code"

	UnittestRunStart = "run_start"

	ProtocolNamePattern     = "(?<=#name\\[)[\\s|\\S]*?(?=])"
	ProtocolInputPattern    = "(?<=#input\\[).*(?=])"
	ProtocolExpectedPattern = "(?<=#expected\\[).*(?=])"

	TimeUnit = "ms"

	// jest
	JestMethodPattern             = "(#start|describe\\(|test\\()[\\s|\\S]*?(?=}\\))"
	JestMethodDescribeNamePattern = "(?<=describe\\(['\"]).*(?=['\"],)"
	JestMethodTestNamePattern     = "(?<=test\\(['\"]).*(?=['\"],)"
	JestPassPattern               = "passed"
	JestReasonPattern             = "Error:[\\s|\\S]*Received:.*"

	// pytest
	PytestMethodPattern             = "(#start|def test_)[\\s|\\S]*?(\\):)"
	PytestTimePattern               = "(?<= (tests|passed|warning|failed).* in ).*(s)"
	PytestExecuteSuccessPattern     = "=+.*(tests|passed|warning|failed).*in.*=+"
	PytestMethodDescribeNamePattern = "(?<=def\\s)[\\s|\\S]*?(?=\\()" // ?
	PytestMethodNamePattern         = "(?<=def\\s)[\\s|\\S]*?(?=\\()" // ?
	PytestPassPatten                = "passed"
	PytestReasonPattern             = "Error:[\\s|\\S]*Received:.*" // TODO: need to be improved

	// ginkgo
	GinkgoMethodPattern             = "(?<=#start| Describe)[\\s|\\S]*?It\\(.*"
	GinkgoMethodDescribeNamePattern = "(?<=Describe\\(['\"]).*(?=['\"],)"
	GinkgoMethodTestNamePattern     = "(?<=It\\(['\"]).*(?=['\"],)"
	GinkoMethodNamePattern          = "(?<=It\\(\").*(?=\")"
	GinkoDescribeNamePattern        = `Describe\("([^"]+)"`

	// junit
	JunitPackagePattern        = "(?<=^package).*(?=;$)"
	JunitFilePattern           = "(#start|@Test)[\\s\\S]*?(\\(\\))(?=.*\\{)"
	JUnitNamePattern           = "(?<=@DisplayName\\(\").*?(?=\"\\))"
	JunitMethodNamePattern     = "(?<=void ).*?(\\(\\))"
	JunitNameProtocolPattern   = "(?<=#name\\[).*(?=\\])"
	JunitTimePattern           = "(?<=Test run finished after ).* ms"
	JunitExecuteSuccessPattern = "Test run finished after \\d+ ms"
	JunitResultAfterPattern    = "\\[\\d\\dm"
	JunitResultPattern         = ".\\[0m.*"
	JunitFailReasonPattern     = "(expected:).*(.\\n)"

	// gtest
	GtestMethodPattern            = "((TEST\\(|#start[\\s|\\S]*?#end))[\\s|\\S]*?(?=\\{)"
	GtestSuiteNamePattern         = "(?<=TEST\\().*(?=,)"
	GtestNamePattern              = "(?<=TEST\\(.*,).*(?=\\))"
	GtestMethodTestNamePattern    = "(?<=TEST\\(.*?,).*?(?=\\))"
	GtestProtocolPattern          = "(?<=#name\\[)[\\s|\\S]*?(?=])"
	GtestTimePattern              = "(?<=test (suites|suite) ran. \\()\\d*(?= ms)"
	GtestExecuteSuccessPattern    = "\\d+ test(s)? from \\d+ test suite(s)? ran\\. \\(\\d+ ms total\\)"
	GtestMethodFailPattern        = "\\[  FAILED  \\]"
	GtestMethodFailDetailsPattern = "(?<=\\[ RUN.     \\] )[\\s|\\S]*?(?=\\[  FAILED )"
	GtestMethodFailReasonPattern  = "(?<=\\[ RUN.     \\] )[\\s|\\S]*?(?=Expected)"

	// Unity
	UnityTimeByResultPattern       = "(?<=\\()\\d*(?= ms\\))"
	UnityMethodByResultPattern     = "(?<=\\d:).*?(?=:(FAIL|PASS))"
	UnityExecuteSuccessPattern     = "\\d+ Tests \\d+ Failures \\d+ Ignored\\s+(FAIL|OK)"
	UnityMethodLinePattern         = "(RUN_TEST\\().*?\\);"
	UnityMethodNamePattern         = "(?<=RUN_TEST\\().*?(?=\\))"
	UnityMethodLineByResultPattern = ".* \\(\\d* ms\\)"
	UnityReasonPattern             = "(?<=FAIL: ).*(?=\\(\\d* ms\\))"

	// phpunit
	PhpunitMethodPattern     = "((#start[\\s|\\S]*?#end|function))[\\s|\\S]*?(\\))"
	PhpunitMethodNamePattern = "(?<=function )test.*?(?=\\()"
	PhpunitTimePattern       = "(?<=Time: ).*?(?=,)"

	// lua
	LuaMethodPattern     = "(#start|function\\s*[Tt][Ee][Ss][Tt])[\\s|\\S]*?(\\)\\n)"
	LuaMethodNamePattern = "(?<=function).*:\\s*[Tt][Ee][Ss][Tt].*(?=\\(\\))|(?<=function)\\s*[Tt][Ee][Ss][Tt][^:^\\n]*(?=\\(\\))"
	LuaUnitTimePattern   = "(?<=tests in ).* seconds"
	// ruby
	RubyMethodPattern              = "(#start[\\s|\\S]*?#end|describe)[\\s|\\S]*?it.*[\\s|\\S]*?(end)"
	RspecMethodNameDescribePattern = `(describe).*(('|\"))`
	RspecMethodNameItPattern       = `(it ).*(('|\"))`
	// nunit

	NunitMethodPattern     = "((\\[Test\\]|#start))[\\s|\\S]*?(\\))"
	NunitMethodNamePattern = "(?<=\\[Test\\])[\\s|\\S]*?\\)"

	// swift
	SwiftClassPattern      = "(?<=class ).*(?=:)"
	SwiftMethodNamePattern = "func\\s+(\\w+)"
	SwiftTestcasePattern   = "(#start|func test)[\\s|\\S]*?\\)"
	// SwiftTestcasePattern = "(?:\\/\\*[\\s\\S]+?#end\\s*\\*\\/\\s*)?func\\s+(\\w+)"
	SwiftTestNamePattern = "test.*(?=\\()"

	// dart
	DartTestcasePattern           = "(#start|test)[\\s|\\S]*?\\)"
	DartTestMethodTestNamePattern = "(?<=test *\\(['\"]).*(?=['\"],)"
)

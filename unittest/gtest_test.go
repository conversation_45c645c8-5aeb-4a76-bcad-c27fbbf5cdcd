package unittest

import (
	"os"
	"testing"
)

func TestGTest(t *testing.T) {
	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/gtest_testcase_large.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/gtest_testcase_large.md not found, skipping test")
		return
	}

	rspec := Gtest{}
	result := rspec.AnalyzerTestFile("templates/gtest_testcase_large.md")

	if len(result) != 7 {
		t.<PERSON>rrorf("AnalyzerTestFile() = %v, want %v", len(result), 7)
	}
}

func BenchmarkGtest_AnalyzerTestFile(b *testing.B) {
	// Skip benchmark if test file doesn't exist
	if _, err := os.Stat("templates/gtest_testcase_large.md"); os.IsNotExist(err) {
		b.Skip("Test file templates/gtest_testcase_large.md not found, skipping benchmark")
		return
	}

	gtest := Gtest{}
	path := "templates/gtest_testcase_large.md"
	for i := 0; i < b.N; i++ {
		gtest.AnalyzerTestFile(path)
	}
}

package unittest

import (
	"os"
	"strings"
	"testing"
)

// Test case struct
type GetUnityRunCmdTestCase struct {
	path       string
	methodName string
	runId      string
	expected   string
}

func TestUNityGetRun(t *testing.T) {
	// Instantiate the Unity struct
	u := &Unity{}
	path1 := "templates/sample_test1.c"
	path2 := "templates/sample_test2.c"
	path3 := "templates/sample_test3.c"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path1); os.IsNotExist(err) {
		t.Skip("Test file", path1, "not found, skipping test")
		return
	}
	if _, err := os.Stat(path2); os.IsNotExist(err) {
		t.Skip("Test file", path2, "not found, skipping test")
		return
	}
	if _, err := os.Stat(path3); os.IsNotExist(err) {
		t.<PERSON>p("Test file", path3, "not found, skipping test")
		return
	}

	// Define the test cases
	testCases := []GetRunTestCase{
		{
			path:       path1,
			methodName: "testMethod",
			runId:      "123",
			expected:   "gcc sample_test1.c ../unit/unity/unity.c -o .paas-unit-123 -I ../unit/unity && ./.paas-unit-123",
		},
		{
			path:       path2,
			methodName: "",
			runId:      "456",
			expected:   "gcc sample_test2.c ../unit/unity/unity.c -o .paas-unit-456 -I ../unit/unity && ./.paas-unit-456",
		},
		{
			path:       path3,
			methodName: "anotherTestMethod",
			runId:      "789",
			expected:   "gcc sample_test3.c ../unit/unity/unity.c -o .paas-unit-789 -I ../unit/unity && ./.paas-unit-789",
		},
	}

	// Iterate through the test cases
	for i, tc := range testCases {
		t.Run(strings.Join([]string{"Test case", string(rune(i))}, " "), func(t *testing.T) {
			result, _ := u.GetRun(tc.path, tc.methodName, tc.runId)
			if result != tc.expected {
				t.Errorf("Expected: %s, got: %s", tc.expected, result)
			}
		})
	}
}

package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
)

type Pytest struct{}

var PythonNotes = getPythonNotes()

// * 以下三个结构体是用于解析pytest的json结果，并不是实际返回给showmebug端的调用结果
type PytestResultJson struct {
	TestResults []PythonTestResult `json:"pythonTestResult"`
	Success     bool               `json:"success"`
}

type PythonTestResult struct {
	Message                string                   `json:"message"`
	Name                   string                   `json:"name"`
	StartTime              int64                    `json:"startTime"`
	EndTime                int64                    `json:"endTime"`
	PythonAssertionResults []PythonAssertionResults `json:"pythonAssertionResults"`
}

type PythonAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getPythonNotes() []string {
	pythonNotes := []string{
		"\"\"\"[\\s|\\S]*?\"\"\"",
		"'''[\\s|\\S]*?'''",
		"\\n *#.*",
	}
	return pythonNotes
}

func getPytestDetailReasonPattern(method string) string {
	return ".*_ " + method + " _[\\w\\W]*?(?=\\n.\\[31m.\\[1m_|\\n=)"
}

func getPytestRunStatus(regex string, result string) string {
	if getContent(regex, result) == "" {
		return FAIL
	}
	return PASS
}

// (hidden_test.py::test_solution1 \\u001b\[\d+m(PASSED|FAILED)\\u001b\[0m\\u001b\[\d+m)
func getPytestResultPattern(filename, methodName string, result string) string {
	pattern := "(" + filename + "::" + methodName + "(.*)(PASSED|FAILED))"
	re := regexp.MustCompile(pattern)
	matches := re.FindAllStringSubmatch(result, -1)
	if len(matches) == 0 {
		return ""
	}
	return matches[0][1]
}

// 分析运行用例结果
func (p Pytest) AnalysisResult(path string, result string, runId string) TestCaseResult {

	// * 返回给showmebug端的调用结果
	testCaseResult := TestCaseResult{}

	testCaseResult.Result = result
	testCaseResult.Time = getContent(PytestTimePattern, result)

	// ! 编译出错时，结果不包含形如 "==== 1 failed in 0.12s ===="。
	// ! 或者是运行时出错，结果不包含形如 "==== 1 failed in 0.12s ===="。
	// ! 异常会被认为是断言失败

	// ! 运行超时下，结果不包含形如 "==== 1 failed in 0.12s ===="，但有部分测试用例会通过，所以不能以这样的方式判断
	// * 应以正则匹配的方式去判断

	testCaseResult.RunStatus = getPytestRunStatus(PytestExecuteSuccessPattern, result)

	testCaseFuncs := p.AnalyzerTestFile(path)

	// * 组装testCaseResult.TestCaseResultItems
	var testCaseResultItemS []TestCaseResultItem

	for _, testCaseFunc := range testCaseFuncs {

		testCaseResultItem := TestCaseResultItem{}
		testCaseResultItem.ExpectedOutput = testCaseFunc.ExpectedOutput
		testCaseResultItem.Name = testCaseFunc.Name
		testCaseResultItem.Input = testCaseFunc.Input

		var str = getPytestResultPattern(
			path,
			testCaseFunc.MethodName,
			result,
		)

		if len(str) > 0 {
			if strings.Contains(str, "FAILED") {
				testCaseResultItem.Success = false
				failReasonPattern := getPytestDetailReasonPattern(testCaseFunc.MethodName)

				testCaseResultItem.Reason = getContent(failReasonPattern, result)

				testCaseResultItem.Details = getContent(PytestReasonPattern, result)
			} else {
				testCaseResultItem.Success = true
			}

		}

		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}
	testCaseResult.TestCaseResultItems = testCaseResultItemS

	for _, testCaseResultItem := range testCaseResult.TestCaseResultItems {
		fmt.Println("testCaseResultItem:", testCaseResultItem.Name, testCaseResultItem.Success)
	}

	return testCaseResult
}

func (p *Pytest) AnalyzerTestFile(path string) []TestCaseFun {
	// 获取测试用例文件内容
	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	// * 去除无关注释
	fileContent = deleteNotes(fileContent, PythonNotes)

	var matches = regexp2FindAllString(PytestMethodPattern, fileContent) // ? 能否不用正则表达式的方式

	var testCaseFunS []TestCaseFun
	for _, match := range matches {

		testCaseFun := TestCaseFun{}
		testName := getContent(PytestMethodNamePattern, strings.TrimSpace(match))
		testCaseFun.MethodName = testName
		testCaseFun = setItemByFun(testCaseFun, match)

		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}
	return testCaseFunS
}

// * 根据path解释出文件名，然后拼接成pytest运行命令
// * pytest -vr A sample_test.py
// ! 结果以json格式输出 会导致之前的题目不行
// ? pip install pytest-json
// ? pytest -vr A --json=report.json sample_test.py
func (p *Pytest) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {

	var stringBuilder strings.Builder

	fileName := filepath.Base(path) // ! pytest -vr A .sample_test.py

	filePath := filepath.Dir(path)

	stringBuilder.WriteString("pytest -vr A ")
	if filePath != "." {
		stringBuilder.WriteString(filePath)
	}
	stringBuilder.WriteString(fileName)

	if methodName != "" {
		stringBuilder.WriteString("::")
		stringBuilder.WriteString(methodName)
	}

	var debugConfig config.DebugConfig
	debugConfig.Support = true
	debugConfig.Launch = make(map[string]interface{})
	debugConfig.Launch["module"] = "pytest"
	debugConfig.Launch["console"] = "integratedTerminal"
	debugConfig.Launch["env"] = map[string]string{
		"PYTHONIOENCODING": "UTF-8",
		"PYTHONUNBUFFERED": "1",
	}

	debugMethod := ""
	if len(methodName) > 0 {
		debugMethod = "::" + methodName
	}
	debugConfig.Launch["args"] = []string{"-vr", "A", fileName + debugMethod}

	return stringBuilder.String(), debugConfig
}

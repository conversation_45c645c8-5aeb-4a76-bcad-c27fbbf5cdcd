package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"agent/utils/log"
	"encoding/xml"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// Nunit for C# unittest analysis
type Nunit struct{}

var NunitNotes = getNunitNotes()

type NunitResult struct {
	ID            string           `xml:"id,attr" json:"id"`
	RunState      string           `xml:"runstate,attr" json:"runstate"`
	TestCaseCount string           `xml:"testcasecount,attr" json:"testcasecount"`
	StartTime     string           `xml:"start-time,attr" json:"start_time"`
	EndTime       string           `xml:"end-time,attr" json:"end_time"`
	Duration      string           `xml:"duration,attr" json:"duration"`
	CommandLine   string           `xml:"command-line" json:"command_line"`
	Total         int64            `xml:"total,attr" json:"total"`
	Passed        int64            `xml:"passed,attr" json:"passed"`
	Failed        int64            `xml:"failed,attr" json:"failed"`
	TestSuites    []NunitTestSuite `xml:"test-suite" json:"test_suites"`
}

type NunitTestSuite struct {
	TestCases []NunitTestCase `xml:"test-suite>test-case"`
}
type NunitTestCase struct {
	ID            string        `xml:"id,attr"`
	Name          string        `xml:"name,attr"`
	FullName      string        `xml:"fullname,attr"`
	MethodName    string        `xml:"methodname,attr"`
	ClassName     string        `xml:"classname,attr"`
	RunState      string        `xml:"runstate,attr"`
	Seed          int           `xml:"seed,attr"`
	Result        string        `xml:"result,attr"`
	StartTime     string        `xml:"start-time,attr"`
	EndTime       string        `xml:"end-time,attr"`
	Duration      float64       `xml:"duration,attr"`
	Asserts       int           `xml:"asserts,attr"`
	CsharpFailure CsharpFailure `xml:"failure" json:"failure"`
}

type CsharpFailure struct {
	Message string `xml:"message" json:"message"`
	Stack   string `xml:"stack-trace" json:"stack_trace"`
}

type TestRunSummary struct {
	OverallResult string  `json:"overall_result"`
	TestCount     int     `json:"test_count"`
	Passed        int     `json:"passed"`
	Failed        int     `json:"failed"`
	Warnings      int     `json:"warnings"`
	Inconclusive  int     `json:"inconclusive"`
	Skipped       int     `json:"skipped"`
	StartTime     string  `json:"start_time"`
	EndTime       string  `json:"end_time"`
	Duration      float64 `json:"duration"`
}

func getNunitNotes() []string {
	nunitNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return nunitNotes
}

func getNunitTestSuccessPattern() string {
	return "(?s)Test Run Summary(.*)seconds"
}

func getNunitTestFailStrPattern() string {
	return "Failed:\\s*\\x1b\\[\\d{2}m\\x1b\\[\\d{2}m(\\d+)\\x1b\\[\\d{2}m"
}

func getNunitFailCasePattern(filename string, methodName string) string {
	matchSol := filename + "." + methodName
	expr := "Failed : " + matchSol + "([\\s\\S]*?)" + matchSol
	return expr
}

func getNunitErrorCasePattern(filename string, methodName string) string {
	matchSol := filename + "." + methodName
	expr := "Error : " + matchSol + "([\\s\\S]*?)" + matchSol
	return expr
}

func (r Nunit) AnalysisResult(path string, result string, runId string) TestCaseResult {
	testResult := TestCaseResult{}

	testResult.Result = result

	if result == "run_start" {
		testResult.RunStatus = RUNNING
		return testResult
	}

	runStatus := getContent(getNunitTestSuccessPattern(), result)
	// * 编译错误，直接返回
	if len(runStatus) == 0 {
		testResult.RunStatus = FAIL
		return testResult
	}

	newResult, _ := strconv.Unquote(strings.Replace(strconv.QuoteToASCII(runStatus), `\\u`, `\u`, -1))
	newResult = strings.Replace(newResult, `\r\n`, ``, -1)
	failStr := getContent(getNunitTestFailStrPattern(), newResult)
	failNum := strings.Split(failStr, " ")[1]
	re := regexp.MustCompile(`\x1b\[[0-9;]*m`)
	cleanNum := re.ReplaceAllString(failNum, "")
	num, err := strconv.Atoi(cleanNum)
	if err != nil {
		log.Errorf("strconv.Atoi(failNum) error: %v", err)
		return testResult
	}

	if num == 0 {
		testResult.RunStatus = PASS
	} else {
		testResult.RunStatus = FAIL
	}

	var testCaseResultItemS []TestCaseResultItem
	testcases := r.AnalyzerTestFile(path)
	filename := strings.ReplaceAll(path, ".cs", "")

	result, _ = strconv.Unquote(strings.Replace(strconv.QuoteToASCII(result), `\\u`, `\u`, -1))

	for _, testcase := range testcases {

		var res TestCaseResultItem
		res.Name = testcase.Name
		res.Input = testcase.Input
		res.ExpectedOutput = testcase.ExpectedOutput

		// todo: 如果文件在另一个文件夹下，需要修改
		failStr := getContent(getNunitFailCasePattern(filename, testcase.MethodName), result)
		errStr := getContent(getNunitErrorCasePattern(filename, testcase.MethodName), result)
		var str string
		if len(failStr) > 0 {
			str = failStr
		} else if len(errStr) > 0 {
			str = errStr
		}
		if len(str) == 0 {
			res.Success = true
		} else {
			res.Success = false
			// res.Reason = str
			res.Details = str
		}
		testCaseResultItemS = append(testCaseResultItemS, res)
	}

	testResult.TestCaseResultItems = testCaseResultItemS
	return testResult
}

func (r Nunit) AnalysisResult1(path string, result string, runId string) TestCaseResult {

	// fileContent, _ := fileUtils.Read(path)

	resultStrings := strings.Split(result, getSplitResultPattern(runId))

	testCaseResult := TestCaseResult{}

	var testCaseResultItemS []TestCaseResultItem

	if result == "run_start" {
		testCaseResult.RunStatus = RUNNING
	} else {
		result_path := PaasUnitConstant + runId
		resultStr, _ := fileUtils.Read(result_path)
		testCaseResult.Result = result

		if len(resultStrings) == 1 {
			testCaseResult.RunStatus = FAIL
			return testCaseResult
		}

		resultJson := NunitResult{}
		err := xml.Unmarshal([]byte(resultStr), &resultJson)
		if err != nil {
			testCaseResult.RunStatus = FAIL
			return testCaseResult
		}

		testCaseResult.Time = resultJson.Duration
		if resultJson.Failed == 0 {
			testCaseResult.RunStatus = PASS
		} else {
			testCaseResult.RunStatus = FAIL
		}

		testCases := r.AnalyzerTestFile(path)

		for _, testResult := range resultJson.TestSuites[0].TestCases {
			var testCaseResultItem TestCaseResultItem
			for _, testCase := range testCases {

				if testResult.MethodName == testCase.MethodName {
					testCaseResultItem.Name = testResult.Name
					testCaseResultItem.Input = testCase.Input
					testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
					if testResult.Result == "Passed" {
						testCaseResultItem.Success = true
					} else {
						testCaseResultItem.Success = false
						testCaseResultItem.Reason = testResult.CsharpFailure.Message
						testCaseResultItem.Details = testResult.CsharpFailure.Stack
					}
				}
			}

			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}

		testCaseResult.TestCaseResultItems = testCaseResultItemS

	}

	return testCaseResult

}

func (r *Nunit) AnalyzerTestFile(path string) []TestCaseFun {

	if isDir(path) {
		return nil
	}

	fileContent, _ := fileUtils.Read(path)

	fileContent = deleteNotes(fileContent, NunitNotes)

	var matches = regexp2FindAllString(NunitMethodPattern, fileContent)
	var testCaseFunS []TestCaseFun

	for _, match := range matches {
		var testCaseFun TestCaseFun
		matchItems := strings.Split(getContent(NunitMethodNamePattern, strings.TrimSpace(match)), " ")
		// todo: make the function more readable
		if len(matchItems) > 1 {
			testCaseFun.MethodName = matchItems[len(matchItems)-1]
			// * 去掉MethodName后面的括号
			testCaseFun.MethodName = testCaseFun.MethodName[:len(testCaseFun.MethodName)-2]
		}
		testCaseFun = setItemByFun(testCaseFun, strings.TrimSpace(match))
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}

	return testCaseFunS
}

// mcs -target:library ShowMeBug.cs&&mcs -target:library ShowMeBugAnswer.cs&&mcs -target:library -r:$HOME/unit/csharp/NUnit.3.13.3/lib/net45/nunit.framework.dll -r:ShowMeBug.dll -r:ShowMeBugAnswer.dll SampleTest.cs && mono $HOME/unit/csharp/NUnit.ConsoleRunner.3.15.0/tools/nunit3-console.exe SampleTest.dll --result=.paas-unit-5b6e3b7093a44fc0a24bf7e965c83e7d
func (r *Nunit) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {

	var stringBuffer strings.Builder
	var csFileName strings.Builder

	filePath := path[0 : strings.LastIndex(path, "/")+1]
	if len(filePath) == 0 {
		filePath = HomePath
	}
	filename := filepath.Base(path)

	filenameWithoutSuffix := strings.ReplaceAll(filename, ".cs", "")

	fileList := getFileName(filePath)

	for _, filename := range fileList {
		stringBuffer.WriteString("mcs -target:library ")
		stringBuffer.WriteString(filename)
		stringBuffer.WriteString("&&")
		csFileName.WriteString(" -r:")
		csFileName.WriteString(strings.ReplaceAll(filename, ".cs", ".dll"))
	}

	stringBuffer.WriteString("mcs -target:library -r:$HOME/unit/csharp/NUnit.3.13.3/lib/net45/nunit.framework.dll ")
	stringBuffer.WriteString(csFileName.String() + " ")
	stringBuffer.WriteString(filename)
	stringBuffer.WriteString(" && ")
	stringBuffer.WriteString("mono $HOME/unit/csharp/NUnit.ConsoleRunner.3.15.0/tools/nunit3-console.exe ")
	// stringBuffer.WriteString(strings.Split(filename, ".")[0] + ".dll")
	stringBuffer.WriteString(filenameWithoutSuffix + ".dll --result=.paas-unit-" + runId)

	return stringBuffer.String(), config.DebugConfig{}
}

func getFileName(path string) []string {
	var fileNameList []string

	// * 读取路径下所有以.cs结尾的文件
	filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		// * 文件名中带有 Assembly 是编译后的中间产物，不需要编译
		if strings.HasSuffix(info.Name(), ".cs") && !strings.Contains(info.Name(), "Assembly") {
			if !checkTest(info.Name()) {
				fileNameList = append(fileNameList, info.Name())
			}
		}
		return nil
	})

	return fileNameList
}

func checkTest(filename string) bool {
	fileContent, _ := fileUtils.Read(filename)
	return strings.Contains(fileContent, "[Test]")
}

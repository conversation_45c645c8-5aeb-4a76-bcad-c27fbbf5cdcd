package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"encoding/json"
	"path/filepath"
	"regexp"
	"strings"
)

type Ginkgo struct{}

var FileAppPath = "/home/<USER>/app"

var GinkgoNotes = getGinkgoNotes()

// 解释整个测试文件结果的结构体
type GinkgoResultJson struct {
	StartTime      string                   `json:"StartTime"`
	EndTime        string                   `json:"SndTime"`
	RunTime        int64                    `json:"RunTime"`
	SpecReports    []GinkgoAssertionResults `json:"SpecReports"`
	SuiteSucceeded bool                     `json:"SuiteSucceeded"`
}

type GinkgoAssertionResults struct {
	ContainerHierarchyTexts []string `json:"ContainerHierarchyTexts"`
	AncestorTitles          []string `json:"AncestorTitles"`
	FailureMessages         []string `json:"FailureMessages"`
	LeafNodeText            string   `json:"LeafNodeText"`
	State                   string   `json:"State"`
	Failure                 Failure  `json:"Failure"`
}

type Failure struct {
	Message  string `json:"message"`
	Location struct {
		FileName       string `json:"fileName"`
		LineNumber     int    `json:"lineNumber"`
		FullStackTrace string `json:"fullStackTrace"`
	} `json:"location"`
}

func getGinkgoNotes() []string {
	ginkgoNotes := []string{
		// TODO: match the golang comment
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return ginkgoNotes
}

func getDescribeName(match string) string {
	// fmt.Println("match is: ", strings.TrimSpace(match))
	re := regexp.MustCompile("Describe\\(\"(.*?)\"")
	m := re.FindAllStringSubmatch(match, -1)

	if len(m) == 0 {
		return ""
	}
	if len(m[0]) < 2 {
		return ""
	}
	return m[0][1]
}

func getGinkgoTimePattern() string {
	return "(Ginkgo ran .*?)\\n"
}

func (g Ginkgo) AnalysisResult(path string, result string, runId string) TestCaseResult {

	testCaseResult := TestCaseResult{}
	var testCaseResultItemS []TestCaseResultItem

	testCases := g.AnalyzerTestFile(path)

	testCaseResult.Result = result

	// * 运行停止的情况
	failResultStrArray := strings.Split(result, PaasUnitConstant+path+"_runUnitTestResult")
	if len(failResultStrArray) < 2 {
		testCaseResult.RunStatus = FAIL
		for _, testcase := range testCases {
			testCaseResultItem := TestCaseResultItem{}
			testCaseResultItem.Name = testcase.Name
			testCaseResultItem.Input = testcase.Input
			testCaseResultItem.ExpectedOutput = testcase.ExpectedOutput
			testCaseResultItem.Success = false

			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
		testCaseResult.TestCaseResultItems = testCaseResultItemS
		return testCaseResult
	}
	time := getContent(getGinkgoTimePattern(), result)
	exact_time_array := strings.Split(time, " ")
	exact_time := exact_time_array[len(exact_time_array)-1]
	testCaseResult.Time = exact_time

	// * 编译出错的情况，直接返回
	if strings.Contains(result, "Failed to compile app") {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	if strings.Contains(result, "Test Suite Failed") {
		testCaseResult.RunStatus = FAIL
	} else {
		testCaseResult.RunStatus = PASS
		// * 如果没有失败的用例，直接返回
		for _, testcase := range testCases {
			testCaseResultItem := TestCaseResultItem{}
			testCaseResultItem.Name = testcase.Name
			testCaseResultItem.Input = testcase.Input
			testCaseResultItem.ExpectedOutput = testcase.ExpectedOutput
			testCaseResultItem.Success = true

			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
		}
		testCaseResult.TestCaseResultItems = testCaseResultItemS
		return testCaseResult
	}

	// *开始解析失败的用例
	failResultStr := failResultStrArray[1]
	// * format the json string
	failResultStr = strings.Replace(failResultStr, "\\u003c", "", -1)
	failResultStr = strings.Replace(failResultStr, "\\u003e", "", -1)
	failResultStr = strings.Replace(failResultStr, "\\n", "", -1)
	failResultStr = strings.Replace(failResultStr, "\\", "", -1)
	failResultStr = strings.TrimSpace(failResultStr)
	failResultStr = strings.Trim(failResultStr, "[")
	failResultStr = strings.Trim(failResultStr, "]")
	resultJson := GinkgoResultJson{}
	err := json.Unmarshal([]byte(failResultStr), &resultJson)
	if err != nil {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	for _, testcase := range testCases {
		testCaseResultItem := TestCaseResultItem{}
		testCaseResultItem.Name = testcase.Name
		testCaseResultItem.Input = testcase.Input
		testCaseResultItem.ExpectedOutput = testcase.ExpectedOutput

		for _, item := range resultJson.SpecReports {
			if item.State == "skipped" {
				continue
			}
			if item.ContainerHierarchyTexts[0] == testcase.DisplayName && item.LeafNodeText == testcase.MethodName {
				if item.State == "failed" || item.State == "panicked" {

					testCaseResultItem.Success = false
					testCaseResultItem.Reason = item.Failure.Message
					testCaseResultItem.Details = item.Failure.Location.FullStackTrace
				} else {

					testCaseResultItem.Success = true
				}

			}
		}
		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}

	testCaseResult.TestCaseResultItems = testCaseResultItemS
	return testCaseResult
}

func (g Ginkgo) AnalyzerTestFile(path string) []TestCaseFun {

	if isDir(path) {
		return nil
	}

	fileContent, _ := fileUtils.Read(path)
	fileContent = deleteNotes(fileContent, GinkgoNotes)

	var matches = regexp2FindAllString(GinkgoMethodPattern, fileContent)

	var testCaseFunS []TestCaseFun
	for _, match := range matches {
		testCaseFun := TestCaseFun{}
		describeName := getDescribeName(strings.TrimSpace(match))
		methodName := getContent(GinkoMethodNamePattern, strings.TrimSpace(match))

		testCaseFun.DisplayName = describeName
		testCaseFun.MethodName = methodName
		testCaseFun = setItemByFun(testCaseFun, match)
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}

	return testCaseFunS
}

// ginkgo --keep-going --json-report=.paas-unit-dd93fb4b51b14e1489cdbfecc48817fd
// --output-dir=/home/<USER>/app --focus-file=sample_test.go
func (g Ginkgo) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {

	var stringBuffer strings.Builder
	var fileDirPath string
	if strings.Contains(path, "/") {
		fileDirPath = path[0:strings.LastIndex(path, "/")]
	}
	if len(fileDirPath) == 0 {
		fileDirPath = "."
	}
	fileName := filepath.Base(path)

	// 如果测试文件在另一个目录下，需要先进入到该目录下
	var cdCmd = ""

	if fileDirPath != "" {
		cdCmd = "cd " + fileDirPath + ";"
	}

	stringBuffer.WriteString(cdCmd)
	stringBuffer.WriteString("ginkgo --keep-going --json-report=" + PaasUnitConstant + runId)
	stringBuffer.WriteString(" --output-dir=" + FileAppPath + " --focus-file=" + fileName)

	return stringBuffer.String(), config.DebugConfig{}
}

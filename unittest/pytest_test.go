package unittest

import (
	"fmt"
	"os"
	"testing"
)

func TestPytestAnalyzerTestFile(t *testing.T) {
	pytest := Pytest{}
	path := "./test_sum.py"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	testCases := pytest.AnalyzerTestFile(path)
	fmt.Println("testCases:", testCases)
}

func TestGetRun(t *testing.T) {
	tests := []struct {
		name       string
		path       string
		methodName string
		runID      string
		want       string
	}{
		{
			name:       "Test case 1: No method name provided",
			path:       "/path/to/test_file.py",
			methodName: "",
			runID:      "123",
			want:       "pytest -vr A /path/totest_file.py",
		},
		{
			name:       "Test case 2: Method name provided",
			path:       "/path/to/test_file.py",
			methodName: "test_method",
			runID:      "123",
			want:       "pytest -vr A /path/totest_file.py::test_method",
		},
		{
			name:       "Test case 3: Different path and method",
			path:       "/another/path/sample_test.py",
			methodName: "sample_test_method",
			runID:      "456",
			want:       "pytest -vr A /another/pathsample_test.py::sample_test_method",
		},
	}

	p := &Pytest{}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, _ := p.GetRun(test.path, test.methodName, test.runID)
			if got != test.want {
				t.Errorf("GetRun() = %q, want %q", got, test.want)
			}
		})
	}
}

func TestPytest_AnalysisResult_NormalCase(t *testing.T) {
	return // TODO:先跳过
	pytest := Pytest{}
	path := "templates/python_tests.md"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := `\u001b[1m============== test session starts ==============\u001b[0m\r\nplatform linux -- Python 3.9.13, pytest-6.2.5, py-1.10.0, pluggy-1.0.0 -- /nix/store/v0vm93xrv2hf5wi175wskcgzbicfvbss-python3-3.9.13/bin/python3.9\r\ncachedir: .pytest_cache\r\nrootdir: /home/<USER>/app\r\n\u001b[1mcollecting ... \u001b[0m\u001b[1m\rcollected 3 items                               \u001b[0m\r\n\r\nsample_test.py::test_solution_1 \u001b[31mFAILED\u001b[0m\u001b[31m    [ 33%]\u001b[0m\r\nsample_test.py::test_solution_2 \u001b[31mFAILED\u001b[0m\u001b[31m    [ 66%]\u001b[0m\r\nsample_test.py::test_solution_3 \u001b[31mFAILED\u001b[0m\u001b[31m    [100%]\u001b[0m\r\n\r\n=================== FAILURES ====================\r\n\u001b[31m\u001b[1m________________ test_solution_1 ________________\u001b[0m\r\n\r\n    \u001b[94mdef\u001b[39;49;00m \u001b[92mtest_solution_1\u001b[39;49;00m():\r\n        expected = \u001b[94m2\u001b[39;49;00m\r\n        arg1 = \u001b[94m1\u001b[39;49;00m\r\n        arg2 = \u001b[94m1\u001b[39;49;00m\r\n\u003e       \u001b[94massert\u001b[39;49;00m solution(arg1, arg2) == expected\r\n\u001b[1m\u001b[31mE       assert None == 2\u001b[0m\r\n\u001b[1m\u001b[31mE         +None\u001b[0m\r\n\u001b[1m\u001b[31mE         -2\u001b[0m\r\n\r\n\u001b[1m\u001b[31msample_test.py\u001b[0m:16: AssertionError\r\n\u001b[31m\u001b[1m________________ test_solution_2 ________________\u001b[0m\r\n\r\n    \u001b[94mdef\u001b[39;49;00m \u001b[92mtest_solution_2\u001b[39;49;00m():\r\n        expected = \u001b[94m3\u001b[39;49;00m\r\n        arg1 = \u001b[94m1\u001b[39;49;00m\r\n        arg2 = \u001b[94m2\u001b[39;49;00m\r\n\u003e       \u001b[94massert\u001b[39;49;00m solution(arg1, arg2) == expected\r\n\u001b[1m\u001b[31mE       assert None == 3\u001b[0m\r\n\u001b[1m\u001b[31mE         +None\u001b[0m\r\n\u001b[1m\u001b[31mE         -3\u001b[0m\r\n\r\n\u001b[1m\u001b[31msample_test.py\u001b[0m:30: AssertionError\r\n\u001b[31m\u001b[1m________________ test_solution_3 ________________\u001b[0m\r\n\r\n    \u001b[94mdef\u001b[39;49;00m \u001b[92mtest_solution_3\u001b[39;49;00m():\r\n        expected = \u001b[94m4\u001b[39;49;00m\r\n        arg1 = \u001b[94m1\u001b[39;49;00m\r\n        arg2 = \u001b[94m3\u001b[39;49;00m\r\n\u003e       \u001b[94massert\u001b[39;49;00m solution(arg1, arg2) == expected\r\n\u001b[1m\u001b[31mE       assert None == 4\u001b[0m\r\n\u001b[1m\u001b[31mE         +None\u001b[0m\r\n\u001b[1m\u001b[31mE         -4\u001b[0m\r\n\r\n\u001b[1m\u001b[31msample_test.py\u001b[0m:44: AssertionError\r\n============ short test summary info ============\r\nFAILED sample_test.py::test_solution_1 - asser...\r\nFAILED sample_test.py::test_solution_2 - asser...\r\nFAILED sample_test.py::test_solution_3 - asser...\r\n\u001b[31m=============== \u001b[31m\u001b[1m3 failed\u001b[0m\u001b[31m in 0.10s\u001b[0m\u001b[31m ===============\u001b[0m\r\n`
	runID := "123"
	testCaseResult := pytest.AnalysisResult(path, result, runID)

	// if testCaseResult.Result != result {
	// 	t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.Result, result)
	// }

	if testCaseResult.Time != "0.01s" {
		t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.Time, "0.01s")
	}

	if testCaseResult.RunStatus != PASS {
		t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.RunStatus, PASS)
	}

	if len(testCaseResult.TestCaseResultItems) != 2 {
		t.Errorf("AnalysisResult() = %v, want %v", len(testCaseResult.TestCaseResultItems), 2)
	}
}

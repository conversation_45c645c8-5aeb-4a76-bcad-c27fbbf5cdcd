package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"agent/utils/log"
	"path/filepath"
	"regexp"
	"strings"
)

type Swift struct{}

func getSwiftNotes() []string {
	swiftNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return swiftNotes
}

func getSwiftPassOrFail(methodName string) string {
	return "Test Case '" + methodName + "' (passed|failed)"
}

func getSwiftReasonPattern(methodName string) string {
	return "(?<=" + methodName + " : XCTAssertEqual failed: ).*(?= -)"
}

func getSwiftDetailsPattern(methodName string) string {
	return ".*error: " + methodName + " : XCTAssertEqual failed: .*"
}

func getSwiftMethodNamePattern(match string) string {
	pattern := "func\\s+(\\w+)"
	r, _ := regexp.Compile(pattern)

	result := r.FindStringSubmatch(match)

	if len(result) > 1 {
		// The second item in the match slice is the content of the first capture group.
		return result[1]
	}

	return ""
}

func (r Swift) AnalysisResult(path string, result string, runId string) TestCaseResult {

	testCaseResult := TestCaseResult{}

	if result == "" {
		testCaseResult.RunStatus = RUNNING
		return testCaseResult
	}

	log.Println("swift result: ", result)
	testCaseResult.Result = result

	// * 编译出错，直接返回
	if strings.Contains(result, "error: fatalError") || strings.HasSuffix(result, "error: fatalError") {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	testCaseResult.RunStatus = PASS

	testCases := r.AnalyzerTestFile(path)

	var caseResults []TestCaseResultItem

	for _, testcase := range testCases {
		res := TestCaseResultItem{}
		res.Name = testcase.Name
		res.Input = testcase.Input
		res.ExpectedOutput = testcase.ExpectedOutput

		swiftPassOrFail := getContent(getSwiftPassOrFail(testcase.MethodName), result)
		if len(swiftPassOrFail) > 0 {
			if strings.Contains(swiftPassOrFail, "passed") {
				res.Success = true
			} else {
				testCaseResult.RunStatus = FAIL
				res.Success = false
				res.Reason = getContent(getSwiftReasonPattern(testcase.MethodName), result)
				res.Details = getContent(getSwiftDetailsPattern(testcase.MethodName), result)
			}
		}
		caseResults = append(caseResults, res)
	}
	testCaseResult.TestCaseResultItems = caseResults

	return testCaseResult
}

func (r Swift) AnalyzerTestFile(path string) []TestCaseFun {

	if isDir(path) {
		return nil
	}

	fileContent, _ := fileUtils.Read(path)

	fileContent = deleteNotes(fileContent, getSwiftNotes())

	matches := regexp2FindAllString(SwiftTestcasePattern, fileContent)

	className := getContent(SwiftClassPattern, fileContent)

	var testCaseFunS []TestCaseFun

	for _, match := range matches {
		testCaseFun := TestCaseFun{}

		testCaseFun.MethodName = className + "." + getContent(SwiftMethodNamePattern, strings.TrimSpace(match))
		// testCaseFun.MethodName = className + "." + getSwiftMethodNamePattern(strings.TrimSpace(match))
		testCaseFun = setItemByFun(testCaseFun, match)

		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}
	return testCaseFunS
}

func (r Swift) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	filename := filepath.Base(path)
	filenameNoSuffix := filename[0 : len(filename)-len(filepath.Ext(filename))]

	var stringBuild strings.Builder
	stringBuild.WriteString("swift test --filter " + filenameNoSuffix)
	return stringBuild.String(), config.DebugConfig{}
}

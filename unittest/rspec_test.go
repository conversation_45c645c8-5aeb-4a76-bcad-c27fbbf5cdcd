package unittest

import (
	"os"
	"testing"
)

func TestRspec(t *testing.T) {
	return // TODO:先跳过
	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/rspec.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/rspec.md not found, skipping test")
		return
	}

	rspec := Rspec{}
	result := rspec.AnalyzerTestFile("templates/rspec.md")
	if len(result) != 2 {
		t.<PERSON>rrorf("AnalyzerTestFile() = %v, want %v", len(result), 1)
	}
}

func TestRspec_AnalysisResult_NormalCase(t *testing.T) {
	// Skip if test file doesn't exist
	if _, err := os.Stat("./test_spec.rb"); os.IsNotExist(err) {
		t.Skip("Test file ./test_spec.rb not found, skipping test")
		return
	}

	rspec := Rspec{}
	path := "./test_spec.rb"
	result := ""
	runID := "123"
	testCaseResult := rspec.AnalysisResult(path, result, runID)

	// if testCaseResult.Result != result {
	// 	t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.Result, result)
	// }

	// if testCaseResult.Time != "0.00068 seconds" {
	// 	t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.Time, "0.00068 seconds")
	// }

	if testCaseResult.RunStatus != PASS {
		t.Errorf("AnalysisResult() = %v, want %v", testCaseResult.RunStatus, PASS)
	}

	if len(testCaseResult.TestCaseResultItems) != 0 {
		t.Errorf("AnalysisResult() = %v, want %v", len(testCaseResult.TestCaseResultItems), 0)
	}
}

func TestRubyOnRails(t *testing.T) {
	// Skip if test file doesn't exist
	if _, err := os.Stat("templates/ruby_on_rails.md"); os.IsNotExist(err) {
		t.Skip("Test file templates/ruby_on_rails.md not found, skipping test")
		return
	}

	rspec := Rspec{}
	result := rspec.AnalyzerTestFile("templates/ruby_on_rails.md")
	if len(result) != 2 {
		t.Errorf("AnalyzerTestFile() = %v, want %v", len(result), 1)
	}
}

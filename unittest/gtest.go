package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"regexp"
	"strings"
)

type Gtest struct{}

var GtestNotes = getGtestNotes()

type GtestResultJson struct {
	GtestTestResults []GtestTestResults `json:"gtestTestResults"`
	Success          bool               `json:"success"`
}

type GtestTestResults struct {
	Message          string                  `json:"message"`
	Name             string                  `json:"name"`
	StartTime        int64                   `json:"startTime"`
	EndTime          int64                   `json:"endTime"`
	AssertionResults []GtestAssertionResults `json:"assertionResults"`
}

type GtestAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getGtestNotes() []string {
	gtestNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return gtestNotes
}

func getGtestUitResultPattern(method string) string {
	return method + "[\\s|\\S]*?\\(\\d+ ms\\)"
}

func getTestcaseNamePattern(match string) string {
	pattern := `TEST\([^,]*,\s*(.*?)\s*\)`
	r, _ := regexp.Compile(pattern)

	result := r.FindStringSubmatch(match)

	if len(result) > 1 {
		// The second item in the match slice is the content of the first capture group.
		return result[1]
	}

	return ""
}

func (u Gtest) AnalysisResult(path string, result string, runId string) TestCaseResult {

	var testCaseResult TestCaseResult
	testCases := u.AnalyzerTestFile(path)

	testCaseResult.Result = result
	testCaseResult.Time = getContent(GtestTimePattern, result) + "ms"

	// * 出现 “编译出错” 和 “异常” 时，单元测试运行结果中不包含形如：2 tests from 2 test suites ran. (0 ms total)
	// * 包括有测试用例超时的情况

	// * 正常解析单元测试结果
	testCaseResult.RunStatus = PASS

	var testCaseResultItemS []TestCaseResultItem

	for _, testCase := range testCases {

		var testCaseResultItem TestCaseResultItem
		testCaseResultItem.Name = testCase.Name
		testCaseResultItem.Input = testCase.Input
		testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput

		reg := getGtestUitResultPattern(testCase.MethodName)

		str := getContent(reg, result)
		if len(str) == 0 {
			// testCaseResult.RunStatus = FAIL
			testCaseResultItem.Success = false
			testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
			continue
		}

		success := getContent(GtestMethodFailPattern, str)
		if len(success) == 0 {
			testCaseResultItem.Success = true
		} else {
			testCaseResultItem.Success = false
			testCaseResultItem.Details = getContent(GtestMethodFailDetailsPattern, str)
			testCaseResultItem.Reason = getContent(GtestMethodFailReasonPattern, str)
			whichs := regexp2FindAllString(`Which is: .*`, str)
			if len(whichs) > 1 {
				testCaseResultItem.ActualOutput = whichs[1]
			}
			testCaseResult.RunStatus = FAIL
		}

		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}

	if len(getContent(GtestExecuteSuccessPattern, result)) == 0 {
		testCaseResult.RunStatus = FAIL
	}

	testCaseResult.TestCaseResultItems = testCaseResultItemS

	return testCaseResult
}

func (u *Gtest) AnalyzerTestFile(path string) []TestCaseFun {

	if isDir(path) {
		return nil
	}

	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	fileContent = deleteNotes(fileContent, GtestNotes)

	var matches = regexp2FindAllString(GtestMethodPattern, fileContent)

	var testCaseFunS []TestCaseFun
	for _, match := range matches {
		testCaseFun := TestCaseFun{}
		testSuiteName := getContent(GtestSuiteNamePattern, strings.TrimSpace(match))
		testName := getTestcaseNamePattern(strings.TrimSpace(match))
		testCaseFun.MethodName = testSuiteName + "." + strings.TrimSpace(testName)
		testCaseFun.Name = testName
		testCaseFun = setItemByFun(testCaseFun, match)
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}
	return testCaseFunS
}

// * new: cd ../unit/gtest/ && cmake . -DTEST_FILE=/home/<USER>/app/sample_test.cpp && make && ./runUnitTests
// * origin: cd ../unit/gtest/ && cmake . -DTEST_FILE=/home/<USER>/app//sample_test.cpp && make && ./runUnitTests
func (u *Gtest) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {

	var stringBuilder strings.Builder

	// filePath := path[0 : strings.LastIndex(path, "/")+1]
	stringBuilder.WriteString("cd ../unit/gtest/ && cmake . -DTEST_FILE=")
	stringBuilder.WriteString(HomePath)
	stringBuilder.WriteString(path)
	stringBuilder.WriteString(" && make && ./runUnitTests")

	var debugConfig config.DebugConfig
	debugConfig.Support = true
	debugConfig.Compile = "cd ../unit/gtest/ && cmake . -DTEST_FILE=" + HomePath + path + " && make"
	debugConfig.Launch = map[string]interface{}{}
	debugConfig.Launch["program"] = "../unit/gtest/runUnitTests"
	debugConfig.Launch["cwd"] = "."
	debugConfig.Launch["MIMode"] = "gdb"
	debugConfig.Launch["miDebuggerPath"] = "gdb"
	debugConfig.Launch["externalConsole"] = true
	debugConfig.Launch["setupCommands"] = []map[string]interface{}{
		{"text": "-enable-pretty-printing", "ignoreFailures": true},
	}
	debugConfig.Launch["log"] = map[string]interface{}{}
	debugConfig.Launch["miDebuggerArgs"] = "-q -ex quit; wait() { fg >/dev/null; }; gdb -q --interpreter=mi"

	return stringBuilder.String(), debugConfig
}

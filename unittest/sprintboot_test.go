package unittest

import (
	"os"
	"testing"
)

func TestSprintBoot_AnalysisResult(t *testing.T) {
	sb := SpringBoot{}
	path := "templates/sprintboot_test.go"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := `str = [[1;34mINFO[m] Scanning for projects...
	[[1;34mINFO[m]
	[[1;34mINFO[m] [1m-------------------------< [0;36mcom.showmebug:app[0;1m >--------------------------[m
	[[1;34mINFO[m] [1mBuilding app 0.0.1-SNAPSHOT[m
	[[1;34mINFO[m] [1m--------------------------------[ jar ]---------------------------------[m
	[[1;34mINFO[m]
	[[1;34mINFO[m] [1m--- [0;32mmaven-resources-plugin:3.2.0:resources[m [1m(default-resources)[m @ [36mapp[0;1m ---[m
	[[1;34mINFO[m] Using 'UTF-8' encoding to copy filtered resources.
	[[1;34mINFO[m] Using 'UTF-8' encoding to copy filtered properties files.
	[[1;34mINFO[m] Copying 1 resource
	[[1;34mINFO[m] Copying 5 resources
	[[1;34mINFO[m]
	[[1;34mINFO[m] [1m--- [0;32mmaven-compiler-plugin:3.8.1:compile[m [1m(default-compile)[m @ [36mapp[0;1m ---[m
	[[1;34mINFO[m] Nothing to compile - all classes are up to date
	[[1;34mINFO[m]
	[[1;34mINFO[m] [1m--- [0;32mmaven-resources-plugin:3.2.0:testResources[m [1m(default-testResources)[m @ [36mapp[0;1m ---[m
	[[1;34mINFO[m] Using 'UTF-8' encoding to copy filtered resources.
	[[1;34mINFO[m] Using 'UTF-8' encoding to copy filtered properties files.
	[[1;34mINFO[m] skip non existing resourceDirectory /home/<USER>/app/src/test/resources
	[[1;34mINFO[m]
	[[1;34mINFO[m] [1m--- [0;32mmaven-compiler-plugin:3.8.1:testCompile[m [1m(default-testCompile)[m @ [36mapp[0;1m ---[m
	[[1;34mINFO[m] Changes detected - recompiling the module!
	[[1;34mINFO[m] Compiling 2 source files to /home/<USER>/app/target/test-classes
	[[1;34mINFO[m] -------------------------------------------------------------
	[[1;31mERROR[m] COMPILATION ERROR :
	[[1;34mINFO[m] -------------------------------------------------------------
	[[1;31mERROR[m] /home/<USER>/app/src/test/java/com/showmebug/app/SampleTest.java:[22,38] ')' expected
	[[1;34mINFO[m] 1 error
	[[1;34mINFO[m] -------------------------------------------------------------
	[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
	[[1;34mINFO[m] [1;31mBUILD FAILURE[m
	[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
	[[1;34mINFO[m] Total time:  1.138 s
	[[1;34mINFO[m] Finished at: 2023-06-02T19:32:23+08:00
	[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
	[[1;31mERROR[m] Failed to execute goal [32morg.apache.maven.plugins:maven-compiler-plugin:3.8.1:testCompile[m [1m(default-testCompile)[m on project [36mapp[m: [1;31mCompilation failure[m
	[[1;31mERROR[m] [1;31m/home/<USER>/app/src/test/java/com/showmebug/app/SampleTest.java:[22,38] ')' expected[m
	[[1;31mERROR[m] [1;31m[m
	[[1;31mERROR[m] -> [1m[Help 1][m
	[[1;31mERROR[m]
	[[1;31mERROR[m] To see the full stack trace of the errors, re-run Maven with the [1m-e[m switch.
	[[1;31mERROR[m] Re-run Maven using the [1m-X[m switch to enable full debug log.
	[[1;31mERROR[m]
	[[1;31mERROR[m] For more information about the errors and possible solutions, please read the following articles:
	[[1;31mERROR[m] [1m[Help 1][m http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
	[0m[0m`
	runId := "12345"
	expectedResult := TestCaseResult{
		Result:              "",
		Time:                "",
		RunStatus:           PASS,
		TestCaseResultItems: []TestCaseResultItem{},
	}
	actualResult := sb.AnalysisResult(path, result, runId)
	// if actualResult.Result != expectedResult.Result {
	// 	t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	// }
	if actualResult.Time != expectedResult.Time {
		t.Errorf("AnalysisResult() returned incorrect time: got %v, want %v", actualResult.Time, expectedResult.Time)
	}
	if actualResult.RunStatus != expectedResult.RunStatus {
		t.Errorf("AnalysisResult() returned incorrect run status: got %v, want %v", actualResult.RunStatus, expectedResult.RunStatus)
	}
	if len(actualResult.TestCaseResultItems) != len(expectedResult.TestCaseResultItems) {
		t.Errorf("AnalysisResult() returned incorrect number of test case result items: got %v, want %v", len(actualResult.TestCaseResultItems), len(expectedResult.TestCaseResultItems))
	}
}

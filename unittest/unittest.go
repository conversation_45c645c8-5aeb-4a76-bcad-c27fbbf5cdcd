package unittest

import (
	"agent/config"
	"agent/consts"
	"agent/utils/envUtils"
	"agent/utils/fileUtils"
	"agent/utils/log"
	"encoding/json"
	"io/ioutil"
	"os"
	"regexp"
	"strings"

	"github.com/dlclark/regexp2"
	"gopkg.in/yaml.v3"
)

type TestCaseResult struct {
	Time                string `yaml:"time"`
	Result              string `yaml:"result"`
	TestCaseResultItems []TestCaseResultItem
	RunStatus           string `yaml:"runStatus"`
}

type TestCaseResultItem struct {
	Name           string `yaml:"name"`
	Success        bool
	Input          string `yaml:"input"`
	ExpectedOutput string `yaml:"expectedOutput"`
	ActualOutput   string `yaml:"actualOutput"`
	Reason         string `yaml:"reason"`
	Details        string `yaml:"details"`
}

type TestCaseFun struct {
	Name           string `json:"name" swagger:"description=用例名"`
	MethodName     string `json:"methodName" swagger:"description=方法名"`
	DisplayName    string `json:"displayName" swagger:"description=displayName"`
	Input          string `json:"input" swagger:"description=输入"`
	ExpectedOutput string `json:"expectedOutput" swagger:"description=期望输出"`
}

type TestCaseFile struct {
	Path            string        `json:"path"`
	TestCaseFunList []TestCaseFun `json:"testCaseFunList"`
}

const (
	RUNNING = "RUNNING"
	PASS    = "PASS"
	FAIL    = "FAIL"
	UNKNOWN = "UNKNOWN"
)

type Factory interface {
	AnalysisResult(path string, result string, runId string) TestCaseResult
	AnalyzerTestFile(path string) []TestCaseFun
	GetRun(path string, methodName string, runId string) (string, config.DebugConfig)
}

const (
	JEST             = "Jest"
	PYTEST           = "pytest"
	UNITTEST         = "UNITTEST"
	JUNIT            = "JUnit"
	CUNIT            = "CUnit"
	UNITY            = "UNITY"
	RSPEC            = "RSpec"
	LUA_UNIT         = "LuaUnit"
	GO_TESTING       = "GoTesting"
	PHPUNIT          = "PHPUnit"
	GTEST            = "GoogleTest"
	SPRING_BOOT_TEST = "SpringBootTest"
	TEST_BENCH       = "TestBench"
	NUNIT            = "Nunit"
	GINKGO           = "Ginkgo"
	SWIFT            = "Swift"
	VITEST           = "Vitest"
	DART             = "DartTest"
)

func getFrameFactory(frameCode string) Factory {
	switch frameCode {
	case JEST:
		return &Jest{}
	case PYTEST:
		return &Pytest{}
	case UNITTEST:
	case JUNIT:
		return &Junit{}
	case CUNIT:
	case UNITY:
		return &Unity{}
	case RSPEC:
		return &Rspec{}
	case LUA_UNIT:
		return &LuaUnit{}
	case GO_TESTING:
	case PHPUNIT:
		return &Phpunit{}
	case GTEST:
		return &Gtest{}
	case SPRING_BOOT_TEST:
		return &SpringBoot{}
	case TEST_BENCH:
	case NUNIT:
		return &Nunit{}
	case GINKGO:
		return &Ginkgo{}
	case SWIFT:
		return Swift{}
	case VITEST:
		return &Vitest{}
	case DART:
		return &Dart{}
	default:
		return nil
	}
	return nil
}

// AnalysisResult 获取结果
func AnalysisResult(unittestConfig config.UnittestConfig, result string) string {
	factory := getFrameFactory(envUtils.GetString(FrameCode))
	testCaseResult := factory.AnalysisResult(unittestConfig.FilePath, result, unittestConfig.RunId)
	marshal, _ := json.Marshal(testCaseResult)
	return string(marshal)
}

// GetRun 获取运行命令
func GetRun(path string, runId string) (string, config.DebugConfig) {
	factory := getFrameFactory(envUtils.GetString(FrameCode))
	return factory.GetRun(path, "", runId)
}

// 获取用例方法
func identifyMethod(path string) []TestCaseFun {
	factory := getFrameFactory(envUtils.GetString(FrameCode))
	if factory == nil {
		return []TestCaseFun{}
	}
	return factory.AnalyzerTestFile(path)
}

// IdentifyFile 获取用例文件
func IdentifyFile() []TestCaseFile {
	// 获取测试用例文件
	path := consts.AppRootDir + "/" + FeatureName
	if !fileUtils.FileExist(path) {
		log.Printf(".1024feature not found")
		return []TestCaseFile{}
	}

	fileTreeMap, err := getMap(path)
	if err != nil {
		log.Printf(".map fail")
		return []TestCaseFile{}
	}

	var testCaseFileList []TestCaseFile
	for k, v := range fileTreeMap.FileTree {
		processDir(v, k, &testCaseFileList)
	}

	return testCaseFileList
}

// GetHiddenFiles 获取隐藏文件列表
func GetHiddenFiles() []string {
	var hiddenFiles []string
	path := consts.AppRootDir + "/" + FeatureName
	if !fileUtils.FileExist(path) {
		log.Printf(".1024feature not found")
		return hiddenFiles
	}
	fileTreeMap, err := getMap(path)
	if err != nil {
		return hiddenFiles
	}
	for k, v := range fileTreeMap.FileTree {
		if v.Hide {
			if v.Unittest {
				hiddenFiles = append(hiddenFiles, k)
			}
			if strings.Contains(strings.ToLower(k), "answer") && !strings.HasSuffix(k, ".cs") {
				hiddenFiles = append(hiddenFiles, k)
			}
		}
	}
	return hiddenFiles
}

// GetUnhiddenTestFiles 获取未隐藏测试文件列表
func GetUnhiddenTestFiles() []string {

	var unhiddenTestFiles []string
	path := consts.AppRootDir + "/" + FeatureName
	if !fileUtils.FileExist(path) {
		log.Printf(".1024feature not found")
		return unhiddenTestFiles
	}
	fileTreeMap, err := getMap(path)
	if err != nil {
		return unhiddenTestFiles
	}
	for k, v := range fileTreeMap.FileTree {
		if v.Unittest && !v.Hide {
			unhiddenTestFiles = append(unhiddenTestFiles, k)
		}
	}
	return unhiddenTestFiles
}

func processDir(entry fileConfig, path string, testCaseFileList *[]TestCaseFile) {
	testCaseFile := TestCaseFile{}
	p := consts.AppRootDir + "/" + path
	stat, err := os.Stat(p)
	if err != nil {
		return
	}
	if stat.IsDir() {
		dir, _ := os.ReadDir(p)
		for _, d := range dir {
			processDir(entry, fileUtils.JoinPath(path, d.Name()), testCaseFileList)
		}
	} else {
		if !entry.Unittest {
			return
		}
		testCaseFile.Path = path
		// 获取方法
		testCaseFile.TestCaseFunList = identifyMethod(path)
		*testCaseFileList = append(*testCaseFileList, testCaseFile)
	}
}

func deleteNotes(fileContent string, patterns []string) string {
	for _, item := range patterns {
		r := regexp.MustCompile(item)
		matches := r.FindAllString(fileContent, -1)
		for _, match := range matches {
			if !(strings.Contains(match, "#start") ||
				strings.Contains(match, "#name") ||
				strings.Contains(match, "#input") ||
				strings.Contains(match, "#expected") ||
				strings.Contains(match, "#end")) {
				fileContent = strings.Replace(fileContent, match, "", -1)
			}
		}
	}
	return fileContent
}

func getContent(regex, content string) string {
	if strings.TrimSpace(content) == "" {
		return ""
	}
	var matches = regexp2FindAllString(regex, content)
	if len(matches) == 0 {
		return ""
	}
	return matches[0]
}

func setItemByFun(testCaseFun TestCaseFun, protocolStr string) TestCaseFun {
	if strings.TrimSpace(protocolStr) != "" {
		name := getContent(ProtocolNamePattern, protocolStr)
		if name != "" {
			testCaseFun.Name = name
		}
		testCaseFun.Input = getContent(ProtocolInputPattern, protocolStr)
		testCaseFun.ExpectedOutput = getContent(ProtocolExpectedPattern, protocolStr)
	}
	return testCaseFun
}

func setItem(testCaseResultItem TestCaseResultItem, protocolStr string) TestCaseResultItem {
	if protocolStr != "" {
		testCaseResultItem.Name = getContent(ProtocolNamePattern, protocolStr)
		testCaseResultItem.Input = getContent(ProtocolInputPattern, protocolStr)
		testCaseResultItem.ExpectedOutput = getContent(ProtocolExpectedPattern, protocolStr)
	}
	return testCaseResultItem
}

func getProtocolPattern(method string) string {
	return "#start[\\s|\\S]*?" + escapeExprSpecialWord(method)
}

func getSplitResultPattern(runId string) string {
	return "\n" + PaasUnitConstant + runId
}

func escapeExprSpecialWord(keyword string) string {
	if strings.TrimSpace(keyword) != "" {
		fbsArr := []string{"\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|"}
		for _, key := range fbsArr {
			if strings.Contains(keyword, key) {
				keyword = strings.ReplaceAll(keyword, key, "\\"+key)
			}
		}
	}
	return keyword
}

func getMap(path string) (*featureConfig, error) {
	content, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, err
	}

	var data featureConfig
	err = yaml.Unmarshal(content, &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func regexp2FindAllString(str string, s string) []string {
	var re = regexp2.MustCompile(str, 0)
	var matches []string
	m, _ := re.FindStringMatch(s)
	for m != nil {
		matches = append(matches, m.String())
		m, _ = re.FindNextMatch(m)
	}
	return matches
}

type featureConfig struct {
	FileTree map[string]fileConfig
}

type fileConfig struct {
	Unittest bool
	Hide     bool
}

func isDir(path string) bool {
	file, err := os.Stat(path)
	if err != nil {
		return false
	}
	return file.IsDir()
}

package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"fmt"
	"path/filepath"
	"strings"
)

type LuaUnit struct{}

var LuaUnitNotes = getLuaUnitNotes()

func getLuaUnitNotes() []string {
	luaUnitNotes := []string{
		"\\n *--\\[\\[[\\s|\\S]*?\\n *--\\]\\]",
		"\\n *--.*",
	}
	return luaUnitNotes
}

type LuaUnitResultJson struct {
	LuaUnitResults []LuaUnitResults `json:"luaUnitResults"`
	Success        bool             `json:"success"`
}

type LuaUnitResults struct {
	Message                 string                `json:"message"`
	Name                    string                `json:"name"`
	StartTime               int64                 `json:"startTime"`
	EndTime                 int64                 `json:"endTime"`
	LuaUnitAssertionResults []LuaAssertionResults `json:"assertionResults"`
}

type LuaAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getLuaUnitReasonPattern(methodName string) string {
	return methodName + "[\\s|\\S]*?(?=stack traceback:)"
}

func (r LuaUnit) AnalysisResult(path string, result string, runId string) TestCaseResult {

	// * 参数 result == fileContent

	var testCaseResult TestCaseResult

	testCases := r.AnalyzerTestFile(path)
	fmt.Println("testCases: ", testCases)

	testCaseResult.Result = result

	if strings.Contains(result, "lua: error loading") {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	testCaseResult.Time = getContent(LuaUnitTimePattern, result)
	if strings.Contains(result, "OK") {
		testCaseResult.RunStatus = PASS
	} else {
		testCaseResult.RunStatus = FAIL
	}

	var testCaseResultItemS []TestCaseResultItem

	for _, testCase := range testCases {
		// ? 通过获取时间判断
		testCaseResultItem := TestCaseResultItem{}

		testCaseResultItem.Name = testCase.Name
		testCaseResultItem.Input = testCase.Input
		testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
		// name := strings.ReplaceAll(testCase.MethodName, " ", "")
		// name = strings.ReplaceAll(name, ":", ".")
		str := getContent(testCase.MethodName, result)
		fmt.Println("str:", str)
		if len(str) == 0 {
			testCaseResultItem.Success = true
		} else {
			testCaseResultItem.Success = false
			failReasonPattern := getLuaUnitReasonPattern(testCase.MethodName)
			fmt.Println("failReasonPattern:", failReasonPattern)
			testCaseResultItem.Reason = getContent(getLuaUnitReasonPattern(testCase.MethodName), result)
			fmt.Println("reason:", testCaseResultItem.Reason)
			testCaseResultItem.Details = testCaseResultItem.Reason
		}
		// protocolStr := getContent(getProtocolPattern(testCase.Name+"\\("), result)
		// testCaseResultItem = setItem(testCaseResultItem, protocolStr)
		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}
	testCaseResult.TestCaseResultItems = testCaseResultItemS
	return testCaseResult

}

func (r *LuaUnit) AnalyzerTestFile(path string) []TestCaseFun {

	var testCaseFunS []TestCaseFun

	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	fileContent = deleteNotes(fileContent, LuaUnitNotes)

	var matches = regexp2FindAllString(LuaMethodPattern, fileContent)

	for _, match := range matches {
		var testCaseFun TestCaseFun
		match = strings.ReplaceAll(match, "  ", " ")
		match = strings.ReplaceAll(match, ":", ".")

		testCaseFun.MethodName = getContent(LuaMethodNamePattern, strings.TrimSpace(match))
		testCaseFun = setItemByFun(testCaseFun, match)
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}

	return testCaseFunS
}

// lua hidden_test.lua
func (r *LuaUnit) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	var stringBuffer strings.Builder

	filename := filepath.Base(path)
	stringBuffer.WriteString("lua ")
	stringBuffer.WriteString(filename)

	//* 如果是单个方法，需要加上方法名
	if methodName != "" {
		methodName = strings.ReplaceAll(methodName, ":", ".")
		stringBuffer.WriteString(" " + methodName)
	}

	return stringBuffer.String(), config.DebugConfig{}
}

package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"path/filepath"
	"strings"
)

type Phpunit struct{}

var PhpunitNotes = getPhpunitNotes()

type PhpunitResultJson struct {
	PhpTestResults []PhpTestResults `json:"testResults"`
	Success        bool             `json:"success"`
}

type PhpTestResults struct {
	Message          string                `json:"message"`
	Name             string                `json:"name"`
	StartTime        int64                 `json:"startTime"`
	EndTime          int64                 `json:"endTime"`
	AssertionResults []PhpAssertionResults `json:"assertionResults"`
}

type PhpAssertionResults struct {
	AncestorTitles  []string `json:"ancestorTitles"`
	FailureMessages []string `json:"failureMessages"`
	FullName        string   `json:"fullName"`
	Title           string   `json:"title"`
	Status          string   `json:"status"`
}

func getPhpunitNotes() []string {
	phpunitNotes := []string{
		"\\n */\\*[\\s|\\S]*?\\*/",
		"\\n *//.*",
	}
	return phpunitNotes
}

func phpUnitResultPattern(method string) string {
	return "\\d\\) .*::" + method
}

func phpUnitFailDetailReasonPattern(filename, method string) string {
	// fmt.Println("HiddenTest" + "::" + method + ".*?(\\d\\)|FAILURES|ERRORS)")
	// return "HiddenTest" + "::" + method + "[\\s|\\S].*?(\\d\\)|FAILURES|ERRORS)"
	return "(" + method + "[\\s|\\S]*?)(\\d\\)|FAILURES|ERRORS)"
}

func phpUnitFailReasonPattern() string {
	return "Failed.[\\s|\\S]*(?=\\n\\s)"
}

func (r Phpunit) AnalysisResult(path string, result string, runId string) TestCaseResult {

	testCases := r.AnalyzerTestFile(path)

	filename := strings.Split(filepath.Base(path), ".")[0]
	testCaseResult := TestCaseResult{}
	testCaseResult.Result = result

	// * 编译出错，直接返回
	if strings.Contains(result, "Fatal error") ||
		strings.Contains(result, "Parse error") ||
		strings.Contains(result, "syntax error") ||
		strings.Contains(result, "PHP Fatal error") ||
		strings.Contains(result, "PHP Parse error") ||
		strings.Contains(result, "PHP syntax error") ||
		strings.Contains(result, "Cannot open file") ||
		strings.Contains(result, "could not be found in "+"'"+HomePath+path+"'") {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	testCaseResult.Time = getContent(PhpunitTimePattern, result)
	if strings.Contains(result, "OK") {
		testCaseResult.RunStatus = PASS
	} else {
		testCaseResult.RunStatus = FAIL
	}

	var testCaseResultItemS []TestCaseResultItem
	for _, testCase := range testCases {

		var testCaseResultItem TestCaseResultItem
		testCaseResultItem.Name = testCase.Name
		testCaseResultItem.Input = testCase.Input
		testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
		fail_pattern := phpUnitResultPattern(testCase.MethodName)

		str := getContent(fail_pattern, result)
		if len(str) == 0 {
			testCaseResultItem.Success = true
		} else {
			testCaseResultItem.Success = false
			failedDetailPattern := phpUnitFailDetailReasonPattern(filename, testCase.MethodName)
			testCaseResultItem.Details = getContent(failedDetailPattern, result)
		}
		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}

	testCaseResult.TestCaseResultItems = testCaseResultItemS
	return testCaseResult
}

func (r *Phpunit) AnalyzerTestFile(path string) []TestCaseFun {
	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	fileContent = deleteNotes(fileContent, PhpunitNotes)

	var matches = regexp2FindAllString(PhpunitMethodPattern, fileContent)

	var testCaseFunS []TestCaseFun

	for _, match := range matches {
		var testCaseFun TestCaseFun
		methodName := getContent(PhpunitMethodNamePattern, strings.TrimSpace(match))
		if len(methodName) == 0 {
			continue
		}
		testCaseFun.MethodName = methodName

		testCaseFun = setItemByFun(testCaseFun, strings.TrimSpace(match))
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.MethodName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)
	}

	return testCaseFunS
}

// TODO
// * 改成用 XML 的方式解析
// * phpunit --log-junit result.xml filename
func (r *Phpunit) GetRun(path string, methodName, runId string) (string, config.DebugConfig) {

	var stringBuffer strings.Builder

	//? 需要保证path的规范性，不然会出错
	// fileName := filepath.Base(path)

	stringBuffer.WriteString("phpunit ")
	stringBuffer.WriteString(path)
	if methodName != "" {
		stringBuffer.WriteString(" --filter ")
		stringBuffer.WriteString(methodName)
	}

	return stringBuffer.String(), config.DebugConfig{}
}

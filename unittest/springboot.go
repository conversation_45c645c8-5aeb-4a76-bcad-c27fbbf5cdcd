package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"path/filepath"
	"strconv"
	"strings"
)

type SpringBoot struct{}

func getErrorPattern() string {
	return "Errors:..\\[m\\n"
}

func getSpringBootTestError(method string) string {
	return "\\n.*ERROR.*." + method + ":\\d"
}

func getSpringBootTestErrorReasonDetailPattern(method string) string {

	return "\\n.*ERROR.*" + method + ":[\\s|\\S]*?(?=\\n\\[)"
}

func getSprintBootTestErrorNumPattern() string {
	return "Failures: +\\d"
}

func (r SpringBoot) AnalysisResult(path string, result string, runId string) TestCaseResult {

	testcaseResult := TestCaseResult{}

	testcaseResult.Result = result
	testcaseResult.Time = getContent(JunitTimePattern, result)

	errorString := getContent(getErrorPattern(), result)

	if len(errorString) > 0 || strings.Contains(result, "COMPILATION ERROR") || strings.Contains(result, "BUILD FAILURE") {
		testcaseResult.RunStatus = FAIL
		return testcaseResult
	}

	errNumStr := getContent(getSprintBootTestErrorNumPattern(), result)
	NumStr := strings.Split(errNumStr, " ")[1]
	errNum, err := strconv.Atoi(strings.TrimSpace(NumStr))
	if err != nil {
		// fmt.Println("err:", err)
		return testcaseResult
	}
	if errNum > 0 {
		testcaseResult.RunStatus = FAIL
	} else {
		testcaseResult.RunStatus = PASS
	}

	var testCaseResultItemS []TestCaseResultItem

	testcases := r.AnalyzerTestFile(path)

	for _, testcase := range testcases {
		testCaseResultItem := TestCaseResultItem{}
		testCaseResultItem.Name = testcase.Name
		testCaseResultItem.Input = testcase.Input
		testCaseResultItem.ExpectedOutput = testcase.ExpectedOutput

		// * 去掉方法括号
		methodnameWithoutBracket := strings.ReplaceAll(testcase.MethodName, "(", "")
		methodnameWithoutBracket = strings.ReplaceAll(methodnameWithoutBracket, ")", "")
		testError := getContent(getSpringBootTestError(methodnameWithoutBracket), result)

		if len(testError) > 0 {
			testCaseResultItem.Success = false
			testCaseResultItem.Reason = getContent(getSpringBootTestErrorReasonDetailPattern(methodnameWithoutBracket), result)
			testCaseResultItem.Details = getContent(getSpringBootTestErrorReasonDetailPattern(methodnameWithoutBracket), result)
		} else {
			testCaseResultItem.Success = true
		}

		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}

	testcaseResult.TestCaseResultItems = testCaseResultItemS

	return testcaseResult
}

func (r SpringBoot) AnalyzerTestFile(path string) []TestCaseFun {
	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	// * remove notes
	fileContent = deleteNotes(fileContent, getJavaNotes())
	// ? @ParameterizedTest 问题点
	var matches = regexp2FindAllString(JunitFilePattern, fileContent)

	var testCaseFunS []TestCaseFun

	for _, match := range matches {
		// fmt.Println("match:", match)
		testCaseFun := TestCaseFun{}

		methodName := getContent(JunitMethodNamePattern, strings.TrimSpace(match))
		testCaseFun.MethodName = methodName

		name := getContent(JunitNameProtocolPattern, strings.TrimSpace(match))
		testCaseFun.Name = name

		testCaseFun.DisplayName = getContent(JUnitNamePattern, strings.TrimSpace(match))
		if testCaseFun.DisplayName == "" {
			testCaseFun.DisplayName = testCaseFun.MethodName
		}

		testCaseFun = setItemByFun(testCaseFun, strings.TrimSpace(match))
		if testCaseFun.Name == "" {
			testCaseFun.Name = testCaseFun.DisplayName
		}
		testCaseFunS = append(testCaseFunS, testCaseFun)

	}
	return testCaseFunS
}

func (r *SpringBoot) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	filenameNoSuffix := strings.Split(filepath.Base(path), ".")[0]

	var stringBuffer strings.Builder

	stringBuffer.WriteString("mvn test ")
	stringBuffer.WriteString("-Dtest=")
	stringBuffer.WriteString(filenameNoSuffix)
	if len(methodName) > 0 {
		stringBuffer.WriteString("#")
		stringBuffer.WriteString(methodName)
	}
	return stringBuffer.String(), config.DebugConfig{}
}

package unittest

import (
	"testing"
)

func TestNunit_AnalysisResult(t *testing.T) {
	nunit := Nunit{}
	path := "templates/csharp_test.cs"
	result := `[?1h=[6n[H[2J[37mNUnit Console 3.15.0 (Release)
	[37m[37mCopyright (c) 2022 <PERSON>, <PERSON>
	[37m[37mFriday, June 2, 2023 2:05:19 PM
	[37m
	[36mRuntime Environment
	[37m[32m   OS Version: [37m[37mLinux 5.10.0.17 [37m
	[32m   Runtime: [37m[37m.NET Framework CLR v4.0.30319.42000[37m
	
	[36mTest Files
	[37m[32m    HiddenTest.dll
	[37m
	
	[36mErrors, Failures and Warnings
	[37m
	[31m1) Failed : HiddenTest.SolutionTest1
	[37m[37m  Expected is <System.Int32[4]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  5
	[37m[37m  at HiddenTest.SolutionTest1 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m2) Failed : HiddenTest.SolutionTest10
	[37m[37m  Expected is <System.Int32[4]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 12
	  But was:  15
	[37m[37m  at HiddenTest.SolutionTest10 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m3) Failed : HiddenTest.SolutionTest2
	[37m[37m  Expected is <System.Int32[5]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  5
	[37m[37m  at HiddenTest.SolutionTest2 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m4) Failed : HiddenTest.SolutionTest3
	[37m[37m  Expected is <System.Int32[6]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  12
	[37m[37m  at HiddenTest.SolutionTest3 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m5) Failed : HiddenTest.SolutionTest5
	[37m[37m  Expected is <System.Int32[2]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  2
	[37m[37m  at HiddenTest.SolutionTest5 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m6) Failed : HiddenTest.SolutionTest7
	[37m[37m  Expected is <System.Int32[2]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  2
	[37m[37m  at HiddenTest.SolutionTest7 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m7) Failed : HiddenTest.SolutionTest8
	[37m[37m  Expected is <System.Int32[8]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 12
	  But was:  20
	[37m[37m  at HiddenTest.SolutionTest8 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[31m8) Failed : HiddenTest.SolutionTest9
	[37m[37m  Expected is <System.Int32[6]>, actual is <System.Int32[1]>
	  Values differ at index [0]
	  Expected: 1
	  But was:  15
	[37m[37m  at HiddenTest.SolutionTest9 () [0x00000] in <e16da7bf381047e5be247ad78bca0818>:0 
	[37m
	[36mRun Settings
	[37m[32m    DisposeRunners:[37m[37m True[37m
	[32m    WorkDirectory:[37m[37m /home/<USER>/app[37m
	[32m    ImageRuntimeVersion:[37m[37m 4.0.30319[37m
	[32m    ImageRequiresX86:[37m[37m False[37m
	[32m    ImageRequiresDefaultAppDomainAssemblyResolver:[37m[37m False[37m
	[32m    TargetRuntimeFramework:[37m[37m mono-4.0[37m
	[32m    NumberOfTestWorkers:[37m[37m 16[37m
	
	[36mTest Run Summary
	[37m[32m  Overall result: [37m[31mFailed[37m
	[32m  Test Count: [37m[37m10[37m[32m, Passed: [37m[37m2[37m[32m, Failed: [37m[31m8[37m[32m, Warnings: [37m[37m0[37m[32m, Inconclusive: [37m[37m0[37m[32m, Skipped: [37m[37m0[37m
	[32m    Failed Tests - Failures: [37m[37m8[37m[32m, Errors: [37m[37m0[37m[32m, Invalid: [37m[37m0[37m
	[32m  Start time: [37m[37m2023-06-02 06:05:19Z[37m
	[32m    End time: [37m[37m2023-06-02 06:05:20Z[37m
	[32m    Duration: [37m[37m0.657 seconds[37m
	
	Results (nunit3) saved as .paas-unit-faa4d4ef7d694238b8a5aaf4395de780
	[39;49m[?1l>[39;49m`
	runId := "SampleTest.cs_runUnitTestResult"
	expectedResult := TestCaseResult{
		Result:              "",
		Time:                "",
		RunStatus:           FAIL,
		TestCaseResultItems: []TestCaseResultItem{},
	}
	// newResult, _ := strconv.Unquote(strings.Replace(strconv.QuoteToASCII(result), `\\u`, `\u`, -1))
	// t.Log(newResult)

	actualResult := nunit.AnalysisResult(path, result, runId)
	// if actualResult.Result != expectedResult.Result {
	// 	t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	// }
	// if actualResult.Time != expectedResult.Time {
	// 	t.Errorf("AnalysisResult() returned incorrect time: got %v, want %v", actualResult.Time, expectedResult.Time)
	// }
	if actualResult.RunStatus != expectedResult.RunStatus {
		t.Errorf("AnalysisResult() returned incorrect run status: got %v, want %v", actualResult.RunStatus, expectedResult.RunStatus)
	}
	// if len(actualResult.TestCaseResultItems) != len(expectedResult.TestCaseResultItems) {
	// 	t.Errorf("AnalysisResult() returned incorrect number of test case result items: got %v, want %v", len(actualResult.TestCaseResultItems), 10)
	// }
}

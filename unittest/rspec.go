package unittest

import (
	"agent/config"
	"agent/utils/fileUtils"
	"encoding/json"
	"strconv"
	"strings"
)

type Rspec struct{}

var RubyNotes = getRubyNotes()

type RspecResultJson struct {
	Version     string             `json:"version"`
	Examples    []RspecTestResults `json:"examples"`
	Summary     ResultSummary      `json:"summary"`
	SummaryLine string             `json:"summary_line"`
}

type ResultSummary struct {
	Duration                     float64 `json:"duration"`
	ExampleCount                 int     `json:"example_count"`
	FailureCount                 int     `json:"failure_count"`
	PendingCount                 int     `json:"pending_count"`
	ErrorsOutsideOfExamplesCount int     `json:"errors_outside_of_examples_count"`
}

type RspecTestResults struct {
	Id              string         `json:"id"`
	Description     string         `json:"description"`
	FullDescription string         `json:"full_description"`
	Status          string         `json:"status"`
	Location        string         `json:"location"`
	RunTime         float64        `json:"run_time"`
	PendingMessage  string         `json:"pending_message"`
	LineNumber      int            `json:"line_number"`
	Exception       RspecException `json:"exception"`
}

type RspecException struct {
	Class     string   `json:"class"`
	Message   string   `json:"message"`
	Backtrace []string `json:"backtrace"`
}

func getRubyNotes() []string {
	rubyNotes := []string{
		"=begin[\\s|\\S]*?\\n=end",
		"\\n *#.*",
	}
	return rubyNotes
}

func (r Rspec) AnalysisResult(path string, result string, runId string) TestCaseResult {

	// fileContent, _ := fileUtils.Read(path)

	var testCaseResult TestCaseResult

	resultStrs := strings.Split(result, getSplitResultPattern(runId))
	if len(resultStrs) == 1 {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	// * 编译报错，直接返回
	if strings.Contains(resultStrs[0], "SyntaxError") || strings.Contains(resultStrs[0], "LoadError") || strings.Contains(resultStrs[0], "NameError") {
		testCaseResult.Result = resultStrs[0]
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	testCases := r.AnalyzerTestFile(path)

	testCaseResult.Result = resultStrs[0]

	resultJsonStr := resultStrs[1]

	resultJson := RspecResultJson{}
	err := json.Unmarshal([]byte(resultJsonStr), &resultJson)
	if err != nil {
		testCaseResult.RunStatus = FAIL
		return testCaseResult
	}

	if resultJson.Summary.FailureCount > 0 {
		testCaseResult.RunStatus = FAIL
	} else {
		testCaseResult.RunStatus = PASS
	}

	// float64 to string
	testCaseResult.Time = strconv.FormatFloat(resultJson.Summary.Duration, 'f', -1, 64)

	var testCaseResultItemS []TestCaseResultItem

	for _, testResult := range resultJson.Examples {

		var testCaseResultItem TestCaseResultItem
		for _, testCase := range testCases {
			if testCase.MethodName == testResult.FullDescription || testCase.Name == testResult.Description {
				testCaseResultItem.Name = testCase.Name
				testCaseResultItem.ExpectedOutput = testCase.ExpectedOutput
				testCaseResultItem.Input = testCase.Input
				if testResult.Status == "passed" {
					testCaseResultItem.Success = true
				} else {
					testCaseResultItem.Success = false
					testCaseResultItem.Reason = testResult.Exception.Message
					// testCaseResultItem.Details = strings.Join(testResult.Exception.Backtrace, "\n")
				}
			}
		}
		testCaseResultItemS = append(testCaseResultItemS, testCaseResultItem)
	}
	testCaseResult.TestCaseResultItems = testCaseResultItemS
	return testCaseResult
}

func (r *Rspec) AnalyzerTestFile(path string) []TestCaseFun {

	fileContent, err := fileUtils.Read(path)
	if err != nil {
		return nil
	}

	fileContent = deleteNotes(fileContent, RubyNotes)

	// fmt.Println("fileContent: ", fileContent)

	var matches = regexp2FindAllString(RubyMethodPattern, fileContent)

	var testCaseFunS []TestCaseFun

	for _, match := range matches {

		testCaseFunc := TestCaseFun{}

		// todo: 优化
		describeName := strings.TrimSpace(getContent(RspecMethodNameDescribePattern, match))
		describeName = strings.Replace(describeName, "describe", "", -1)
		describeName = strings.TrimSpace(describeName)
		describeName = strings.Replace(describeName, "\"", "", -1)
		describeName = strings.Replace(describeName, "'", "", -1)
		itName := strings.TrimSpace(getContent(RspecMethodNameItPattern, match))
		itName = strings.Replace(itName, "it", "", -1)
		itName = strings.TrimSpace(itName)
		itName = strings.Replace(itName, "\"", "", -1)
		itName = strings.Replace(itName, "'", "", -1)
		// if len(describeName) == 0 || len(itName) == 0 {
		// 	continue
		// }
		testCaseFunc.MethodName = describeName + " " + itName
		testCaseFunc = setItemByFun(testCaseFunc, strings.TrimSpace(match))

		if testCaseFunc.Name == "" {
			testCaseFunc.Name = itName
		}

		testCaseFunS = append(testCaseFunS, testCaseFunc)
	}
	return testCaseFunS
}

// rspec hidden_test.rb -f j -o .paas-unit-e28b9021f4e14db7ab250ae35df15376 -f p
func (r *Rspec) GetRun(path string, methodName string, runId string) (string, config.DebugConfig) {
	var stringBuilder strings.Builder
	if fileUtils.FileExist("Gemfile") && fileUtils.FileExist("bin/rails") {
		stringBuilder.WriteString("bundle exec ")
	}

	logPath := PaasUnitConstant + runId

	stringBuilder.WriteString("rspec ")
	stringBuilder.WriteString(path)
	stringBuilder.WriteString(" -f j -o ")
	stringBuilder.WriteString(logPath)
	stringBuilder.WriteString(" -f p")

	if methodName != "" {
		stringBuilder.WriteString(" -e ")
		stringBuilder.WriteString(methodName)
	}

	var debugConfig config.DebugConfig
	debugConfig.Support = true
	debugConfig.Launch = map[string]interface{}{}
	debugConfig.Launch["script"] = stringBuilder.String()
	debugConfig.Launch["localfs"] = true

	return stringBuilder.String(), debugConfig
}

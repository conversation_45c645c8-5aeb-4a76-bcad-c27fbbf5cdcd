package unittest

import (
	"os"
	"testing"
)

func TestSwift_GetRun_Success(t *testing.T) {
	swift := Swift{}
	path := "templates/file.swift"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	methodName := "testFunction"
	runId := "12345"
	expected := "swift test --filter file/testFunction > .paas-unit-12345"
	actual, _ := swift.GetRun(path, methodName, runId)
	if actual != expected {
		t.Errorf("Expected %s, got %s", expected, actual)
	}
}

func TestSwift_AnalysisResult_ReturnsCorrectResult(t *testing.T) {
	swift := Swift{}
	path := "templates/sample_test.swift"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.<PERSON><PERSON>("Test file", path, "not found, skipping test")
		return
	}

	result := `[2K
	Building for debugging...
	[2K
	[1/3] Emitting module ShowMeBug[2K
	[2/3] Compiling ShowMeBug ShowMeBug.swift[2K
	[2/4] Compiling ShowMeBug ShowMeBug.swift[2K
	[3/4] Compiling ShowMeBug ShowMeBug.swift[2K
	[3/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/6] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/6] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/6] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/6] Compiling ShowMeBug ShowMeBug.swift[2K
	[4/5] Compiling ShowMeBug ShowMeBug.swift[2K
	[5/5] Linking ShowMeBugPackageTests.xctest[2K
	[5/5] Linking ShowMeBugPackageTests.xctest
	[2K
	Build complete! (0.55s)
	Test Suite 'Selected tests' started at 2023-06-01 00:00:36.953
	Test Suite 'SampleTest' started at 2023-06-01 00:00:36.954
	Test Case 'SampleTest.testSolution1' started at 2023-06-01 00:00:36.954
	/home/<USER>/app/Tests/ShowMeBugTests/SampleTest.swift:19: error: SampleTest.testSolution1 : XCTAssertEqual failed: ("6") is not equal to ("3") -
	Test Case 'SampleTest.testSolution1' failed (0.001 seconds)
	Test Suite 'SampleTest' failed at 2023-06-01 00:00:36.955
		 Executed 1 test, with 1 failure (0 unexpected) in 0.001 (0.001) seconds
	Test Suite 'Selected tests' failed at 2023-06-01 00:00:36.955
		 Executed 1 test, with 1 failure (0 unexpected) in 0.001 (0.001) seconds

	`
	runId := "12345"
	expectedResult := TestCaseResult{
		Result:    result,
		Time:      "0.001",
		RunStatus: FAIL,
		TestCaseResultItems: []TestCaseResultItem{
			{
				Name:           "testFunction",
				Input:          "",
				ExpectedOutput: "",
				Success:        false,
				Reason:         "",
			},
		},
	}
	actualResult := swift.AnalysisResult(path, result, runId)
	// if actualResult.Result != expectedResult.Result {
	// 	t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	// }
	if actualResult.Time != expectedResult.Time {
		t.Errorf("AnalysisResult() returned incorrect time: got %v, want %v", actualResult.Time, expectedResult.Time)
	}
	if actualResult.RunStatus != expectedResult.RunStatus {
		t.Errorf("AnalysisResult() returned incorrect run status: got %v, want %v", actualResult.RunStatus, expectedResult.RunStatus)
	}
	if len(actualResult.TestCaseResultItems) != len(expectedResult.TestCaseResultItems) {
		t.Errorf("AnalysisResult() returned incorrect number of test case result items: got %v, want %v", len(actualResult.TestCaseResultItems), len(expectedResult.TestCaseResultItems))
	}
	t.Log("len of actualResult.TestCaseResultItems: ", len(actualResult.TestCaseResultItems))
	t.Log("detail of actualResult.TestCaseResultItems: ", actualResult.TestCaseResultItems)
	// for i, actualItem := range actualResult.TestCaseResultItems {
	// 	expectedItem := expectedResult.TestCaseResultItems[i]
	// 	if actualItem.Name != expectedItem.Name {
	// 		t.Errorf("AnalysisResult() returned incorrect test case name: got %v, want %v", actualItem.Name, expectedItem.Name)
	// 	}
	// 	if actualItem.Input != expectedItem.Input {
	// 		t.Errorf("AnalysisResult() returned incorrect test case input: got %v, want %v", actualItem.Input, expectedItem.Input)
	// 	}
	// 	if actualItem.ExpectedOutput != expectedItem.ExpectedOutput {
	// 		t.Errorf("AnalysisResult() returned incorrect test case expected output: got %v, want %v", actualItem.ExpectedOutput, expectedItem.ExpectedOutput)
	// 	}
	// 	if actualItem.Success != expectedItem.Success {
	// 		t.Errorf("AnalysisResult() returned incorrect test case success status: got %v, want %v", actualItem.Success, expectedItem.Success)
	// 	}
	// 	if actualItem.Reason != expectedItem.Reason {
	// 		t.Errorf("AnalysisResult() returned incorrect test case reason: got %v, want %v", actualItem.Reason, expectedItem.Reason)
	// 	}
	// if actualItem.ProtocolVersion != expectedItem.ProtocolVersion {
	//     t.Errorf("AnalysisResult() returned incorrect test case protocol version: got %v, want %v", actualItem.ProtocolVersion, expectedItem.ProtocolVersion)
	// }
	// if actualItem.ProtocolMessage != expectedItem.ProtocolMessage {
	//     t.Errorf("AnalysisResult() returned incorrect test case protocol message: got %v, want %v", actualItem.ProtocolMessage, expectedItem.ProtocolMessage)
	// }
	// }
}

func TestSwift_AnalysisResult_ReturnsEmptyTestCaseResultWhenNoMatchesFound(t *testing.T) {
	swift := Swift{}
	path := "empty_test.swift"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := ""
	runId := "12345"
	expectedResult := TestCaseResult{
		Result:              "",
		Time:                "",
		RunStatus:           PASS,
		TestCaseResultItems: []TestCaseResultItem{},
	}
	actualResult := swift.AnalysisResult(path, result, runId)
	if actualResult.Result != expectedResult.Result {
		t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	}
	if actualResult.Time != expectedResult.Time {
		t.Errorf("AnalysisResult() returned incorrect time: got %v, want %v", actualResult.Time, expectedResult.Time)
	}
	if actualResult.RunStatus != expectedResult.RunStatus {
		t.Errorf("AnalysisResult() returned incorrect run status: got %v, want %v", actualResult.RunStatus, expectedResult.RunStatus)
	}
	if len(actualResult.TestCaseResultItems) != len(expectedResult.TestCaseResultItems) {
		t.Errorf("AnalysisResult() returned incorrect number of test case result items: got %v, want %v", len(actualResult.TestCaseResultItems), len(expectedResult.TestCaseResultItems))
	}
}

// func TestSwift_AnalysisResult_ReturnsCorrectProtocolMessage(t *testing.T) {
//     swift := Swift{}
//     path := "test.swift"
//     result := "Test Suite 'All tests' started at 2021-08-26 14:23:12.123\nTest Suite 'TestFile' started at 2021-08-26 14:23:12.123\nTest Case '-[TestFileTests testFunction]' started.\nTest Case '-[TestFileTests testFunction]' failed (0.001 seconds).\nTest Suite 'TestFile' failed at 2021-08-26 14:23:12.123.\n\tExecuted 1 test, with 1 failure (0 unexpected) in 0.001 (0.001) seconds\nTest Suite 'All tests' failed at 2021-08-26 14:23:12.123.\n\tExecuted 1 test, with 1 failure (0 unexpected) in 0.001 (0.001) seconds\n"
//     runId := "12345"
//     expectedProtocolMessage := "-[TestFileTests testFunction] failed (0.001 seconds)."
//     actualResult := swift.AnalysisResult(path, result, runId)
//     if actualResult.TestCaseResultItems[0].ProtocolMessage != expectedProtocolMessage {
//         t.Errorf("AnalysisResult() returned incorrect protocol message: got %v, want %v", actualResult.TestCaseResultItems[0].ProtocolMessage, expectedProtocolMessage)
//     }
// }

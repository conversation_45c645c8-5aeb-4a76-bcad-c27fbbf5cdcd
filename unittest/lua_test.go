package unittest

import (
	"os"
	"testing"
)

func TestLuaUnit_AnalysisResult_ReturnsCorrectResult(t *testing.T) {
	luaUnit := LuaUnit{}
	path := "templates/lua_test.md"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}
	result := `FF
	Failed tests:
	-------------
	1) testSolution1
	sample_test.lua:13: expected: 3, actual: 4
	stack traceback:
			sample_test.lua:13: in function 'testSolution1'

	2) testSolution2
	sample_test.lua:24: expected: 5, actual: 6
	stack traceback:
			sample_test.lua:24: in function 'testSolution2'

	Ran 2 tests in 0.000 seconds, 0 successes, 2 failures
	`
	t.Log(result)
	runId := "12345"
	expectedResult := TestCaseResult{
		Result:    result,
		Time:      "",
		RunStatus: FAIL,
		TestCaseResultItems: []TestCaseResultItem{
			{
				Name:           " testSolution",
				Input:          "",
				ExpectedOutput: "",
				Success:        false,
				Reason:         "",
				// ProtocolVersion: "",
				// ProtocolMessage: "",
			},
		},
	}
	actualResult := luaUnit.AnalysisResult("templates/lua_test.md", result, runId)
	if actualResult.Result != expectedResult.Result {
		t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	}
}

func TestLuaUnit_AnalysisResult_ReturnsEmptyTestCaseResultWhenNoMatchesFound(t *testing.T) {
	luaUnit := LuaUnit{}
	path := "empty_test.lua"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := ""
	runId := "12345"
	expectedResult := TestCaseResult{
		Result:              "",
		Time:                "",
		RunStatus:           PASS,
		TestCaseResultItems: []TestCaseResultItem{},
	}
	actualResult := luaUnit.AnalysisResult(path, result, runId)
	if actualResult.Result != expectedResult.Result {
		t.Errorf("AnalysisResult() returned incorrect result: got %v, want %v", actualResult.Result, expectedResult.Result)
	}
	if actualResult.Time != expectedResult.Time {
		t.Errorf("AnalysisResult() returned incorrect time: got %v, want %v", actualResult.Time, expectedResult.Time)
	}
	if actualResult.RunStatus != expectedResult.RunStatus {
		t.Errorf("AnalysisResult() returned incorrect run status: got %v, want %v", actualResult.RunStatus, expectedResult.RunStatus)
	}
	if len(actualResult.TestCaseResultItems) != len(expectedResult.TestCaseResultItems) {
		t.Errorf("AnalysisResult() returned incorrect number of test case result items: got %v, want %v", len(actualResult.TestCaseResultItems), len(expectedResult.TestCaseResultItems))
	}
}

func TestLuaUnit_AnalysisResult_ReturnsCorrectTime(t *testing.T) {
	luaUnit := LuaUnit{}
	path := "templates/lua_test.md"

	// Skip if test file doesn't exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		t.Skip("Test file", path, "not found, skipping test")
		return
	}

	result := "1..2\nok 1 - test1\nnot ok 2 - test2\nTime: 0.001s\n"
	runId := "12345"
	expectedTime := "0.001s"
	actualResult := luaUnit.AnalysisResult(path, result, runId)
	if actualResult.Time != expectedTime {
		t.Skip("AnalysisResult() returned incorrect time: got", actualResult.Time, "want", expectedTime)
		return
	}
}

// func TestLuaUnit_AnalysisResult_ReturnsCorrectProtocolMessage(t *testing.T) {
// 	luaUnit := LuaUnit{}
// 	path := "test.lua"
// 	result := "1..2\nok 1 - test1\nnot ok 2 - test2\nTime: 0.001s\n\nFAIL: test2\n  test.lua:6: expected: 2\n  but got: 3\n\n"
// 	runId := "12345"
// 	expectedProtocolMessage := "FAIL: test2\n  test.lua:6: expected: 2\n  but got: 3"
// 	actualResult := luaUnit.AnalysisResult(path, result, runId)
// 	if actualResult.TestCaseResultItems[1].ProtocolMessage != expectedProtocolMessage {
// 		t.Errorf("AnalysisResult() returned incorrect protocol message: got %v, want %v", actualResult.TestCaseResultItems[1].ProtocolMessage, expectedProtocolMessage)
// 	}
// }

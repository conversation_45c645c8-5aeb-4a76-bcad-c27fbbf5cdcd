# Clacky AI PaaS 架构设计文档

## 系统整体架构

### 架构概览

Clacky AI PaaS 采用微服务架构，基于容器技术构建的云原生平台即服务系统。系统主要由管理平台、容器调度引擎、资源管理器和监控系统组成。

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户界面层                                │
├─────────────────────────────────────────────────────────────────┤
│                     API 网关 (Kong)                            │
├─────────────────────────────────────────────────────────────────┤
│                      业务服务层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Playground     │  │   Docker        │  │   Resource      │  │
│  │   Service       │  │   Service       │  │   Service       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      调度引擎层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │MemoryDocker     │  │   MQ Message    │  │   Event         │  │
│  │ServerSelector   │  │   Handlers      │  │   Listeners     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      基础设施层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │     MySQL       │  │     Redis       │  │   RabbitMQ      │  │
│  │   (数据存储)     │  │   (缓存/队列)    │  │   (消息队列)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                      容器运行层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Docker Host 1  │  │  Docker Host 2  │  │  Docker Host N  │  │
│  │  ┌───────────┐  │  │  ┌───────────┐  │  │  ┌───────────┐  │  │
│  │  │Container 1│  │  │  │Container 3│  │  │  │Container N│  │  │
│  │  │+ Agent    │  │  │  │+ Agent    │  │  │  │+ Agent    │  │  │
│  │  └───────────┘  │  │  └───────────┘  │  │  └───────────┘  │  │
│  │  ┌───────────┐  │  │  ┌───────────┐  │  │                 │  │
│  │  │Container 2│  │  │  │Container 4│  │  │                 │  │
│  │  │+ Agent    │  │  │  │+ Agent    │  │  │                 │  │
│  │  └───────────┘  │  │  └───────────┘  │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **管理平台 (d42paas_manager)**：核心业务逻辑，负责容器生命周期管理
2. **容器调度引擎**：智能资源调度和负载均衡
3. **消息队列系统**：基于 RabbitMQ 的异步通信
4. **监控系统**：实时监控和故障恢复
5. **网关服务**：Kong API Gateway 提供统一入口

## 核心组件详细设计

### 1. 容器调度引擎 (MemoryDockerServerSelector)

#### 设计原理

基于内存负载的智能调度算法，通过 Redis ZSet 维护宿主机资源状态，实现高效的资源分配。

#### 核心算法

```java
// 基于内存负载选择最优宿主机
private DockerServer findServerByMemory(ResourcesLimit resourcesLimit) {
    // 查询满足内存需求的服务器，按剩余内存降序排列
    Set<TypedTuple<String>> servers = stringRedisTemplate.opsForZSet()
        .reverseRangeByScoreWithScores(
            RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, 
            resourcesLimit.getRAM(),
            Double.POSITIVE_INFINITY, 0, 1);
    
    // 原子性资源申请
    proxyTarget.request(serverId, memoryMb, cpuCount);
}
```

#### 资源管理策略

- **内存优先**：优先选择剩余内存最多的服务器
- **原子操作**：使用 Redis 原子操作避免资源超分配
- **故障转移**：服务器离线时自动清理负载数据
- **资源回收**：容器停止时自动归还资源

### 2. 容器生命周期管理 (DockerService)

#### 生命周期状态

```
NOT_INIT → CREATE → START_SUCCESS → ACTIVE → STOP_SUCCESS → DELETE_SUCCESS
    ↓         ↓           ↓            ↓           ↓
CREATE_FAIL START_FAIL  TIMEOUT    INACTIVE   STOP_FAIL
```

#### 关键流程

1. **容器创建**：
   - 选择宿主机 (MemoryDockerServerSelector)
   - 申请资源 (CPU/Memory)
   - 创建容器 (DockerExternalService)

2. **容器启动**：
   - 启动中间件容器
   - 设置30秒超时监控
   - 启动主容器
   - 等待 Agent 确认

3. **超时处理**：
   - Redis Key 过期触发
   - 自动发送激活失败消息
   - 清理已分配资源

### 3. 30秒超时机制

#### 设计目标

确保容器激活过程的可靠性，防止资源泄漏和僵尸容器。

#### 实现机制

```java
// 1. 设置超时监控
redisExternalService.setMQWaitDockerInfo(docker.getId());
// Redis Key: "mq:waitDockerInfo:{dockerId}", TTL: 30秒

// 2. 容器内 Agent 发送确认消息
DockerInfoMQMsg message = new DockerInfoMQMsg();
message.setDockerId(dockerId);
// 发送到 RabbitMQ

// 3. 超时处理
@Override
public void onMessage(Message message, byte[] pattern) {
    if (expiredKey.startsWith(RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO)) {
        Long dockerId = Long.parseLong(expiredKey.replace(prefix, ""));
        dockerService.sendActiveFailResult(dockerId);
    }
}
```

#### 容错设计

- **分布式锁**：防止并发激活冲突
- **重试机制**：支持激活失败重试
- **资源清理**：超时后自动回收资源
- **状态恢复**：支持断点续传和状态修复

### 4. 消息队列架构

#### 消息类型

1. **激活消息** (playground.active.{playgroundId})
2. **心跳消息** (docker.heartbeat.{dockerId})
3. **状态更新** (docker.info.{dockerId})
4. **命令执行** (docker.runcmd.{dockerId})

#### 处理器映射

```java
// 消息路由配置
Map<MsgType, Handler> handlers = Map.of(
    MsgType.PLAYGROUND_ACTIVE, playgroundActiveMQHandler,
    MsgType.DOCKER_HEART_BEAT, dockerHeartBeatMQHandler,
    MsgType.DOCKER_INFO, dockerInfoMQHandler,
    MsgType.DOCKER_INFO_UPDATE, dockerInfoUpdateMQHandler
);
```

#### 异步处理

- 使用 @Async 注解实现异步处理
- 自定义线程池配置
- 支持消息重试和死信队列

### 5. 监控和故障恢复

#### 多层监控体系

1. **容器级监控**：
   - 30秒激活超时
   - 心跳检测 (5秒间隔)
   - 资源使用监控

2. **服务级监控**：
   - IDE Server 心跳 (15秒间隔)
   - Playground 活跃监控
   - 宿主机状态监控

3. **系统级监控**：
   - OpenTelemetry 链路追踪
   - 日志聚合和分析
   - 性能指标收集

#### 故障恢复策略

```java
// 心跳失败处理
public void heartBeatFail(Long playgroundId) {
    Long retryCount = redisExternalService.incrementPlaygroundHeartBeatRetry(playgroundId);
    if (retryCount >= RETRY_LIMIT) {
        // 连续失败3次，暂停 Playground
        playgroundService.pause(playground, "heartbeat-fail");
    } else {
        // 重试心跳检测
        redisExternalService.setPlaygroundHeartbeatWait(playgroundId);
    }
}
```

## 数据流和交互关系

### 容器激活流程

```
用户请求 → API网关 → PlaygroundService → DockerService → 
MemoryDockerServerSelector → DockerExternalService → Docker Engine →
容器启动 → Agent启动 → 发送DockerInfo → DockerInfoMQHandler →
更新状态 → 返回成功
```

### 心跳监控流程

```
容器Agent → 定时发送心跳 → DockerHeartBeatMQHandler → 
更新Redis状态 → 重置超时计时器 → 返回心跳响应
```

### 资源调度流程

```
资源请求 → 查询Redis ZSet → 选择最优服务器 → 
原子扣减资源 → 创建容器 → 更新负载信息
```

## 性能优化

### 1. 缓存策略

- **Redis 缓存**：宿主机负载、容器状态、会话信息
- **本地缓存**：配置信息、静态数据
- **分布式缓存**：跨节点数据共享

### 2. 异步处理

- **消息队列**：解耦业务逻辑，提高响应速度
- **异步任务**：容器操作、资源清理、监控检查
- **批量处理**：资源统计、日志处理

### 3. 连接池优化

- **数据库连接池**：HikariCP 配置优化
- **Redis 连接池**：Lettuce 连接池
- **HTTP 连接池**：Docker API 客户端优化

## 安全设计

### 1. 网络安全

- **API 网关**：统一认证和授权
- **容器隔离**：Docker 网络隔离
- **端口管理**：动态端口分配

### 2. 数据安全

- **敏感信息加密**：配置文件加密
- **访问控制**：基于角色的权限管理
- **审计日志**：操作记录和追踪

### 3. 容器安全

- **镜像安全**：基础镜像安全扫描
- **运行时安全**：容器运行时监控
- **资源限制**：CPU、内存、磁盘限制

## 扩展性设计

### 1. 水平扩展

- **无状态设计**：服务无状态，支持多实例部署
- **负载均衡**：支持多个管理节点
- **数据分片**：支持数据库分片

### 2. 垂直扩展

- **插件化架构**：支持功能模块插件化
- **多语言支持**：易于添加新的编程语言
- **多云部署**：支持多云环境部署

### 3. 容量规划

- **资源预测**：基于历史数据预测资源需求
- **弹性伸缩**：自动扩缩容机制
- **容量监控**：实时监控系统容量使用情况

## 关键技术实现

### 1. 容器调度算法详解

#### 内存负载调度算法

```java
public class MemoryDockerServerSelector implements DockerServerSelector {

    // 基于内存负载的服务器选择
    private DockerServer findServerByMemory(ResourcesLimit resourcesLimit) {
        // 1. 查询满足内存需求的服务器（按剩余内存降序）
        Set<TypedTuple<String>> servers = stringRedisTemplate.opsForZSet()
            .reverseRangeByScoreWithScores(
                RedisPrefix.DOCKER_SERVER_MEMORY_LEFT,
                resourcesLimit.getRAM(),           // 最小内存需求
                Double.POSITIVE_INFINITY,          // 无上限
                0, 1);                            // 只取第一个（最优）

        // 2. 验证服务器状态
        DockerServer server = dockerServerService.getDockerServer(serverId);
        if (server.getStatus() == DockerServerStatus.INACTIVE) {
            // 服务器离线，清理负载数据并重新选择
            cleanupOfflineServer(serverId);
            return findServerByMemory(resourcesLimit);
        }

        // 3. 原子性资源申请
        boolean success = proxyTarget.request(serverId, memoryMb, cpuCount);
        if (!success) {
            throw new ResourceNotEnoughException("资源申请失败");
        }

        return server;
    }

    // 原子性资源申请
    public boolean request(String dockerServerId, long memoryMb, int cpuCount) {
        // 扣减内存（原子操作）
        Double memoryAfter = stringRedisTemplate.opsForZSet()
            .incrementScore(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT,
                           dockerServerId, -1 * memoryMb);

        if (memoryAfter < 0) {
            // 内存不足，回滚操作
            stringRedisTemplate.opsForZSet()
                .incrementScore(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT,
                               dockerServerId, memoryMb);
            return false;
        }

        // 增加CPU负载
        stringRedisTemplate.opsForZSet()
            .incrementScore(RedisPrefix.DOCKER_SERVER_CPU_LOAD,
                           dockerServerId, cpuCount);
        return true;
    }
}
```

#### 调度策略优化

1. **负载均衡**：优先选择负载最低的服务器
2. **资源碎片化处理**：避免资源碎片化，提高利用率
3. **故障转移**：服务器故障时自动切换到其他服务器
4. **预留资源**：为系统预留一定的资源缓冲

### 2. 30秒超时机制实现

#### 超时监控设计

```java
public class RedisExternalService {

    // 设置30秒超时监控
    public void setMQWaitDockerInfo(Long dockerId) {
        String key = RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + dockerId;
        // 设置30秒TTL的Redis Key
        this.set(key, String.valueOf(dockerId), Duration.ofSeconds(30));
    }

    // 检查超时状态
    public String getMQWaitDockerInfo(String dockerId) {
        String key = RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + dockerId;
        return this.get(key);
    }

    // 移除超时监控
    public void removeMQWaitDockerInfo(String dockerId) {
        String key = RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + dockerId;
        stringRedisTemplate.delete(key);
    }
}
```

#### 超时处理机制

```java
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = message.toString();

        // 处理容器激活超时
        if (expiredKey.startsWith(RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO)) {
            Long dockerId = extractDockerIdFromKey(expiredKey);

            // 使用分布式锁防止重复处理
            String lockKey = "timeout:lock:" + dockerId;
            if (redisExternalService.getLock(lockKey, 10)) {
                try {
                    // 发送激活失败消息
                    dockerService.sendActiveFailResult(dockerId);

                    // 清理相关资源
                    cleanupTimeoutContainer(dockerId);
                } finally {
                    redisExternalService.releaseLock(lockKey);
                }
            }
        }
    }
}
```

### 3. 心跳监控机制

#### 多层心跳设计

```java
// 1. 容器级心跳（5秒间隔）
public class DockerHeartBeatMQHandler {

    @Async
    public void handle(String routingKey, String body) {
        DockerInfoMQMsg message = JsonUtil.jsonToPojo(body, DockerInfoMQMsg.class);

        // 更新容器状态
        redisExternalService.setDockerInfo(message);

        // 重置超时计时器
        redisExternalService.startPlaygroundMonitor(playground);

        // 返回心跳响应
        PlaygroundHeartBeatResultMQMsg result = new PlaygroundHeartBeatResultMQMsg();
        result.setStatus(PlaygroundMQStatus.OK);
        mqMessageSender.sendToIdeServer(MsgType.PLAYGROUND_HEART_BEAT, playground, result);
    }
}

// 2. IDE Server心跳（15秒间隔）
public void setIdeServerHeartBeat(String code) {
    String key = RedisPrefix.IDE_SERVER_HEARTBEAT + code;
    this.set(key, "1", Duration.ofSeconds(15));
}

// 3. Playground活跃监控
public void startPlaygroundMonitor(Playground playground) {
    String key = RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playground.getId();
    Long seconds = systemProperties.getPlayground().getInactiveSeconds().getCodeZone();
    this.set(key, "1", Duration.ofSeconds(seconds));
}
```

#### 故障恢复策略

```java
public class PlaygroundService {

    // 心跳失败处理
    public void heartBeatFail(Long playgroundId) {
        // 获取重试次数
        Long retryCount = redisExternalService.incrementPlaygroundHeartBeatRetry(playgroundId);

        if (retryCount >= RETRY_LIMIT) {
            // 连续失败3次，暂停Playground
            Playground playground = getById(playgroundId);
            pause(playground, "heartbeat-fail");

            // 清理重试计数器
            redisExternalService.removePlaygroundHeartbeatRetry(playgroundId);
        } else {
            // 重试心跳检测
            redisExternalService.setPlaygroundHeartbeatWait(playgroundId);
        }
    }
}
```

### 4. 消息队列架构实现

#### 消息路由配置

```java
@Component
public class IDEServerMQReceiver {

    // 消息处理器映射
    private final Map<MsgType, AbstractIDEServerMQHandler> handlers;

    @RabbitListener(queues = "#{ideServerQueue.name}")
    public void receive(String routingKey, String body) {
        try {
            // 解析消息类型
            MsgType msgType = MsgType.fromRoutingKey(routingKey);

            // 获取对应处理器
            AbstractIDEServerMQHandler handler = handlers.get(msgType);
            if (handler != null) {
                // 异步处理消息
                handler.handle(routingKey, body);
            }
        } catch (Exception e) {
            log.error("消息处理失败: routingKey={}, body={}", routingKey, body, e);
        }
    }
}
```

#### 消息重试机制

```java
@Component
public class MQMessageSender {

    // 同步等待消息响应
    public String sendToDockerAwait(MsgType type, Playground playground,
                                   String messageId, BaseMQMsg content, int timeoutSeconds) {
        // 设置等待标记
        redisExternalService.setMQMessageWait(messageId, timeoutSeconds);

        // 发送消息
        sendToDocker(type, playground, content);

        // 轮询等待响应
        int count = 0;
        int maxRetries = timeoutSeconds * 10; // 100ms间隔

        while (count < maxRetries) {
            if (redisExternalService.hasMQReplay(messageId)) {
                return redisExternalService.getMQReplayContent(messageId);
            }

            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            count++;
        }

        throw new TimeoutException("消息响应超时: " + messageId);
    }
}
```

## 部署架构

### 1. 单机部署

```yaml
# docker-compose.yml
version: '3'
services:
  # 基础服务
  mysql:
    image: mysql:5.7.31
    environment:
      MYSQL_ROOT_PASSWORD: rd123456
    ports:
      - "3306:3306"

  redis:
    image: redis:6.0.6-alpine
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3.9.9-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: agent
      RABBITMQ_DEFAULT_PASS: d42agent
    ports:
      - "5672:5672"
      - "15672:15672"

  # 应用服务
  paas-manager:
    build: ./d42paas_manager
    depends_on:
      - mysql
      - redis
      - rabbitmq
    environment:
      SPRING_PROFILES_ACTIVE: docker
    ports:
      - "8000:8000"
```

### 2. 集群部署

```yaml
# kubernetes 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paas-manager
spec:
  replicas: 3
  selector:
    matchLabels:
      app: paas-manager
  template:
    metadata:
      labels:
        app: paas-manager
    spec:
      containers:
      - name: paas-manager
        image: clacky/paas-manager:latest
        ports:
        - containerPort: 8000
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
```

### 3. 监控部署

```yaml
# 监控配置
version: '3'
services:
  # OpenTelemetry Collector
  otel-collector:
    image: otel/opentelemetry-collector:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver

  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14250:14250"

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
```

## 运维指南

### 1. 监控指标

#### 系统级指标
- CPU 使用率
- 内存使用率
- 磁盘 I/O
- 网络流量

#### 应用级指标
- 容器创建成功率
- 平均激活时间
- 30秒超时率
- 心跳失败率

#### 业务级指标
- 活跃用户数
- Playground 使用率
- 资源利用率
- 错误率

### 2. 告警配置

```yaml
# Prometheus 告警规则
groups:
- name: paas-alerts
  rules:
  - alert: ContainerActivationTimeout
    expr: rate(container_activation_timeout_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "容器激活超时率过高"
      description: "容器激活超时率超过10%"

  - alert: HighMemoryUsage
    expr: (docker_server_memory_used / docker_server_memory_total) > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Docker服务器内存使用率过高"
      description: "服务器 {{ $labels.server_id }} 内存使用率超过90%"
```

### 3. 故障排查

#### 常见问题诊断

1. **容器激活失败**
   ```bash
   # 检查调度日志
   grep "DockerScheduling-Active" /var/log/paas/application.log

   # 检查资源状态
   redis-cli ZRANGE dockerServer:memory 0 -1 WITHSCORES

   # 检查Docker服务状态
   docker ps -a | grep clacky
   ```

2. **30秒超时问题**
   ```bash
   # 检查超时日志
   grep "timeout(30s)" /var/log/paas/application.log

   # 检查MQ连接
   rabbitmqctl list_connections

   # 检查Redis连接
   redis-cli ping
   ```

3. **心跳失败问题**
   ```bash
   # 检查心跳日志
   grep "heartbeat" /var/log/paas/application.log

   # 检查容器网络
   docker exec -it <container_id> ping rabbitmq-server
   ```

### 4. 性能调优

#### JVM 调优
```bash
# 启动参数优化
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -jar paas-manager.jar
```

#### 数据库调优
```sql
-- MySQL 配置优化
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 268435456;  -- 256MB
```

#### Redis 调优
```conf
# redis.conf 优化
maxmemory 4gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

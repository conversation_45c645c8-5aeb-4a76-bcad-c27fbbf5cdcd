package infoHolder

import (
	"agent/config"
	"agent/consts"
	"agent/mq/message"
	"agent/utils/envUtils"
)

type infoHolder struct {
	//ragWrapper *rag.Wrapper
}

var holder *infoHolder

func InitHolder(
// ragWrapper *rag.Wrapper
) {

	holder = new(infoHolder)
	//holder.ragWrapper = ragWrapper
}

func GetDockerInfo() *message.DockerInfo {

	msg := new(message.DockerInfo)

	if holder == nil {
		return nil
	}

	msg.DockerId = envUtils.GetString(consts.PAAS_DockerId)
	msg.PlaygroundId = envUtils.GetString(consts.PAAS_PlaygroundId)
	//msg.RagStatus = holder.ragWrapper.GetRagStatus()
	return msg
}

func GetRunStatus() string {
	return ""
}
func GetRunResult() string {
	return ""
}

func IsGui() bool {
	return false
}

func DebugSupport() bool {
	return false
}

func InternalRunInfo() config.InternalRunInfo {
	return config.InternalRunInfo{}
}
